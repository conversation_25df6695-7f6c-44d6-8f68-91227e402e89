<?php
// Check if constants are already defined
if (!defined('DB_HOST')) {
    require_once __DIR__ . '/../config/database.php';
}

// Database class with singleton pattern
class Database {
    private static $instance = null;
    private $conn;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            // Log the error and show a user-friendly message
            error_log("Database Connection Error: " . $e->getMessage());
            die("Could not connect to the database. Please contact your system administrator.");
        }
    }
    
    // Get singleton instance
    public static function getInstance() {
        if (self::$instance == null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }
    
    // Get the connection
    public function getConnection() {
        return $this->conn;
    }
    
    // Prevent cloning of the instance
    private function __clone() {}
    
    // Prevent unserializing of the instance
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// For backward compatibility with code that uses $conn
if (!isset($conn)) {
    $conn = getDBConnection();
} 