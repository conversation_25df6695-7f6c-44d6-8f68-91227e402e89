// Logistics Dashboard Modern Interactions

// Custom plugin for doughnut chart center text
const centerTextPlugin = {
    id: 'centerText',
    afterDraw: (chart) => {
        if (chart.config.type === 'doughnut') {
            // Get canvas context
            const ctx = chart.ctx;

            // Get chart area dimensions
            const chartArea = chart.chartArea;
            const centerX = (chartArea.left + chartArea.right) / 2;
            const centerY = (chartArea.top + chartArea.bottom) / 2;

            // Calculate total value
            const total = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);

            // Save context state
            ctx.save();

            // Draw total label
            ctx.font = '600 14px Inter, sans-serif';
            ctx.fillStyle = '#64748b';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('TOTAL ASSETS', centerX, centerY - 15);

            // Draw total value
            ctx.font = '700 24px Inter, sans-serif';
            ctx.fillStyle = '#2E7D32';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(total.toString(), centerX, centerY + 15);

            // Restore context state
            ctx.restore();
        }
    }
};

document.addEventListener('DOMContentLoaded', function() {
    // Counter animation for stat values
    animateCounters();

    // Initialize charts with modern styling
    initializeCharts();

    // Add hover effects to cards
    initializeCardEffects();

    // Add custom animations
    initializeAnimations();
});

// Animate counter values with a smooth counting effect
function animateCounters() {
    const counterElements = document.querySelectorAll('.counter-value');

    counterElements.forEach(counter => {
        const target = parseInt(counter.innerText.replace(/,/g, ''), 10);
        const duration = 1500; // ms
        const start = 0;
        const startTime = performance.now();

        // Skip animation for zero values
        if (target === 0) return;

        function updateCounter(currentTime) {
            const elapsedTime = currentTime - startTime;
            const progress = Math.min(elapsedTime / duration, 1);

            // Easing function for smoother animation
            const easeOutQuart = function(t) {
                return 1 - Math.pow(1 - t, 4);
            };

            const easedProgress = easeOutQuart(progress);
            const currentValue = Math.floor(start + (target - start) * easedProgress);

            counter.innerText = new Intl.NumberFormat().format(currentValue);

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        }

        requestAnimationFrame(updateCounter);
    });
}

// Initialize charts with custom styling
function initializeCharts() {
    // Initialize inventory activity chart if it exists
    const inventoryActivityChart = document.getElementById('inventoryActivityChart');
    if (inventoryActivityChart) {
        const ctx = inventoryActivityChart.getContext('2d');

        // Get chart data from the page
        const activityDates = chartData?.activityDates || [];
        const stockInData = chartData?.stockInData || [];
        const stockOutData = chartData?.stockOutData || [];
        const transferData = chartData?.transferData || [];

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: activityDates,
                datasets: [
                    {
                        label: 'Stock In',
                        data: stockInData,
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 0.7)',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 4,
                        pointBackgroundColor: '#FFFFFF',
                        pointBorderColor: 'rgba(76, 175, 80, 1)',
                        pointBorderWidth: 2,
                        pointHoverRadius: 6,
                        fill: true
                    },
                    {
                        label: 'Stock Out',
                        data: stockOutData,
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 0.7)',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 4,
                        pointBackgroundColor: '#FFFFFF',
                        pointBorderColor: 'rgba(220, 53, 69, 1)',
                        pointBorderWidth: 2,
                        pointHoverRadius: 6,
                        fill: true
                    },
                    {
                        label: 'Transfers',
                        data: transferData,
                        backgroundColor: 'rgba(14, 165, 233, 0.2)',
                        borderColor: 'rgba(14, 165, 233, 0.7)',
                        borderWidth: 2,
                        tension: 0.4,
                        pointRadius: 4,
                        pointBackgroundColor: '#FFFFFF',
                        pointBorderColor: 'rgba(139, 195, 74, 1)',
                        pointBorderWidth: 2,
                        pointHoverRadius: 6,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(17, 24, 39, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: true,
                        usePointStyle: true,
                        boxWidth: 8,
                        boxPadding: 4,
                        titleFont: {
                            weight: 600,
                            size: 13
                        },
                        bodyFont: {
                            size: 12
                        },
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.raw} items`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            color: '#64748b'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(226, 232, 240, 0.5)'
                        },
                        ticks: {
                            font: {
                                size: 11
                            },
                            color: '#64748b',
                            padding: 8
                        }
                    }
                }
            }
        });
    }

    // Initialize assets by category chart if it exists
    const assetsByCategoryChart = document.getElementById('assetsByCategoryChart');
    if (assetsByCategoryChart) {
        const ctx = assetsByCategoryChart.getContext('2d');

        // Get chart data from the page
        let categoryLabels = categoryChartData?.categoryLabels || [];
        let categoryData = categoryChartData?.categoryData || [];
        let categoryColors = categoryChartData?.categoryColors || [];

        // Filter out supply categories (IT supplies, medical supplies, office supplies)
        const filteredData = [];
        const filteredLabels = [];
        const filteredColors = [];

        for (let i = 0; i < categoryLabels.length; i++) {
            const label = categoryLabels[i];
            // Skip any category that contains 'supply' or 'supplies' in the name
            if (!label.toLowerCase().includes('supply') && !label.toLowerCase().includes('supplies')) {
                filteredLabels.push(label);
                filteredData.push(categoryData[i]);
                filteredColors.push(categoryColors[i]);
            }
        }

        // Replace the original arrays with filtered ones
        categoryLabels = filteredLabels;
        categoryData = filteredData;
        categoryColors = filteredColors;

        // Register the center text plugin
        Chart.register(centerTextPlugin);

        const doughnutChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: categoryLabels,
                datasets: [{
                    data: categoryData,
                    backgroundColor: [
                        'rgba(46, 125, 50, 0.85)',   // Dark green
                        'rgba(33, 150, 243, 0.85)',   // Blue
                        'rgba(156, 39, 176, 0.85)',   // Purple
                        'rgba(255, 152, 0, 0.85)',    // Orange
                        'rgba(244, 67, 54, 0.85)',    // Red
                        'rgba(0, 188, 212, 0.85)'     // Cyan
                    ],
                    borderColor: '#FFFFFF',
                    borderWidth: 3,
                    hoverOffset: 15,
                    borderRadius: 6,
                    spacing: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '65%',
                radius: '90%',
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1500,
                    easing: 'easeOutQuart'
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            pointStyle: 'circle',
                            font: {
                                size: 11,
                                family: "'Inter', sans-serif"
                            }
                        }
                    },

                    tooltip: {
                        backgroundColor: 'rgba(17, 24, 39, 0.9)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: true,
                        usePointStyle: true,
                        boxWidth: 8,
                        boxPadding: 4,
                        titleFont: {
                            weight: 600,
                            size: 13
                        },
                        bodyFont: {
                            size: 12
                        },
                        callbacks: {
                            label: function(context) {
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                const label = context.label || 'Unknown';
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });

        // Add chart type toggle functionality
        document.querySelectorAll('.chart-type-toggle').forEach(button => {
            button.addEventListener('click', function() {
                const chartId = this.dataset.chart;
                const chartType = this.dataset.type;

                // Set active state for the button
                document.querySelectorAll(`[data-chart="${chartId}"]`).forEach(btn => {
                    btn.classList.remove('active');
                });
                this.classList.add('active');

                if (chartId === 'assetsByCategoryChart') {
                    // Store reference to the current chart instance
                    let currentChart = Chart.getChart(assetsByCategoryChart);

                    // Destroy existing chart if it exists
                    if (currentChart) {
                        currentChart.destroy();
                    }

                    // Get chart data from the page
                    let toggleCategoryLabels = categoryChartData?.categoryLabels || [];
                    let toggleCategoryData = categoryChartData?.categoryData || [];

                    // Filter out supply categories (IT supplies, medical supplies, office supplies)
                    const toggleFilteredData = [];
                    const toggleFilteredLabels = [];

                    for (let i = 0; i < toggleCategoryLabels.length; i++) {
                        const label = toggleCategoryLabels[i];
                        // Skip any category that contains 'supply' or 'supplies' in the name
                        if (!label.toLowerCase().includes('supply') && !label.toLowerCase().includes('supplies')) {
                            toggleFilteredLabels.push(label);
                            toggleFilteredData.push(toggleCategoryData[i]);
                        }
                    }

                    // Create new chart with selected type
                    const customColors = [
                        'rgba(46, 125, 50, 0.85)',   // Dark green
                        'rgba(33, 150, 243, 0.85)',   // Blue
                        'rgba(156, 39, 176, 0.85)',   // Purple
                        'rgba(255, 152, 0, 0.85)',    // Orange
                        'rgba(244, 67, 54, 0.85)',    // Red
                        'rgba(0, 188, 212, 0.85)'     // Cyan
                    ];

                    // Create different configurations based on chart type
                    let newConfig;

                    if (chartType === 'doughnut') {
                        newConfig = {
                            type: 'doughnut',
                            data: {
                                labels: toggleFilteredLabels,
                                datasets: [{
                                    data: toggleFilteredData,
                                    backgroundColor: customColors,
                                    borderColor: '#FFFFFF',
                                    borderWidth: 3,
                                    borderRadius: 6,
                                    hoverOffset: 15,
                                    spacing: 5
                                }]
                            },
                        };
                    } else {
                        // Bar chart configuration
                        newConfig = {
                            type: 'bar',
                            data: {
                                labels: toggleFilteredLabels,
                                datasets: [{
                                    data: toggleFilteredData,
                                    backgroundColor: customColors,
                                    borderColor: customColors,
                                    borderWidth: 0,
                                    borderRadius: 6,
                                    barPercentage: 0.7,
                                    borderSkipped: false
                                }]
                            },
                        };
                    }

                    // Add common options
                    newConfig.options = {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: chartType === 'doughnut' ? '65%' : null,
                        radius: chartType === 'doughnut' ? '90%' : null,
                        indexAxis: chartType === 'bar' ? 'y' : 'x',
                        animation: {
                            animateRotate: chartType === 'doughnut',
                            animateScale: chartType === 'doughnut',
                            duration: 1500,
                            easing: 'easeOutQuart'
                        },
                        plugins: {
                            legend: {
                                display: chartType === 'doughnut',
                                position: 'bottom',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true,
                                    pointStyle: 'circle',
                                    font: {
                                        size: 11,
                                        family: "'Inter', sans-serif"
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(17, 24, 39, 0.9)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                padding: 12,
                                cornerRadius: 8,
                                displayColors: true,
                                usePointStyle: true,
                                boxWidth: 8,
                                boxPadding: 4,
                                titleFont: {
                                    weight: 600,
                                    size: 13
                                },
                                bodyFont: {
                                    size: 12
                                },
                                callbacks: {
                                    label: function(context) {
                                        const value = context.raw || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                        const label = context.label || 'Unknown';
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        scales: chartType === 'bar' ? {
                            x: {
                                beginAtZero: true,
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    color: '#64748b'
                                }
                            },
                            y: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    },
                                    color: '#64748b'
                                }
                            }
                        } : {}
                    };

                    // Create new chart
                    if (chartType === 'doughnut') {
                        // Register the center text plugin for doughnut chart
                        Chart.register(centerTextPlugin);
                    } else if (chartType === 'bar') {
                        // For bar chart, make sure we don't show the legend at all
                        if (newConfig.options && newConfig.options.plugins && newConfig.options.plugins.legend) {
                            newConfig.options.plugins.legend.display = false;
                        }
                    }
                    new Chart(ctx, newConfig);
                }
            });
        });
    }
}

// Add interactive effects to dashboard cards
function initializeCardEffects() {
    // Add a subtle tilt effect on hover for cards
    const cards = document.querySelectorAll('.dashboard-card');

    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            // Get position relative to the card
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calculate rotation based on mouse position
            // Limit rotation to a small amount for a subtle effect
            const rotateX = (y / rect.height - 0.5) * -5; // -2.5 to 2.5 degrees
            const rotateY = (x / rect.width - 0.5) * 5; // -2.5 to 2.5 degrees

            // Apply the transform - more subtle for white design
            this.style.transform = `perspective(1000px) rotateX(${rotateX/2}deg) rotateY(${rotateY/2}deg) translateY(-5px)`;
        });

        // Reset on mouse leave
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

// Initialize custom animations
function initializeAnimations() {
    // Staggered entrance animations for dashboard cards
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Subtle shimmer effect on dashboard icons
    const icons = document.querySelectorAll('.dashboard-icon');
    icons.forEach(icon => {
        // Create shimmer element
        const shimmer = document.createElement('div');
        shimmer.classList.add('icon-shimmer');
        shimmer.style.cssText = `
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            z-index: 2;
            animation: shimmer 3s infinite;
        `;

        // Add keyframes for shimmer animation
        if (!document.querySelector('#shimmer-keyframes')) {
            const style = document.createElement('style');
            style.id = 'shimmer-keyframes';
            style.textContent = `
                @keyframes shimmer {
                    0% { left: -100%; }
                    40% { left: 100%; }
                    100% { left: 100%; }
                }
            `;
            document.head.appendChild(style);
        }

        icon.appendChild(shimmer);
    });
}