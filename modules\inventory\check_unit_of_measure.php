<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Check if unit_of_measure column exists in sku_master table
$checkColumnQuery = "SHOW COLUMNS FROM sku_master LIKE 'unit_of_measure'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);

if (mysqli_num_rows($checkColumnResult) == 0) {
    // Column doesn't exist, add it
    $addColumnQuery = "ALTER TABLE sku_master ADD COLUMN unit_of_measure VARCHAR(20) NOT NULL DEFAULT 'pcs' AFTER description";

    if (mysqli_query($conn, $addColumnQuery)) {
        echo "Column 'unit_of_measure' added successfully to sku_master table.<br>";
    } else {
        echo "Error adding column: " . mysqli_error($conn) . "<br>";
    }
} else {
    echo "Column 'unit_of_measure' already exists in sku_master table.<br>";
}

// Close connection
mysqli_close($conn);
?>
