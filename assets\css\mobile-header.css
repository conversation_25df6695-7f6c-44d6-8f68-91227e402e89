/**
 * Mobile Header Styles
 * This file contains styles for the mobile header toolbar and panels
 */

/* Mobile Header Toolbar */
@media (max-width: 767.98px) {
    .mobile-header-toolbar {
        margin-left: auto;
    }

    .mobile-header-toolbar .btn-link {
        color: var(--primary-green);
        font-size: 1.25rem;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin-left: 0.5rem;
        border-radius: 8px;
        background-color: rgba(46, 125, 50, 0.1);
        transition: all 0.2s ease;
    }

    .mobile-header-toolbar .btn-link:hover {
        background-color: rgba(46, 125, 50, 0.2);
        transform: scale(1.05);
    }

    .mobile-header-toolbar .btn-link:active {
        transform: scale(0.95);
    }

    /* Mobile Avatar */
    .mobile-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
        margin: 0;
    }

    /* Mobile Notification Badge */
    .mobile-notification-badge {
        position: absolute;
        top: 0;
        right: 0;
        font-size: 0.65rem;
        padding: 0.15rem 0.35rem;
        transform: translate(25%, -25%);
    }

    /* Hide desktop elements on mobile */
    .navbar-collapse {
        display: none !important;
    }
}

/* Mobile Panels */
.mobile-search-panel,
.mobile-notifications-panel,
.mobile-profile-panel {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: 1080;
    flex-direction: column;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.mobile-search-panel.show,
.mobile-notifications-panel.show,
.mobile-profile-panel.show {
    display: flex;
    transform: translateY(0);
}

.mobile-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: var(--primary-green);
    color: white;
}

.mobile-panel-header h5 {
    margin: 0;
    font-size: 1.1rem;
}

.mobile-panel-header .btn-close {
    filter: brightness(0) invert(1);
}

.mobile-panel-body {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Mobile Search */
.mobile-search-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.mobile-search-box {
    position: relative;
    margin-bottom: 1rem;
}

.mobile-search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.mobile-search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    font-size: 1rem;
}

.mobile-search-results {
    flex: 1;
    overflow-y: auto;
}

/* Mobile Search Results */
.mobile-search-loading,
.mobile-search-error,
.mobile-search-no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
}

.mobile-search-loading .spinner-border,
.mobile-search-error i,
.mobile-search-no-results i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.mobile-search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    margin-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.mobile-search-results-count {
    font-size: 0.85rem;
    color: #6c757d;
}

.mobile-search-section {
    margin-bottom: 1.5rem;
}

.mobile-search-section-title {
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: var(--primary-green);
    font-size: 0.95rem;
}

.mobile-search-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.2s ease;
}

.mobile-search-item:hover {
    background-color: #e9ecef;
}

.mobile-search-item-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary-green);
    border-radius: 50%;
    margin-right: 0.75rem;
}

.mobile-search-item-content {
    flex: 1;
}

.mobile-search-item-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.mobile-search-item-subtitle {
    font-size: 0.8rem;
    color: #6c757d;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.mobile-search-item-subtitle .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.mobile-search-view-all {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Mobile Notifications */
.mobile-notifications-container {
    height: 100%;
}

.mobile-notifications-list {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.mobile-notification-item {
    display: block;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.2s ease;
}

.mobile-notification-item:hover {
    background-color: #e9ecef;
}

.mobile-notification-item.unread {
    background-color: rgba(46, 125, 50, 0.05);
    border-left: 3px solid var(--primary-green);
}

.mobile-notification-item .notification-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary-green);
    border-radius: 50%;
    margin-right: 0.75rem;
}

.mobile-notification-item .notification-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.mobile-notification-item .notification-message {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.mobile-notification-item .notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

/* Mobile Profile */
.mobile-profile-container {
    height: 100%;
}

.mobile-profile-header {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.mobile-profile-avatar {
    width: 60px;
    height: 60px;
    min-width: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-green);
    color: white;
    border-radius: 50%;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.mobile-profile-info {
    flex: 1;
}

.mobile-profile-name {
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
}

.mobile-profile-role {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
    text-transform: capitalize;
}

.mobile-profile-menu {
    display: flex;
    flex-direction: column;
}

.mobile-profile-menu-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.2s ease;
}

.mobile-profile-menu-item:hover {
    background-color: #e9ecef;
}

/* Mobile Panel Overlay */
.mobile-panel-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1070;
    animation: fadeIn 0.3s ease;
}

.mobile-panel-overlay.show {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
