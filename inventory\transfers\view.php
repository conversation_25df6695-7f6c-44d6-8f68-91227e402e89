<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_HIMU, ROLE_DEPARTMENT, ROLE_HEALTH_CENTER])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get transfer ID
$transferId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$transferId) {
    setFlashMessage('error', 'Invalid transfer request');
    header("Location: index.php");
    exit;
}

try {
    // Fetch transfer details with all related information
    $stmt = $db->prepare("
        SELECT t.*, 
               i.name as item_name, i.sku,
               cat.name as category_name,
               src_dept.name as source_department,
               src_hc.name as source_health_center,
               dest_dept.name as destination_department,
               dest_hc.name as destination_health_center,
               req_user.full_name as requested_by_name,
               req_user.email as requested_by_email,
               app_user.full_name as approved_by_name,
               app_user.email as approved_by_email,
               COALESCE(fa.quantity, c.quantity) as current_quantity,
               IF(fa.fixed_asset_id IS NOT NULL, 'fixed_asset', 'consumable') as item_type
        FROM transfers t
        JOIN items i ON t.item_id = i.item_id
        JOIN categories cat ON i.category_id = cat.category_id
        LEFT JOIN departments src_dept ON t.source_department_id = src_dept.department_id
        LEFT JOIN health_centers src_hc ON t.source_health_center_id = src_hc.health_center_id
        LEFT JOIN departments dest_dept ON t.destination_department_id = dest_dept.department_id
        LEFT JOIN health_centers dest_hc ON t.destination_health_center_id = dest_hc.health_center_id
        LEFT JOIN users req_user ON t.requested_by = req_user.user_id
        LEFT JOIN users app_user ON t.approved_by = app_user.user_id
        LEFT JOIN fixed_assets fa ON i.item_id = fa.item_id
        LEFT JOIN consumables c ON i.item_id = c.item_id
        WHERE t.transfer_id = ?
    ");
    $stmt->execute([$transferId]);
    $transfer = $stmt->fetch();

    if (!$transfer) {
        throw new Exception('Transfer request not found');
    }

    // Check if user has access to this transfer
    $currentUser = $auth->getCurrentUser();
    if ($currentUser['role'] === ROLE_DEPARTMENT) {
        if ($transfer['source_department_id'] != $currentUser['department_id'] && 
            $transfer['destination_department_id'] != $currentUser['department_id']) {
            throw new Exception('Access denied');
        }
    } elseif ($currentUser['role'] === ROLE_HEALTH_CENTER) {
        if ($transfer['source_health_center_id'] != $currentUser['health_center_id'] && 
            $transfer['destination_health_center_id'] != $currentUser['health_center_id']) {
            throw new Exception('Access denied');
        }
    } elseif ($currentUser['role'] === ROLE_HIMU) {
        if (!($transfer['category_name'] === 'IT Equipment' || 
              $transfer['category_name'] === 'IT Supply' || 
              $transfer['requires_himu_approval'])) {
            throw new Exception('Access denied');
        }
    }

    // Fetch activity logs for this transfer
    $stmt = $db->prepare("
        SELECT al.*, u.full_name, u.email
        FROM activity_logs al
        JOIN users u ON al.user_id = u.user_id
        WHERE al.module = 'transfers'
        AND al.record_id = ?
        ORDER BY al.created_at DESC
    ");
    $stmt->execute([$transferId]);
    $activityLogs = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error viewing transfer: " . $e->getMessage());
    setFlashMessage('error', 'Error loading transfer details');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Transfer Request Details</h1>
        <div>
            <?php if ($transfer['status'] === 'pending' && canApproveTransfer($currentUser['role'], $transfer)): ?>
                <a href="approve.php?id=<?php echo $transferId; ?>" class="btn btn-success">
                    <i class="fas fa-check"></i> Review Transfer
                </a>
            <?php endif; ?>
            <?php if ($transfer['status'] === 'pending' && $transfer['requested_by'] === $currentUser['user_id']): ?>
                <a href="cancel.php?id=<?php echo $transferId; ?>" 
                   class="btn btn-danger"
                   onclick="return confirm('Are you sure you want to cancel this transfer request?')">
                    <i class="fas fa-times"></i> Cancel Request
                </a>
            <?php endif; ?>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Transfer Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Transfer Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Reference Number:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Status:</strong>
                        </div>
                        <div class="col-md-8">
                            <span class="badge bg-<?php 
                                echo $transfer['status'] === 'approved' ? 'success' : 
                                    ($transfer['status'] === 'pending' ? 'warning' : 
                                        ($transfer['status'] === 'rejected' ? 'danger' : 
                                            ($transfer['status'] === 'completed' ? 'info' : 
                                                ($transfer['status'] === 'himu_approved' ? 'primary' : 'secondary')))); 
                            ?>">
                                <?php echo ucfirst(str_replace('_', ' ', $transfer['status'])); ?>
                            </span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Item:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['item_name']); ?><br>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['sku']); ?></small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Category:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['category_name']); ?>
                            <?php if ($transfer['requires_himu_approval']): ?>
                                <span class="badge bg-info">Requires HIMU Approval</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Type:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo $transfer['item_type'] === 'fixed_asset' ? 'Fixed Asset' : 'Consumable'; ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Quantity:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo number_format($transfer['quantity']); ?>
                            <?php if ($transfer['item_type'] === 'consumable'): ?>
                                <br><small class="text-muted">Current Stock: <?php echo number_format($transfer['current_quantity']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>From:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php 
                            echo $transfer['source_department'] ? 
                                htmlspecialchars($transfer['source_department']) : 
                                ($transfer['source_health_center'] ? 
                                    htmlspecialchars($transfer['source_health_center']) : 'N/A'); 
                            ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>To:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php 
                            echo $transfer['destination_department'] ? 
                                htmlspecialchars($transfer['destination_department']) : 
                                ($transfer['destination_health_center'] ? 
                                    htmlspecialchars($transfer['destination_health_center']) : 'N/A'); 
                            ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Requested By:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['requested_by_name']); ?><br>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['requested_by_email']); ?></small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Request Date:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo formatDate($transfer['request_date']); ?>
                        </div>
                    </div>
                    <?php if ($transfer['remarks']): ?>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Request Remarks:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo nl2br(htmlspecialchars($transfer['remarks'])); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if ($transfer['approved_by']): ?>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Reviewed By:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($transfer['approved_by_name']); ?><br>
                                <small class="text-muted"><?php echo htmlspecialchars($transfer['approved_by_email']); ?></small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Review Date:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo formatDate($transfer['approval_date']); ?>
                            </div>
                        </div>
                        <?php if ($transfer['approval_remarks']): ?>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <strong>Review Remarks:</strong>
                                </div>
                                <div class="col-md-8">
                                    <?php echo nl2br(htmlspecialchars($transfer['approval_remarks'])); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Activity Timeline -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Activity Timeline</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php foreach ($activityLogs as $log): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-0">
                                        <?php 
                                        echo $log['action'] === 'create' ? 'Transfer Requested' :
                                            ($log['action'] === 'approve' ? 'Transfer Approved' :
                                                ($log['action'] === 'reject' ? 'Transfer Rejected' : 'Transfer Cancelled'));
                                        ?>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo formatDate($log['created_at']); ?> by 
                                        <?php echo htmlspecialchars($log['full_name']); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Timeline styles */
.timeline {
    position: relative;
    padding: 1rem;
    margin: 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    height: 100%;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    padding-left: 2.5rem;
    padding-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: 0;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #0d6efd;
    border: 2px solid #fff;
    margin-left: -6px;
}

.timeline-content {
    padding: 0.5rem 0;
}
</style>

<?php require_once '../../templates/footer.php'; ?>
