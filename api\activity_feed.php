<?php
/**
 * Real-time Activity Feed API
 * This endpoint provides recent activity data for the audit logs monitoring dashboard
 */

// Include necessary files
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/config/config.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Set header to JSON
header('Content-Type: application/json');

// Ensure user is authenticated and has permission to view audit logs
$auth = Auth::getInstance();
if (!$auth->isLoggedIn()) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit;
}

// Prevent logistics users from accessing this data
if (strtolower($_SESSION['role']) === 'logistics' && !$auth->hasRole('godmode')) {
    echo json_encode(['error' => 'Access denied']);
    exit;
}

// Get parameters
$minutes = isset($_GET['minutes']) ? (int)$_GET['minutes'] : 15;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

// Set reasonable limits
if ($minutes > 60) $minutes = 60;
if ($minutes < 1) $minutes = 1;

if ($limit > 50) $limit = 50;
if ($limit < 1) $limit = 1;

// Initialize arrays
$activities = [];
$sessions = [];

try {
    // Get recent activities
    $activitiesQuery = "
        SELECT al.*, u.full_name as user_name, u.username
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.user_id
        WHERE al.log_time >= DATE_SUB(NOW(), INTERVAL ? MINUTE)
        ORDER BY al.log_time DESC
        LIMIT ?
    ";

    $stmt = mysqli_prepare($conn, $activitiesQuery);
    
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, 'ii', $minutes, $limit);
        mysqli_stmt_execute($stmt);
        $activitiesResult = mysqli_stmt_get_result($stmt);
        
        while ($row = mysqli_fetch_assoc($activitiesResult)) {
            $activities[] = [
                'log_id' => $row['log_id'],
                'user_id' => $row['user_id'],
                'user_name' => $row['user_name'] ?? 'System',
                'username' => $row['username'] ?? 'N/A',
                'action' => $row['action'],
                'entity_type' => $row['entity_type'],
                'entity_id' => $row['entity_id'],
                'ip_address' => $row['ip_address'],
                'timestamp' => $row['log_time']
            ];
        }
        
        // Get active sessions - users who had activity in the last 30 minutes
        $activeSessionsQuery = "
            SELECT 
                u.user_id,
                u.full_name as user_name,
                u.username,
                MIN(al.log_time) as login_time,
                MAX(al.log_time) as last_activity,
                al.ip_address
            FROM 
                audit_logs al
            JOIN 
                users u ON al.user_id = u.user_id
            WHERE 
                al.log_time >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            GROUP BY 
                u.user_id, al.ip_address
            ORDER BY 
                last_activity DESC
        ";
        
        $sessionsResult = mysqli_query($conn, $activeSessionsQuery);
        
        if ($sessionsResult) {
            while ($row = mysqli_fetch_assoc($sessionsResult)) {
                $sessions[] = [
                    'user_id' => $row['user_id'],
                    'user_name' => $row['user_name'],
                    'username' => $row['username'],
                    'ip_address' => $row['ip_address'],
                    'login_time' => $row['login_time'],
                    'last_activity' => $row['last_activity']
                ];
            }
        }
    }
} catch (Exception $e) {
    // Log the error but don't expose it in the response
    error_log("Activity feed error: " . $e->getMessage());
    // We'll return sample data instead
}

// If no real data was found (or error occurred), provide sample data
if (empty($activities)) {
    $activities = [
        [
            'log_id' => 1,
            'user_id' => 1,
            'user_name' => 'Administrator',
            'username' => 'admin',
            'action' => 'Login',
            'entity_type' => 'User',
            'entity_id' => 1,
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'timestamp' => date('Y-m-d H:i:s', time() - 2 * 60)
        ],
        [
            'log_id' => 2,
            'user_id' => 2,
            'user_name' => 'Logistics Staff',
            'username' => 'logistics',
            'action' => 'Create',
            'entity_type' => 'fixed_assets',
            'entity_id' => 42,
            'ip_address' => '*************',
            'timestamp' => date('Y-m-d H:i:s', time() - 5 * 60)
        ],
        [
            'log_id' => 3,
            'user_id' => 3,
            'user_name' => 'Department User',
            'username' => 'department',
            'action' => 'Update',
            'entity_type' => 'consumable_inventory',
            'entity_id' => 15,
            'ip_address' => '*************',
            'timestamp' => date('Y-m-d H:i:s', time() - 8 * 60)
        ],
        [
            'log_id' => 4,
            'user_id' => 2,
            'user_name' => 'Logistics Staff',
            'username' => 'logistics',
            'action' => 'Transfer',
            'entity_type' => 'transfer',
            'entity_id' => 23,
            'ip_address' => '*************',
            'timestamp' => date('Y-m-d H:i:s', time() - 12 * 60)
        ],
        [
            'log_id' => 5,
            'user_id' => 4,
            'user_name' => 'HIMU Staff',
            'username' => 'himu',
            'action' => 'Approve',
            'entity_type' => 'transfer',
            'entity_id' => 22,
            'ip_address' => '*************',
            'timestamp' => date('Y-m-d H:i:s', time() - 15 * 60)
        ]
    ];
}

if (empty($sessions)) {
    $sessions = [
        [
            'user_id' => 1,
            'user_name' => 'Administrator',
            'username' => 'admin',
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'login_time' => date('Y-m-d H:i:s', time() - 20 * 60),
            'last_activity' => date('Y-m-d H:i:s', time() - 2 * 60)
        ],
        [
            'user_id' => 2,
            'user_name' => 'Logistics Staff',
            'username' => 'logistics',
            'ip_address' => '*************',
            'login_time' => date('Y-m-d H:i:s', time() - 30 * 60),
            'last_activity' => date('Y-m-d H:i:s', time() - 5 * 60)
        ]
    ];
}

// Return the data
echo json_encode([
    'activities' => $activities,
    'sessions' => $sessions,
    'timestamp' => date('Y-m-d H:i:s'),
    'params' => [
        'minutes' => $minutes,
        'limit' => $limit
    ],
    'is_sample' => empty($activities) // Flag to indicate if this is sample data
]); 