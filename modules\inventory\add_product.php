<?php
// Start output buffering at the beginning of the file
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern add product CSS
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-add-product-modern.css">';
echo '<link rel="stylesheet" href="/choims/assets/css/sku-entry-fix.css">';

// Ensure user is logged in
requireLogin();

// Prevent superadmin from accessing this page (direct URL protection)
if (strtolower($_SESSION['role']) === 'superadmin') {
    // Redirect to list view instead
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

// Get categories for dropdown
$categoryQuery = "
    SELECT category_id, category_name
    FROM categories
    WHERE category_name IN ('Office Supply', 'Medical Supply', 'IT Supply')
    ORDER BY category_name
";
$categoryResult = mysqli_query($conn, $categoryQuery);

// Get all consumable SKUs for dropdown
$skuQuery = "
    SELECT s.sku_id, s.sku_code, s.sku_name, c.category_name, c.category_id
    FROM sku_master s
    JOIN categories c ON s.category_id = c.category_id
    WHERE s.item_type = 'Consumable' OR
          c.category_name IN ('Office Supply', 'Medical Supply', 'IT Supply')
    ORDER BY s.sku_code
";
$skuResult = mysqli_query($conn, $skuQuery);
$skus = [];
while ($sku = mysqli_fetch_assoc($skuResult)) {
    $skus[] = $sku;
}

// No need to query locations anymore as we're using a fixed location (PROPERTY SUPPLY/LOGISTIC MANAGEMENT UNIT)

// Initialize variables for prefilling the form
$prefill = false;
$prefill_sku_code = '';
$prefill_sku_name = '';
$prefill_category_id = '';
$prefill_sku_id = '';
$prefill_description = '';
$prefill_initial_stock = 0;
$prefill_unit_price = 0;
$prefill_notes = '';
$prefill_low_stock_threshold = 50;
$prefill_critical_threshold = 10;

// Check if we're prefilling from stock_in.php
if (isset($_GET['prefill']) && $_GET['prefill'] == 1) {
    $prefill = true;

    if (isset($_GET['sku_code'])) {
        $prefill_sku_code = sanitizeInput($_GET['sku_code']);
    }

    if (isset($_GET['sku_name'])) {
        $prefill_sku_name = sanitizeInput($_GET['sku_name']);
    }

    if (isset($_GET['category_id'])) {
        $prefill_category_id = sanitizeInput($_GET['category_id']);
    }

    if (isset($_GET['sku_id'])) {
        $prefill_sku_id = sanitizeInput($_GET['sku_id']);
    }

    // Get additional details from the database if needed
    if (!empty($prefill_sku_id)) {
        $skuDetailsQuery = "
            SELECT s.*, c.category_name
            FROM sku_master s
            JOIN categories c ON s.category_id = c.category_id
            WHERE s.sku_id = ?
        ";
        $skuDetailsStmt = mysqli_prepare($conn, $skuDetailsQuery);
        mysqli_stmt_bind_param($skuDetailsStmt, 'i', $prefill_sku_id);
        mysqli_stmt_execute($skuDetailsStmt);
        $skuDetailsResult = mysqli_stmt_get_result($skuDetailsStmt);

        if ($skuDetailsResult && mysqli_num_rows($skuDetailsResult) > 0) {
            $skuDetails = mysqli_fetch_assoc($skuDetailsResult);

            // If any parameters weren't passed in URL, get them from DB
            if (empty($prefill_sku_code)) {
                $prefill_sku_code = $skuDetails['sku_code'];
            }

            if (empty($prefill_sku_name)) {
                $prefill_sku_name = $skuDetails['sku_name'];
            }

            if (empty($prefill_category_id)) {
                $prefill_category_id = $skuDetails['category_id'];
            }
        }
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate inputs
    $sku_code = sanitizeInput($_POST['sku_code']);
    $sku_name = sanitizeInput($_POST['sku_name']);
    $category_id = sanitizeInput($_POST['category_id']);
    $description = sanitizeInput($_POST['description']);
    $initial_stock = sanitizeInput($_POST['initial_stock']);
    $low_stock_threshold = sanitizeInput($_POST['low_stock_threshold']);
    $critical_threshold = isset($_POST['critical_threshold']) ? sanitizeInput($_POST['critical_threshold']) : intval($low_stock_threshold / 5); // Default to 20% of low stock threshold
    $unit_price = sanitizeInput($_POST['unit_price']);
    $unit_type = sanitizeInput($_POST['unit_type']);
    $notes = sanitizeInput($_POST['notes']);
    $location_id = sanitizeInput($_POST['location_id']);

    // Check if we're processing an existing SKU from prefill
    $existing_sku_id = null;
    if ($prefill && !empty($prefill_sku_id)) {
        $existing_sku_id = $prefill_sku_id;
    } else {
        // Only check for duplicate SKU code if we're not handling an existing SKU
        $checkQuery = "SELECT sku_id FROM sku_master WHERE sku_code = ?";
        $checkStmt = mysqli_prepare($conn, $checkQuery);
        mysqli_stmt_bind_param($checkStmt, 's', $sku_code);
        mysqli_stmt_execute($checkStmt);
        $checkResult = mysqli_stmt_get_result($checkStmt);

        if (mysqli_num_rows($checkResult) > 0) {
            $row = mysqli_fetch_assoc($checkResult);
            $existing_sku_id = $row['sku_id'];
        }
    }

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // If it's a new SKU, insert into sku_master
        if ($existing_sku_id === null) {
            // Insert into sku_master
            $skuQuery = "
                INSERT INTO sku_master (
                    sku_code, sku_name, category_id, item_type, description, unit_of_measure, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, 'Consumable', ?, ?, NOW(), NOW()
                )
            ";
            $skuStmt = mysqli_prepare($conn, $skuQuery);
            mysqli_stmt_bind_param(
                $skuStmt,
                'ssiss',
                $sku_code, $sku_name, $category_id, $description, $unit_type
            );
            mysqli_stmt_execute($skuStmt);

            // Get newly created sku_id
            $sku_id = mysqli_insert_id($conn);

            // Create audit log for new SKU
            createAuditLog(
                $_SESSION['user_id'],
                'create',
                'sku_master',
                $sku_id,
                null,
                json_encode([
                    'sku_code' => $sku_code,
                    'sku_name' => $sku_name,
                    'category_id' => $category_id,
                    'description' => $description,
                    'unit_of_measure' => $unit_type
                ])
            );

            // Create detailed audit log for new SKU
            $sku_data = [
                'sku_code' => $sku_code,
                'sku_name' => $sku_name,
                'category_id' => $category_id,
                'item_type' => 'Consumable',
                'description' => $description,
                'unit_of_measure' => $unit_type
            ];

            // Get category name for the log
            $categoryNameQuery = "SELECT category_name FROM categories WHERE category_id = ?";
            $categoryNameStmt = mysqli_prepare($conn, $categoryNameQuery);
            mysqli_stmt_bind_param($categoryNameStmt, 'i', $category_id);
            mysqli_stmt_execute($categoryNameStmt);
            $categoryNameResult = mysqli_stmt_get_result($categoryNameStmt);
            if ($categoryRow = mysqli_fetch_assoc($categoryNameResult)) {
                $sku_data['category_name'] = $categoryRow['category_name'];
            }
            mysqli_stmt_close($categoryNameStmt);

            // Log the SKU creation
            logDetailedAction($conn, $_SESSION['user_id'], 'create', 'consumable', $sku_id, [
                'entity_name' => $sku_name,
                'item_name' => $sku_name,
                'item_sku' => $sku_code,
                'item_category' => $sku_data['category_name'] ?? 'Unknown',
                'changes_summary' => "Created new SKU: {$sku_code} - {$sku_name}",
                'new_values' => json_encode($sku_data)
            ]);
        } else {
            // Use the existing SKU ID
            $sku_id = $existing_sku_id;

            // Update the unit_of_measure for the existing SKU
            $updateUnitQuery = "UPDATE sku_master SET unit_of_measure = ? WHERE sku_id = ?";
            $updateUnitStmt = mysqli_prepare($conn, $updateUnitQuery);
            mysqli_stmt_bind_param($updateUnitStmt, 'si', $unit_type, $sku_id);
            mysqli_stmt_execute($updateUnitStmt);

            // Check if this SKU already has inventory at this location
            $checkInventoryQuery = "SELECT inventory_id FROM consumable_inventory WHERE sku_id = ? AND location_id = ?";
            $checkInventoryStmt = mysqli_prepare($conn, $checkInventoryQuery);
            mysqli_stmt_bind_param($checkInventoryStmt, 'ii', $sku_id, $location_id);
            mysqli_stmt_execute($checkInventoryStmt);
            $checkInventoryResult = mysqli_stmt_get_result($checkInventoryStmt);

            if (mysqli_num_rows($checkInventoryResult) > 0) {
                $error = "This product already exists in inventory at this location. Please use the Stock In page to add stock.";
                mysqli_rollback($conn);
                // We'll show the error and exit the try-catch block
                throw new Exception($error);
            }
        }

        // Determine status based on initial stock and unit type
        $status = 'Available';

        // Special handling for box unit type - only mark as Out of Stock if quantity is 0
        if ($unit_type === 'box') {
            if ($initial_stock <= 0) {
                $status = 'Out of Stock';
            } elseif ($initial_stock <= $low_stock_threshold) {
                $status = 'Low Stock';
            }
        } else {
            // Standard handling for other unit types
            if ($initial_stock <= $critical_threshold) {
                $status = 'Out of Stock';
            } elseif ($initial_stock <= $low_stock_threshold) {
                $status = 'Low Stock';
            }
        }

        // Insert inventory record
        $inventoryQuery = "
            INSERT INTO consumable_inventory (
                sku_id, location_id, current_quantity, min_quantity,
                status, low_stock_threshold, critical_threshold, last_restock_date,
                created_at, updated_at
            ) VALUES (
                ?, ?, ?, 0, ?, ?, ?, NOW(), NOW(), NOW()
            )
        ";
        $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
        mysqli_stmt_bind_param(
            $inventoryStmt,
            'iiisii',
            $sku_id, $location_id, $initial_stock, $status, $low_stock_threshold, $critical_threshold
        );
        mysqli_stmt_execute($inventoryStmt);

        // Get newly created inventory_id
        $inventory_id = mysqli_insert_id($conn);

        // Record transaction if initial stock > 0
        if ($initial_stock > 0) {
            $transactionQuery = "
                INSERT INTO consumable_transactions (
                    inventory_id, transaction_type, quantity,
                    destination_location_id, reference_document, unit_cost,
                    remarks, performed_by, transaction_date
                ) VALUES (
                    ?, 'Stock In', ?, ?, 'Initial Stock', ?, ?, ?, NOW()
                )
            ";
            $transactionStmt = mysqli_prepare($conn, $transactionQuery);
            $stockInRemarks = "Initial stock with unit type: " . $unit_type;
            if (!empty($notes)) {
                $stockInRemarks .= ". Notes: " . $notes;
            }

            mysqli_stmt_bind_param(
                $transactionStmt,
                'iiidsi',
                $inventory_id, $initial_stock, $location_id, $unit_price, $stockInRemarks, $_SESSION['user_id']
            );
            mysqli_stmt_execute($transactionStmt);
        }

        // Create audit log for inventory
        createAuditLog(
            $_SESSION['user_id'],
            'create',
            'consumable_inventory',
            $inventory_id,
            null,
            json_encode([
                'sku_id' => $sku_id,
                'location_id' => $location_id,
                'current_quantity' => $initial_stock,
                'status' => $status,
                'low_stock_threshold' => $low_stock_threshold,
                'critical_threshold' => $critical_threshold
            ])
        );

        // Create detailed audit log for inventory
        // Get SKU details
        $skuQuery = "SELECT sku_name, sku_code FROM sku_master WHERE sku_id = ?";
        $skuStmt = mysqli_prepare($conn, $skuQuery);
        mysqli_stmt_bind_param($skuStmt, 'i', $sku_id);
        mysqli_stmt_execute($skuStmt);
        $skuResult = mysqli_stmt_get_result($skuStmt);
        $sku = mysqli_fetch_assoc($skuResult);
        mysqli_stmt_close($skuStmt);

        // Get location name
        $locationQuery = "SELECT location_name FROM locations WHERE location_id = ?";
        $locationStmt = mysqli_prepare($conn, $locationQuery);
        mysqli_stmt_bind_param($locationStmt, 'i', $location_id);
        mysqli_stmt_execute($locationStmt);
        $locationResult = mysqli_stmt_get_result($locationStmt);
        $location = mysqli_fetch_assoc($locationResult);
        mysqli_stmt_close($locationStmt);

        // Prepare inventory data for logging
        $inventory_data = [
            'sku_id' => $sku_id,
            'sku_name' => $sku['sku_name'] ?? 'Unknown',
            'sku_code' => $sku['sku_code'] ?? 'Unknown',
            'location_id' => $location_id,
            'location_name' => $location['location_name'] ?? 'Unknown',
            'current_quantity' => $initial_stock,
            'status' => $status,
            'low_stock_threshold' => $low_stock_threshold,
            'critical_threshold' => $critical_threshold
        ];

        // Log the inventory creation
        logConsumableAction($conn, $_SESSION['user_id'], 'create', $inventory_id, null, $inventory_data);

        // If initial stock was added, log the stock transaction
        if ($initial_stock > 0 && isset($transactionStmt)) {
            $transaction_id = mysqli_insert_id($conn);
            $transaction_data = [
                'inventory_id' => $inventory_id,
                'transaction_type' => 'Stock In',
                'quantity' => $initial_stock,
                'destination_location_id' => $location_id,
                'destination_location_name' => $location['location_name'] ?? 'Unknown',
                'reference_document' => 'Initial Stock',
                'unit_cost' => $unit_price,
                'remarks' => $stockInRemarks
            ];
            logStockTransaction($conn, $_SESSION['user_id'], 'stock_in', $transaction_id, $transaction_data);
        }

        // Commit transaction
        mysqli_commit($conn);

        // Success message to show after redirect
        $_SESSION['success_message'] = ($existing_sku_id === null)
            ? "Product created and added to inventory successfully!"
            : "Existing product added to inventory successfully!";

        // Redirect to inventory list
        if (headers_sent()) {
            echo '<script>window.location.href = "/choims/modules/inventory/list.php?success=1";</script>';
            exit();
        } else {
        header("Location: /choims/modules/inventory/list.php?success=1");
        exit();
        }

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error = "Error creating product: " . $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <!-- Page heading -->
    <div class="page-header mb-4 animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8">
                <?php if ($prefill): ?>
                <h1 class="page-title"><i class="fas fa-box-open"></i> Add Existing SKU to Consumables</h1>
                <p class="page-subtitle">Register an existing SKU in the consumables system for your location</p>
                <?php else: ?>
                <h1 class="page-title"><i class="fas fa-box-open"></i> Add New Product</h1>
                <p class="page-subtitle">Register a new consumable item in the consumables system</p>
                <?php endif; ?>
            </div>
            <div class="col-md-4 text-md-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/choims/dashboards/logistics.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="/choims/modules/inventory/list.php">Consumables</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add Product</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1 fw-bold">Error!</h5>
                    <p class="mb-0"><?php echo $error; ?></p>
                </div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($prefill): ?>
        <div class="alert alert-info d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>
                    <h5 class="mb-1 fw-bold">Form Prefilled</h5>
                    <p class="mb-0">This form has been prefilled with data from the SKU you selected. Please review and adjust the information as needed before saving.</p>
                </div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-12">
            <form method="post" action="" id="productForm" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Product Basic Information -->
                        <div class="card animate__animated animate__fadeInUp animate__faster">
                            <div class="card-header">
                                <div class="card-header-title">
                                    <i class="fas fa-info-circle"></i>
                                    Basic Information
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <div class="sku-selection-container">
                                                <!-- Toggle buttons for selection mode -->
                                                <div class="btn-group mb-3 w-100" role="group" aria-label="SKU Entry Mode">
                                                    <button type="button" class="btn btn-outline-primary active" id="select_existing_sku">Select Existing SKU</button>
                                                    <button type="button" class="btn btn-outline-primary" id="enter_new_sku">Enter New SKU</button>
                                                </div>

                                                <!-- Existing SKU dropdown -->
                                                <div id="sku_select_container">
                                                    <select class="form-select rounded-select" id="sku_code_select">
                                                        <option value="">Type to search or select a SKU code</option>
                                                        <?php foreach ($skus as $sku): ?>
                                                        <option value="<?php echo htmlspecialchars($sku['sku_code']); ?>"
                                                                data-sku-id="<?php echo $sku['sku_id']; ?>"
                                                                data-sku-name="<?php echo htmlspecialchars($sku['sku_name']); ?>"
                                                                data-category-id="<?php echo $sku['category_id']; ?>">
                                                            <?php echo htmlspecialchars($sku['sku_code']); ?> - <?php echo htmlspecialchars($sku['sku_name']); ?> (<?php echo htmlspecialchars($sku['category_name']); ?>)
                                                        </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <!-- New SKU input field -->
                                                <input type="text" class="form-control rounded-pill" id="sku_code" name="sku_code" required value="<?php echo $prefill_sku_code; ?>" placeholder="Enter new SKU code" style="display: none;">
                                            </div>
                                            <label for="sku_code_select" class="sku-label">SKU Code <span class="text-danger">*</span></label>
                                            <div class="form-text mt-2" id="sku_help_text">Select from existing SKUs or click "Enter New SKU" button above</div>
                                            <div class="invalid-feedback">Please enter a SKU code</div>
                                            <div id="sku_warning" style="display: none;"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="sku_name" name="sku_name" required value="<?php echo $prefill_sku_name; ?>">
                                            <label for="sku_name">Product Name <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please enter a product name</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="category_id" name="category_id" required>
                                                <option value="">Select Category</option>
                                                <?php
                                                mysqli_data_seek($categoryResult, 0);
                                                while ($category = mysqli_fetch_assoc($categoryResult)) {
                                                    $selected = ($prefill_category_id == $category['category_id']) ? 'selected' : '';
                                                    echo "<option value='" . $category['category_id'] . "' {$selected}>" . $category['category_name'] . "</option>";
                                                }
                                                ?>
                                            </select>
                                            <label for="category_id">Category <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please select a category</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="hidden" id="location_id" name="location_id" value="5">
                                            <input type="text" class="form-control bg-light" id="location_display" value="PROPERTY SUPPLY/LOGISTIC MANAGEMENT UNIT" readonly>
                                            <label for="location_display">Location</label>
                                            <div class="form-text text-muted">Location is automatically set</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="description" name="description" style="height: 100px"><?php echo $prefill_description; ?></textarea>
                                            <label for="description">Description</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information (moved to bottom) -->
                        <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.2s;">
                            <div class="card-header">
                                <div class="card-header-title">
                                    <i class="fas fa-sticky-note"></i>
                                    Additional Information
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="notes" name="notes" style="height: 150px"><?php echo $prefill_notes; ?></textarea>
                                    <label for="notes">Notes</label>
                                </div>

                                <div class="tips-card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.3s;">
                                    <div class="tips-card-title">
                                        <i class="fas fa-info-circle"></i>
                                        Consumables Management Tips
                                    </div>
                                    <ul>
                                        <li>Set appropriate thresholds based on usage</li>
                                        <li>Keep descriptions clear and concise</li>
                                        <li>Use consistent SKU naming conventions</li>
                                    </ul>
                                </div>

                                <div class="status-levels-card animate__animated animate__fadeInUp animate__faster mt-4" style="animation-delay: 0.4s;">
                                    <div class="tips-card-title">
                                        <i class="fas fa-chart-line"></i>
                                        Stock Status Levels
                                    </div>
                                    <div class="status-level">
                                        <span class="badge bg-success bg-opacity-10 text-success status-level-badge">
                                            <i class="fas fa-check-circle"></i> Available
                                        </span>
                                        <span class="status-level-text">Above low stock threshold</span>
                                    </div>
                                    <div class="status-level">
                                        <span class="badge bg-warning bg-opacity-10 text-warning status-level-badge">
                                            <i class="fas fa-exclamation-circle"></i> Low Stock
                                        </span>
                                        <span class="status-level-text">Between critical and low thresholds</span>
                                    </div>
                                    <div class="status-level">
                                        <span class="badge bg-danger bg-opacity-10 text-danger status-level-badge">
                                            <i class="fas fa-times-circle"></i> Out of Stock
                                        </span>
                                        <span class="status-level-text">Below critical threshold</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Consumables Details (renamed from Inventory Details) -->
                        <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.1s;">
                            <div class="card-header">
                                <div class="card-header-title">
                                    <i class="fas fa-clipboard-list"></i>
                                    Consumables Details
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="initial_stock" name="initial_stock" min="0" value="<?php echo $prefill_initial_stock; ?>" required>
                                            <label for="initial_stock">Initial Stock <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please enter a valid quantity</div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold" min="1" value="50" required>
                                            <label for="low_stock_threshold">Low Stock Alert <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please enter a threshold value</div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="critical_threshold" name="critical_threshold" min="0" value="10">
                                            <label for="critical_threshold">Critical Threshold</label>
                                            <div class="form-text">Defaults to 0 for box items, 20% of low stock for other units</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="unit_price" name="unit_price" min="0" step="0.01" value="<?php echo $prefill_unit_price; ?>">
                                            <label for="unit_price">Unit Price</label>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <div class="input-group">
                                                <select class="form-select" id="unit_type" name="unit_type" required>
                                                    <option value="">Select Unit Type</option>
                                                    <?php
                                                    // Check if unit_types table exists
                                                    $checkTableQuery = "SHOW TABLES LIKE 'unit_types'";
                                                    $checkTableResult = mysqli_query($conn, $checkTableQuery);

                                                    if (mysqli_num_rows($checkTableResult) > 0) {
                                                        // Table exists, fetch unit types from database
                                                        $unitTypesQuery = "SELECT unit_type FROM unit_types ORDER BY unit_type";
                                                        $unitTypesResult = mysqli_query($conn, $unitTypesQuery);

                                                        while ($unitType = mysqli_fetch_assoc($unitTypesResult)) {
                                                            echo "<option value='" . htmlspecialchars($unitType['unit_type']) . "'>" . htmlspecialchars($unitType['unit_type']) . "</option>";
                                                        }
                                                    } else {
                                                        // Table doesn't exist, use hardcoded values
                                                        $defaultUnitTypes = ['pcs', 'box', 'pack', 'bottle', 'roll', 'set', 'gallon', 'ream'];
                                                        foreach ($defaultUnitTypes as $type) {
                                                            echo "<option value='" . htmlspecialchars($type) . "'>" . htmlspecialchars($type) . "</option>";
                                                        }
                                                    }
                                                    ?>
                                                </select>
                                                <button type="button" class="btn btn-success" id="addUnitTypeBtn" data-bs-toggle="modal" data-bs-target="#addUnitTypeModal">
                                                    <i class="fas fa-plus"></i> New
                                                </button>
                                            </div>
                                            <label for="unit_type">Unit Type <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please select a unit type</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Buttons (moved to right side) -->
                                <div class="mt-4 mb-3 animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.5s;">
                                    <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                        <i class="fas fa-save me-2"></i><?php echo ($prefill) ? 'Add to Consumables' : 'Save Product'; ?>
                                    </button>
                                    <a href="/choims/modules/inventory/list.php" class="btn btn-outline-secondary btn-lg w-100">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern styling is now in inventory-add-product-modern.css -->

<!-- Using standard HTML select with keyboard filtering -->

<!-- Custom CSS for this page -->
<style>
/* Fix for the SKU code text overlay issue */
.sku-label {
    z-index: 999;
    pointer-events: none;
}

/* Ensure the SKU selection container has proper spacing */
.sku-selection-container {
    margin-top: 10px;
    position: relative;
}

/* Fix for the tab buttons */
.btn-group {
    margin-bottom: 15px !important;
    z-index: 1000;
    position: relative;
}

/* Ensure the select2 dropdown is properly positioned */
#sku_select_container {
    position: relative;
    z-index: 1;
}

/* Improve the form layout */
.form-floating > label {
    z-index: 99;
}

/* Make buttons more prominent */
.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Improve card styling */
.card {
    margin-bottom: 25px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

/* Ensure form elements have consistent height */
.form-control, .form-select {
    height: calc(3.5rem + 2px) !important;
}

/* Fix for unit type input group */
.form-floating > .input-group {
    height: calc(3.5rem + 2px) !important;
    display: flex;
}

.form-floating > .input-group > .form-select {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.form-floating > .input-group > .btn {
    height: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    z-index: 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show loading animation
    document.querySelectorAll('.animate__animated').forEach(function(element, index) {
        element.style.opacity = '0';
        setTimeout(function() {
            element.style.opacity = '1';
        }, 100 * index);
    });

    // Add keyboard navigation for standard dropdown
    const skuSelect = document.getElementById('sku_code_select');

    // Enable filtering when typing in the dropdown
    skuSelect.addEventListener('keydown', function(e) {
        // Don't handle special keys like arrows, enter, etc.
        if (e.key.length > 1) return;

        const letter = e.key.toLowerCase();
        const options = Array.from(this.options);

        // Find the first option that starts with the typed letter
        const matchingOption = options.find(option => {
            // Skip the placeholder option
            if (!option.value) return false;

            // Check if the option text starts with the typed letter
            return option.text.toLowerCase().startsWith(letter);
        });

        // If found, select it
        if (matchingOption) {
            this.value = matchingOption.value;

            // Trigger change event to update other fields
            const event = new Event('change', { bubbles: true });
            this.dispatchEvent(event);
        }
    });

    // Handle toggle between existing SKU selection and new SKU entry
    $('#select_existing_sku').on('click', function() {
        $(this).addClass('active');
        $('#enter_new_sku').removeClass('active');
        $('#sku_select_container').show();
        $('#sku_code').hide();
        $('#sku_help_text').text('Select from existing SKUs or click "Enter New SKU" button above');
    });

    $('#enter_new_sku').on('click', function() {
        $(this).addClass('active');
        $('#select_existing_sku').removeClass('active');
        $('#sku_select_container').hide();
        $('#sku_code').show().focus();
        $('#sku_help_text').text('Enter a new unique SKU code');

        // Clear any selected SKU
        $('#sku_code_select').val(null).trigger('change');
        $('#sku_warning').hide();
    });

    // Handle SKU selection
    $('#sku_code_select').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const skuCode = selectedOption.val();
        const skuName = selectedOption.data('sku-name');
        const skuId = selectedOption.data('sku-id');
        const categoryId = selectedOption.data('category-id');

        if (skuCode) {
            // Fill in the SKU code field
            $('#sku_code').val(skuCode);

            // Fill in other fields if the SKU was selected from dropdown
            if (skuName) {
                $('#sku_name').val(skuName);
            }

            if (categoryId) {
                $('#category_id').val(categoryId);
            }

            // Check if this SKU already exists in inventory at this location
            $.ajax({
                url: '/choims/modules/inventory/check_inventory.php',
                type: 'GET',
                data: {
                    sku_id: skuId,
                    location_id: $('#location_id').val()
                },
                success: function(response) {
                    const data = JSON.parse(response);
                    if (data.exists) {
                        // Show warning that this SKU already exists in inventory
                        $('#sku_warning').html(`
                            <div class="alert alert-warning d-flex align-items-center animate__animated animate__fadeInUp mt-3" role="alert">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <strong>Warning:</strong> This SKU already exists in inventory at this location.
                                        <a href="/choims/modules/inventory/stock_in.php?sku_id=${skuId}" class="alert-link fw-bold">Click here</a> to add stock instead.
                                    </div>
                                    <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            </div>
                        `).show();
                    } else {
                        $('#sku_warning').hide();
                    }
                },
                error: function() {
                    console.error('Error checking inventory');
                }
            });
        }
    });

    // No need for select2:open event with standard dropdown

    // Bootstrap validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        // Make sure the hidden SKU code field gets the value from either source
        if ($('#enter_new_sku').hasClass('active')) {
            // We're in manual entry mode, make sure the SKU code field is used
            const skuCode = $('#sku_code').val().trim();
            if (skuCode) {
                // Make sure the form uses this value
                $('#sku_code').attr('name', 'sku_code');
            }
        } else {
            // We're in select mode, get the value from the select
            const selectedSkuCode = $('#sku_code_select').val();
            if (selectedSkuCode) {
                // Copy the selected value to the hidden input
                $('#sku_code').val(selectedSkuCode);
                $('#sku_code').attr('name', 'sku_code');
            }
        }

        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();

            // If validation fails, make sure the correct input is visible
            if ($('#sku_code').val() === '' && $('#enter_new_sku').hasClass('active')) {
                // Focus on the SKU code field if it's empty and we're in manual mode
                $('#sku_code').focus();
            }
        }

        form.classList.add('was-validated');
    }, false);

    // Automatically calculate critical threshold as 20% of low stock threshold
    // But adjust for box unit type to prevent 1 box from being "Out of Stock"
    const lowStockInput = document.getElementById('low_stock_threshold');
    const criticalInput = document.getElementById('critical_threshold');
    const unitTypeSelect = document.getElementById('unit_type');

    function updateCriticalThreshold() {
        if (lowStockInput.value) {
            const lowStock = parseInt(lowStockInput.value);
            const unitType = unitTypeSelect.value;

            // For box unit type, set critical threshold to 0 to prevent "Out of Stock" status for 1 box
            if (unitType === 'box') {
                criticalInput.value = 0;
            } else {
                criticalInput.value = Math.max(1, Math.floor(lowStock * 0.2));
            }
        }
    }

    // Update critical threshold when low stock threshold changes
    lowStockInput.addEventListener('input', updateCriticalThreshold);

    // Update critical threshold when unit type changes
    unitTypeSelect.addEventListener('change', updateCriticalThreshold);

    // Set initial values
    if (lowStockInput.value) {
        updateCriticalThreshold();
    }

    // Add dismiss functionality to alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const closeButton = alert.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                alert.classList.add('animate__fadeOutUp');
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }
    });

    // Add animation to the SKU warning
    const skuWarning = document.getElementById('sku_warning');
    if (skuWarning) {
        skuWarning.classList.add('animate__animated', 'animate__fadeIn');
    }

    // Handle adding new unit type
    $('#saveUnitTypeBtn').on('click', function() {
        const unitType = $('#new_unit_type').val().trim();

        if (!unitType) {
            $('#unitTypeModalMessage').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Please enter a unit type
                </div>
            `);
            return;
        }

        // Show loading state
        $(this).html('<i class="fas fa-spinner fa-spin me-1"></i>Saving...');
        $(this).prop('disabled', true);

        // Send AJAX request
        $.ajax({
            url: '/choims/modules/inventory/add_unit_type_ajax.php',
            type: 'POST',
            data: {
                unit_type: unitType
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    $('#unitTypeModalMessage').html(`
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            ${response.message}
                        </div>
                    `);

                    // Add the new unit type to the dropdown
                    $('#unit_type').append(`<option value="${response.unit_type}">${response.unit_type}</option>`);

                    // Select the new unit type
                    $('#unit_type').val(response.unit_type);

                    // Clear the form
                    $('#new_unit_type').val('');

                    // Close the modal after a short delay
                    setTimeout(function() {
                        $('#addUnitTypeModal').modal('hide');
                        $('#unitTypeModalMessage').html('');
                    }, 1500);
                } else {
                    // Show error message
                    $('#unitTypeModalMessage').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${response.error}
                        </div>
                    `);
                }
            },
            error: function() {
                // Show error message
                $('#unitTypeModalMessage').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        An error occurred while adding the unit type
                    </div>
                `);
            },
            complete: function() {
                // Reset button state
                $('#saveUnitTypeBtn').html('<i class="fas fa-save me-1"></i>Save Unit Type');
                $('#saveUnitTypeBtn').prop('disabled', false);
            }
        });
    });

    // Clear modal message when modal is hidden
    $('#addUnitTypeModal').on('hidden.bs.modal', function() {
        $('#unitTypeModalMessage').html('');
        $('#new_unit_type').val('');
    });
});
</script>

<!-- Add Unit Type Modal -->
<div class="modal fade" id="addUnitTypeModal" tabindex="-1" aria-labelledby="addUnitTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addUnitTypeModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>Add New Unit Type
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUnitTypeForm">
                    <div class="mb-3">
                        <label for="new_unit_type" class="form-label">Unit Type <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="new_unit_type" name="unit_type" required>
                        <div class="form-text">Enter a new unit type (e.g., carton, tube, etc.)</div>
                    </div>
                </form>
                <div id="unitTypeModalMessage"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" id="saveUnitTypeBtn" class="btn btn-success">
                    <i class="fas fa-save me-1"></i>Save Unit Type
                </button>
            </div>
        </div>
    </div>
</div>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>