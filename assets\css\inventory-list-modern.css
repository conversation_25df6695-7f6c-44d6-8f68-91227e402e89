/* Modern Inventory List Page Styling */

/* Stats Cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 6px 12px rgba(0, 0, 0, 0.08);
  padding: 1.75rem;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  opacity: 1;
}

.stat-card.stat-primary::before { background-color: var(--primary); }
.stat-card.stat-success::before { background-color: var(--success); }
.stat-card.stat-info::before { background-color: var(--info); }
.stat-card.stat-warning::before { background-color: var(--warning); }
.stat-card.stat-danger::before { background-color: var(--danger); }

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.stat-card.stat-primary .stat-icon { background-color: var(--primary); color: white; }
.stat-card.stat-success .stat-icon { background-color: var(--success); color: white; }
.stat-card.stat-info .stat-icon { background-color: var(--info); color: white; }
.stat-card.stat-warning .stat-icon { background-color: var(--warning); color: white; }
.stat-card.stat-danger .stat-icon { background-color: var(--danger); color: white; }

.stat-icon i {
  font-size: 1.75rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--dark);
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.95rem;
  color: var(--gray-500);
  font-weight: 500;
}

.bg-primary-bg {
  background-color: rgba(46, 125, 50, 0.1);
}

.bg-success-bg {
  background-color: rgba(16, 185, 129, 0.1);
}

.bg-warning-bg {
  background-color: rgba(245, 158, 11, 0.1);
}

.bg-danger-bg {
  background-color: rgba(239, 68, 68, 0.1);
}

.bg-info-bg {
  background-color: rgba(59, 130, 246, 0.1);
}

.rounded-4 {
  border-radius: 1rem !important;
}

.card .progress {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-full);
}

.card .progress-bar {
  border-radius: var(--radius-full);
}

.category-icon, .location-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.location-card {
  transition: all 0.2s ease;
  border-color: var(--gray-200) !important;
  background-color: var(--white);
  position: relative;
  overflow: hidden;
}

.location-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
  border-color: var(--primary-light) !important;
}

.user-location {
  border-color: var(--success) !important;
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
}

.user-location:hover {
  border-color: var(--success) !important;
}

.user-location-badge {
  position: absolute;
  top: 10px;
  right: -30px;
  background-color: var(--success);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 3px 30px;
  transform: rotate(45deg);
  z-index: 1;
}
:root {
  /* Colors - Soft Green Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --green-gradient: linear-gradient(90deg, var(--primary), var(--primary-light));
  --secondary: #607D8B;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Dashboard header section */
.dashboard-header {
  position: relative;
  background: var(--white);
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 1.5rem 2rem 1rem;
  border-radius: 0 0 20px 20px;
  color: var(--dark);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border-bottom: 1px solid var(--primary-soft);
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.dashboard-header::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
  pointer-events: none;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.dashboard-header .title-container {
  display: flex;
  align-items: center;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin: 0;
  letter-spacing: -0.025em;
}

.dashboard-title-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
}

.dashboard-title-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3), rgba(0, 0, 0, 0.05));
  pointer-events: none;
}

.dashboard-actions {
  display: flex;
  gap: 0.75rem;
}

.dashboard-date {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin: 0;
  position: relative;
  z-index: 1;
}

.dashboard-date i {
  margin-right: 0.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  color: var(--white);
  background: var(--primary-gradient);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 20px rgba(46, 125, 50, 0.25), 0 6px 6px rgba(46, 125, 50, 0.22), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  transform: translateY(-3px);
  box-shadow: 0 15px 25px rgba(46, 125, 50, 0.3), 0 10px 10px rgba(46, 125, 50, 0.2), inset 0 -2px 5px rgba(0, 0, 0, 0.2);
  color: var(--white);
}

.action-btn:hover::after {
  opacity: 1;
}

.action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(78, 115, 223, 0.3), inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-btn i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.action-primary {
  background: var(--primary-ultra-soft);
  border: 1px solid var(--primary-soft);
  color: var(--primary);
  box-shadow: 0 2px 4px rgba(46, 125, 50, 0.1);
}

.action-primary:hover {
  background: var(--primary-bg);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(46, 125, 50, 0.15);
  color: var(--primary-dark);
}

/* Responsive Adjustments for Header */
@media (max-width: 992px) {
  .dashboard-header {
    padding: 1.25rem 1.5rem 0.75rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-title-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .action-btn {
    padding: 0.7rem 1.25rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1.25rem 1.5rem 0.75rem;
  }

  .header-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .dashboard-actions {
    margin-top: 1rem;
    width: 100%;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-title-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .action-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .action-btn i {
    margin-right: 0.5rem;
  }
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: var(--space-5);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

/* Filter Section */
.filter-section {
  margin-bottom: var(--space-5);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.filter-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.filter-badge {
  background-color: var(--primary);
  color: var(--white);
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  margin-left: var(--space-2);
}

.filter-card {
  background-color: var(--white);
  border-radius: var(--radius-md);
  border: none;
  box-shadow: var(--shadow-sm);
  padding: var(--space-4);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.input-group-text {
  background-color: var(--gray-100);
  border: 1px solid var(--gray-300);
  color: var(--primary);
  border-radius: var(--radius-md);
}

.input-group .form-control {
  border-radius: var(--radius-md);
}

.input-group .input-group-text + .form-control {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group .form-control:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.btn-sm {
  padding: 0.4rem 1rem;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: #0ca678;
  border-color: #0ca678;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-light {
  background-color: var(--white);
  border-color: var(--gray-200);
  color: var(--gray-500);
}

.btn-light:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-rounded {
  border-radius: var(--radius-full);
}

/* Alerts */
.alert {
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  margin-right: var(--space-4);
}

.alert-success .alert-icon {
  background-color: rgba(16, 185, 129, 0.2);
}

.alert-danger .alert-icon {
  background-color: rgba(239, 68, 68, 0.2);
}

.alert-warning .alert-icon {
  background-color: rgba(245, 158, 11, 0.2);
}

/* Tables */
.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table th {
  font-weight: 600;
  color: var(--dark);
  border-bottom-width: 1px;
  padding: var(--space-3) var(--space-4);
  background-color: var(--gray-100);
}

.table th:first-child {
  border-top-left-radius: var(--radius-md);
}

.table th:last-child {
  border-top-right-radius: var(--radius-md);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-bottom-color: var(--gray-200);
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--gray-100);
}

.table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-md);
}

.table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-md);
}

.table-responsive {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background-color: var(--white);
}

/* Status Badges */
.badge {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
}

.badge i {
  margin-right: 0.25rem;
}

.badge.bg-primary.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-success.bg-opacity-10 {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: #10b981 !important;
}

.badge.bg-warning.bg-opacity-10 {
  background-color: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
}

.badge.bg-danger.bg-opacity-10 {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.badge.bg-info.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-secondary.bg-opacity-10 {
  background-color: rgba(107, 114, 128, 0.1) !important;
  color: #6b7280 !important;
}

/* Item Icons */
.item-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-bg);
  color: var(--primary);
  border-radius: var(--radius-md);
  margin-right: var(--space-3);
}

/* DataTables Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin-bottom: var(--space-3);
}

.dataTables_wrapper .dataTables_length select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.25rem 2rem 0.25rem 0.5rem;
  background-color: var(--white);
}

.dataTables_wrapper .dataTables_filter input {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: var(--radius-full);
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: var(--primary);
  color: var(--white) !important;
  border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gray-100);
  color: var(--primary) !important;
  border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  color: var(--gray-400) !important;
}

/* Fix DataTables styling to respect our rounded corners */
div.dataTables_wrapper div.dataTables_filter {
  margin-top: var(--space-3);
}

.dataTables_wrapper .row:first-child {
  margin-bottom: var(--space-3);
}

.dataTables_wrapper .row:last-child {
  margin-top: var(--space-3);
}

.dataTables_wrapper table.dataTable {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.dataTables_wrapper table.dataTable thead th {
  border-bottom: 1px solid var(--gray-200);
}

.dataTables_wrapper table.dataTable thead th:first-child {
  border-top-left-radius: var(--radius-md);
}

.dataTables_wrapper table.dataTable thead th:last-child {
  border-top-right-radius: var(--radius-md);
}

.dataTables_wrapper table.dataTable tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-md);
}

.dataTables_wrapper table.dataTable tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-md);
}

/* Loading State */
.table-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.table-loading-state .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: var(--space-4);
  color: var(--primary);
}

.table-loading-state p {
  color: var(--gray-500);
  font-size: 1.1rem;
  margin-bottom: 0;
}

/* Modal Styling */
.modal-content {
  border: none;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.modal-header {
  border-bottom: none;
  padding: var(--space-4) var(--space-5);
}

.modal-body {
  padding: var(--space-5);
}

.modal-footer {
  border-top: none;
  padding: var(--space-4) var(--space-5);
  background-color: var(--gray-100);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-8) var(--space-4);
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: var(--space-4);
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-500);
  margin-bottom: var(--space-2);
}

.empty-state-description {
  color: var(--gray-400);
  margin-bottom: var(--space-4);
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-card {
    padding: 1.25rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    margin-right: 1rem;
  }

  .stat-icon i {
    font-size: 1.5rem;
  }

  .stat-value {
    font-size: 1.75rem;
  }

  .card-header {
    padding: var(--space-3) var(--space-4);
  }

  .card-body {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 45px;
    height: 45px;
    margin-right: 0.75rem;
  }

  .stat-icon i {
    font-size: 1.25rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .filter-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-actions {
    margin-top: var(--space-2);
  }

  .btn {
    padding: 0.5rem 1rem;
  }
}
