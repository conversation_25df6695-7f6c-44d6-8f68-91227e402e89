/**
 * Custom Modal Implementation
 * This script provides a reliable modal implementation that doesn't rely on Bootstrap
 */

// Store all modal instances
const customModals = {};

// Initialize custom modals
function initCustomModals() {
  // Create overlay element if it doesn't exist
  if (!document.getElementById('customModalOverlay')) {
    const overlay = document.createElement('div');
    overlay.id = 'customModalOverlay';
    overlay.className = 'custom-modal-overlay';
    document.body.appendChild(overlay);

    // Close modals when clicking on overlay
    overlay.addEventListener('click', function() {
      closeAllCustomModals();
    });
  }

  // Find all repair modals and convert them to custom modals
  document.querySelectorAll('.repair-modal').forEach(function(modalElement) {
    const modalId = modalElement.id;
    const modalContent = modalElement.querySelector('.modal-content');
    const modalHeader = modalElement.querySelector('.modal-header');
    const modalBody = modalElement.querySelector('.modal-body');
    const modalFooter = modalElement.querySelector('.modal-footer');
    const modalTitle = modalElement.querySelector('.modal-title');

    // Create custom modal container
    const customModal = document.createElement('div');
    customModal.id = 'custom-' + modalId;
    customModal.className = 'custom-modal';

    // Create custom modal header
    const customModalHeader = document.createElement('div');
    customModalHeader.className = 'custom-modal-header';

    // Create custom modal title
    const customModalTitle = document.createElement('h5');
    customModalTitle.className = 'custom-modal-title';
    customModalTitle.innerHTML = modalTitle.innerHTML;

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.type = 'button';
    closeButton.className = 'custom-modal-close';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', function() {
      closeCustomModal('custom-' + modalId);
    });

    // Assemble header
    customModalHeader.appendChild(customModalTitle);
    customModalHeader.appendChild(closeButton);

    // Create custom modal body
    const customModalBody = document.createElement('div');
    customModalBody.className = 'custom-modal-body';
    customModalBody.innerHTML = modalBody.innerHTML;

    // Create custom modal footer
    const customModalFooter = document.createElement('div');
    customModalFooter.className = 'custom-modal-footer';

    // Convert footer buttons
    const footerButtons = modalFooter.querySelectorAll('button');
    footerButtons.forEach(function(button) {
      const customButton = document.createElement('button');
      customButton.type = 'button';
      customButton.className = 'custom-modal-btn';

      if (button.classList.contains('btn-secondary')) {
        customButton.classList.add('custom-modal-btn-secondary');
      } else {
        customButton.classList.add('custom-modal-btn-primary');
      }

      customButton.innerHTML = button.innerHTML;

      // Handle cancel button
      if (button.getAttribute('data-bs-dismiss') === 'modal') {
        customButton.addEventListener('click', function() {
          closeCustomModal('custom-' + modalId);
        });
      }

      // Handle form submission
      if (button.getAttribute('type') === 'submit') {
        const formId = button.getAttribute('form');
        if (formId) {
          customButton.addEventListener('click', function() {
            document.getElementById(formId).submit();
          });
        }
      }

      customModalFooter.appendChild(customButton);
    });

    // Assemble custom modal
    customModal.appendChild(customModalHeader);
    customModal.appendChild(customModalBody);
    customModal.appendChild(customModalFooter);

    // Add to document
    document.body.appendChild(customModal);

    // Store modal instance
    customModals[modalId] = {
      element: customModal,
      originalModal: modalElement
    };

    // Update form elements inside the custom modal
    const forms = customModalBody.querySelectorAll('form');
    forms.forEach(function(form) {
      // Make sure the form submits properly
      form.addEventListener('submit', function() {
        closeCustomModal('custom-' + modalId);
      });

      // Ensure all form field values are properly transferred from original modal
      const originalForm = modalElement.querySelector('form[id="' + form.id + '"]');
      if (originalForm) {
        // Transfer values from all input fields
        form.querySelectorAll('input, select, textarea').forEach(function(input) {
          const originalInput = originalForm.querySelector('[name="' + input.name + '"]');
          if (originalInput && originalInput.value) {
            input.value = originalInput.value;
          }
        });
      }
    });

    // Convert Bootstrap badges to custom badges
    const badges = customModalBody.querySelectorAll('.badge');
    badges.forEach(function(badge) {
      if (badge.classList.contains('bg-danger')) {
        badge.classList.remove('badge', 'bg-danger');
        badge.classList.add('custom-badge', 'custom-badge-danger');
      } else if (badge.classList.contains('bg-warning')) {
        badge.classList.remove('badge', 'bg-warning');
        badge.classList.add('custom-badge', 'custom-badge-warning');
      }
    });

    // Convert Bootstrap alerts to custom alerts
    const alerts = customModalBody.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
      if (alert.classList.contains('alert-info')) {
        alert.classList.remove('alert', 'alert-info');
        alert.classList.add('custom-alert', 'custom-alert-info');
      }
    });
  });
}

// Function to open a custom modal
function openCustomModal(modalId) {
  // First, close any open modals to prevent stacking issues
  closeAllCustomModals();

  const customModalId = 'custom-' + modalId;
  const customModal = document.getElementById(customModalId);
  const overlay = document.getElementById('customModalOverlay');
  const originalModal = document.getElementById(modalId);

  if (customModal && overlay) {
    // Synchronize form values between original and custom modal
    if (originalModal) {
      syncFormValues(originalModal, customModal);
    }

    // Reset display properties
    overlay.style.display = 'block';
    customModal.style.display = 'block';

    // Force a reflow to ensure transitions work
    void customModal.offsetWidth;

    // Show overlay
    overlay.classList.add('show');

    // Show modal
    customModal.classList.add('show');

    // Lock body scrolling
    document.body.classList.add('modal-open-custom');

    console.log('Opened custom modal:', customModalId);
  } else {
    console.error('Custom modal not found:', customModalId);
  }
}

// Function to synchronize form values between original and custom modal
function syncFormValues(originalModal, customModal) {
  // Find all forms in the original modal
  const originalForms = originalModal.querySelectorAll('form');

  originalForms.forEach(function(originalForm) {
    // Find the corresponding form in the custom modal
    const customForm = customModal.querySelector('form[id="' + originalForm.id + '"]');

    if (customForm) {
      // Transfer values from all input fields
      originalForm.querySelectorAll('input, select, textarea').forEach(function(originalInput) {
        if (originalInput.name) {
          const customInput = customForm.querySelector('[name="' + originalInput.name + '"]');
          if (customInput) {
            // Add debug logging for cost field
            if (originalInput.name === 'cost') {
              console.log('Syncing cost field:', originalInput.name, 'Original value:', originalInput.value, 'Custom value before:', customInput.value);
            }

            customInput.value = originalInput.value;

            // Add more debug logging for cost field
            if (originalInput.name === 'cost') {
              console.log('Custom value after sync:', customInput.value);
            }

            // For select elements, also update the selected option
            if (customInput.tagName === 'SELECT') {
              const selectedOption = originalInput.options[originalInput.selectedIndex];
              if (selectedOption) {
                for (let i = 0; i < customInput.options.length; i++) {
                  if (customInput.options[i].value === selectedOption.value) {
                    customInput.selectedIndex = i;
                    break;
                  }
                }
              }
            }

            // For checkboxes and radio buttons
            if (originalInput.type === 'checkbox' || originalInput.type === 'radio') {
              customInput.checked = originalInput.checked;
            }
          }
        }
      });
    }
  });
}

// Function to close a custom modal
function closeCustomModal(customModalId) {
  const customModal = document.getElementById(customModalId);
  const overlay = document.getElementById('customModalOverlay');

  if (customModal) {
    // Hide modal
    customModal.classList.remove('show');
    customModal.style.display = 'none';

    // Always hide overlay and unlock body scrolling to prevent stuck state
    if (overlay) {
      overlay.classList.remove('show');
      overlay.style.display = 'none';
    }

    // Unlock body scrolling
    document.body.classList.remove('modal-open-custom');

    console.log('Closed custom modal:', customModalId);
  }
}

// Function to close all custom modals
function closeAllCustomModals() {
  // Hide all modals
  const allModals = document.querySelectorAll('.custom-modal');
  allModals.forEach(function(modal) {
    modal.classList.remove('show');
    modal.style.display = 'none';
  });

  // Hide overlay
  const overlay = document.getElementById('customModalOverlay');
  if (overlay) {
    overlay.classList.remove('show');
    overlay.style.display = 'none';
  }

  // Unlock body scrolling
  document.body.classList.remove('modal-open-custom');

  console.log('Closed all custom modals');
}

// Initialize when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  // Initialize custom modals
  initCustomModals();

  // Dispatch event to notify that custom modals have been initialized
  document.dispatchEvent(new CustomEvent('customModalsInitialized'));

  // Add click handlers to buttons that should open modals
  document.querySelectorAll('.custom-modal-trigger').forEach(function(button) {
    const modalId = button.getAttribute('data-modal-id');
    if (modalId) {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        openCustomModal(modalId);
      });
    }
  });

  // Also handle old Bootstrap modal buttons for backward compatibility
  document.querySelectorAll('[data-bs-toggle="modal"]').forEach(function(button) {
    const targetId = button.getAttribute('data-bs-target');
    if (targetId) {
      const modalId = targetId.replace('#', '');

      button.addEventListener('click', function(e) {
        e.preventDefault();
        openCustomModal(modalId);
      });
    }
  });

  // Add keyboard event listener to close modals when Escape key is pressed
  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
      closeAllCustomModals();
    }
  });

  // Add a failsafe cleanup function
  window.addEventListener('beforeunload', function() {
    closeAllCustomModals();
  });
});
