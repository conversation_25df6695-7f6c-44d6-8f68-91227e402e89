/* Monthly Status Page Styling */
:root {
    --primary-color: #4e73df;
    --primary-dark: #2e59d9;
    --primary-light: #6f8df7;
    --success-color: #1cc88a;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --info-color: #36b9cc;
    --dark-color: #5a5c69;
    --light-color: #f8f9fc;
    --card-border-radius: 0.75rem;
    --transition-speed: 0.3s;
}

/* Status Cards */
.stat-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--card-border-radius);
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
    border: none;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.stat-card .card-body {
    padding: 1.5rem;
    position: relative;
    z-index: 1;
}

.stat-card-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.stat-card-success {
    background: linear-gradient(135deg, var(--success-color), #0da868);
    color: white;
}

.stat-card-warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a800);
    color: white;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.stat-link {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: color var(--transition-speed);
}

.stat-link:hover {
    color: white;
}

.stat-icon-wrapper {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0.25;
    font-size: 3rem;
    transition: transform var(--transition-speed);
}

.stat-card:hover .stat-icon-wrapper {
    transform: scale(1.1);
}

/* Content Card */
.content-card {
    border-radius: var(--card-border-radius);
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: box-shadow var(--transition-speed);
    overflow: hidden;
    margin-bottom: 1.5rem;
    background-color: white;
}

.content-card:hover {
    box-shadow: 0 0.3rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.content-card .card-header {
    background: linear-gradient(135deg, #4e73df, #224abe);
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content-card .card-header h5 {
    margin-bottom: 0;
    font-weight: 600;
}

/* Table Styling */
.table {
    margin-bottom: 0;
    background-color: white;
}

.table thead th {
    background-color: #ffffff;
    border-bottom: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem 1.5rem;
    color: #6e707e;
}

.table tbody td {
    padding: 1rem 1.5rem;
    vertical-align: middle;
    border-top: 1px solid #f0f0f0;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(78, 115, 223, 0.03);
}

.table tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

/* Status Badges */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 600;
    border-radius: 0.375rem;
}

.badge.bg-success {
    background-color: var(--success-color) !important;
}

.badge.bg-warning {
    background-color: var(--warning-color) !important;
    color: #5a5c69;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    padding: 0.375rem 1rem;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #0da868;
    border-color: #0da868;
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-info:hover {
    background-color: #2aa3b9;
    border-color: #2aa3b9;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-outline-warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-outline-warning:hover {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

/* Filter Buttons */
.filter-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.filter-buttons .btn {
    border-radius: 2rem;
    padding: 0.375rem 1rem;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Page Title */
.page-title {
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 1rem;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 2px;
}

.page-title h1 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-title p {
    color: #6e707e;
    margin-bottom: 0;
}

/* Progress Bar */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.progress-bar {
    border-radius: 4px;
}

/* Status Row Styling */
.status-row.completed {
    background-color: rgba(28, 200, 138, 0.05);
}

.status-row.pending {
    background-color: rgba(246, 194, 62, 0.05);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .stat-value {
        font-size: 1.75rem;
    }

    .stat-icon-wrapper {
        font-size: 2rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.75rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .content-card {
        background-color: #2c3144;
    }

    .content-card .card-header {
        background-color: #343a50;
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .table thead th {
        background-color: #2c3144;
        color: #d1d3e2;
    }

    .table tbody td {
        border-top-color: rgba(255, 255, 255, 0.1);
    }

    .table tbody tr:hover {
        background-color: rgba(78, 115, 223, 0.1);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Hover Effects */
.btn-group .btn {
    position: relative;
    overflow: hidden;
}

.btn-group .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.btn-group .btn:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}
