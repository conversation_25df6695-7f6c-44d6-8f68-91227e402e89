<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get summary statistics
try {
    // Get total fixed assets
    $stmt = $db->query("SELECT COUNT(*) as total FROM fixed_assets");
    $fixedAssetsCount = $stmt->fetch()['total'];

    // Get total consumables
    $stmt = $db->query("SELECT SUM(quantity) as total FROM inventory");
    $result = $stmt->fetch();
    $consumablesCount = isset($result['total']) ? $result['total'] : 0;

    // Get total items count 
    $stmt = $db->query("SELECT COUNT(*) as total FROM items");
    $itemsCount = $stmt->fetch()['total'];

    // Get pending transfers count
    $stmt = $db->query("SELECT COUNT(*) as total FROM transfer_requests WHERE status='pending'");
    $pendingTransfersCount = $stmt->fetch()['total'];

    // Get low stock count
    $stmt = $db->query("
        SELECT COUNT(*) as total 
        FROM inventory i 
        WHERE i.quantity <= i.minimum_stock AND i.minimum_stock > 0
    ");
    $lowStockCount = $stmt->fetch()['total'];

    // Get recently added fixed assets
    $stmt = $db->query("
        SELECT fa.*, i.name as item_name, i.sku
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        ORDER BY fa.created_at DESC
        LIMIT 5
    ");
    $recentFixedAssets = $stmt->fetchAll();

    // Get recent stock transactions
    $stmt = $db->query("
        SELECT st.*, i.name as item_name, i.sku, u.full_name as created_by_name
        FROM stock_transactions st
        JOIN items i ON st.item_id = i.item_id
        JOIN users u ON st.created_by = u.user_id
        ORDER BY st.created_at DESC
        LIMIT 5
    ");
    $recentTransactions = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    setFlashMessage('error', 'An error occurred while loading inventory data.');
    
    // Initialize with default values
    $fixedAssetsCount = 0;
    $consumablesCount = 0;
    $itemsCount = 0;
    $pendingTransfersCount = 0;
    $lowStockCount = 0;
    $recentFixedAssets = [];
    $recentTransactions = [];
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4">Inventory Management Dashboard</h1>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Fixed Assets
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($fixedAssetsCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-laptop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Consumables
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($consumablesCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($itemsCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Transfers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($pendingTransfersCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($lowStockCount); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body d-flex flex-wrap">
                    <a href="../../inventory/fixed-assets/index.php" class="btn btn-primary m-2">
                        <i class="fas fa-laptop"></i> Fixed Assets
                    </a>
                    <a href="../../inventory/consumables/index.php" class="btn btn-success m-2">
                        <i class="fas fa-boxes"></i> Consumables
                    </a>
                    <a href="../../inventory/transfers/index.php" class="btn btn-info m-2">
                        <i class="fas fa-exchange-alt"></i> Transfers
                    </a>
                    <a href="items.php" class="btn btn-secondary m-2">
                        <i class="fas fa-list"></i> Item Management
                    </a>
                    <a href="categories.php" class="btn btn-warning m-2">
                        <i class="fas fa-tags"></i> Categories
                    </a>
                    <a href="suppliers.php" class="btn btn-danger m-2">
                        <i class="fas fa-truck"></i> Suppliers
                    </a>
                    <a href="reports.php" class="btn btn-dark m-2">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Cards -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Recently Added Fixed Assets</h6>
                    <a href="../../inventory/fixed-assets/index.php" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentFixedAssets)): ?>
                        <p class="text-center text-muted">No recent fixed assets found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Serial Number</th>
                                        <th>Status</th>
                                        <th>Date Added</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentFixedAssets as $asset): ?>
                                        <tr>
                                            <td>
                                                <?php echo htmlspecialchars($asset['item_name']); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($asset['sku']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($asset['serial_number']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $asset['status'] === 'in_use' ? 'success' : 
                                                        ($asset['status'] === 'available' ? 'primary' : 
                                                        ($asset['status'] === 'under_repair' ? 'warning' : 'danger')); 
                                                ?>">
                                                    <?php echo ucwords(str_replace('_', ' ', $asset['status'])); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($asset['created_at']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-success">Recent Stock Transactions</h6>
                    <a href="../../inventory/consumables/index.php" class="btn btn-sm btn-success">View All</a>
                </div>
                <div class="card-body">
                    <?php if (empty($recentTransactions)): ?>
                        <p class="text-center text-muted">No recent transactions found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Type</th>
                                        <th>Quantity</th>
                                        <th>Created By</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentTransactions as $transaction): ?>
                                        <tr>
                                            <td>
                                                <?php echo htmlspecialchars($transaction['item_name']); ?><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($transaction['sku']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $transaction['transaction_type'] === 'in' ? 'success' : 
                                                        ($transaction['transaction_type'] === 'out' ? 'danger' : 'info'); 
                                                ?>">
                                                    <?php echo strtoupper($transaction['transaction_type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo number_format($transaction['quantity']); ?></td>
                                            <td><?php echo htmlspecialchars($transaction['created_by_name']); ?></td>
                                            <td><?php echo formatDate($transaction['created_at']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../templates/footer.php'; ?> 