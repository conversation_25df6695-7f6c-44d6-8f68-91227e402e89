<?php
// Use relative paths instead of $_SERVER['DOCUMENT_ROOT']
require_once('../../includes/database.php');
require_once('../../includes/functions.php');

// Initialize session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get asset ID from URL
$asset_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : 0;

// Get asset details
$query = "
    SELECT fa.*, sm.sku_code, sm.sku_name, c.category_name, l.location_name, s.source_name, sup.supplier_name,
           u.full_name as created_by_name
    FROM fixed_assets fa
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    LEFT JOIN sources s ON fa.source_id = s.source_id
    LEFT JOIN suppliers sup ON fa.supplier_id = sup.supplier_id
    LEFT JOIN users u ON fa.created_by = u.user_id
    WHERE fa.asset_id = ?
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Asset not found
    $asset_found = false;
} else {
    $asset_found = true;
    $asset = mysqli_fetch_assoc($result);
}

// Format date function
function formatQRDate($date) {
    if (empty($date) || $date == '0000-00-00') {
        return 'N/A';
    }
    return date('M d, Y', strtotime($date));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Details | Paranaque City Health Office</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Hide Footer CSS -->
    <link href="../../assets/css/hide-footer.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #004d40;
            --secondary-color: #00796b;
            --accent-color: #00897b;
            --light-color: #e0f2f1;
            --bg-color: #f5f8f7;
            --text-color: #2d3436;
            --text-secondary: #00695c;
            --danger-color: #b71c1c;
            --card-radius: 12px;
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2.5rem;
            --shadow-sm: 0 2px 5px rgba(0,0,0,0.06);
            --shadow-md: 0 3px 8px rgba(0,0,0,0.08);
            --shadow-lg: 0 8px 16px rgba(0,0,0,0.1);
            --transition-speed: 0.25s;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            padding-bottom: var(--spacing-xl);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: var(--spacing-lg) 0;
            text-align: center;
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0) 100%);
            pointer-events: none;
        }

        .logo {
            max-height: 65px;
            margin-bottom: var(--spacing-sm);
            filter: drop-shadow(0 2px 3px rgba(0,0,0,0.2));
            transition: transform var(--transition-speed);
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .header h1 {
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-xs);
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .header p {
            opacity: 0.9;
            font-weight: 300;
            letter-spacing: 0.5px;
        }

        .container {
            max-width: 768px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        .card {
            border: none;
            border-radius: var(--card-radius);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-lg);
            overflow: hidden;
            transition: box-shadow var(--transition-speed), transform var(--transition-speed);
            background-color: #fff;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
            color: white;
            font-weight: 500;
            padding: var(--spacing-md);
            font-size: 1rem;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, rgba(255,255,255,0.3), rgba(255,255,255,0.05));
        }

        .info-row {
            display: flex;
            border-bottom: 1px solid rgba(230, 240, 238, 0.7);
            padding: var(--spacing-md);
            transition: background-color var(--transition-speed);
        }

        .info-row:hover {
            background-color: rgba(224, 242, 241, 0.25);
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            width: 40%;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
        }

        .info-label i {
            margin-right: 8px;
            opacity: 0.7;
            font-size: 0.85rem;
        }

        .info-value {
            width: 60%;
            font-weight: 400;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 50px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-block;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all var(--transition-speed);
        }

        .status-available {
            background-color: var(--accent-color);
            color: white;
        }

        .status-inuse {
            background-color: var(--secondary-color);
            color: white;
        }

        .status-repair {
            background-color: #4db6ac;
            color: white;
        }

        .status-defective {
            background-color: var(--danger-color);
            color: white;
        }

        .footer {
            text-align: center;
            margin-top: var(--spacing-xl);
            color: var(--text-secondary);
            font-size: 0.75rem;
            padding: var(--spacing-lg) 0;
            opacity: 0.8;
        }

        .btn-primary {
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
            border: none;
            padding: 0.6rem 1.5rem;
            border-radius: 50px;
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-speed);
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .btn-primary:hover, .btn-primary:focus {
            background: linear-gradient(to right, var(--accent-color), var(--secondary-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .alert {
            border-radius: var(--card-radius);
            border: none;
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-md) var(--spacing-lg);
        }

        .alert-danger {
            background-color: #ffebee;
            color: var(--danger-color);
        }

        .card-body {
            padding: 0;
        }

        .card-body.has-content {
            padding: var(--spacing-md) var(--spacing-lg);
            line-height: 1.7;
        }

        h2 {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: var(--spacing-md);
            color: var(--text-secondary);
            position: relative;
            display: inline-block;
            padding-bottom: var(--spacing-xs);
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40px;
            height: 3px;
            background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
            border-radius: 3px;
        }

        /* Print styles */
        @media print {
            body {
                background-color: white;
                padding: 0;
                font-size: 12pt;
            }

            .header {
                box-shadow: none;
                padding: 10pt 0;
            }

            .card {
                break-inside: avoid;
                box-shadow: none;
                margin-bottom: 15pt;
                border: 1pt solid #e0e0e0;
            }

            .btn-primary {
                display: none;
            }

            .footer {
                margin-top: 15pt;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 var(--spacing-sm);
            }

            .header {
                padding: var(--spacing-md) 0;
            }

            .logo {
                max-height: 50px;
            }

            h2 {
                font-size: 1.1rem;
            }

            .card {
                border-radius: 8px;
            }

            .card-header {
                padding: var(--spacing-sm) var(--spacing-md);
            }
        }

        @media (max-width: 576px) {
            .info-row {
                flex-direction: column;
                padding: var(--spacing-sm) var(--spacing-md);
            }

            .info-label, .info-value {
                width: 100%;
            }

            .info-label {
                margin-bottom: 0.25rem;
                font-size: 0.85rem;
            }

            .info-value {
                font-size: 0.95rem;
            }

            .card-header {
                padding: var(--spacing-sm) var(--spacing-md);
                font-size: 0.95rem;
            }

            .status-badge {
                font-size: 0.65rem;
                padding: 3px 8px;
            }

            h2::after {
                width: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <img src="../../assets/img/prqlogo2.png" alt="Logo" class="logo">
            <h1 class="h5">PARANAQUE CITY HEALTH OFFICE</h1>
            <p class="mb-0" style="font-size: 0.85rem;">Asset Management System</p>
        </div>
    </div>

    <div class="container">
        <?php if (!$asset_found): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i> Asset not found
            </div>
            <p class="text-center">The requested asset could not be found in our database.</p>
        <?php else: ?>
            <h2>Asset Details</h2>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-info-circle me-2"></i>Basic Information</span>
                    <?php
                    $statusClass = '';
                    $statusIcon = '';
                    switch ($asset['status']) {
                        case 'In use':
                            $statusClass = 'status-inuse';
                            $statusIcon = 'fa-play-circle';
                            break;
                        case 'Available':
                            $statusClass = 'status-available';
                            $statusIcon = 'fa-check-circle';
                            break;
                        case 'Under Repair':
                            $statusClass = 'status-repair';
                            $statusIcon = 'fa-tools';
                            break;
                        case 'Defective':
                            $statusClass = 'status-defective';
                            $statusIcon = 'fa-exclamation-circle';
                            break;
                        default:
                            $statusClass = '';
                            $statusIcon = 'fa-circle';
                    }
                    ?>
                    <span class="status-badge <?php echo $statusClass; ?>">
                        <i class="fas <?php echo $statusIcon; ?> me-1"></i><?php echo $asset['status']; ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-hashtag"></i>Asset ID</div>
                        <div class="info-value"><?php echo $asset['asset_id']; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-tag"></i>Asset Name</div>
                        <div class="info-value"><?php echo $asset['asset_name']; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-barcode"></i>SKU</div>
                        <div class="info-value"><?php echo $asset['sku_code'] . ' - ' . $asset['sku_name']; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-fingerprint"></i>Serial Number</div>
                        <div class="info-value"><?php echo isset($asset['serial_number']) ? $asset['serial_number'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-folder"></i>Category</div>
                        <div class="info-value"><?php echo $asset['category_name']; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-map-marker-alt"></i>Current Location</div>
                        <div class="info-value"><?php echo $asset['location_name']; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-user"></i>Assigned To</div>
                        <div class="info-value"><?php echo isset($asset['assigned_to']) ? $asset['assigned_to'] : 'N/A'; ?></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-clipboard-list me-2"></i>Specifications & Details
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-cog"></i>Model</div>
                        <div class="info-value"><?php echo isset($asset['model']) ? $asset['model'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-building"></i>Unit/Section</div>
                        <div class="info-value"><?php echo isset($asset['unit_section']) ? $asset['unit_section'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-file-alt"></i>Local MR</div>
                        <div class="info-value"><?php echo isset($asset['local_mr']) ? $asset['local_mr'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-calendar-alt"></i>Purchase Date</div>
                        <div class="info-value"><?php echo formatQRDate($asset['purchase_date']); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-shield-alt"></i>Warranty Expiry</div>
                        <div class="info-value"><?php echo formatQRDate($asset['warranty_expiry']); ?></div>
                    </div>
                    <?php if (!empty($asset['unit_cost'])): ?>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-tag"></i>Unit Cost</div>
                        <div class="info-value">₱<?php echo number_format($asset['unit_cost'], 2); ?></div>
                    </div>
                    <?php endif; ?>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-receipt"></i>Receipt Type</div>
                        <div class="info-value"><?php echo isset($asset['receipt_type']) ? $asset['receipt_type'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-sort-numeric-up"></i>Series Number</div>
                        <div class="info-value"><?php echo isset($asset['series_number']) ? $asset['series_number'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-industry"></i>Supplier</div>
                        <div class="info-value"><?php echo isset($asset['supplier_name']) ? $asset['supplier_name'] : 'N/A'; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-box"></i>Source</div>
                        <div class="info-value"><?php echo isset($asset['source_name']) ? $asset['source_name'] : 'N/A'; ?></div>
                    </div>
                </div>
            </div>

            <?php if (!empty($asset['specifications'])): ?>
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-microchip me-2"></i>Technical Specifications
                </div>
                <div class="card-body has-content">
                    <p><?php echo nl2br($asset['specifications']); ?></p>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($asset['remarks'])): ?>
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-comment-alt me-2"></i>Remarks
                </div>
                <div class="card-body has-content">
                    <p><?php echo nl2br($asset['remarks']); ?></p>
                </div>
            </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <i class="fas fa-server me-2"></i>System Information
                </div>
                <div class="card-body">
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-user-edit"></i>Created By</div>
                        <div class="info-value"><?php echo $asset['created_by_name']; ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-calendar-plus"></i>Created On</div>
                        <div class="info-value"><?php echo date('M d, Y h:i A', strtotime($asset['created_at'])); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label"><i class="fas fa-history"></i>Last Updated</div>
                        <div class="info-value"><?php echo date('M d, Y h:i A', strtotime($asset['updated_at'])); ?></div>
                    </div>
                </div>
            </div>

            <?php if (isset($_SESSION['user_id'])): ?>
            <div class="text-center mb-4">
                <a href="../../modules/assets/view.php?id=<?php echo $asset_id; ?>" class="btn btn-primary">
                    <i class="fas fa-clipboard-list me-1"></i> View Full Details
                </a>
            </div>
            <?php endif; ?>
        <?php endif; ?>

        <div class="footer">
            <p>© <?php echo date('Y'); ?> Paranaque City Health Office. All Rights Reserved.</p>
            <p>Asset Management System</p>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>