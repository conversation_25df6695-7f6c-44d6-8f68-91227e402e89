<?php
// Include required files
$base_path = $_SERVER['DOCUMENT_ROOT'] . '/choims';
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/auth.php');
require_once($base_path . '/includes/header.php');

// Add the modern asset view CSS
echo '<link rel="stylesheet" href="/choims/assets/css/asset-view-modern.css">';

// Ensure user is logged in and has appropriate role
requireLogin();

// Check if the user has appropriate permissions
if (!hasRole('Logistics') && !hasRole('Superadmin') && !hasRole('Godmode')) {
    header("Location: /choims/access-denied.php");
    exit();
}

// Get the asset ID from URL
$asset_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : 0;

// Get asset details
$assetQuery = "
    SELECT fa.*, sm.sku_name, sm.sku_code, l.location_name, c.category_name
    FROM fixed_assets fa
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN locations l ON fa.current_location_id = l.location_id
    JOIN categories c ON sm.category_id = c.category_id
    WHERE fa.asset_id = ?
";
$assetStmt = mysqli_prepare($conn, $assetQuery);
mysqli_stmt_bind_param($assetStmt, 'i', $asset_id);
mysqli_stmt_execute($assetStmt);
$assetResult = mysqli_stmt_get_result($assetStmt);

if (mysqli_num_rows($assetResult) == 0) {
    // Asset not found
    echo '<div class="container-fluid mt-4"><div class="alert alert-danger">Asset not found.</div></div>';
    require_once($base_path . '/includes/footer.php');
    exit();
}

$asset = mysqli_fetch_assoc($assetResult);

// Get MR history for the asset
$historyQuery = "
    SELECT 
        mh.*,
        u.full_name as created_by_name,
        DATE_FORMAT(mh.start_date, '%d %b %Y %h:%i %p') as formatted_start_date,
        DATE_FORMAT(mh.end_date, '%d %b %Y %h:%i %p') as formatted_end_date
    FROM mr_history mh
    LEFT JOIN users u ON mh.created_by = u.user_id
    WHERE mh.asset_id = ?
    ORDER BY mh.start_date DESC
";
$historyStmt = mysqli_prepare($conn, $historyQuery);
mysqli_stmt_bind_param($historyStmt, 'i', $asset_id);
mysqli_stmt_execute($historyStmt);
$historyResult = mysqli_stmt_get_result($historyStmt);
?>

<div class="container-fluid">
    <!-- Asset Header -->
    <div class="asset-header animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8 d-flex align-items-center">
                <div class="asset-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div>
                    <h1 class="asset-title">MR History</h1>
                    <div class="asset-subtitle">
                        <span class="me-3">
                            <i class="fas fa-desktop me-1"></i> <?php echo htmlspecialchars($asset['asset_name']); ?>
                        </span>
                        <span class="me-3">
                            <i class="fas fa-tag me-1"></i> <?php echo htmlspecialchars($asset['sku_code']); ?>
                        </span>
                        <span class="me-3">
                            <i class="fas fa-list me-1"></i> <?php echo htmlspecialchars($asset['category_name']); ?>
                        </span>
                        <span>
                            <i class="fas fa-map-marker-alt me-1"></i> <?php echo htmlspecialchars($asset['location_name']); ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex flex-wrap gap-2 justify-content-md-end">
                    <a href="/choims/modules/assets/view.php?id=<?php echo $asset_id; ?>" class="btn btn-light action-btn">
                        <i class="fas fa-arrow-left"></i> Back to Asset
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-12">
            <!-- MR History Card -->
            <div class="asset-card animate__animated animate__fadeInUp">
                <div class="asset-card-header">
                    <div class="asset-card-title">
                        <i class="fas fa-history"></i> Material Requisition History
                    </div>
                    <div class="badge bg-primary bg-opacity-10">
                        <?php echo mysqli_num_rows($historyResult); ?> records
                    </div>
                </div>
                <div class="asset-card-body px-0">
                    <?php if (mysqli_num_rows($historyResult) > 0): ?>
                        <div class="mr-timeline px-4">
                            <?php while ($history = mysqli_fetch_assoc($historyResult)): ?>
                                <div class="mr-item">
                                    <div class="mr-header">
                                        <div class="mr-date">
                                            <?php if ($history['end_date']): ?>
                                                <span class="badge bg-secondary bg-opacity-10 p-2">
                                                    <i class="far fa-calendar-alt me-1"></i> 
                                                    <?php echo htmlspecialchars($history['formatted_start_date']); ?>
                                                    <i class="fas fa-arrow-right mx-1"></i>
                                                    <?php echo htmlspecialchars($history['formatted_end_date']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-success bg-opacity-10 p-2">
                                                    <i class="far fa-calendar-alt me-1"></i> 
                                                    <?php echo htmlspecialchars($history['formatted_start_date']); ?>
                                                    <span class="badge bg-success ms-2">Current</span>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="mr-content">
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <div class="field-label"><i class="fas fa-file-alt me-1"></i> MR Number</div>
                                                <div class="field-value fw-bold"><?php echo htmlspecialchars($history['local_mr']); ?></div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="field-label"><i class="fas fa-receipt me-1"></i> Receipt Type</div>
                                                <div class="field-value"><?php echo htmlspecialchars($history['receipt_type']); ?></div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="field-label"><i class="fas fa-user me-1"></i> Assigned To</div>
                                                <div class="field-value"><?php echo htmlspecialchars($history['assigned_to']); ?></div>
                                            </div>
                                        </div>
                                        
                                        <!-- Duration & Record Info -->
                                        <div class="bg-light rounded p-3 mt-2 small">
                                            <div class="row text-muted">
                                                <div class="col-md-6">
                                                    <i class="fas fa-user me-1"></i> Recorded by: <strong><?php echo htmlspecialchars($history['created_by_name']); ?></strong>
                                                </div>
                                                <div class="col-md-6">
                                                    <i class="fas fa-clock me-1"></i> Duration: 
                                                    <strong>
                                                    <?php
                                                    $start = new DateTime($history['start_date']);
                                                    $end = $history['end_date'] ? new DateTime($history['end_date']) : new DateTime();
                                                    $interval = $start->diff($end);
                                                    
                                                    if ($interval->y > 0) {
                                                        echo $interval->format('%y years, %m months');
                                                    } elseif ($interval->m > 0) {
                                                        echo $interval->format('%m months, %d days');
                                                    } elseif ($interval->d > 0) {
                                                        echo $interval->format('%d days');
                                                    } else {
                                                        echo $interval->format('%h hours, %i minutes');
                                                    }
                                                    ?>
                                                    </strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-state-icon mr">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <h6 class="empty-state-title">No MR History</h6>
                            <p class="empty-state-description">No Material Requisition history records found for this asset.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once($base_path . '/includes/footer.php');
?> 