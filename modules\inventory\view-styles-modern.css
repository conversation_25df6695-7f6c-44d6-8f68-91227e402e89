/* Modern UI Styles for Inventory View Page */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;

  /* Gradients */
  --green-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Main container styling */
.inventory-view-container {
  padding: var(--space-5) var(--space-4);
  max-width: 1400px;
  margin: 0 auto;
}

/* Inventory Header */
.inventory-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: fadeIn 0.5s ease;
}

.inventory-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--green-gradient);
}

.inventory-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  line-height: 1.3;
  word-wrap: break-word;
  max-width: 100%;
}

.inventory-subtitle {
  color: var(--gray-500);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-1);
}

/* Card styling */
.inventory-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  overflow: hidden;
  transition: var(--transition);
  animation: slideInUp 0.5s ease;
}

.inventory-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-3px);
}

.inventory-card .card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.inventory-card .card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--green-gradient);
}

.inventory-card .card-header h6 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.inventory-card .card-header h6 i {
  color: var(--primary);
  font-size: 1.1rem;
}

.inventory-card .card-body {
  padding: var(--space-5);
}

/* Status badge styling */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.status-badge.available {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-badge.available::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success);
}

.status-badge.low-stock {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-badge.low-stock::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--warning);
}

.status-badge.out-of-stock {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.status-badge.out-of-stock::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--danger);
}

/* Inventory info styling */
.inventory-info-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.inventory-info-item {
  padding: var(--space-3);
  background-color: var(--gray-100);
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.inventory-info-item:hover {
  background-color: var(--gray-200);
}

.inventory-info-label {
  font-weight: 600;
  color: var(--gray-500);
  font-size: 0.85rem;
  margin-bottom: var(--space-1);
}

.inventory-info-value {
  color: var(--dark);
  font-size: 1rem;
  font-weight: 500;
}

/* Stock level indicators */
.stock-level {
  display: flex;
  align-items: center;
  margin-top: var(--space-4);
  padding: var(--space-3);
  background-color: var(--gray-100);
  border-radius: var(--radius);
}

.stock-level-item {
  flex: 1;
  text-align: center;
  padding: var(--space-2);
}

.stock-level-label {
  font-size: 0.8rem;
  color: var(--gray-500);
  margin-bottom: var(--space-1);
}

.stock-level-value {
  font-size: 1.25rem;
  font-weight: 600;
}

.stock-level-value.current {
  color: var(--primary);
}

.stock-level-value.min {
  color: var(--info);
}

.stock-level-value.low {
  color: var(--warning);
}

.stock-level-value.critical {
  color: var(--danger);
}

/* Chart styling */
.chart-container {
  position: relative;
  height: 300px;
  margin-top: var(--space-4);
}

.chart-options {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  z-index: 10;
}

.chart-option {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  color: var(--gray-500);
  font-size: 0.8rem;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-fast);
}

.chart-option:hover {
  background-color: var(--gray-100);
  color: var(--primary);
}

.chart-option.active {
  background-color: var(--primary-bg);
  color: var(--primary);
  border-color: var(--primary-light);
}

/* Timeline styling */
.timeline {
  position: relative;
  padding: var(--space-2) 0;
}

.timeline::before {
  content: "";
  position: absolute;
  top: 0;
  left: 10px;
  width: 3px;
  height: 100%;
  background-color: var(--gray-200);
  border-radius: 3px;
}

.timeline-item {
  position: relative;
  padding-left: 30px;
  margin-bottom: var(--space-4);
}

.timeline-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: var(--white);
  border: 3px solid var(--primary);
  z-index: 1;
}

.timeline-item.stock-in::before {
  border-color: var(--success);
}

.timeline-item.stock-out::before {
  border-color: var(--danger);
}

.timeline-item.transfer::before {
  border-color: var(--info);
}

.timeline-item.adjustment::before {
  border-color: var(--warning);
}

.timeline-date {
  font-weight: 600;
  margin-bottom: var(--space-1);
  color: var(--gray-500);
  font-size: 0.85rem;
}

.timeline-content {
  background-color: var(--white);
  border-radius: var(--radius);
  padding: var(--space-3);
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary);
}

.timeline-item.stock-in .timeline-content {
  border-left-color: var(--success);
}

.timeline-item.stock-out .timeline-content {
  border-left-color: var(--danger);
}

.timeline-item.transfer .timeline-content {
  border-left-color: var(--info);
}

.timeline-item.adjustment .timeline-content {
  border-left-color: var(--warning);
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
  justify-content: flex-end;
}

.action-button {
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  border-radius: var(--radius);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  transition: var(--transition-fast);
  border: none;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.action-button.disabled {
  opacity: 0.65;
  cursor: not-allowed;
  pointer-events: none;
  background-color: var(--gray-400);
  color: var(--white);
  border-color: var(--gray-400);
}

.action-button i {
  font-size: 0.9rem;
}

.action-button.primary {
  background-color: var(--primary);
  color: white;
}

.action-button.primary:hover {
  background-color: var(--primary-dark);
}

.action-button.success {
  background-color: var(--success);
  color: white;
}

.action-button.success:hover {
  background-color: #0ca678;
}

.action-button.secondary {
  background-color: var(--secondary);
  color: white;
}

.action-button.secondary:hover {
  background-color: #4b636e;
}

/* Modal styling */
.modal-content {
  border: none;
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.modal-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

.modal-title {
  font-weight: 600;
  color: var(--primary);
  font-size: 1.1rem;
}

.modal-body {
  padding: var(--space-5);
}

.modal-footer {
  background-color: var(--gray-100);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-5);
}

/* Form controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: var(--transition-fast);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .inventory-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-start;
  }

  .inventory-info-section {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .inventory-view-container {
    padding: var(--space-3);
  }

  .inventory-card .card-body {
    padding: var(--space-3);
  }

  .action-button {
    padding: 0.5rem 1rem;
  }

  .stock-level {
    flex-direction: column;
  }

  .stock-level-item {
    width: 100%;
    margin-bottom: var(--space-2);
  }
}
