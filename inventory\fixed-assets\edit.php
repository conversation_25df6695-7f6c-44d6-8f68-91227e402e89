<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get asset ID from URL
$assetId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$assetId) {
    setFlashMessage('error', 'Invalid asset ID');
    header("Location: index.php");
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db->beginTransaction();

        // Update item details
        $stmt = $db->prepare("
            UPDATE items i
            JOIN fixed_assets fa ON i.item_id = fa.item_id
            SET i.category_id = ?,
                i.name = ?,
                i.specifications = ?,
                i.unit_cost = ?
            WHERE fa.asset_id = ?
        ");
        $stmt->execute([
            $_POST['category_id'],
            $_POST['item_name'],
            $_POST['specifications'],
            $_POST['unit_cost'],
            $assetId
        ]);

        // Update fixed asset
        $stmt = $db->prepare("
            UPDATE fixed_assets
            SET serial_number = ?,
                source_id = ?,
                supplier_id = ?,
                model = ?,
                assigned_to = ?,
                department_id = ?,
                health_center_id = ?,
                local_mr = ?,
                status = ?,
                purchase_date = ?,
                warranty_expiry = ?,
                receipt_type = ?,
                series_number = ?,
                remarks = ?
            WHERE asset_id = ?
        ");

        // Map "Local MR" to "D.R" for database compatibility
        $receipt_type = $_POST['receipt_type'];
        if ($receipt_type === 'local_mr') {
            $receipt_type = 'dr';
        }

        $stmt->execute([
            $_POST['serial_number'],
            $_POST['source_id'],
            $_POST['supplier_id'],
            $_POST['model'],
            $_POST['assigned_to'] ?: null,
            $_POST['department_id'] ?: null,
            $_POST['health_center_id'] ?: null,
            $_POST['local_mr'],
            $_POST['status'],
            $_POST['purchase_date'],
            $_POST['warranty_expiry'] ?: null,
            $receipt_type,
            $_POST['series_number'],
            $_POST['remarks'],
            $assetId
        ]);

        // Log the activity
        $auth->logActivity($auth->getCurrentUser()['user_id'], 'update', 'fixed_assets', $assetId);

        $db->commit();
        setFlashMessage('success', 'Fixed asset updated successfully');
        header("Location: view.php?id=" . $assetId);
        exit;
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Error updating fixed asset: " . $e->getMessage());
        setFlashMessage('error', 'Error updating fixed asset. Please try again.');
    }
}

// Fetch asset details
try {
    $query = "
        SELECT fa.*, i.name as item_name, i.sku, i.specifications, i.unit_cost,
               i.category_id, c.name as category_name
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        WHERE fa.asset_id = ?
    ";
    $stmt = $db->prepare($query);
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();

    if (!$asset) {
        setFlashMessage('error', 'Asset not found');
        header("Location: index.php");
        exit;
    }

    // Fetch required data for dropdowns
    // Categories
    $stmt = $db->query("SELECT category_id, name FROM categories WHERE status = 'active'");
    $categories = $stmt->fetchAll();

    // Sources
    $stmt = $db->query("SELECT source_id, name FROM sources WHERE status = 'active'");
    $sources = $stmt->fetchAll();

    // Suppliers
    $stmt = $db->query("SELECT supplier_id, name FROM suppliers WHERE status = 'active'");
    $suppliers = $stmt->fetchAll();

    // Users
    $stmt = $db->query("SELECT user_id, full_name FROM users WHERE status = 'active'");
    $users = $stmt->fetchAll();

    // Departments
    $stmt = $db->query("SELECT department_id, name FROM departments WHERE status = 'active'");
    $departments = $stmt->fetchAll();

    // Health Centers
    $stmt = $db->query("SELECT health_center_id, name FROM health_centers WHERE status = 'active'");
    $healthCenters = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching asset details: " . $e->getMessage());
    setFlashMessage('error', 'Error loading asset details');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Edit Fixed Asset</h1>
        <div>
            <a href="view.php?id=<?php echo $assetId; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <!-- Item Details -->
                <h5 class="mb-3">Item Details</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="category_id" class="form-label required">Category</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>"
                                            <?php echo $asset['category_id'] == $category['category_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="sku" class="form-label">SKU</label>
                            <input type="text" class="form-control" id="sku" value="<?php echo htmlspecialchars($asset['sku']); ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="item_name" class="form-label required">Item Name</label>
                            <input type="text" class="form-control" id="item_name" name="item_name"
                                   value="<?php echo htmlspecialchars($asset['item_name']); ?>" required>
                        </div>
                    </div>
                </div>

                <!-- Asset Details -->
                <h5 class="mb-3">Asset Details</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="serial_number" class="form-label required">Serial Number</label>
                            <input type="text" class="form-control" id="serial_number" name="serial_number"
                                   value="<?php echo htmlspecialchars($asset['serial_number']); ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="model" class="form-label">Model</label>
                            <input type="text" class="form-control" id="model" name="model"
                                   value="<?php echo htmlspecialchars($asset['model']); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="specifications" class="form-label">Specifications</label>
                            <textarea class="form-control" id="specifications" name="specifications" rows="3"><?php echo htmlspecialchars($asset['specifications']); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Source and Cost -->
                <h5 class="mb-3">Source and Cost</h5>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="source_id" class="form-label required">Source</label>
                            <select class="form-select" id="source_id" name="source_id" required>
                                <option value="">Select Source</option>
                                <?php foreach ($sources as $source): ?>
                                    <option value="<?php echo $source['source_id']; ?>"
                                            <?php echo $asset['source_id'] == $source['source_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($source['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="supplier_id" class="form-label required">Supplier</label>
                            <select class="form-select" id="supplier_id" name="supplier_id" required>
                                <option value="">Select Supplier</option>
                                <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?php echo $supplier['supplier_id']; ?>"
                                            <?php echo $asset['supplier_id'] == $supplier['supplier_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($supplier['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="unit_cost" class="form-label required">Unit Cost</label>
                            <input type="number" class="form-control" id="unit_cost" name="unit_cost" step="0.01"
                                   value="<?php echo htmlspecialchars($asset['unit_cost']); ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="purchase_date" class="form-label required">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase_date" name="purchase_date"
                                   value="<?php echo htmlspecialchars($asset['purchase_date']); ?>" required>
                        </div>
                    </div>
                </div>

                <!-- Assignment -->
                <h5 class="mb-3">Assignment</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="assigned_to" class="form-label">Assigned To</label>
                            <select class="form-select" id="assigned_to" name="assigned_to">
                                <option value="">Select User</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['user_id']; ?>"
                                            <?php echo $asset['assigned_to'] == $user['user_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="department_id" class="form-label">Department</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $department): ?>
                                    <option value="<?php echo $department['department_id']; ?>"
                                            <?php echo $asset['department_id'] == $department['department_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($department['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="health_center_id" class="form-label">Health Center</label>
                            <select class="form-select" id="health_center_id" name="health_center_id">
                                <option value="">Select Health Center</option>
                                <?php foreach ($healthCenters as $center): ?>
                                    <option value="<?php echo $center['health_center_id']; ?>"
                                            <?php echo $asset['health_center_id'] == $center['health_center_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($center['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Additional Details -->
                <h5 class="mb-3">Additional Details</h5>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="status" class="form-label required">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="available" <?php echo $asset['status'] === 'available' ? 'selected' : ''; ?>>Available</option>
                                <option value="in_use" <?php echo $asset['status'] === 'in_use' ? 'selected' : ''; ?>>In Use</option>
                                <option value="under_repair" <?php echo $asset['status'] === 'under_repair' ? 'selected' : ''; ?>>Under Repair</option>
                                <option value="defective" <?php echo $asset['status'] === 'defective' ? 'selected' : ''; ?>>Defective</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="warranty_expiry" class="form-label">Warranty Expiry</label>
                            <input type="date" class="form-control" id="warranty_expiry" name="warranty_expiry"
                                   value="<?php echo $asset['warranty_expiry'] ? htmlspecialchars($asset['warranty_expiry']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="receipt_type" class="form-label required">Receipt Type</label>
                            <select class="form-select" id="receipt_type" name="receipt_type" required>
                                <option value="local_mr" <?php echo ($asset['receipt_type'] === 'local_mr' || $asset['receipt_type'] === 'dr' || $asset['receipt_type'] === 'sales_invoice' || $asset['receipt_type'] === 'po') ? 'selected' : ''; ?>>Local MR</option>
                                <option value="ptr" <?php echo $asset['receipt_type'] === 'ptr' ? 'selected' : ''; ?>>P.T.R</option>
                                <option value="or" <?php echo $asset['receipt_type'] === 'or' ? 'selected' : ''; ?>>O.R</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="series_number" class="form-label">Series Number</label>
                            <input type="text" class="form-control" id="series_number" name="series_number"
                                   value="<?php echo htmlspecialchars($asset['series_number']); ?>">
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="local_mr" class="form-label">Local MR</label>
                            <input type="text" class="form-control" id="local_mr" name="local_mr"
                                   value="<?php echo htmlspecialchars($asset['local_mr']); ?>">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"><?php echo htmlspecialchars($asset['remarks']); ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="view.php?id=<?php echo $assetId; ?>" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Fixed Asset</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Prevent department and health center from being selected simultaneously
document.getElementById('department_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('health_center_id').value = '';
        document.getElementById('health_center_id').disabled = true;
    } else {
        document.getElementById('health_center_id').disabled = false;
    }
});

document.getElementById('health_center_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('department_id').value = '';
        document.getElementById('department_id').disabled = true;
    } else {
        document.getElementById('department_id').disabled = false;
    }
});

// Initialize the mutual exclusion on page load
window.addEventListener('load', function() {
    if (document.getElementById('department_id').value) {
        document.getElementById('health_center_id').disabled = true;
    } else if (document.getElementById('health_center_id').value) {
        document.getElementById('department_id').disabled = true;
    }
});
</script>

<?php require_once '../../templates/footer.php'; ?>
