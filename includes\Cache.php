<?php
/**
 * Enhanced caching system for CHOIMS
 *
 * This class provides caching functionality to improve performance
 * by reducing database queries for frequently accessed data.
 * Supports both in-memory and file-based caching for persistence between requests.
 */
class Cache {
    private static $instance = null;
    private $cache = [];
    private $ttl = [];
    private $cacheEnabled = true;
    private $fileCacheEnabled = true;
    private $cacheDir = '';

    /**
     * Get singleton instance
     *
     * @return Cache
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Default settings
        $this->cacheEnabled = true;
        $this->fileCacheEnabled = true;
        $this->cacheDir = __DIR__ . '/../cache';

        // Create cache directory if it doesn't exist
        if ($this->fileCacheEnabled && !is_dir($this->cacheDir)) {
            if (!mkdir($this->cacheDir, 0755, true)) {
                error_log("Failed to create cache directory: " . $this->cacheDir);
                $this->fileCacheEnabled = false;
            }
        }
    }

    /**
     * Enable or disable caching
     *
     * @param bool $enabled
     */
    public function setEnabled($enabled) {
        $this->cacheEnabled = $enabled;
    }

    /**
     * Enable or disable file-based caching
     *
     * @param bool $enabled
     */
    public function setFileCacheEnabled($enabled) {
        $this->fileCacheEnabled = $enabled;
    }

    /**
     * Check if a key exists in the cache and is not expired
     *
     * @param string $key
     * @return bool
     */
    public function has($key) {
        if (!$this->cacheEnabled) {
            return false;
        }

        // Check in-memory cache first
        if (isset($this->cache[$key])) {
            // Check if the item has expired
            if (isset($this->ttl[$key]) && $this->ttl[$key] < time()) {
                $this->delete($key);
                return false;
            }
            return true;
        }

        // If not in memory and file cache is enabled, try to load from file
        if ($this->fileCacheEnabled) {
            return $this->loadFromFile($key);
        }

        return false;
    }

    /**
     * Load a cache item from file
     *
     * @param string $key
     * @return bool True if loaded successfully and not expired
     */
    private function loadFromFile($key) {
        $cacheFile = $this->getCacheFilePath($key);

        if (!file_exists($cacheFile)) {
            return false;
        }

        // Check file modification time for TTL
        $data = file_get_contents($cacheFile);
        if ($data === false) {
            return false;
        }

        $cacheData = unserialize($data);
        if ($cacheData === false) {
            // Invalid cache data, remove the file
            @unlink($cacheFile);
            return false;
        }

        // Check expiration
        if (isset($cacheData['expires']) && $cacheData['expires'] < time()) {
            // Expired, remove the file
            @unlink($cacheFile);
            return false;
        }

        // Load into memory cache
        $this->cache[$key] = $cacheData['data'];
        if (isset($cacheData['expires'])) {
            $this->ttl[$key] = $cacheData['expires'];
        }

        return true;
    }

    /**
     * Get the file path for a cache key
     *
     * @param string $key
     * @return string
     */
    private function getCacheFilePath($key) {
        return $this->cacheDir . '/' . md5($key) . '.cache';
    }

    /**
     * Get a value from the cache
     *
     * @param string $key
     * @param mixed $default Default value if key doesn't exist
     * @return mixed
     */
    public function get($key, $default = null) {
        if ($this->has($key)) {
            return $this->cache[$key];
        }
        return $default;
    }

    /**
     * Store a value in the cache
     *
     * @param string $key
     * @param mixed $value
     * @param int $ttlSeconds Time to live in seconds (0 = no expiration)
     */
    public function set($key, $value, $ttlSeconds = 0) {
        if (!$this->cacheEnabled) {
            return;
        }

        // Store in memory
        $this->cache[$key] = $value;

        $expires = 0;
        if ($ttlSeconds > 0) {
            $expires = time() + $ttlSeconds;
            $this->ttl[$key] = $expires;
        } else {
            unset($this->ttl[$key]); // No expiration
        }

        // Store in file if enabled
        if ($this->fileCacheEnabled) {
            $this->saveToFile($key, $value, $expires);
        }
    }

    /**
     * Save a cache item to file
     *
     * @param string $key
     * @param mixed $value
     * @param int $expires Expiration timestamp
     * @return bool Success
     */
    private function saveToFile($key, $value, $expires = 0) {
        if (!$this->fileCacheEnabled) {
            return false;
        }

        $cacheFile = $this->getCacheFilePath($key);

        $cacheData = [
            'data' => $value
        ];

        if ($expires > 0) {
            $cacheData['expires'] = $expires;
        }

        $serialized = serialize($cacheData);

        return file_put_contents($cacheFile, $serialized, LOCK_EX) !== false;
    }

    /**
     * Remove a value from the cache
     *
     * @param string $key
     */
    public function delete($key) {
        // Remove from memory
        unset($this->cache[$key]);
        unset($this->ttl[$key]);

        // Remove from file if enabled
        if ($this->fileCacheEnabled) {
            $cacheFile = $this->getCacheFilePath($key);
            if (file_exists($cacheFile)) {
                @unlink($cacheFile);
            }
        }
    }

    /**
     * Clear all cached items
     */
    public function clear() {
        // Clear memory cache
        $this->cache = [];
        $this->ttl = [];

        // Clear file cache if enabled
        if ($this->fileCacheEnabled) {
            $files = glob($this->cacheDir . '/*.cache');
            if ($files !== false) {
                foreach ($files as $file) {
                    @unlink($file);
                }
            }
        }
    }

    /**
     * Get data from cache or execute a callback to retrieve and cache data
     *
     * @param string $key Cache key
     * @param int $ttlSeconds Cache lifetime in seconds
     * @param callable $callback Function to execute if cache misses
     * @return mixed
     */
    public function remember($key, $ttlSeconds, callable $callback) {
        if ($this->has($key)) {
            return $this->get($key);
        }

        $value = $callback();
        $this->set($key, $value, $ttlSeconds);
        return $value;
    }

    /**
     * Get multiple cache items at once
     *
     * @param array $keys Array of cache keys
     * @return array Associative array of key => value pairs
     */
    public function getMultiple(array $keys) {
        $values = [];
        foreach ($keys as $key) {
            $values[$key] = $this->get($key);
        }
        return $values;
    }

    /**
     * Set multiple cache items at once
     *
     * @param array $items Associative array of key => value pairs
     * @param int $ttlSeconds Cache lifetime in seconds
     */
    public function setMultiple(array $items, $ttlSeconds = 0) {
        foreach ($items as $key => $value) {
            $this->set($key, $value, $ttlSeconds);
        }
    }

    /**
     * Get cache statistics
     *
     * @return array Cache statistics
     */
    public function getStats() {
        $stats = [
            'memory' => [
                'count' => count($this->cache),
                'size' => $this->getMemoryUsage()
            ],
            'file' => [
                'count' => 0,
                'size' => 0
            ]
        ];

        if ($this->fileCacheEnabled && is_dir($this->cacheDir)) {
            $files = glob($this->cacheDir . '/*.cache');
            if ($files !== false) {
                $stats['file']['count'] = count($files);
                $stats['file']['size'] = 0;

                foreach ($files as $file) {
                    $stats['file']['size'] += filesize($file);
                }
            }
        }

        return $stats;
    }

    /**
     * Get approximate memory usage of the cache
     *
     * @return int Approximate memory usage in bytes
     */
    private function getMemoryUsage() {
        $size = 0;

        foreach ($this->cache as $key => $value) {
            // Approximate size calculation
            $size += strlen(serialize($value));
            $size += strlen($key);
        }

        return $size;
    }
}