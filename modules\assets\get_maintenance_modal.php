<?php
// Include necessary files with relative paths
require_once('../../config/database.php');
require_once('../../includes/functions.php');
require_once('../../includes/auth.php');

// Make sure user is logged in
if (!isLoggedIn()) {
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['error' => 'Record ID is required']);
    exit;
}

$record_id = $_GET['id'];

// Get maintenance record details
$query = "
    SELECT mr.*, u.full_name as technician_name
    FROM maintenance_records mr
    LEFT JOIN users u ON mr.technician_id = u.user_id
    WHERE mr.record_id = ?
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $record_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    echo json_encode(['error' => 'Record not found']);
    exit;
}

$record = mysqli_fetch_assoc($result);

// Determine status class and icon
$statusClass = '';
$statusIcon = '';
$typeIcon = '';

switch ($record['status']) {
    case 'Completed':
        $statusClass = 'success';
        $statusIcon = 'fas fa-check-circle';
        break;
    case 'In Progress':
        $statusClass = 'warning';
        $statusIcon = 'fas fa-clock';
        break;
    case 'Scheduled':
        $statusClass = 'info';
        $statusIcon = 'fas fa-calendar-alt';
        break;
    default:
        $statusClass = 'secondary';
        $statusIcon = 'fas fa-question-circle';
}

switch ($record['maintenance_type']) {
    case 'Preventive':
        $typeIcon = 'fas fa-shield-alt';
        break;
    case 'Corrective':
        $typeIcon = 'fas fa-wrench';
        break;
    case 'Calibration':
        $typeIcon = 'fas fa-sliders-h';
        break;
    default:
        $typeIcon = 'fas fa-tools';
}

// Format the modal content as HTML
$html = '
<div class="modal-dialog modal-dialog-centered">
    <div class="modal-content modern-modal">
        <div class="modal-header">
            <h5 class="modal-title" id="viewMaintenanceModalLabel' . $record['record_id'] . '">
                <div class="d-flex align-items-center">
                    <div class="modal-icon ' . $statusClass . '">
                        <i class="' . $typeIcon . '"></i>
                    </div>
                    <span class="ms-2">Maintenance Record</span>
                </div>
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="maintenance-status-banner ' . $statusClass . ' mb-4">
                <div class="status-icon">
                    <i class="' . $statusIcon . '"></i>
                </div>
                <div class="status-content">
                    <div class="status-title">' . $record['status'] . '</div>
                    <div class="status-subtitle">' . $record['maintenance_type'] . ' Maintenance</div>
                </div>
                <div class="status-id">#' . $record['record_id'] . '</div>
            </div>

            <div class="info-card mb-3">
                <div class="info-card-header">
                    <i class="fas fa-calendar-alt me-2"></i> Dates
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <div class="info-label">Maintenance Date</div>
                        <div class="info-value">' . formatDate($record['maintenance_date']) . '</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Next Scheduled</div>
                        <div class="info-value">' . (formatDate($record['next_scheduled_date']) ?: 'Not scheduled') . '</div>
                    </div>
                </div>
            </div>

            <div class="info-card mb-3">
                <div class="info-card-header">
                    <i class="fas fa-info-circle me-2"></i> Details
                </div>
                <div class="info-card-body">
                    <div class="info-item">
                        <div class="info-label">Performed By</div>
                        <div class="info-value">';

if (!empty($record['performed_by'])) {
    $html .= '<span class="user-badge maintenance">
                <i class="fas fa-user-cog me-1"></i>
                ' . $record['performed_by'] . '
            </span>';
} elseif (!empty($record['technician_name'])) {
    $html .= '<span class="user-badge maintenance">
                <i class="fas fa-user-cog me-1"></i>
                ' . $record['technician_name'] . '
            </span>';
} else {
    $html .= '<span class="text-muted">Not specified</span>';
}

$html .= '      </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Cost</div>
                        <div class="info-value">';

if (!empty($record['cost'])) {
    $html .= '<span class="cost-badge">₱' . number_format($record['cost'], 2) . '</span>';
} else {
    $html .= '<span class="text-muted">No cost recorded</span>';
}

$html .= '      </div>
                    </div>
                </div>
            </div>';

if (!empty($record['description'])) {
    $html .= '
            <div class="info-card mb-3">
                <div class="info-card-header">
                    <i class="fas fa-clipboard-list me-2"></i> Description
                </div>
                <div class="info-card-body">
                    <div class="notes-content">
                        ' . nl2br(htmlspecialchars($record['description'])) . '
                    </div>
                </div>
            </div>';
}

$html .= '
        </div>
        <div class="modal-footer border-0 pt-0">
            <button type="button" class="btn btn-soft-secondary" data-bs-dismiss="modal">
                <i class="fas fa-times me-1"></i> Close
            </button>';

if ((hasRole('GodMode') || hasRole('Superadmin') || hasRole('HIMU')) || (hasRole('Logistics') && $record['status'] != 'Completed')) {
    $html .= '
            <a href="/choims/modules/maintenance/edit.php?id=' . $record['record_id'] . '" class="btn btn-soft-primary">
                <i class="fas fa-edit me-1"></i> Edit Record
            </a>';
}

$html .= '
        </div>
    </div>
</div>';

// Add debugging information
$debug = "<!-- Debug info: Modal content for record ID: $record_id -->";

// Return the HTML with proper content type
header('Content-Type: text/html; charset=utf-8');
echo $debug . $html;
?>
