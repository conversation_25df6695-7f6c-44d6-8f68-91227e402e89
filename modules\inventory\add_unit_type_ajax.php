<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');

// Ensure user is logged in
requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Invalid request method']);
    exit;
}

// Get form data
$unit_type = isset($_POST['unit_type']) ? trim($_POST['unit_type']) : '';

// Validate required fields
if (empty($unit_type)) {
    echo json_encode(['error' => 'Unit type is required']);
    exit;
}

// Check if unit_types table exists
$checkTableQuery = "SHOW TABLES LIKE 'unit_types'";
$checkTableResult = mysqli_query($conn, $checkTableQuery);

if (mysqli_num_rows($checkTableResult) == 0) {
    // Table doesn't exist, create it
    $createTableQuery = "
        CREATE TABLE unit_types (
            unit_type_id INT PRIMARY KEY AUTO_INCREMENT,
            unit_type VARCHAR(50) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INT,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ";
    
    if (!mysqli_query($conn, $createTableQuery)) {
        echo json_encode(['error' => 'Failed to create unit_types table: ' . mysqli_error($conn)]);
        exit;
    }
    
    // Insert default unit types
    $defaultUnitTypes = ['pcs', 'box', 'pack', 'bottle', 'roll', 'set', 'gallon', 'ream'];
    foreach ($defaultUnitTypes as $defaultType) {
        $insertDefaultQuery = "INSERT INTO unit_types (unit_type, created_by) VALUES (?, ?)";
        $insertDefaultStmt = mysqli_prepare($conn, $insertDefaultQuery);
        mysqli_stmt_bind_param($insertDefaultStmt, 'si', $defaultType, $_SESSION['user_id']);
        mysqli_stmt_execute($insertDefaultStmt);
    }
}

// Check if unit type already exists
$checkQuery = "SELECT unit_type_id FROM unit_types WHERE unit_type = ?";
$checkStmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($checkStmt, 's', $unit_type);
mysqli_stmt_execute($checkStmt);
$checkResult = mysqli_stmt_get_result($checkStmt);

if (mysqli_num_rows($checkResult) > 0) {
    echo json_encode(['error' => 'This unit type already exists']);
    exit;
}

// Insert new unit type
$insertQuery = "INSERT INTO unit_types (unit_type, created_by) VALUES (?, ?)";
$insertStmt = mysqli_prepare($conn, $insertQuery);
mysqli_stmt_bind_param($insertStmt, 'si', $unit_type, $_SESSION['user_id']);

if (mysqli_stmt_execute($insertStmt)) {
    $unit_type_id = mysqli_insert_id($conn);
    
    // Create audit log
    createAuditLog(
        $_SESSION['user_id'],
        'create',
        'unit_types',
        $unit_type_id,
        null,
        json_encode([
            'unit_type' => $unit_type
        ])
    );
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Unit type added successfully',
        'unit_type_id' => $unit_type_id,
        'unit_type' => $unit_type
    ]);
} else {
    echo json_encode(['error' => 'Failed to add unit type: ' . mysqli_error($conn)]);
}
?>
