<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in and has appropriate permissions
requireLogin();
if (!hasRole('Logistics')) {
    header('Location: /choims/access-denied.php');
    exit;
}

// Check if the consumable_inventory table has the is_deleted column
$checkColumnQuery = "SHOW COLUMNS FROM consumable_inventory LIKE 'is_deleted'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);

// If is_deleted column doesn't exist, add it
if (mysqli_num_rows($checkColumnResult) == 0) {
    $addColumnQuery = "ALTER TABLE consumable_inventory ADD COLUMN is_deleted TINYINT(1) NOT NULL DEFAULT 0";
    if (!mysqli_query($conn, $addColumnQuery)) {
        die("Error adding is_deleted column: " . mysqli_error($conn));
    }
}

// Validate input
if (!isset($_POST['inventory_id']) || empty($_POST['inventory_id'])) {
    $_SESSION['error'] = "Invalid inventory ID.";
    header('Location: /choims/modules/inventory/list.php');
    exit;
}

$inventory_id = sanitizeInput($_POST['inventory_id']);

// Check if inventory exists
$checkQuery = "SELECT * FROM consumable_inventory WHERE inventory_id = ?";
$stmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($stmt, 'i', $inventory_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    $_SESSION['error'] = "Inventory item not found.";
    header('Location: /choims/modules/inventory/list.php');
    exit;
}

$inventory = mysqli_fetch_assoc($result);

// Get old values for audit log
$oldValues = json_encode([
    'is_deleted' => 0
]);

// Perform soft delete
$deleteQuery = "UPDATE consumable_inventory SET is_deleted = 1 WHERE inventory_id = ?";
$stmt = mysqli_prepare($conn, $deleteQuery);
mysqli_stmt_bind_param($stmt, 'i', $inventory_id);

if (mysqli_stmt_execute($stmt)) {
    // Log the action in both audit logs
    logAction(
        $conn,
        $_SESSION['user_id'],
        'Deleted inventory item',
        'inventory',
        $inventory_id,
        $oldValues,
        json_encode(['is_deleted' => 1])
    );

    // Log to detailed audit logs
    logConsumableAction(
        $conn,
        $_SESSION['user_id'],
        'delete',
        $inventory_id,
        json_decode($oldValues, true),
        ['is_deleted' => 1]
    );

    // Get inventory details for notification
    $inventoryDetailsQuery = "SELECT ci.current_quantity, sm.sku_name, c.category_name, l.location_name
                             FROM consumable_inventory ci
                             JOIN sku_master sm ON ci.sku_id = sm.sku_id
                             JOIN categories c ON sm.category_id = c.category_id
                             JOIN locations l ON ci.location_id = l.location_id
                             WHERE ci.inventory_id = ?";
    $detailsStmt = mysqli_prepare($conn, $inventoryDetailsQuery);
    mysqli_stmt_bind_param($detailsStmt, 'i', $inventory_id);
    mysqli_stmt_execute($detailsStmt);
    $detailsResult = mysqli_stmt_get_result($detailsStmt);
    $inventoryDetails = mysqli_fetch_assoc($detailsResult);

    // Create special notification for superadmins
    $notificationTitle = "ALERT: Inventory Item Deleted";
    $notificationMessage = "Inventory item '{$inventoryDetails['sku_name']}' has been deleted by {$_SESSION['username']}\n";
    $notificationMessage .= "Category: {$inventoryDetails['category_name']}\n";
    $notificationMessage .= "Quantity: {$inventoryDetails['current_quantity']}\n";
    $notificationMessage .= "Location: {$inventoryDetails['location_name']}\n";
    $notificationMessage .= "Time: " . date('Y-m-d H:i:s');

    // Insert notification for all superadmins
    $notifyQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id, is_read, created_at)
                    SELECT user_id, 'inventory_deleted', ?, ?, 'inventory', ?, 0, NOW()
                    FROM users
                    WHERE LOWER(role) IN ('superadmin', 'godmode')";
    $notifyStmt = mysqli_prepare($conn, $notifyQuery);
    mysqli_stmt_bind_param($notifyStmt, 'ssi', $notificationTitle, $notificationMessage, $inventory_id);
    mysqli_stmt_execute($notifyStmt);

    $_SESSION['success'] = "Inventory item has been deleted successfully.";
} else {
    $_SESSION['error'] = "Failed to delete inventory item: " . mysqli_error($conn);
}

// Redirect back to inventory list
header('Location: /choims/modules/inventory/list.php');
exit;
?>