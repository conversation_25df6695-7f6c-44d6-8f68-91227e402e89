/* Monthly Inventory Update CSS */

/* Progress Circle */
.monthly-update-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
}

.progress-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin-bottom: 20px;
}

.progress-circle {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #f0f0f0;
    overflow: hidden;
}

.progress-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
        #4e73df 0% var(--percentage, 0%),
        #f0f0f0 var(--percentage, 0%) 100%
    );
}

.progress-circle-inner {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background: white;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) inset;
}

.progress-value {
    font-size: 24px;
    font-weight: bold;
    color: #4e73df;
}

.progress-text {
    font-size: 14px;
    color: #666;
}

/* Stats Summary */
.stats-summary {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 10px;
}

.stat-item {
    text-align: center;
    flex: 1;
    padding: 10px;
    border-radius: 5px;
    background-color: #f8f9fc;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

/* Status Badges - Only define specific badge styles, not the base class */
.badge-monthly-completed {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.badge-monthly-pending {
    background-color: #fff8e1;
    color: #f57c00;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .monthly-update-stats {
        padding: 10px;
    }

    .progress-container {
        width: 120px;
        height: 120px;
    }

    .progress-value {
        font-size: 20px;
    }

    .stats-summary {
        flex-direction: column;
    }

    .stat-item {
        margin-bottom: 10px;
    }
}
