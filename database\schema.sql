-- Create database
CREATE DATABASE IF NOT EXISTS choims;
USE choims;

-- Users table
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('godmode', 'superadmin', 'logistics', 'himu', 'department', 'health_center') NOT NULL,
    department_id INT NULL,
    health_center_id INT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Departments table
CREATE TABLE departments (
    department_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Health Centers table
CREATE TABLE health_centers (
    health_center_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    contact_person VARCHAR(100),
    contact_number VARCHAR(20),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sources table
CREATE TABLE sources (
    source_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Suppliers table
CREATE TABLE suppliers (
    supplier_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    contact_person VARCHAR(100),
    contact_number VARCHAR(20),
    address TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Items table (Fixed Assets and Consumables)
CREATE TABLE items (
    item_id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    sku VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_fixed_asset BOOLEAN DEFAULT FALSE,
    specifications TEXT,
    unit_cost DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);

-- Fixed Assets table
CREATE TABLE fixed_assets (
    asset_id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    serial_number VARCHAR(50) UNIQUE,
    source_id INT NOT NULL,
    supplier_id INT NOT NULL,
    model VARCHAR(100),
    assigned_to INT,  -- References users table
    department_id INT,
    health_center_id INT,
    local_mr VARCHAR(50),
    status ENUM('in_use', 'available', 'under_repair', 'defective') DEFAULT 'available',
    purchase_date DATE,
    warranty_expiry DATE,
    receipt_type ENUM('sales_invoice', 'dr', 'ptr', 'or', 'po'),
    series_number VARCHAR(50),
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(item_id),
    FOREIGN KEY (source_id) REFERENCES sources(source_id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id),
    FOREIGN KEY (assigned_to) REFERENCES users(user_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (health_center_id) REFERENCES health_centers(health_center_id)
);

-- MR History table for fixed assets
CREATE TABLE mr_history (
    history_id INT PRIMARY KEY AUTO_INCREMENT,
    asset_id INT NOT NULL,
    assigned_to VARCHAR(100) NOT NULL,
    local_mr VARCHAR(50) NOT NULL,
    receipt_type ENUM('sales_invoice', 'dr', 'ptr', 'or', 'po') NOT NULL,
    transfer_id INT,
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES fixed_assets(asset_id),
    FOREIGN KEY (created_by) REFERENCES users(user_id)
);

-- Inventory table (for consumables)
CREATE TABLE inventory (
    inventory_id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    department_id INT,
    health_center_id INT,
    quantity INT NOT NULL DEFAULT 0,
    minimum_stock INT DEFAULT 0,
    maximum_stock INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(item_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (health_center_id) REFERENCES health_centers(health_center_id)
);

-- Stock Transactions table
CREATE TABLE stock_transactions (
    transaction_id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    transaction_type ENUM('in', 'out', 'transfer') NOT NULL,
    quantity INT NOT NULL,
    source_department_id INT,
    source_health_center_id INT,
    destination_department_id INT,
    destination_health_center_id INT,
    reference_number VARCHAR(50),
    remarks TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(item_id),
    FOREIGN KEY (source_department_id) REFERENCES departments(department_id),
    FOREIGN KEY (source_health_center_id) REFERENCES health_centers(health_center_id),
    FOREIGN KEY (destination_department_id) REFERENCES departments(department_id),
    FOREIGN KEY (destination_health_center_id) REFERENCES health_centers(health_center_id),
    FOREIGN KEY (created_by) REFERENCES users(user_id)
);

-- Transfer Requests table
CREATE TABLE transfer_requests (
    request_id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    quantity INT NOT NULL,
    source_department_id INT,
    source_health_center_id INT,
    destination_department_id INT,
    destination_health_center_id INT,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    requires_himu_approval BOOLEAN DEFAULT FALSE,
    himu_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    himu_approved_by INT,
    himu_approval_date TIMESTAMP NULL,
    logistics_approval_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    logistics_approved_by INT,
    logistics_approval_date TIMESTAMP NULL,
    requested_by INT NOT NULL,
    remarks TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(item_id),
    FOREIGN KEY (source_department_id) REFERENCES departments(department_id),
    FOREIGN KEY (source_health_center_id) REFERENCES health_centers(health_center_id),
    FOREIGN KEY (destination_department_id) REFERENCES departments(department_id),
    FOREIGN KEY (destination_health_center_id) REFERENCES health_centers(health_center_id),
    FOREIGN KEY (himu_approved_by) REFERENCES users(user_id),
    FOREIGN KEY (logistics_approved_by) REFERENCES users(user_id),
    FOREIGN KEY (requested_by) REFERENCES users(user_id)
);

-- Audit Logs table
CREATE TABLE audit_logs (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT NOT NULL,
    old_values TEXT,
    new_values TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Insert default categories
INSERT INTO categories (name, code, description) VALUES
('IT Equipment', 'ITE', 'Information Technology Equipment'),
('Office Equipment', 'OE', 'General Office Equipment'),
('IT Supply', 'ITS', 'Information Technology Supplies'),
('Office Supply', 'OS', 'General Office Supplies'),
('Medical Equipment', 'ME', 'Medical and Healthcare Equipment');

-- Insert default sources
INSERT INTO sources (name, code) VALUES
('LGU', 'LGU'),
('NGO', 'NGO'),
('DOH', 'DOH'),
('Private Sector', 'PS'),
('Other Agencies', 'OA');

-- Insert default suppliers
INSERT INTO suppliers (name, code) VALUES
('GSO', 'GSO'),
('Doni', 'DONI'),
('Others', 'OTHERS');