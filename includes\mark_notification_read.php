<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Ensure user is logged in
requireLogin();

// Get the notification ID from the request
$notificationId = isset($_POST['notification_id']) ? intval($_POST['notification_id']) : 0;
$userId = $_SESSION['user_id'];

if ($notificationId > 0) {
    // Mark the notification as read
    $query = "
        UPDATE notifications
        SET is_read = 1
        WHERE notification_id = ? AND user_id = ?
    ";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $notificationId, $userId);
    $success = mysqli_stmt_execute($stmt);
    
    // Prepare the response
    $response = [
        'success' => $success,
        'message' => $success ? 'Notification marked as read' : 'Failed to mark notification as read'
    ];
} else {
    $response = [
        'success' => false,
        'message' => 'Invalid notification ID'
    ];
}

// Send the response as JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
?>
