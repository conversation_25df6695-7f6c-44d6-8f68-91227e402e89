<?php
/**
 * A Compatibility library with PHP 5.5's simplified password hashing API.
 *
 * This provides a simple interface to create and verify password hashes for PHP 5.3+
 * which don't have the native password_* functions.
 */

// First, check if the native password functions exist
if (!function_exists('password_hash')) {
    /**
     * Hash the password using the specified algorithm
     *
     * @param string $password The password to hash
     * @param int    $algo     The algorithm to use (Defined by PASSWORD_* constants)
     * @param array  $options  The options for the algorithm
     *
     * @return string|false The hashed password, or false on error
     */
    function password_hash($password, $algo, array $options = array()) {
        if (!function_exists('crypt')) {
            trigger_error("Crypt must be loaded for password_hash to function", E_USER_WARNING);
            return null;
        }
        
        // Check for CRYPT_BLOWFISH support
        if ($algo == 1) {
            $cost = isset($options['cost']) ? (int) $options['cost'] : 10;
            if ($cost < 4 || $cost > 31) {
                trigger_error(sprintf("cost parameter must be between 4 and 31"), E_USER_WARNING);
                return null;
            }
            
            // Generate a salt
            $salt = sprintf('$2y$%02d$', $cost);
            $salt .= substr(str_replace('+', '.', base64_encode(random_bytes(16))), 0, 22);
            
            // Hash the password
            $hash = crypt($password, $salt);
            
            if (strlen($hash) >= 60) {
                return $hash;
            }
            
            return null;
        }
        
        // Use SHA-512 as fallback
        $salt = substr(str_replace('+', '.', base64_encode(random_bytes(16))), 0, 16);
        $hash = hash('sha512', $salt . $password);
        return '$6$' . $salt . '$' . $hash;
    }
    
    /**
     * Get information about the password hash
     *
     * @param string $hash The password hash to extract info from
     *
     * @return array The array of information about the hash
     */
    function password_get_info($hash) {
        $return = array(
            'algo' => 0,
            'algoName' => 'unknown',
            'options' => array(),
        );
        
        if (substr($hash, 0, 4) == '$2y$') {
            $return['algo'] = 1;
            $return['algoName'] = 'bcrypt';
            list($cost) = sscanf($hash, '$2y$%d$');
            $return['options']['cost'] = $cost;
        } else if (substr($hash, 0, 3) == '$6$') {
            $return['algo'] = 2;
            $return['algoName'] = 'sha512';
        }
        
        return $return;
    }
    
    /**
     * Verify a password against a hash
     *
     * @param string $password The password to verify
     * @param string $hash     The hash to verify against
     *
     * @return bool If the password matches the hash
     */
    function password_verify($password, $hash) {
        if (substr($hash, 0, 4) == '$2y$') {
            if (function_exists('crypt')) {
                $result = crypt($password, $hash);
                return $result === $hash;
            }
            return false;
        } else if (substr($hash, 0, 3) == '$6$') {
            // Extract salt
            $salt = substr($hash, 3, 16);
            $fullHash = '$6$' . $salt . '$' . hash('sha512', $salt . $password);
            return $fullHash === $hash;
        }
        
        return false;
    }
    
    /**
     * Determine if the password hash needs to be rehashed
     *
     * @param string $hash    The hash to test
     * @param int    $algo    The algorithm used for new password hashes
     * @param array  $options The options array passed to password_hash
     *
     * @return bool True if the password needs to be rehashed
     */
    function password_needs_rehash($hash, $algo, array $options = array()) {
        $info = password_get_info($hash);
        
        if ($info['algo'] !== $algo) {
            return true;
        }
        
        switch ($algo) {
            case 1:
                $cost = isset($options['cost']) ? (int) $options['cost'] : 10;
                if ($cost !== $info['options']['cost']) {
                    return true;
                }
                break;
        }
        
        return false;
    }
}

/**
 * Generate cryptographically secure pseudo-random bytes
 * 
 * @param int $length The length of bytes to generate
 * @return string The random bytes
 */
if (!function_exists('random_bytes')) {
    function random_bytes($length) {
        $strong = false;
        
        if (function_exists('openssl_random_pseudo_bytes')) {
            $bytes = openssl_random_pseudo_bytes($length, $strong);
            if ($bytes && $strong) {
                return $bytes;
            }
        }
        
        // Fallback to less secure method
        $bytes = '';
        for ($i = 0; $i < $length; $i++) {
            $bytes .= chr(mt_rand(0, 255));
        }
        
        return $bytes;
    }
}
?> 