<?php
/**
 * Password Hashing Script
 * 
 * This script updates all plain text passwords in the database to secure hashed versions.
 * It should be run once during the transition to secure password storage.
 * 
 * IMPORTANT: This script should only be accessible to administrators and should be
 * removed after use.
 */

// Use relative paths
$base_path = dirname(dirname(__FILE__));
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure only administrators can run this script
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || 
    (strtolower($_SESSION['role']) !== 'godmode' && strtolower($_SESSION['role']) !== 'superadmin')) {
    die("Access denied. This script can only be run by administrators.");
}

// Initialize counters
$total_users = 0;
$updated_users = 0;
$already_hashed = 0;
$errors = 0;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
    // Get all users
    $query = "SELECT user_id, username, password FROM users";
    $result = mysqli_query($conn, $query);
    
    if ($result) {
        $total_users = mysqli_num_rows($result);
        
        while ($user = mysqli_fetch_assoc($result)) {
            // Check if password is already hashed
            $is_hashed = (strpos($user['password'], '$2y$') === 0);
            
            if (!$is_hashed) {
                // Hash the plain text password
                $hashed_password = password_hash($user['password'], PASSWORD_DEFAULT);
                
                // Update the user's password
                $update_query = "UPDATE users SET password = ? WHERE user_id = ?";
                $stmt = mysqli_prepare($conn, $update_query);
                mysqli_stmt_bind_param($stmt, 'si', $hashed_password, $user['user_id']);
                
                if (mysqli_stmt_execute($stmt)) {
                    $updated_users++;
                    // Log the update
                    logActivity($conn, 'Update', 'User', $user['user_id'], null, "Password hashed by admin");
                } else {
                    $errors++;
                }
            } else {
                $already_hashed++;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Hashing Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Password Hashing Tool</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes'): ?>
                            <div class="alert alert-info">
                                <h5>Password Hashing Results:</h5>
                                <ul>
                                    <li>Total users: <?php echo $total_users; ?></li>
                                    <li>Users updated: <?php echo $updated_users; ?></li>
                                    <li>Already hashed: <?php echo $already_hashed; ?></li>
                                    <li>Errors: <?php echo $errors; ?></li>
                                </ul>
                            </div>
                            
                            <?php if ($errors > 0): ?>
                                <div class="alert alert-danger">
                                    There were errors during the hashing process. Please check the logs.
                                </div>
                            <?php elseif ($updated_users > 0): ?>
                                <div class="alert alert-success">
                                    Password hashing completed successfully!
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    No passwords needed to be updated. All passwords are already hashed.
                                </div>
                            <?php endif; ?>
                            
                            <div class="alert alert-warning">
                                <strong>Important:</strong> For security reasons, please delete this script after use.
                            </div>
                            
                            <a href="../index.php" class="btn btn-primary">Return to Dashboard</a>
                        <?php else: ?>
                            <p>This tool will update all plain text passwords in the database to secure hashed versions.</p>
                            <p><strong>Warning:</strong> This process cannot be undone. Make sure you have a database backup before proceeding.</p>
                            
                            <form method="post" action="">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                                    <label class="form-check-label" for="confirmCheck">
                                        I confirm that I have a database backup and want to proceed
                                    </label>
                                </div>
                                
                                <input type="hidden" name="confirm" value="yes">
                                <button type="submit" class="btn btn-danger" id="submitBtn" disabled>Hash All Passwords</button>
                                <a href="../index.php" class="btn btn-secondary ms-2">Cancel</a>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Enable submit button only when checkbox is checked
        document.getElementById('confirmCheck').addEventListener('change', function() {
            document.getElementById('submitBtn').disabled = !this.checked;
        });
    </script>
</body>
</html>
