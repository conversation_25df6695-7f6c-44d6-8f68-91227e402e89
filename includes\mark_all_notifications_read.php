<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Ensure user is logged in
requireLogin();

$userId = $_SESSION['user_id'];

// Mark all notifications as read
$query = "
    UPDATE notifications
    SET is_read = 1
    WHERE user_id = ? AND is_read = 0
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $userId);
$success = mysqli_stmt_execute($stmt);

// Prepare the response
$response = [
    'success' => $success,
    'message' => $success ? 'All notifications marked as read' : 'Failed to mark notifications as read'
];

// Send the response as JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
?>
