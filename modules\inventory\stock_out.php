<?php
// Start output buffering at the beginning of the file
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern stock out CSS
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-stock-out-modern.css">';

// Ensure user is logged in
requireLogin();

// Prevent superadmin from accessing this page (direct URL protection)
if (strtolower($_SESSION['role']) === 'superadmin') {
    // Redirect to list view instead
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

// Initialize variables
$userLocationId = getUserLocationId();
$selectedSku = '';
$inventoryItem = null;

// Get all consumable inventory items for the user's location
$inventoryQuery = "
    SELECT
        i.inventory_id, i.sku_id, i.current_quantity, i.low_stock_threshold, i.critical_threshold,
        i.status, l.location_name, s.sku_code, s.sku_name, c.category_name, s.unit_of_measure
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON i.location_id = l.location_id
    WHERE i.location_id = ? AND i.current_quantity > 0
";

// For HIMU users, only show IT supplies (category_id = 4)
// For other users, show all categories including IT supplies
if (strtolower($_SESSION['role']) === 'himu') {
    $inventoryQuery .= " AND c.category_id = 4";
}

$inventoryQuery .= "
    ORDER BY s.sku_code
";
$inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
mysqli_stmt_bind_param($inventoryStmt, 'i', $userLocationId);
mysqli_stmt_execute($inventoryStmt);
$inventoryResult = mysqli_stmt_get_result($inventoryStmt);

// Get locations for transfer dropdown (exclude current location)
$locationQuery = "SELECT location_id, location_name FROM locations WHERE location_id != ? ORDER BY location_name";
$locationStmt = mysqli_prepare($conn, $locationQuery);
mysqli_stmt_bind_param($locationStmt, 'i', $userLocationId);
mysqli_stmt_execute($locationStmt);
$locationResult = mysqli_stmt_get_result($locationStmt);

// If a product is selected, get its details
if (isset($_GET['sku_id']) && !empty($_GET['sku_id'])) {
    $selectedSku = sanitizeInput($_GET['sku_id']);

    $itemQuery = "
        SELECT
            i.inventory_id, i.current_quantity, i.low_stock_threshold, i.critical_threshold,
            i.status, l.location_name, s.sku_code, s.sku_name, c.category_name, s.sku_id,
            s.unit_of_measure
        FROM consumable_inventory i
        JOIN sku_master s ON i.sku_id = s.sku_id
        JOIN categories c ON s.category_id = c.category_id
        JOIN locations l ON i.location_id = l.location_id
        WHERE i.location_id = ? AND s.sku_id = ?
    ";
    $itemStmt = mysqli_prepare($conn, $itemQuery);
    mysqli_stmt_bind_param($itemStmt, 'ii', $userLocationId, $selectedSku);
    mysqli_stmt_execute($itemStmt);
    $itemResult = mysqli_stmt_get_result($itemStmt);

    if (mysqli_num_rows($itemResult) > 0) {
        $inventoryItem = mysqli_fetch_assoc($itemResult);
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate inputs
    $inventory_id = sanitizeInput($_POST['inventory_id']);
    $quantity = sanitizeInput($_POST['quantity']);
    $unit_type = sanitizeInput($_POST['unit_type']);
    $transaction_type = sanitizeInput($_POST['transaction_type']);
    $destination_location_id = NULL;
    $reference_document = !empty($_POST['reference_document']) ? sanitizeInput($_POST['reference_document']) : NULL;
    $notes = !empty($_POST['notes']) ? sanitizeInput($_POST['notes']) : NULL;

    // For transfers, redirect to formal transfer process
    if ($transaction_type == 'Transfer') {
        $destination_location_id = sanitizeInput($_POST['destination_location_id']);
        $redirect_url = "/choims/modules/transfers/initiate.php?inventory_id={$inventory_id}&quantity={$quantity}&notes=" . urlencode($notes);

        // Add destination location to the redirect URL
        $redirect_url .= "&destination_location_id={$destination_location_id}";

        if ($reference_document) {
            $redirect_url .= "&reference_document=" . urlencode($reference_document);
        }

        if (headers_sent()) {
            echo '<script>window.location.href = "' . $redirect_url . '";</script>';
            exit();
        } else {
            header("Location: " . $redirect_url);
            exit();
        }
    }

    // Validate that quantity is > 0
    if ($quantity <= 0) {
        $error = "Quantity must be greater than zero.";
    } else {
        // Begin transaction
        mysqli_begin_transaction($conn);

        try {
            // Get current quantity and other details
            $currentQuery = "SELECT current_quantity, sku_id, low_stock_threshold, critical_threshold FROM consumable_inventory WHERE inventory_id = ?";
            $currentStmt = mysqli_prepare($conn, $currentQuery);
            mysqli_stmt_bind_param($currentStmt, 'i', $inventory_id);
            mysqli_stmt_execute($currentStmt);
            $currentResult = mysqli_stmt_get_result($currentStmt);
            $currentData = mysqli_fetch_assoc($currentResult);

            // Check if enough quantity is available
            if ($quantity > $currentData['current_quantity']) {
                throw new Exception("Insufficient stock. Current quantity: " . $currentData['current_quantity']);
            }

            $newQuantity = $currentData['current_quantity'] - $quantity;

            // Get the unit of measure for this inventory item
            $unitTypeQuery = "SELECT unit_of_measure FROM sku_master WHERE sku_id = ?";
            $unitTypeStmt = mysqli_prepare($conn, $unitTypeQuery);
            mysqli_stmt_bind_param($unitTypeStmt, 'i', $currentData['sku_id']);
            mysqli_stmt_execute($unitTypeStmt);
            $unitTypeResult = mysqli_stmt_get_result($unitTypeStmt);
            $unitTypeData = mysqli_fetch_assoc($unitTypeResult);
            $unitOfMeasure = $unitTypeData['unit_of_measure'] ?? '';

            // Determine status based on new quantity and unit type
            $status = 'Available';

            // Special handling for box unit type - only mark as Out of Stock if quantity is 0
            if ($unitOfMeasure === 'box') {
                if ($newQuantity <= 0) {
                    $status = 'Out of Stock';
                } elseif ($newQuantity <= $currentData['low_stock_threshold']) {
                    $status = 'Low Stock';
                }
            } else {
                // Standard handling for other unit types
                if ($newQuantity <= $currentData['critical_threshold']) {
                    $status = 'Out of Stock';
                } elseif ($newQuantity <= $currentData['low_stock_threshold']) {
                    $status = 'Low Stock';
                }
            }

            // Update inventory
            $updateQuery = "
                UPDATE consumable_inventory
                SET current_quantity = ?, status = ?, updated_at = NOW()
                WHERE inventory_id = ?
            ";
            $updateStmt = mysqli_prepare($conn, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, 'isi', $newQuantity, $status, $inventory_id);
            mysqli_stmt_execute($updateStmt);

            // Handle transfer logic
            if ($transaction_type == 'Transfer') {
                // Check if the destination already has this SKU
                $checkDestQuery = "
                    SELECT inventory_id, current_quantity, low_stock_threshold, critical_threshold
                    FROM consumable_inventory
                    WHERE sku_id = ? AND location_id = ?
                ";
                $checkDestStmt = mysqli_prepare($conn, $checkDestQuery);
                mysqli_stmt_bind_param($checkDestStmt, 'ii', $currentData['sku_id'], $destination_location_id);
                mysqli_stmt_execute($checkDestStmt);
                $checkDestResult = mysqli_stmt_get_result($checkDestStmt);

                if (mysqli_num_rows($checkDestResult) > 0) {
                    // Update existing inventory at destination
                    $destData = mysqli_fetch_assoc($checkDestResult);
                    $destNewQuantity = $destData['current_quantity'] + $quantity;

                    // Determine status for destination location based on unit type
                    $destStatus = 'Available';

                    // Special handling for box unit type - only mark as Out of Stock if quantity is 0
                    if ($unitOfMeasure === 'box') {
                        if ($destNewQuantity <= 0) {
                            $destStatus = 'Out of Stock';
                        } elseif ($destNewQuantity <= $destData['low_stock_threshold']) {
                            $destStatus = 'Low Stock';
                        }
                    } else {
                        // Standard handling for other unit types
                        if ($destNewQuantity <= $destData['critical_threshold']) {
                            $destStatus = 'Out of Stock';
                        } elseif ($destNewQuantity <= $destData['low_stock_threshold']) {
                            $destStatus = 'Low Stock';
                        }
                    }

                    $updateDestQuery = "
                        UPDATE consumable_inventory
                        SET current_quantity = ?, status = ?, last_restock_date = NOW(), updated_at = NOW()
                        WHERE inventory_id = ?
                    ";
                    $updateDestStmt = mysqli_prepare($conn, $updateDestQuery);
                    mysqli_stmt_bind_param($updateDestStmt, 'isi', $destNewQuantity, $destStatus, $destData['inventory_id']);
                    mysqli_stmt_execute($updateDestStmt);

                    $destination_inventory_id = $destData['inventory_id'];
                } else {
                    // Create new inventory entry at destination
                    $insertDestQuery = "
                        INSERT INTO consumable_inventory (
                            sku_id, location_id, current_quantity, min_quantity,
                            status, low_stock_threshold, critical_threshold, last_restock_date,
                            created_at, updated_at
                        ) VALUES (
                            ?, ?, ?, 0, 'Available', ?, ?, NOW(), NOW(), NOW()
                        )
                    ";
                    $insertDestStmt = mysqli_prepare($conn, $insertDestQuery);
                    mysqli_stmt_bind_param(
                        $insertDestStmt,
                        'iiiii',
                        $currentData['sku_id'], $destination_location_id, $quantity,
                        $currentData['low_stock_threshold'], $currentData['critical_threshold']
                    );
                    mysqli_stmt_execute($insertDestStmt);

                    $destination_inventory_id = mysqli_insert_id($conn);
                }

                // Create transaction record for destination (stock in)
                $transactionDestQuery = "
                    INSERT INTO consumable_transactions (
                        inventory_id, transaction_type, quantity,
                        source_location_id, destination_location_id, reference_document,
                        remarks, performed_by, transaction_date
                    ) VALUES (
                        ?, 'Stock In', ?, ?, ?, ?, ?, ?, NOW()
                    )
                ";
                $destRemarks = "Transfer from " . getUserLocation($conn, $userLocationId) . ". Unit Type: " . $unit_type;
                if (!empty($notes)) {
                    $destRemarks .= ". Notes: " . $notes;
                }

                $transactionDestStmt = mysqli_prepare($conn, $transactionDestQuery);
                mysqli_stmt_bind_param(
                    $transactionDestStmt,
                    'iiisssi',
                    $destination_inventory_id, $quantity, $userLocationId, $destination_location_id,
                    $reference_document, $destRemarks, $_SESSION['user_id']
                );
                mysqli_stmt_execute($transactionDestStmt);
            }

            // Record transaction for source (stock out)
            $transactionQuery = "
                INSERT INTO consumable_transactions (
                    inventory_id, transaction_type, quantity,
                    source_location_id, destination_location_id, reference_document,
                    remarks, performed_by, transaction_date
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )
            ";

            $remarks = "Unit Type: " . $unit_type;
            if (!empty($notes)) {
                $remarks .= ". Notes: " . $notes;
            }

            $transactionStmt = mysqli_prepare($conn, $transactionQuery);
            mysqli_stmt_bind_param(
                $transactionStmt,
                'isiisssi',
                $inventory_id, $transaction_type, $quantity, $userLocationId, $destination_location_id,
                $reference_document, $remarks, $_SESSION['user_id']
            );
            mysqli_stmt_execute($transactionStmt);

            // Create audit log
            createAuditLog(
                $_SESSION['user_id'],
                'update',
                'consumable_inventory',
                $inventory_id,
                json_encode(['current_quantity' => $currentData['current_quantity']]),
                json_encode(['current_quantity' => $newQuantity])
            );

            // Get transaction IDs
            $source_transaction_id = mysqli_insert_id($conn);
            $dest_transaction_id = null;
            if (isset($transactionDestStmt)) {
                $dest_transaction_id = mysqli_insert_id($conn) - 1; // The destination transaction was inserted before the source
            }

            // Get SKU details for detailed logging
            $skuQuery = "SELECT sm.sku_name, sm.sku_code, c.category_name, l.location_name
                         FROM sku_master sm
                         JOIN categories c ON sm.category_id = c.category_id
                         JOIN consumable_inventory ci ON sm.sku_id = ci.sku_id
                         JOIN locations l ON ci.location_id = l.location_id
                         WHERE sm.sku_id = ? AND ci.inventory_id = ?";
            $skuStmt = mysqli_prepare($conn, $skuQuery);
            mysqli_stmt_bind_param($skuStmt, 'ii', $currentData['sku_id'], $inventory_id);
            mysqli_stmt_execute($skuStmt);
            $skuResult = mysqli_stmt_get_result($skuStmt);
            $skuData = mysqli_fetch_assoc($skuResult);

            // Get destination location name
            $destLocationName = '';
            if ($destination_location_id) {
                $destLocationQuery = "SELECT location_name FROM locations WHERE location_id = ?";
                $destLocationStmt = mysqli_prepare($conn, $destLocationQuery);
                mysqli_stmt_bind_param($destLocationStmt, 'i', $destination_location_id);
                mysqli_stmt_execute($destLocationStmt);
                $destLocationResult = mysqli_stmt_get_result($destLocationStmt);
                if ($destLocation = mysqli_fetch_assoc($destLocationResult)) {
                    $destLocationName = $destLocation['location_name'];
                }
                mysqli_stmt_close($destLocationStmt);
            }

            // Create detailed audit log for inventory update
            $old_values = [
                'current_quantity' => $currentData['current_quantity']
            ];
            $new_values = [
                'current_quantity' => $newQuantity,
                'status' => $status
            ];
            logConsumableAction($conn, $_SESSION['user_id'], 'update', $inventory_id, $old_values, $new_values);

            // Create detailed audit log for source stock transaction
            $source_transaction_data = [
                'inventory_id' => $inventory_id,
                'transaction_type' => $transaction_type,
                'quantity' => $quantity,
                'source_location_id' => $userLocationId,
                'source_location_name' => $skuData['location_name'] ?? 'Unknown',
                'destination_location_id' => $destination_location_id,
                'destination_location_name' => $destLocationName,
                'reference_document' => $reference_document,
                'remarks' => $remarks,
                'item_name' => $skuData['sku_name'] ?? 'Unknown',
                'item_sku' => $skuData['sku_code'] ?? 'Unknown',
                'item_category' => $skuData['category_name'] ?? 'Unknown'
            ];
            logStockTransaction($conn, $_SESSION['user_id'], 'stock_out', $source_transaction_id, $source_transaction_data);

            // If this was a transfer, log the destination transaction too
            if ($dest_transaction_id && $transaction_type == 'Transfer') {
                $dest_transaction_data = [
                    'inventory_id' => $destination_inventory_id,
                    'transaction_type' => 'Stock In',
                    'quantity' => $quantity,
                    'source_location_id' => $userLocationId,
                    'source_location_name' => $skuData['location_name'] ?? 'Unknown',
                    'destination_location_id' => $destination_location_id,
                    'destination_location_name' => $destLocationName,
                    'reference_document' => $reference_document,
                    'remarks' => $destRemarks,
                    'item_name' => $skuData['sku_name'] ?? 'Unknown',
                    'item_sku' => $skuData['sku_code'] ?? 'Unknown',
                    'item_category' => $skuData['category_name'] ?? 'Unknown'
                ];
                logStockTransaction($conn, $_SESSION['user_id'], 'stock_in', $dest_transaction_id, $dest_transaction_data);
            }

            // Commit transaction
            mysqli_commit($conn);

            // Success message and redirect
            $_SESSION['success_message'] = $quantity . " units " . strtolower($transaction_type) . "red successfully!";
            if (headers_sent()) {
                echo '<script>window.location.href = "/choims/modules/inventory/stock_out.php?success=1";</script>';
                exit();
            } else {
                header("Location: /choims/modules/inventory/stock_out.php?success=1");
                exit();
            }

        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($conn);
            $error = "Error processing stock out: " . $e->getMessage();
        }
    }
}
?>

<div class="container-fluid">
    <!-- Page heading -->
    <div class="page-header mb-4 animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title"><i class="fas fa-minus-circle"></i> Stock Out / Transfer</h1>
                <p class="page-subtitle">Remove items from inventory or transfer to another location</p>
            </div>
            <div class="col-md-4 text-md-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/choims/dashboards/logistics.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="/choims/modules/inventory/list.php">Inventory</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Stock Out</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="d-flex w-100 justify-content-between align-items-center">
            <div><?php echo isset($_SESSION['success_message']) ? $_SESSION['success_message'] : 'Stock out processed successfully!'; ?></div>
            <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="d-flex w-100 justify-content-between align-items-center">
            <div><?php echo $error; ?></div>
            <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Product Selection Card -->
    <div class="card animate__animated animate__fadeInUp animate__faster">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-box"></i>
                Select Product
            </div>
        </div>
        <div class="card-body">
            <form method="get" action="" class="row g-3" id="productSelectForm">
                <div class="col-md-6">
                    <label for="sku_id" class="form-label fw-medium">Choose Product</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-search text-primary"></i></span>
                        <select class="form-select rounded-end-3 border-start-0" id="sku_id" name="sku_id" onchange="this.form.submit()">
                            <option value="">Select a Product</option>
                            <?php
                            // Debug: Count items by category
                            $categoryCount = [];
                            mysqli_data_seek($inventoryResult, 0);
                            while ($debugItem = mysqli_fetch_assoc($inventoryResult)) {
                                if (!isset($categoryCount[$debugItem['category_name']])) {
                                    $categoryCount[$debugItem['category_name']] = 0;
                                }
                                $categoryCount[$debugItem['category_name']]++;
                            }

                            // Check if there are any IT supplies in the database
                            $itSuppliesQuery = "SELECT COUNT(*) as count FROM consumable_inventory i
                                               JOIN sku_master s ON i.sku_id = s.sku_id
                                               WHERE s.category_id = 4";
                            $itSuppliesResult = mysqli_query($conn, $itSuppliesQuery);
                            $itSuppliesCount = mysqli_fetch_assoc($itSuppliesResult)['count'];

                            // Check if there are any IT supplies in the user's location
                            $itSuppliesLocationQuery = "SELECT COUNT(*) as count FROM consumable_inventory i
                                                      JOIN sku_master s ON i.sku_id = s.sku_id
                                                      WHERE s.category_id = 4 AND i.location_id = $userLocationId";
                            $itSuppliesLocationResult = mysqli_query($conn, $itSuppliesLocationQuery);
                            $itSuppliesLocationCount = mysqli_fetch_assoc($itSuppliesLocationResult)['count'];

                            // Display debug info if no items are found
                            if (mysqli_num_rows($inventoryResult) == 0) {
                                echo '<option value="" disabled>No inventory items found in your location</option>';

                                // If there are IT supplies in the database but not in this location
                                if ($itSuppliesCount > 0 && $itSuppliesLocationCount == 0) {
                                    echo '<option value="" disabled>───────────────────────────</option>';
                                    echo '<option value="" disabled>There are ' . $itSuppliesCount . ' IT supplies in the database</option>';
                                    echo '<option value="" disabled>but none are assigned to your location.</option>';
                                    echo '<option value="" disabled>Contact your administrator to transfer</option>';
                                    echo '<option value="" disabled>IT supplies to your location.</option>';
                                }

                                echo '<!-- Debug: User Location ID: ' . $userLocationId . ' -->';
                                echo '<!-- Debug: Total IT Supplies in database: ' . $itSuppliesCount . ' -->';
                                echo '<!-- Debug: IT Supplies in your location: ' . $itSuppliesLocationCount . ' -->';
                            } else {
                                echo '<!-- Debug: Found ' . mysqli_num_rows($inventoryResult) . ' items -->';
                                echo '<!-- Debug: Total IT Supplies in database: ' . $itSuppliesCount . ' -->';
                                echo '<!-- Debug: IT Supplies in your location: ' . $itSuppliesLocationCount . ' -->';
                                foreach ($categoryCount as $cat => $count) {
                                    echo '<!-- Debug: ' . $cat . ': ' . $count . ' items -->';
                                }
                            }

                            $currentCategory = '';
                            mysqli_data_seek($inventoryResult, 0);
                            while ($item = mysqli_fetch_assoc($inventoryResult)):
                                if ($currentCategory != $item['category_name']) {
                                    if ($currentCategory != '') {
                                        echo '</optgroup>';
                                    }
                                    echo '<optgroup label="' . $item['category_name'] . '">';
                                    $currentCategory = $item['category_name'];
                                }
                            ?>
                                <option value="<?php echo $item['sku_id']; ?>" <?php echo ($selectedSku == $item['sku_id']) ? 'selected' : ''; ?>>
                                    <?php echo $item['sku_code']; ?> - <?php echo $item['sku_name']; ?>
                                    (<?php echo $item['current_quantity']; ?> available)
                                </option>
                            <?php
                            endwhile;
                            if ($currentCategory != '') {
                                echo '</optgroup>';
                            }
                            ?>
                        </select>
                    </div>
                    <div class="form-text small">Select the product to remove from inventory or transfer</div>
                </div>
            </form>
        </div>
    </div>

    <?php if ($inventoryItem): ?>
    <!-- Product Info & Stock Out Form -->
    <div class="row">
        <!-- Left Column: Product Info -->
        <div class="col-md-4">
            <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <div class="card-header-title">
                        <i class="fas fa-info-circle"></i>
                        Product Information
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="product-avatar">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                        <h5 class="mb-1"><?php echo $inventoryItem['sku_name']; ?></h5>
                        <p class="mb-0 text-muted"><?php echo $inventoryItem['sku_code']; ?></p>
                        <span class="badge bg-info bg-opacity-10 rounded-pill px-3 py-2 mt-2">
                            <i class="fas fa-tag me-1"></i>
                            <?php echo $inventoryItem['category_name']; ?>
                        </span>
                    </div>

                    <div class="border-top pt-3">
                        <ul class="product-info-list">
                            <li class="product-info-item">
                                <span class="product-info-label">Current Stock</span>
                                <span class="product-info-value"><?php echo $inventoryItem['current_quantity']; ?></span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Status</span>
                                <?php
                                $statusBadgeClass = 'bg-success bg-opacity-10 text-success';
                                $statusIcon = 'fa-check-circle';
                                switch ($inventoryItem['status']) {
                                    case 'Available':
                                        $statusBadgeClass = 'bg-success bg-opacity-10 text-success';
                                        $statusIcon = 'fa-check-circle';
                                        break;
                                    case 'Low Stock':
                                        $statusBadgeClass = 'bg-warning bg-opacity-10 text-warning';
                                        $statusIcon = 'fa-exclamation-circle';
                                        break;
                                    case 'Out of Stock':
                                        $statusBadgeClass = 'bg-danger bg-opacity-10 text-danger';
                                        $statusIcon = 'fa-times-circle';
                                        break;
                                    default:
                                        $statusBadgeClass = 'bg-secondary bg-opacity-10 text-secondary';
                                        $statusIcon = 'fa-question-circle';
                                }
                                ?>
                                <span class="badge rounded-pill <?php echo $statusBadgeClass; ?> px-3 py-2">
                                    <i class="fas <?php echo $statusIcon; ?> me-1"></i>
                                    <?php echo $inventoryItem['status']; ?>
                                </span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Low Stock Threshold</span>
                                <span class="product-info-value"><?php echo $inventoryItem['low_stock_threshold']; ?></span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Critical Threshold</span>
                                <span class="product-info-value"><?php echo $inventoryItem['critical_threshold']; ?></span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Location</span>
                                <span class="product-info-value"><?php echo $inventoryItem['location_name']; ?></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Stock Out Form -->
        <div class="col-md-8">
            <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <div class="card-header-title">
                        <i class="fas fa-minus-circle"></i>
                        Remove Stock
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="" id="stockOutForm">
                        <input type="hidden" name="inventory_id" value="<?php echo $inventoryItem['inventory_id']; ?>">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="stock-calculator mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="stock-calculator-icon">
                                            <i class="fas fa-calculator"></i>
                                        </div>
                                        <div>
                                            <div class="stock-calculator-label">Stock After Removal</div>
                                            <div class="stock-calculator-value" id="newStockTotal"><?php echo $inventoryItem['current_quantity']; ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="transaction_type" class="form-label fw-medium">Transaction Type <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-exchange-alt text-primary"></i></span>
                                    <select class="form-select rounded-end-3 border-start-0" id="transaction_type" name="transaction_type" onchange="toggleDestinationField()" required>
                                        <option value="Stock Out">Stock Out (Usage/Consumption)</option>
                                        <option value="Transfer" id="transfer_option">Transfer to Another Location</option>
                                        <option value="Adjustment">Adjustment/Correction</option>
                                    </select>
                                </div>
                            </div>

                            <div id="destination_field" class="col-md-6" style="display:none;">
                                <label for="destination_location_id" class="form-label fw-medium">Destination Location <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-map-marker-alt text-primary"></i></span>
                                    <select class="form-select rounded-end-3 border-start-0" id="destination_location_id" name="destination_location_id">
                                        <option value="">Select Destination</option>
                                        <?php mysqli_data_seek($locationResult, 0); ?>
                                        <?php while ($location = mysqli_fetch_assoc($locationResult)): ?>
                                            <option value="<?php echo $location['location_id']; ?>">
                                                <?php echo $location['location_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <div class="alert alert-info d-flex align-items-center mt-2 p-2 small">
                                    <div class="alert-icon me-2" style="width: 24px; height: 24px;">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div>
                                        Transfers require approval. Clicking "Process" will initiate a formal transfer request.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <label for="quantity" class="form-label fw-medium">Quantity <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-sort-numeric-down text-primary"></i></span>
                                    <input type="number" class="form-control border-start-0 border-end-0" id="quantity" name="quantity" min="1" max="<?php echo $inventoryItem['current_quantity']; ?>" placeholder="Enter quantity" required>
                                    <select class="form-select rounded-end-3" id="unit_type" name="unit_type" style="max-width: 120px;">
                                        <?php
                                        $unitOptions = ['pcs', 'box', 'pack', 'bottle', 'roll', 'set', 'gallon', 'ream'];
                                        $selectedUnit = isset($inventoryItem['unit_of_measure']) ? $inventoryItem['unit_of_measure'] : 'pcs';

                                        // If the unit from database is not in our list, add it
                                        if (!in_array($selectedUnit, $unitOptions)) {
                                            $unitOptions[] = $selectedUnit;
                                        }

                                        foreach ($unitOptions as $unit) {
                                            $selected = ($unit == $selectedUnit) ? 'selected' : '';
                                            echo "<option value=\"{$unit}\" {$selected}>{$unit}</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="form-text small">Maximum available: <?php echo $inventoryItem['current_quantity']; ?></div>
                            </div>

                            <div class="col-md-6">
                                <label for="reference_document" class="form-label fw-medium">Reference Document</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-file-alt text-primary"></i></span>
                                    <input type="text" class="form-control rounded-end-3 border-start-0" id="reference_document" name="reference_document"
                                           placeholder="Request Form #, Order #, etc.">
                                </div>
                            </div>

                            <div class="col-12">
                                <label for="notes" class="form-label fw-medium">Notes</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-sticky-note text-primary"></i></span>
                                    <textarea class="form-control rounded-end-3 border-start-0" id="notes" name="notes" rows="2"
                                        placeholder="Enter any additional notes or reason for stock removal..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-3 mt-4">
                            <button type="submit" class="btn btn-primary px-4 py-2" id="submit_button">
                                <i class="fas fa-check me-2"></i> Process Stock Out
                            </button>
                            <button type="reset" class="btn btn-outline-secondary px-4 py-2">
                                <i class="fas fa-undo me-1"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function toggleDestinationField() {
    const transactionType = document.getElementById('transaction_type').value;
    const destinationField = document.getElementById('destination_field');
    const destinationSelect = document.getElementById('destination_location_id');
    const submitButton = document.getElementById('submit_button');

    if (transactionType === 'Transfer') {
        destinationField.style.display = 'block';
        destinationSelect.required = true;
        submitButton.innerHTML = '<i class="fas fa-exchange-alt me-2"></i> Initiate Transfer Request';
        submitButton.classList.remove('btn-primary');
        submitButton.classList.add('btn-success');
    } else {
        destinationField.style.display = 'none';
        destinationSelect.required = false;
        submitButton.innerHTML = '<i class="fas fa-check me-2"></i> Process Stock Out';
        submitButton.classList.remove('btn-success');
        submitButton.classList.add('btn-primary');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Show loading animation
    document.querySelectorAll('.animate__animated').forEach(function(element, index) {
        element.style.opacity = '0';
        setTimeout(function() {
            element.style.opacity = '1';
        }, 100 * index);
    });

    // Initialize fields
    toggleDestinationField();

    // Configure dynamic stock calculation
    const quantityInput = document.getElementById('quantity');
    const newStockTotal = document.getElementById('newStockTotal');
    const currentStock = <?php echo $inventoryItem ? $inventoryItem['current_quantity'] : 0; ?>;

    if (quantityInput && newStockTotal) {
        quantityInput.addEventListener('input', function() {
            const quantity = parseInt(this.value) || 0;
            const remaining = Math.max(0, currentStock - quantity);
            newStockTotal.textContent = remaining;

            // Change color based on new total
            if (remaining <= <?php echo $inventoryItem ? $inventoryItem['critical_threshold'] : 0; ?>) {
                newStockTotal.className = 'stock-calculator-value text-danger';
            } else if (remaining <= <?php echo $inventoryItem ? $inventoryItem['low_stock_threshold'] : 0; ?>) {
                newStockTotal.className = 'stock-calculator-value text-warning';
            } else {
                newStockTotal.className = 'stock-calculator-value text-success';
            }
        });
    }

    // Enhanced select options
    if (typeof jQuery !== 'undefined' && jQuery.fn.select2) {
        jQuery('#sku_id').select2({
            placeholder: 'Select a product',
            theme: 'classic'
        });

        jQuery('#destination_location_id').select2({
            placeholder: 'Select a destination',
            theme: 'classic'
        });
    }

    // Make unit type dropdown read-only if a product is selected
    const unitTypeSelect = document.getElementById('unit_type');
    if (unitTypeSelect) {
        // Disable the dropdown to prevent changes
        unitTypeSelect.disabled = true;

        // Add a visual indicator that it's read-only
        unitTypeSelect.parentElement.classList.add('opacity-75');

        // Add a tooltip to explain why it's read-only
        unitTypeSelect.title = "Unit type is set based on the product's configuration and cannot be changed";
    }

    // Add dismiss functionality to alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const closeButton = alert.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                alert.classList.add('animate__fadeOutUp');
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }
    });
});
</script>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>