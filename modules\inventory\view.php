<?php
// Start by only including the essential PHP files needed for permission checking
$base_path = $_SERVER['DOCUMENT_ROOT'] . '/choims';
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/auth.php');

// Initialize session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure user is logged in
requireLogin();

// Get inventory item ID from URL parameter
$inventory_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($inventory_id <= 0) {
    $_SESSION['error'] = "Invalid inventory ID";
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

// Get inventory item details
$inventoryQuery = "
    SELECT
        i.*, s.sku_code, s.sku_name, c.category_name, c.requires_himu_approval, l.location_name,
        (SELECT COUNT(*) FROM transfers t WHERE t.inventory_id = i.inventory_id AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) as has_active_transfer,
        (SELECT t.transfer_id FROM transfers t WHERE t.inventory_id = i.inventory_id AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU') LIMIT 1) as active_transfer_id
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON i.location_id = l.location_id
    WHERE i.inventory_id = ?
";
$inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
mysqli_stmt_bind_param($inventoryStmt, 'i', $inventory_id);
mysqli_stmt_execute($inventoryStmt);
$inventoryResult = mysqli_stmt_get_result($inventoryStmt);

if (mysqli_num_rows($inventoryResult) == 0) {
    $_SESSION['error'] = "Inventory item not found";
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

$inventory = mysqli_fetch_assoc($inventoryResult);

// Check if user has permission to view this inventory item
if (!hasRole('GodMode', 'Superadmin', 'Logistics')) {
    $userLocationId = getUserLocationId();
    if ($userLocationId != $inventory['location_id']) {
        $_SESSION['error'] = "You do not have permission to view this inventory item";
        header("Location: /choims/modules/inventory/list.php");
        exit();
    }
}

// Now include the header after all permission checks
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern inventory view CSS
echo '<link rel="stylesheet" href="/choims/modules/inventory/view-styles-modern.css">';

// Get transaction history with proper permission filtering
$transactionQuery = "
    SELECT
        t.*,
        u.full_name as performed_by_name,
        s.supplier_name,
        src.location_name as source_location,
        dest.location_name as destination_location
    FROM consumable_transactions t
    LEFT JOIN users u ON t.performed_by = u.user_id
    LEFT JOIN suppliers s ON t.supplier_id = s.supplier_id
    LEFT JOIN locations src ON t.source_location_id = src.location_id
    LEFT JOIN locations dest ON t.destination_location_id = dest.location_id
    WHERE t.inventory_id = ?";

// Add location-based filtering for non-privileged users
if (!hasRole('GodMode', 'Superadmin', 'Logistics')) {
    $userLocationId = getUserLocationId();
    $transactionQuery .= " AND (t.source_location_id = ? OR t.destination_location_id = ?)";
}

$transactionQuery .= " ORDER BY t.transaction_date DESC LIMIT 10";

$transactionStmt = mysqli_prepare($conn, $transactionQuery);

if (!hasRole('GodMode', 'Superadmin', 'Logistics')) {
    mysqli_stmt_bind_param($transactionStmt, 'iii', $inventory_id, $userLocationId, $userLocationId);
} else {
    mysqli_stmt_bind_param($transactionStmt, 'i', $inventory_id);
}

mysqli_stmt_execute($transactionStmt);
$transactionResult = mysqli_stmt_get_result($transactionStmt);
?>

<div class="container-fluid inventory-view-container">
    <div class="inventory-header animate-fadeIn">
        <div>
            <h1 class="inventory-title"><i class="fas fa-box"></i> <?php echo htmlspecialchars($inventory['sku_name']); ?></h1>
            <div class="inventory-subtitle">
                Consumable Details | SKU: <?php echo htmlspecialchars($inventory['sku_code']); ?>
                <?php if ($inventory['has_active_transfer'] > 0): ?>
                <a href="/choims/modules/transfers/view.php?id=<?php echo $inventory['active_transfer_id']; ?>" class="text-decoration-none ms-2">
                    <span class="badge bg-info bg-opacity-10 text-info rounded-pill px-3 py-2" data-bs-toggle="tooltip" title="Click to view transfer details">
                        <i class="fas fa-exchange-alt me-1"></i> Transfer Ongoing
                    </span>
                </a>
                <?php endif; ?>
            </div>
        </div>
        <div class="action-buttons">
            <?php if (hasRole('Logistics') &&
                    getUserLocationId() == $inventory['location_id'] &&
                    strtolower($_SESSION['role']) !== 'superadmin'): ?>
                <?php if ($inventory['has_active_transfer'] > 0): ?>
                <span class="action-button primary disabled" data-bs-toggle="tooltip" title="Cannot edit - Transfer in progress">
                    <i class="fas fa-edit"></i> Edit Item
                </span>
                <?php else: ?>
                <a href="/choims/modules/inventory/edit.php?id=<?php echo $inventory_id; ?>" class="action-button primary">
                    <i class="fas fa-edit"></i> Edit Item
                </a>
                <?php endif; ?>
                <?php if ($inventory['has_active_transfer'] > 0): ?>
                <span class="action-button success disabled" data-bs-toggle="tooltip" title="Cannot add stock - Transfer in progress">
                    <i class="fas fa-plus"></i> Add Stock
                </span>
                <?php else: ?>
                <button type="button" class="action-button success" data-bs-toggle="modal" data-bs-target="#addStockModal">
                    <i class="fas fa-plus"></i> Add Stock
                </button>
                <?php endif; ?>
            <?php endif; ?>
            <a href="/choims/modules/inventory/list.php" class="action-button secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Inventory Information -->
        <div class="col-lg-8">
            <div class="inventory-card animate-slideInUp" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <h6 class="m-0"><i class="fas fa-info-circle"></i> Consumable Information</h6>
                    <?php if (!hasRole('Logistics') || getUserLocationId() == $inventory['location_id']): ?>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
                        <i class="fas fa-sync-alt"></i> Change Status
                    </button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <div class="inventory-info-label">Status</div>
                            <?php
                            $statusClass = '';
                            $statusIcon = '';
                            switch ($inventory['status']) {
                                case 'Available':
                                    $statusClass = 'available';
                                    $statusIcon = 'fas fa-check-circle';
                                    break;
                                case 'Low Stock':
                                    $statusClass = 'low-stock';
                                    $statusIcon = 'fas fa-exclamation-circle';
                                    break;
                                case 'Out of Stock':
                                    $statusClass = 'out-of-stock';
                                    $statusIcon = 'fas fa-times-circle';
                                    break;
                                default:
                                    $statusClass = 'bg-secondary';
                                    $statusIcon = 'fas fa-question-circle';
                            }
                            ?>
                            <span class="status-badge <?php echo $statusClass; ?>">
                                <i class="<?php echo $statusIcon; ?>"></i> <?php echo $inventory['status']; ?>
                            </span>
                        </div>
                        <div>
                            <div class="inventory-info-label">Inventory ID</div>
                            <div class="inventory-info-value">#<?php echo $inventory['inventory_id']; ?></div>
                        </div>
                    </div>

                    <div class="inventory-info-section">
                        <div class="inventory-info-item">
                            <div class="inventory-info-label">Category</div>
                            <div class="inventory-info-value"><?php echo $inventory['category_name']; ?></div>
                        </div>
                        <div class="inventory-info-item">
                            <div class="inventory-info-label">Location</div>
                            <div class="inventory-info-value"><?php echo $inventory['location_name']; ?></div>
                        </div>
                        <div class="inventory-info-item">
                            <div class="inventory-info-label">Last Restock</div>
                            <div class="inventory-info-value"><?php echo !empty($inventory['last_restock_date']) ? formatDate($inventory['last_restock_date']) : 'N/A'; ?></div>
                        </div>
                        <div class="inventory-info-item">
                            <div class="inventory-info-label">HIMU Approval Required</div>
                            <div class="inventory-info-value"><?php echo $inventory['requires_himu_approval'] ? 'Yes' : 'No'; ?></div>
                        </div>
                    </div>

                    <div class="stock-level">
                        <div class="stock-level-item">
                            <div class="stock-level-label">Current Quantity</div>
                            <div class="stock-level-value current"><?php echo $inventory['current_quantity']; ?></div>
                        </div>
                        <div class="stock-level-item">
                            <div class="stock-level-label">Minimum Quantity</div>
                            <div class="stock-level-value min"><?php echo $inventory['min_quantity']; ?></div>
                        </div>
                        <div class="stock-level-item">
                            <div class="stock-level-label">Low Stock Threshold</div>
                            <div class="stock-level-value low"><?php echo $inventory['low_stock_threshold']; ?></div>
                        </div>
                        <div class="stock-level-item">
                            <div class="stock-level-label">Critical Threshold</div>
                            <div class="stock-level-value critical"><?php echo $inventory['critical_threshold']; ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stock Movement Graph -->
            <div class="inventory-card animate-slideInUp" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <h6 class="m-0"><i class="fas fa-chart-line"></i> Stock Movement</h6>
                    <div class="d-flex gap-2">
                        <button class="chart-option active stockMovementOption" data-months="6">6 Months</button>
                        <button class="chart-option stockMovementOption" data-months="3">3 Months</button>
                        <button class="chart-option stockMovementOption" data-months="12">12 Months</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="stockMovementChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction History -->
        <div class="col-lg-4">
            <div class="inventory-card animate-slideInUp" style="animation-delay: 0.3s;">
                <div class="card-header">
                    <h6 class="m-0"><i class="fas fa-history"></i> Transaction History</h6>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($transactionResult) > 0): ?>
                        <div class="timeline">
                            <?php while ($transaction = mysqli_fetch_assoc($transactionResult)):
                                $timelineClass = '';
                                switch ($transaction['transaction_type']) {
                                    case 'Stock In':
                                        $timelineClass = 'stock-in';
                                        break;
                                    case 'Stock Out':
                                        $timelineClass = 'stock-out';
                                        break;
                                    case 'Transfer':
                                        $timelineClass = 'transfer';
                                        break;
                                    case 'Adjustment':
                                        $timelineClass = 'adjustment';
                                        break;
                                    default:
                                        $timelineClass = '';
                                }
                            ?>
                                <div class="timeline-item <?php echo $timelineClass; ?>">
                                    <div class="timeline-date">
                                        <?php echo date('M d, Y', strtotime($transaction['transaction_date'])); ?>
                                    </div>
                                    <div class="timeline-content">
                                        <?php
                                        $transactionTypeClass = '';
                                        $transactionIcon = '';
                                        switch ($transaction['transaction_type']) {
                                            case 'Stock In':
                                                $transactionTypeClass = 'text-success';
                                                $transactionIcon = 'fas fa-arrow-circle-down';
                                                break;
                                            case 'Stock Out':
                                                $transactionTypeClass = 'text-danger';
                                                $transactionIcon = 'fas fa-arrow-circle-up';
                                                break;
                                            case 'Transfer':
                                                $transactionTypeClass = 'text-info';
                                                $transactionIcon = 'fas fa-exchange-alt';
                                                break;
                                            case 'Adjustment':
                                                $transactionTypeClass = 'text-warning';
                                                $transactionIcon = 'fas fa-sync-alt';
                                                break;
                                            default:
                                                $transactionTypeClass = 'text-secondary';
                                                $transactionIcon = 'fas fa-circle';
                                        }

                                        $quantityPrefix = '';
                                        if ($transaction['transaction_type'] == 'Stock In' ||
                                            ($transaction['transaction_type'] == 'Transfer' && $transaction['destination_location_id'] == $inventory['location_id'])) {
                                            $quantityPrefix = '+';
                                        } elseif ($transaction['transaction_type'] == 'Stock Out' ||
                                                ($transaction['transaction_type'] == 'Transfer' && $transaction['source_location_id'] == $inventory['location_id'])) {
                                            $quantityPrefix = '-';
                                        }

                                        $transactionDetails = '';
                                        switch ($transaction['transaction_type']) {
                                            case 'Stock In':
                                                $transactionDetails = 'Received from ' . (isset($transaction['supplier_name']) ? $transaction['supplier_name'] : 'Unknown');
                                                break;
                                            case 'Stock Out':
                                                $transactionDetails = 'Consumption';
                                                break;
                                            case 'Transfer':
                                                if ($transaction['source_location_id'] == $inventory['location_id']) {
                                                    $transactionDetails = 'Transfer to ' . $transaction['destination_location'];
                                                } else {
                                                    $transactionDetails = 'Transfer from ' . $transaction['source_location'];
                                                }
                                                break;
                                            case 'Adjustment':
                                                $transactionDetails = 'Inventory adjustment';
                                                break;
                                        }
                                        ?>
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="<?php echo $transactionIcon; ?> <?php echo $transactionTypeClass; ?> me-2"></i>
                                            <strong class="<?php echo $transactionTypeClass; ?>">
                                                <?php echo $transaction['transaction_type']; ?>
                                                (<?php echo $quantityPrefix . $transaction['quantity']; ?>)
                                            </strong>
                                        </div>
                                        <div><?php echo $transactionDetails; ?></div>
                                        <div class="text-sm mt-2">
                                            <?php if (!empty($transaction['reference_document'])): ?>
                                                <span class="badge bg-light text-dark me-2">Ref: <?php echo $transaction['reference_document']; ?></span>
                                            <?php endif; ?>
                                            <span class="badge bg-light text-dark">By: <?php echo $transaction['performed_by_name']; ?></span>
                                        </div>
                                        <?php if (!empty($transaction['remarks'])): ?>
                                            <div class="mt-2 text-muted bg-light p-2 rounded">
                                                <small><?php echo nl2br($transaction['remarks']); ?></small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info d-flex align-items-center">
                            <i class="fas fa-info-circle me-2"></i>
                            No transaction history available for this item.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Stock Modal -->
<?php if (hasRole('Logistics') &&
        getUserLocationId() == $inventory['location_id']): ?>
<div class="modal fade" id="addStockModal" tabindex="-1" aria-labelledby="addStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStockModalLabel"><i class="fas fa-plus-circle me-2"></i>Add Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/choims/modules/inventory/add_stock.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="inventory_id" value="<?php echo $inventory_id; ?>">

                    <div class="mb-3">
                        <label for="transaction_type" class="form-label">Transaction Type *</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-exchange-alt text-primary"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="transaction_type" name="transaction_type" required>
                                <option value="Stock In">Stock In</option>
                                <option value="Stock Out">Stock Out</option>
                                <option value="Adjustment">Adjustment</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity *</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-hashtag text-primary"></i></span>
                            <input type="number" class="form-control rounded-end-3 border-start-0" id="quantity" name="quantity" min="1" required>
                        </div>
                    </div>

                    <div id="supplierDiv" class="mb-3">
                        <label for="supplier_id" class="form-label">Supplier</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-truck text-primary"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="supplier_id" name="supplier_id">
                                <option value="">Select Supplier</option>
                                <?php
                                $supplierQuery = "SELECT supplier_id, supplier_name FROM suppliers ORDER BY supplier_name";
                                $supplierResult = mysqli_query($conn, $supplierQuery);
                                while ($supplier = mysqli_fetch_assoc($supplierResult)):
                                ?>
                                    <option value="<?php echo $supplier['supplier_id']; ?>">
                                        <?php echo $supplier['supplier_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="reference_document" class="form-label">Reference Document</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-file-alt text-primary"></i></span>
                            <input type="text" class="form-control rounded-end-3 border-start-0" id="reference_document" name="reference_document"
                                   placeholder="e.g., Invoice #, Delivery Receipt #">
                        </div>
                    </div>

                    <div id="unitCostDiv" class="mb-3">
                        <label for="unit_cost" class="form-label">Unit Cost</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-tag text-primary"></i></span>
                            <input type="number" step="0.01" class="form-control rounded-end-3 border-start-0" id="unit_cost" name="unit_cost">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Additional notes about this transaction"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Save Transaction</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Status Change Modal -->
<div class="modal fade" id="changeStatusModal" tabindex="-1" aria-labelledby="changeStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changeStatusModalLabel"><i class="fas fa-sync-alt me-2"></i>Change Consumable Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/choims/modules/inventory/update_status.php?id=<?php echo $inventory_id; ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">New Status:</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="">Select Status</option>
                            <option value="Available" <?php echo ($inventory['status'] == 'Available') ? 'selected' : ''; ?>>Available</option>
                            <option value="Low Stock" <?php echo ($inventory['status'] == 'Low Stock') ? 'selected' : ''; ?>>Low Stock</option>
                            <option value="Out of Stock" <?php echo ($inventory['status'] == 'Out of Stock') ? 'selected' : ''; ?>>Out of Stock</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason for Status Change:</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required
                            placeholder="Please provide a reason for changing the status"></textarea>
                    </div>
                    <input type="hidden" name="redirect_url" value="<?php echo $_SERVER['REQUEST_URI']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Timeline CSS is now in the external CSS file -->

<!-- Chart JS for Stock Movement -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 300, hide: 100 },
            container: 'body'
        });
    });
    // Fetch stock movement data
    fetchStockMovementData(6); // Default to 6 months

    // Handle transaction type change to show/hide relevant fields
    document.getElementById('transaction_type').addEventListener('change', function() {
        const transactionType = this.value;
        const supplierDiv = document.getElementById('supplierDiv');
        const unitCostDiv = document.getElementById('unitCostDiv');

        if (transactionType === 'Stock In') {
            supplierDiv.style.display = 'block';
            unitCostDiv.style.display = 'block';
        } else if (transactionType === 'Stock Out') {
            supplierDiv.style.display = 'none';
            unitCostDiv.style.display = 'none';
        } else { // Adjustment
            supplierDiv.style.display = 'none';
            unitCostDiv.style.display = 'block';
        }
    });

    // Handle stock movement option clicks
    document.querySelectorAll('.stockMovementOption').forEach(function(option) {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            // Remove active class from all options
            document.querySelectorAll('.stockMovementOption').forEach(opt => {
                opt.classList.remove('active');
            });
            // Add active class to clicked option
            this.classList.add('active');
            const months = parseInt(this.getAttribute('data-months'));
            fetchStockMovementData(months);
        });
    });
});

function fetchStockMovementData(months) {
    // Get inventory ID from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const inventoryId = urlParams.get('id');

    // Create a loading indicator
    const chartCanvas = document.getElementById('stockMovementChart');
    const loadingElement = document.createElement('div');
    loadingElement.className = 'text-center py-5';
    loadingElement.innerHTML = '<i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading stock movement data...</p>';

    chartCanvas.parentNode.insertBefore(loadingElement, chartCanvas);
    chartCanvas.style.display = 'none';

    // Fetch data from server
    fetch(`/choims/modules/inventory/get_stock_data.php?id=${inventoryId}&months=${months}`)
        .then(response => response.json())
        .then(data => {
            // Remove loading indicator
            loadingElement.remove();
            chartCanvas.style.display = 'block';

            // Render chart with real data
            renderStockMovementChart(data.labels, data.stockData, data.stockInData, data.stockOutData);
        })
        .catch(error => {
            console.error('Error fetching stock movement data:', error);

            // Remove loading indicator and show error
            loadingElement.innerHTML = '<i class="fas fa-exclamation-circle fa-2x text-danger"></i><p class="mt-2">Error loading data. Please try again.</p>';

            // Fallback to empty chart
            renderStockMovementChart([], [], [], []);
        });
}

function renderStockMovementChart(labels, stockData, stockInData, stockOutData) {
    const ctx = document.getElementById('stockMovementChart').getContext('2d');

    // If chart already exists, destroy it
    if (window.stockChart) {
        window.stockChart.destroy();
    }

    window.stockChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Stock Level',
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    data: stockData,
                    fill: true,
                    tension: 0.3
                },
                {
                    label: 'Stock In',
                    borderColor: '#1cc88a',
                    backgroundColor: 'rgba(28, 200, 138, 0)',
                    borderDash: [5, 5],
                    data: stockInData,
                    tension: 0.3
                },
                {
                    label: 'Stock Out',
                    borderColor: '#e74a3b',
                    backgroundColor: 'rgba(231, 74, 59, 0)',
                    borderDash: [5, 5],
                    data: stockOutData,
                    tension: 0.3
                }
            ]
        },
        options: {
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: 10,
                    right: 25,
                    top: 25,
                    bottom: 0
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: "rgb(234, 236, 244)",
                        zeroLineColor: "rgb(234, 236, 244)",
                        drawBorder: false,
                        borderDash: [2],
                        zeroLineBorderDash: [2]
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyColor: "#858796",
                    titleMarginBottom: 10,
                    titleColor: '#6e707e',
                    titleFontSize: 14,
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    padding: 15,
                    displayColors: false,
                    intersect: false,
                    mode: 'index'
                }
            }
        }
    });
}
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>