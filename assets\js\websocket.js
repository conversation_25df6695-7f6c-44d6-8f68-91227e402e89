/**
 * WebSocket client for CHOIMS
 * Provides real-time updates without page refreshes
 */

// WebSocket connection
let socket;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectInterval = 3000; // 3 seconds

// Initialize WebSocket connection
function initWebSocket() {
    // Check if WebSocket is supported by the browser
    if (!window.WebSocket) {
        console.error('WebSocket is not supported by this browser');
        // Fall back to polling
        initPolling();
        return;
    }

    // Create WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const port = 8080; // WebSocket server port
    const wsUrl = `${protocol}//${host}:${port}/choims`;

    try {
        socket = new WebSocket(wsUrl);

        // Connection opened
        socket.addEventListener('open', (event) => {
            console.log('WebSocket connection established');
            reconnectAttempts = 0;

            // Send authentication information
            if (typeof userId !== 'undefined' && typeof userRole !== 'undefined') {
                sendMessage({
                    type: 'auth',
                    userId: userId,
                    userRole: userRole
                });
            }
        });

        // Listen for messages
        socket.addEventListener('message', (event) => {
            handleWebSocketMessage(event.data);
        });

        // Connection closed
        socket.addEventListener('close', (event) => {
            console.log('WebSocket connection closed');

            // Attempt to reconnect
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                setTimeout(initWebSocket, reconnectInterval);
            } else {
                console.error('Maximum reconnection attempts reached. Falling back to polling.');
                initPolling();
            }
        });

        // Connection error
        socket.addEventListener('error', (event) => {
            console.error('WebSocket error:', event);
        });
    } catch (error) {
        console.error('Error creating WebSocket connection:', error);
        initPolling();
    }
}

// Send message through WebSocket
function sendMessage(data) {
    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify(data));
    } else {
        console.error('WebSocket is not connected');
    }
}

// Handle incoming WebSocket messages
function handleWebSocketMessage(data) {
    try {
        const message = JSON.parse(data);

        // Handle different message types
        switch (message.type) {
            case 'notification':
                handleNotification(message.data);
                break;
            case 'inventory_update':
                handleInventoryUpdate(message.data);
                break;
            case 'transfer_update':
                handleTransferUpdate(message.data);
                break;
            case 'ping':
                // Respond to keep-alive ping
                sendMessage({ type: 'pong' });
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    } catch (error) {
        console.error('Error parsing WebSocket message:', error);
    }
}

// Handle notification updates
function handleNotification(data) {
    // Update notification count
    const notificationCounter = document.querySelector('#notificationCount');
    if (notificationCounter) {
        const currentCount = parseInt(notificationCounter.textContent) || 0;
        notificationCounter.textContent = currentCount + 1;
        notificationCounter.style.display = 'inline-block';
    }

    // Add notification to dropdown if it exists
    const notificationDropdown = document.querySelector('#notificationDropdown');
    if (notificationDropdown) {
        const notificationItem = document.createElement('a');
        notificationItem.className = 'dropdown-item notification-item unread';
        notificationItem.href = data.link || '#';
        notificationItem.dataset.notificationId = data.id;

        notificationItem.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="notification-icon bg-${data.type || 'primary'}">
                    <i class="fas fa-${data.icon || 'bell'}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${data.title}</div>
                    <div class="notification-message">${data.message}</div>
                    <div class="notification-time">${data.time || 'Just now'}</div>
                </div>
            </div>
        `;

        notificationDropdown.prepend(notificationItem);
    }

    // Show toast notification
    showToast(data.title, data.message, data.link, data.type);
}

// Handle inventory updates
function handleInventoryUpdate(data) {
    // Update inventory table if on inventory page
    const inventoryTable = document.querySelector('#inventoryTable');
    if (inventoryTable) {
        // Find the row with the matching item ID
        const row = document.querySelector(`tr[data-item-id="${data.item_id}"]`);
        if (row) {
            // Update quantity
            const quantityCell = row.querySelector('.item-quantity');
            if (quantityCell) {
                quantityCell.textContent = data.quantity;

                // Highlight the updated cell
                quantityCell.classList.add('bg-highlight');
                setTimeout(() => {
                    quantityCell.classList.remove('bg-highlight');
                }, 3000);
            }
        } else {
            // If item not in table, might need to refresh
            // Or dynamically add new row
        }
    }
}

// Handle transfer updates
function handleTransferUpdate(data) {
    // Update transfer table if on transfers page
    const transferTable = document.querySelector('#transferTable');
    if (transferTable) {
        // Find the row with the matching transfer ID
        const row = document.querySelector(`tr[data-transfer-id="${data.transfer_id}"]`);
        if (row) {
            // Update status
            const statusCell = row.querySelector('.transfer-status');
            if (statusCell) {
                statusCell.textContent = data.status;
                statusCell.className = `transfer-status badge bg-${getStatusColor(data.status)}`;
            }
        } else {
            // If transfer not in table, might need to add new row
            // Or show notification about new transfer
        }
    }

    // If on logistics dashboard, update awaiting transfers widget
    const awaitingWidget = document.querySelector('.awaiting-confirmation-widget');
    if (awaitingWidget) {
        // Trigger refresh of widget data
        refreshAwaitingTransfers();
    }
}

// Helper function to get status color
function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'pending':
            return 'warning';
        case 'approved':
            return 'success';
        case 'rejected':
            return 'danger';
        case 'in transit':
            return 'info';
        case 'completed':
            return 'primary';
        default:
            return 'secondary';
    }
}

// Refresh awaiting transfers widget
function refreshAwaitingTransfers() {
    fetch('/choims/modules/transfers/awaiting_widget.php')
        .then(response => response.text())
        .then(html => {
            const awaitingWidget = document.querySelector('.awaiting-confirmation-widget');
            if (awaitingWidget) {
                awaitingWidget.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Error refreshing awaiting transfers widget:', error);
        });
}

// Fall back to polling if WebSockets are not available
function initPolling() {
    console.log('Falling back to polling for updates');

    // Check for notifications every 30 seconds
    setInterval(checkForNewNotifications, 30000);

    // Check for inventory updates every 60 seconds if on inventory page
    if (document.querySelector('#inventoryTable')) {
        setInterval(checkForInventoryUpdates, 60000);
    }

    // Check for transfer updates every 45 seconds if on transfers page
    if (document.querySelector('#transferTable')) {
        setInterval(checkForTransferUpdates, 45000);
    }
}

// Polling functions
function checkForNewNotifications() {
    fetch('/choims/modules/notifications/check.php')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                // Update notification counter
                const notificationCounter = document.querySelector('#notificationCount');
                if (notificationCounter) {
                    notificationCounter.textContent = data.count;
                    notificationCounter.style.display = 'inline-block';
                }
            }
        })
        .catch(error => {
            console.error('Error checking for notifications:', error);
        });
}

function checkForInventoryUpdates() {
    // Implement polling for inventory updates
}

function checkForTransferUpdates() {
    // Implement polling for transfer updates
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initWebSocket);
