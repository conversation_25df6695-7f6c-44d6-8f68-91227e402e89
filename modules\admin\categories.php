<?php
// Start a session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/detailed_audit_log.php';

// Check if user is logged in and has superadmin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) ||
    (strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode')) {
    $_SESSION['error'] = "You don't have permission to access this page.";
    header("Location: ../../index.php");
    exit();
}

// Log activity
logActivity($conn, 'Accessed Categories Management Page', 'admin', null);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_category'])) {
        $category_name = trim($_POST['category_name']);
        $category_type = $_POST['category_type'];
        $requires_himu_approval = isset($_POST['requires_himu_approval']) ? 1 : 0;
        $description = trim($_POST['description']);

        // Validation
        if (empty($category_name)) {
            $_SESSION['error'] = "Category name is required.";
        } else {
            // Check if category name already exists
            $check_sql = "SELECT category_id FROM categories WHERE category_name = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("s", $category_name);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Category with this name already exists.";
            } else {
                // Insert new category
                $sql = "INSERT INTO categories (category_name, category_type, requires_himu_approval, description) VALUES (?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssis", $category_name, $category_type, $requires_himu_approval, $description);

                if ($stmt->execute()) {
                    $category_id = $stmt->insert_id;
                    $_SESSION['success'] = "Category added successfully.";

                    // Log to regular audit log
                    logActivity($conn, 'Added new category: ' . $category_name, 'category', $category_id);

                    // Log to detailed audit system
                    $new_values = [
                        'category_name' => $category_name,
                        'category_type' => $category_type,
                        'requires_himu_approval' => $requires_himu_approval,
                        'description' => $description
                    ];
                    logCategoryAction($conn, $_SESSION['user_id'], 'create', $category_id, null, $new_values);
                } else {
                    $_SESSION['error'] = "Error adding category: " . $conn->error;
                }

                $stmt->close();
            }
            $check_stmt->close();
        }
    } elseif (isset($_POST['edit_category'])) {
        $category_id = $_POST['category_id'];
        $category_name = trim($_POST['category_name']);
        $category_type = $_POST['category_type'];
        $requires_himu_approval = isset($_POST['requires_himu_approval']) ? 1 : 0;
        $description = trim($_POST['description']);

        // Validation
        if (empty($category_name)) {
            $_SESSION['error'] = "Category name is required.";
        } else {
            // Check if category name already exists (excluding current category)
            $check_sql = "SELECT category_id FROM categories WHERE category_name = ? AND category_id != ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("si", $category_name, $category_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Category with this name already exists.";
            } else {
                // Update category
                $sql = "UPDATE categories SET category_name = ?, category_type = ?, requires_himu_approval = ?, description = ? WHERE category_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssisi", $category_name, $category_type, $requires_himu_approval, $description, $category_id);

                // Get old category data for logging
                $old_data_sql = "SELECT * FROM categories WHERE category_id = ?";
                $old_data_stmt = $conn->prepare($old_data_sql);
                $old_data_stmt->bind_param("i", $category_id);
                $old_data_stmt->execute();
                $old_data_result = $old_data_stmt->get_result();
                $old_data = $old_data_result->fetch_assoc();
                $old_data_stmt->close();

                if ($stmt->execute()) {
                    $_SESSION['success'] = "Category updated successfully.";

                    // Log to regular audit log
                    logActivity($conn, 'Updated category: ' . $category_name, 'category', $category_id);

                    // Log to detailed audit system
                    $old_values = [
                        'category_name' => $old_data['category_name'],
                        'category_type' => $old_data['category_type'],
                        'requires_himu_approval' => $old_data['requires_himu_approval'],
                        'description' => $old_data['description']
                    ];

                    $new_values = [
                        'category_name' => $category_name,
                        'category_type' => $category_type,
                        'requires_himu_approval' => $requires_himu_approval,
                        'description' => $description
                    ];

                    logCategoryAction($conn, $_SESSION['user_id'], 'update', $category_id, $old_values, $new_values);
                } else {
                    $_SESSION['error'] = "Error updating category: " . $conn->error;
                }

                $stmt->close();
            }
            $check_stmt->close();
        }
    }

    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Get all categories
$sql = "SELECT * FROM categories ORDER BY category_name ASC";
$result = $conn->query($sql);
$categories = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// Include header
include_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Categories Management</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus-circle me-1"></i> Add Category
                    </button>

                    <div class="table-responsive">
                        <table id="categoriesTable" class="table table-bordered table-striped">
                            <thead class="bg-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Category Name</th>
                                    <th>Type</th>
                                    <th>HIMU Approval</th>
                                    <th>Description</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td><?php echo $category['category_id']; ?></td>
                                    <td><?php echo htmlspecialchars($category['category_name']); ?></td>
                                    <td><?php echo htmlspecialchars(isset($category['category_type']) ? $category['category_type'] : 'N/A'); ?></td>
                                    <td>
                                        <?php echo isset($category['requires_himu_approval']) && $category['requires_himu_approval'] ? '<span class="badge bg-success">Required</span>' : '<span class="badge bg-secondary">Not Required</span>'; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars(isset($category['description']) ? $category['description'] : ''); ?></td>
                                    <td><?php echo isset($category['created_at']) ? date('M d, Y h:i A', strtotime($category['created_at'])) : 'N/A'; ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning edit-category"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editCategoryModal"
                                                data-id="<?php echo $category['category_id']; ?>"
                                                data-name="<?php echo htmlspecialchars($category['category_name']); ?>"
                                                data-type="<?php echo htmlspecialchars(isset($category['category_type']) ? $category['category_type'] : 'N/A'); ?>"
                                                data-himu="<?php echo isset($category['requires_himu_approval']) ? $category['requires_himu_approval'] : '0'; ?>"
                                                data-description="<?php echo htmlspecialchars(isset($category['description']) ? $category['description'] : ''); ?>">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addCategoryModalLabel"><i class="fas fa-plus-circle me-2"></i>Add New Category</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="category_name" name="category_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="category_type" class="form-label">Category Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="category_type" name="category_type" required>
                            <option value="Fixed Asset">Fixed Asset</option>
                            <option value="Consumable">Consumable</option>
                        </select>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="requires_himu_approval" name="requires_himu_approval">
                        <label class="form-check-label" for="requires_himu_approval">Requires HIMU Approval</label>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="add_category" class="btn btn-primary">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="editCategoryModalLabel"><i class="fas fa-edit me-2"></i>Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    <div class="mb-3">
                        <label for="edit_category_name" class="form-label">Category Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_category_name" name="category_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_category_type" class="form-label">Category Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="edit_category_type" name="category_type" required>
                            <option value="Fixed Asset">Fixed Asset</option>
                            <option value="Consumable">Consumable</option>
                        </select>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_requires_himu_approval" name="requires_himu_approval">
                        <label class="form-check-label" for="edit_requires_himu_approval">Requires HIMU Approval</label>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="edit_category" class="btn btn-warning">Update Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#categoriesTable').DataTable({
        order: [[0, 'asc']],
        pageLength: 10,
        responsive: true
    });

    // Handle edit category modal data
    const editButtons = document.querySelectorAll('.edit-category');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const type = this.getAttribute('data-type');
            const himu = this.getAttribute('data-himu');
            const description = this.getAttribute('data-description');

            document.getElementById('edit_category_id').value = id;
            document.getElementById('edit_category_name').value = name;
            document.getElementById('edit_category_type').value = type;
            document.getElementById('edit_requires_himu_approval').checked = himu === '1';
            document.getElementById('edit_description').value = description;
        });
    });
});
</script>

<?php
// Include footer
include_once '../../includes/footer.php';
?>