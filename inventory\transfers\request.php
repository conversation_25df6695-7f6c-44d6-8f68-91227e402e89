<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_DEPARTMENT, ROLE_HEALTH_CENTER])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db->beginTransaction();

        // Generate reference number
        $stmt = $db->query("SELECT MAX(CAST(SUBSTRING(reference_number, -6) AS UNSIGNED)) as max_num FROM transfers WHERE reference_number LIKE 'TRF-%'");
        $maxNum = $stmt->fetch()['max_num'];
        $nextNum = str_pad(($maxNum + 1), 6, '0', STR_PAD_LEFT);
        $referenceNumber = 'TRF-' . $nextNum;

        // Get item details to determine approval workflow
        $stmt = $db->prepare("
            SELECT i.*, cat.name as category_name 
            FROM items i 
            JOIN categories cat ON i.category_id = cat.category_id 
            WHERE i.item_id = ?
        ");
        $stmt->execute([$_POST['item_id']]);
        $item = $stmt->fetch();

        // Determine if HIMU approval is required
        $requiresHimuApproval = ($item['category_name'] === 'IT Equipment' || $item['category_name'] === 'IT Supply');

        // Insert transfer request
        $stmt = $db->prepare("
            INSERT INTO transfers (
                reference_number, item_id, quantity, 
                source_department_id, source_health_center_id,
                destination_department_id, destination_health_center_id,
                status, remarks, requested_by, request_date,
                requires_himu_approval
            ) VALUES (
                ?, ?, ?,
                NULLIF(?, ''), NULLIF(?, ''),
                NULLIF(?, ''), NULLIF(?, ''),
                'pending', ?, ?, NOW(),
                ?
            )
        ");
        $stmt->execute([
            $referenceNumber,
            $_POST['item_id'],
            $_POST['quantity'],
            $_POST['source_department_id'],
            $_POST['source_health_center_id'],
            $_POST['destination_department_id'],
            $_POST['destination_health_center_id'],
            $_POST['remarks'],
            $auth->getCurrentUser()['user_id'],
            $requiresHimuApproval
        ]);
        $transferId = $db->lastInsertId();

        // Log the activity
        $auth->logActivity($auth->getCurrentUser()['user_id'], 'create', 'transfers', $transferId);

        $db->commit();
        setFlashMessage('success', 'Transfer request created successfully');
        header("Location: view.php?id=" . $transferId);
        exit;
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Error creating transfer request: " . $e->getMessage());
        setFlashMessage('error', 'Error creating transfer request. Please try again.');
    }
}

try {
    // Fetch active items (both fixed assets and consumables)
    $query = "
        SELECT i.item_id, i.name, i.sku, cat.name as category_name,
               COALESCE(fa.quantity, c.quantity) as available_quantity,
               COALESCE(fa.status, c.status) as item_status,
               IF(fa.fixed_asset_id IS NOT NULL, 'fixed_asset', 'consumable') as item_type,
               COALESCE(fa.department_id, NULL) as current_department_id,
               COALESCE(fa.health_center_id, NULL) as current_health_center_id
        FROM items i
        JOIN categories cat ON i.category_id = cat.category_id
        LEFT JOIN fixed_assets fa ON i.item_id = fa.item_id
        LEFT JOIN consumables c ON i.item_id = c.item_id
        WHERE (fa.fixed_asset_id IS NOT NULL OR c.consumable_id IS NOT NULL)
        AND (fa.status = 'available' OR c.status IN ('in_stock', 'low_stock'))
        ORDER BY i.name
    ";
    $stmt = $db->query($query);
    $items = $stmt->fetchAll();

    // Fetch departments
    $stmt = $db->query("SELECT department_id, name FROM departments WHERE status = 'active' ORDER BY name");
    $departments = $stmt->fetchAll();

    // Fetch health centers
    $stmt = $db->query("SELECT health_center_id, name FROM health_centers WHERE status = 'active' ORDER BY name");
    $healthCenters = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching form data: " . $e->getMessage());
    setFlashMessage('error', 'Error loading form data');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">New Transfer Request</h1>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <!-- Item Selection -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="item_id" class="form-label required">Select Item</label>
                            <select class="form-select" id="item_id" name="item_id" required>
                                <option value="">Select Item</option>
                                <?php foreach ($items as $item): ?>
                                    <option value="<?php echo $item['item_id']; ?>" 
                                            data-type="<?php echo $item['item_type']; ?>"
                                            data-quantity="<?php echo $item['available_quantity']; ?>"
                                            data-department="<?php echo $item['current_department_id']; ?>"
                                            data-health-center="<?php echo $item['current_health_center_id']; ?>"
                                            data-category="<?php echo $item['category_name']; ?>">
                                        <?php echo htmlspecialchars($item['name']); ?> 
                                        (<?php echo htmlspecialchars($item['sku']); ?>) - 
                                        <?php echo $item['item_type'] === 'fixed_asset' ? 'Fixed Asset' : 'Consumable'; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="quantity" class="form-label required">Quantity</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   min="1" required>
                            <div class="form-text">Available: <span id="available_quantity">0</span></div>
                        </div>
                    </div>
                </div>

                <!-- Source Location -->
                <h5 class="mb-3">Source Location</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="source_department_id" class="form-label">Department</label>
                            <select class="form-select" id="source_department_id" name="source_department_id">
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['department_id']; ?>">
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="source_health_center_id" class="form-label">Health Center</label>
                            <select class="form-select" id="source_health_center_id" name="source_health_center_id">
                                <option value="">Select Health Center</option>
                                <?php foreach ($healthCenters as $center): ?>
                                    <option value="<?php echo $center['health_center_id']; ?>">
                                        <?php echo htmlspecialchars($center['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Destination Location -->
                <h5 class="mb-3">Destination Location</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="destination_department_id" class="form-label">Department</label>
                            <select class="form-select" id="destination_department_id" name="destination_department_id">
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['department_id']; ?>">
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="destination_health_center_id" class="form-label">Health Center</label>
                            <select class="form-select" id="destination_health_center_id" name="destination_health_center_id">
                                <option value="">Select Health Center</option>
                                <?php foreach ($healthCenters as $center): ?>
                                    <option value="<?php echo $center['health_center_id']; ?>">
                                        <?php echo htmlspecialchars($center['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Additional Details -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info" id="approval_info" style="display: none;">
                    This transfer will require approval from:
                    <ul id="approval_list"></ul>
                </div>

                <div class="text-end">
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Submit Transfer Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Handle item selection
document.getElementById('item_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const itemType = selectedOption.dataset.type;
    const quantity = selectedOption.dataset.quantity;
    const category = selectedOption.dataset.category;
    const currentDepartment = selectedOption.dataset.department;
    const currentHealthCenter = selectedOption.dataset.healthCenter;

    // Update available quantity
    document.getElementById('available_quantity').textContent = quantity;
    
    // Set max quantity for consumables
    const quantityInput = document.getElementById('quantity');
    if (itemType === 'consumable') {
        quantityInput.max = quantity;
    } else {
        quantityInput.max = 1;
        quantityInput.value = 1;
    }

    // Pre-select current location
    if (currentDepartment) {
        document.getElementById('source_department_id').value = currentDepartment;
        document.getElementById('source_health_center_id').value = '';
        document.getElementById('source_health_center_id').disabled = true;
    } else if (currentHealthCenter) {
        document.getElementById('source_health_center_id').value = currentHealthCenter;
        document.getElementById('source_department_id').value = '';
        document.getElementById('source_department_id').disabled = true;
    }

    // Show approval requirements
    const approvalInfo = document.getElementById('approval_info');
    const approvalList = document.getElementById('approval_list');
    approvalList.innerHTML = '';

    if (category === 'IT Equipment' || category === 'IT Supply') {
        approvalInfo.style.display = 'block';
        approvalList.innerHTML += '<li>HIMU (for IT equipment/supply)</li>';
        if (currentHealthCenter) {
            approvalList.innerHTML += '<li>Logistics</li>';
        }
    } else if (currentHealthCenter) {
        approvalInfo.style.display = 'block';
        approvalList.innerHTML += '<li>Logistics</li>';
    } else {
        approvalInfo.style.display = 'none';
    }
});

// Mutual exclusivity for source location
document.getElementById('source_department_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('source_health_center_id').value = '';
        document.getElementById('source_health_center_id').disabled = true;
    } else {
        document.getElementById('source_health_center_id').disabled = false;
    }
});

document.getElementById('source_health_center_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('source_department_id').value = '';
        document.getElementById('source_department_id').disabled = true;
    } else {
        document.getElementById('source_department_id').disabled = false;
    }
});

// Mutual exclusivity for destination location
document.getElementById('destination_department_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('destination_health_center_id').value = '';
        document.getElementById('destination_health_center_id').disabled = true;
    } else {
        document.getElementById('destination_health_center_id').disabled = false;
    }
});

document.getElementById('destination_health_center_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('destination_department_id').value = '';
        document.getElementById('destination_department_id').disabled = true;
    } else {
        document.getElementById('destination_department_id').disabled = false;
    }
});
</script>

<?php require_once '../../templates/footer.php'; ?>
