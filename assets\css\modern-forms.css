/* Modern React-like UI Styles for CHOIMS */

/* Form Containers */
.react-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.25rem;
  border: none;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.react-card-header {
  padding: 0.75rem 1.25rem;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.react-card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2E7D32;
}

.react-card-body {
  padding: 1rem 1.25rem;
}

.react-card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Form Controls */
.form-floating {
  position: relative;
  margin-bottom: 0.875rem;
}

.form-floating input.form-control,
.form-floating select.form-control,
.form-floating textarea.form-control {
  height: calc(3rem + 2px);
  line-height: 1.25;
  padding: 1.25rem 0.75rem 0.375rem;
  font-size: 0.95rem;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #fff;
  transition: all 0.2s;
}

.form-floating textarea.form-control {
  min-height: calc(3rem + 2px);
  height: auto;
}

.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: calc(3rem + 2px);
  padding: 1.25rem 0.75rem 0.375rem;
  pointer-events: none;
  transform-origin: 0 0;
  transition: opacity .1s ease-in-out, transform .1s ease-in-out;
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  font-size: 0.85rem;
}

.form-floating input::placeholder,
.form-floating textarea::placeholder {
  color: transparent;
}

.form-floating > select.form-control {
  padding-top: 1.5rem;
  padding-bottom: 0.375rem;
}

/* Focus and filled states */
.form-floating input.form-control:focus,
.form-floating select.form-control:focus,
.form-floating textarea.form-control:focus {
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.15);
  border-color: #2E7D32;
}

.form-floating input.form-control:focus ~ label,
.form-floating select.form-control:focus ~ label,
.form-floating textarea.form-control:focus ~ label {
  color: #2E7D32;
}

/* Form Grid */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.form-col {
  flex: 1 0 0%;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

/* Support more columns in a row */
.form-row-6 {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.form-col-6 {
  flex: 0 0 16.666667%;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

/* Compact layout for dense forms */
.form-row-compact {
  margin-bottom: 0;
}

.form-row-compact .form-floating {
  margin-bottom: 0.5rem;
}

/* Buttons */
.react-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
  border-radius: 8px;
  transition: all 0.15s ease-in-out;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  letter-spacing: 0.01em;
}

.react-btn-primary {
  background-color: #2E7D32;
  border: 1px solid #2E7D32;
  color: white;
}

.react-btn-primary:hover {
  background-color: #1B5E20;
  border-color: #1B5E20;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.react-btn-secondary {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #495057;
}

.react-btn-secondary:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.react-btn-icon {
  margin-right: 0.5rem;
}

/* Validation styles */
.is-invalid {
  border-color: #dc3545 !important;
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.75rem;
  margin-top: 0.125rem;
}

/* Alert components */
.react-alert {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.react-alert-success {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.react-alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.react-alert-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

/* Compact style for side-by-side view */
.compact-view {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.compact-view .react-card {
  flex: 1 0 45%;
  margin-bottom: 1rem;
}

.compact-view-3 {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.compact-view-3 .react-card {
  flex: 1 0 30%;
  margin-bottom: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .compact-view .react-card,
  .compact-view-3 .react-card {
    flex: 1 0 100%;
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .form-col {
    flex: 0 0 100%;
    margin-bottom: 0.5rem;
  }
  
  .react-card-footer {
    flex-direction: column;
  }
  
  .react-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
} 