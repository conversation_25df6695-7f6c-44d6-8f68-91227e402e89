<?php
// Set Philippines timezone
date_default_timezone_set('Asia/Manila');

// Database connection settings - PRODUCTION
// Replace these values with your actual production database credentials
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'choims_user'); // Use a dedicated user with limited privileges
define('DB_PASSWORD', 'StrongPassword123!'); // Use a strong password
define('DB_NAME', 'choims');

// Also define the constants used in includes/database.php for compatibility
define('DB_HOST', 'localhost');
define('DB_USER', 'choims_user');
define('DB_PASS', 'StrongPassword123!');

// Function to get database connection
function getDBConnection() {
    static $conn = null;
    
    if ($conn === null) {
        try {
            // Establish database connection
            $conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
            
            // Check if connection was successful
            if (!$conn) {
                // Log error instead of displaying it
                error_log("Database connection failed: " . mysqli_connect_error());
                die("Database connection failed. Please check the error log for details.");
            }
            
            // Set character set
            mysqli_set_charset($conn, "utf8mb4");
            
            // Set SQL mode to prevent zero dates
            mysqli_query($conn, "SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'");
        } catch (Exception $e) {
            error_log("Database connection exception: " . $e->getMessage());
            die("Database connection failed. Please check the error log for details.");
        }
    }
    
    return $conn;
}

// Establish database connection for backward compatibility
$conn = getDBConnection();
?>
