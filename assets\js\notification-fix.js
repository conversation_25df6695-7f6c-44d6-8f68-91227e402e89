/**
 * Fix for notification links in header dropdown
 * This script ensures that clicking on notification items in the header properly redirects to the target page
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add debug logging
    console.log('Notification fix script loaded');

    // Function to handle notification clicks
    function handleNotificationClick(e) {
        // Prevent default action
        e.preventDefault();

        // Get the notification ID and target URL
        const notificationId = this.getAttribute('data-notification-id');
        const targetUrl = this.getAttribute('href');

        // Debug logging
        console.log('Notification clicked:', {
            id: notificationId,
            url: targetUrl,
            text: this.textContent.trim(),
            isBatchTransfer: targetUrl.includes('/batch/view.php')
        });

        // Mark the notification as read via AJAX
        fetch('/choims/modules/notifications/mark_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'notification_id=' + notificationId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove unread styling
                this.classList.remove('unread');

                // Update notification counter
                const badge = document.querySelector('.badge.bg-notification');
                if (badge) {
                    const currentCount = parseInt(badge.textContent, 10);
                    if (currentCount > 1) {
                        badge.textContent = currentCount - 1;
                    } else {
                        badge.remove();
                    }

                    // Play notification sound
                    if (typeof playNotificationSound === 'function') {
                        playNotificationSound();
                    }
                }
            }

            // Always redirect to the target URL, even if marking as read fails
            if (targetUrl && targetUrl !== '#') {
                console.log('Redirecting to:', targetUrl);

                // Special handling for batch transfers
                if (targetUrl.includes('/batch/view.php')) {
                    console.log('Handling batch transfer redirect');
                    // Use setTimeout to ensure the redirect happens after the current event loop
                    setTimeout(() => {
                        window.location.href = targetUrl;
                    }, 50);
                } else {
                    window.location.href = targetUrl;
                }
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);

            // Still redirect even if there's an error
            if (targetUrl && targetUrl !== '#') {
                console.log('Redirecting after error to:', targetUrl);
                window.location.href = targetUrl;
            }
        });
    }

    // Function to attach event listeners to notification items
    function attachNotificationListeners() {
        // Get all notification items in the dropdown
        const notificationItems = document.querySelectorAll('.dropdown-item.notification-item');
        console.log('Found notification items:', notificationItems.length);

        // Attach click event listener to each notification item
        notificationItems.forEach(item => {
            // Log the notification item details
            console.log('Attaching listener to notification:', {
                id: item.getAttribute('data-notification-id'),
                href: item.getAttribute('href'),
                text: item.textContent.trim().substring(0, 30) + '...',
                isBatchTransfer: item.getAttribute('href').includes('/batch/view.php')
            });

            // Remove any existing listeners to prevent duplicates
            item.removeEventListener('click', handleNotificationClick);

            // Add the click event listener
            item.addEventListener('click', handleNotificationClick);
        });
    }

    // Initial attachment of event listeners
    attachNotificationListeners();

    // Re-attach listeners when the dropdown is shown (for dynamically added notifications)
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    if (notificationsDropdown) {
        notificationsDropdown.addEventListener('shown.bs.dropdown', attachNotificationListeners);
    }

    // Add a direct click handler for batch transfer notifications
    document.addEventListener('click', function(e) {
        // Check if the clicked element is a notification item or a child of one
        const notificationItem = e.target.closest('.dropdown-item.notification-item');
        if (notificationItem) {
            const href = notificationItem.getAttribute('href');
            // Check if it's a batch transfer notification
            if (href && href.includes('/batch/view.php')) {
                console.log('Direct handler caught batch transfer click:', href);
                e.preventDefault();

                // Mark as read
                const notificationId = notificationItem.getAttribute('data-notification-id');
                if (notificationId) {
                    fetch('/choims/modules/notifications/mark_read.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'notification_id=' + notificationId
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Marked batch notification as read:', data);
                    })
                    .catch(error => {
                        console.error('Error marking batch notification as read:', error);
                    });
                }

                // Redirect with a slight delay
                setTimeout(() => {
                    window.location.href = href;
                }, 100);
            }
        }
    });

    // Handle WebSocket or AJAX-loaded notifications
    // Create a MutationObserver to watch for new notifications added to the DOM
    const dropdownMenu = document.querySelector('.notification-dropdown');
    if (dropdownMenu) {
        const observer = new MutationObserver(function(mutations) {
            attachNotificationListeners();
        });

        observer.observe(dropdownMenu, {
            childList: true,
            subtree: true
        });
    }
});
