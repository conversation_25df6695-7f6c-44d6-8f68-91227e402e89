/* 
 * Modern React-like UI Styles for Admin Users Page
 * CHOIMS - City Health Office Inventory Management System
 */

:root {
  /* Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  
  /* Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-all: all 0.2s ease;
}

/* Page Container */
.users-container {
  padding: 1.5rem;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: none;
  transition: var(--transition-all);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: 1rem 1.25rem;
}

.card-header h6 {
  margin: 0;
  font-weight: 600;
  color: var(--gray-800);
}

.card-body {
  padding: 1.25rem;
}

/* Buttons */
.btn {
  font-weight: 500;
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  transition: var(--transition-all);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background-color: var(--gray-200);
  border-color: var(--gray-200);
  color: var(--gray-700);
}

.btn-secondary:hover {
  background-color: var(--gray-300);
  border-color: var(--gray-300);
  color: var(--gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

.btn-success:hover {
  background-color: #0da271;
  border-color: #0da271;
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-danger {
  background-color: var(--danger);
  border-color: var(--danger);
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Alerts */
.alert {
  border-radius: var(--radius);
  padding: 1rem 1.25rem;
  border: none;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-success {
  background-color: #ecfdf5;
  color: #065f46;
}

.alert-danger {
  background-color: #fef2f2;
  color: #991b1b;
}

/* Tables */
.table-container {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.table {
  margin-bottom: 0;
}

.table th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
}

.table tr:last-child td {
  border-bottom: none;
}

.table tbody tr {
  transition: var(--transition-all);
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

/* User Card in Table */
.user-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: var(--gray-500);
}

/* Status Badges */
.badge {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-success {
  background-color: #ecfdf5;
  color: #065f46;
}

.badge-danger {
  background-color: #fef2f2;
  color: #991b1b;
}

.badge-warning {
  background-color: #fffbeb;
  color: #92400e;
}

.badge-info {
  background-color: #eff6ff;
  color: #1e40af;
}

/* Role Badges */
.role-badge {
  padding: 0.35em 0.75em;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
}

.role-superadmin {
  background-color: #eff6ff;
  color: #1e40af;
}

.role-admin {
  background-color: #f0fdf4;
  color: #166534;
}

.role-user {
  background-color: #f3f4f6;
  color: #4b5563;
}

.role-godmode {
  background-color: #fef2f2;
  color: #991b1b;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Modals */
.modal-content {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.modal-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: 1.25rem 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Form Controls */
.form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.5rem 0.75rem;
  transition: var(--transition-all);
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.form-select {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.5rem 2.25rem 0.5rem 0.75rem;
  transition: var(--transition-all);
}

.form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

/* Search Box */
.search-container {
  position: relative;
  max-width: 300px;
  margin-bottom: 1rem;
}

.search-container .form-control {
  padding-left: 2.5rem;
  border-radius: var(--radius-full);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
}

/* Filter Section */
.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0;
  white-space: nowrap;
}

/* DataTables Custom Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length select {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.25rem 1.5rem 0.25rem 0.5rem;
}

.dataTables_wrapper .dataTables_filter input {
  border-radius: var(--radius-full);
  border: 1px solid var(--gray-300);
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  margin-left: 0;
  width: 250px;
}

.dataTables_wrapper .dataTables_filter label {
  position: relative;
}

.dataTables_wrapper .dataTables_filter label::before {
  content: "\f002";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  z-index: 1;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.25rem 0.75rem;
  margin: 0 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--gray-800) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-item {
    width: 100%;
  }
  
  .action-buttons {
    flex-wrap: wrap;
  }
  
  .search-container {
    max-width: 100%;
  }
  
  .dataTables_wrapper .dataTables_filter input {
    width: 100%;
  }
}
