<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Check if unit_of_measure column exists
$checkColumnQuery = "SHOW COLUMNS FROM sku_master LIKE 'unit_of_measure'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);

if (mysqli_num_rows($checkColumnResult) == 0) {
    // Column doesn't exist, add it
    $addColumnQuery = "ALTER TABLE sku_master ADD COLUMN unit_of_measure VARCHAR(20) NOT NULL DEFAULT 'pcs' AFTER description";

    if (mysqli_query($conn, $addColumnQuery)) {
        echo "Column 'unit_of_measure' added successfully to sku_master table.<br>";
    } else {
        echo "Error adding column: " . mysqli_error($conn) . "<br>";
        exit;
    }
}

// Get all SKUs that don't have a unit_of_measure or have the default value
$skusQuery = "SELECT sku_id, sku_name FROM sku_master WHERE unit_of_measure = '' OR unit_of_measure = 'Unit' OR unit_of_measure IS NULL";
$skusResult = mysqli_query($conn, $skusQuery);

echo "<h2>Updating Unit Types for SKUs</h2>";
echo "<table border='1'>";
echo "<tr><th>SKU ID</th><th>SKU Name</th><th>Old Unit Type</th><th>New Unit Type</th><th>Status</th></tr>";

$updatedCount = 0;
$skippedCount = 0;

while ($sku = mysqli_fetch_assoc($skusResult)) {
    $skuId = $sku['sku_id'];

    // Find the most recent transaction for this SKU that has a unit type in the remarks
    $transactionQuery = "
        SELECT ct.remarks
        FROM consumable_transactions ct
        JOIN consumable_inventory ci ON ct.inventory_id = ci.inventory_id
        WHERE ci.sku_id = ?
        AND ct.remarks LIKE '%Unit Type:%'
        ORDER BY ct.transaction_date DESC
        LIMIT 1
    ";

    $transactionStmt = mysqli_prepare($conn, $transactionQuery);
    mysqli_stmt_bind_param($transactionStmt, 'i', $skuId);
    mysqli_stmt_execute($transactionStmt);
    $transactionResult = mysqli_stmt_get_result($transactionStmt);

    if ($transaction = mysqli_fetch_assoc($transactionResult)) {
        // Extract unit type from remarks
        if (preg_match('/Unit Type: ([a-zA-Z]+)/', $transaction['remarks'], $matches)) {
            $unitType = $matches[1];

            // Update the SKU with the unit type
            $updateQuery = "UPDATE sku_master SET unit_of_measure = ? WHERE sku_id = ?";
            $updateStmt = mysqli_prepare($conn, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, 'si', $unitType, $skuId);

            if (mysqli_stmt_execute($updateStmt)) {
                echo "<tr>";
                echo "<td>" . $skuId . "</td>";
                echo "<td>" . $sku['sku_name'] . "</td>";
                echo "<td>Empty/Default</td>";
                echo "<td>" . $unitType . "</td>";
                echo "<td>Updated</td>";
                echo "</tr>";
                $updatedCount++;
            } else {
                echo "<tr>";
                echo "<td>" . $skuId . "</td>";
                echo "<td>" . $sku['sku_name'] . "</td>";
                echo "<td>Empty/Default</td>";
                echo "<td>" . $unitType . "</td>";
                echo "<td>Error: " . mysqli_error($conn) . "</td>";
                echo "</tr>";
                $skippedCount++;
            }
        } else {
            echo "<tr>";
            echo "<td>" . $skuId . "</td>";
            echo "<td>" . $sku['sku_name'] . "</td>";
            echo "<td>Empty/Default</td>";
            echo "<td>N/A</td>";
            echo "<td>Skipped - No unit type found in remarks</td>";
            echo "</tr>";
            $skippedCount++;
        }
    } else {
        echo "<tr>";
        echo "<td>" . $skuId . "</td>";
        echo "<td>" . $sku['sku_name'] . "</td>";
        echo "<td>Empty/Default</td>";
        echo "<td>N/A</td>";
        echo "<td>Skipped - No transactions found</td>";
        echo "</tr>";
        $skippedCount++;
    }
}

echo "</table>";
echo "<p>Updated: " . $updatedCount . " SKUs</p>";
echo "<p>Skipped: " . $skippedCount . " SKUs</p>";

// Close connection
mysqli_close($conn);
?>
