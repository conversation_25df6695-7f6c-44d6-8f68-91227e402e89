/* Health Center Dashboard - Modern Design */

:root {
  /* Modern Color Palette - Health Center Theme */
  --health-primary: #2E7D32;
  --health-primary-light: #4CAF50;
  --health-primary-dark: #1B5E20;
  --health-secondary: #66BB6A;
  --health-accent: #81C784;
  --health-text-on-primary: #ffffff;
  --health-text-primary: #1e293b;
  --health-text-secondary: #475569;
  --health-bg-primary: #ffffff;
  --health-bg-secondary: #f8fafc;
  --health-bg-accent: #E8F5E9;
  --health-border-color: #C8E6C9;
  
  /* Status Colors */
  --success: #4CAF50;
  --warning: #FFC107;
  --danger: #F44336;
  --info: #03A9F4;
  
  /* Shadows & Effects */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 16px 24px rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 20px 30px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius: 12px;
  --radius-md: 16px;
  --radius-lg: 24px;
  --radius-full: 9999px;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
}

/* Base styles */
body {
  background-color: #f5f7fa;
  color: var(--health-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: 1.5rem;
}

/* Dashboard Header */
.health-dashboard-header {
  background: linear-gradient(135deg, var(--health-primary-dark), var(--health-primary));
  border-radius: var(--radius);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.health-dashboard-header::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.1)' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.health-title-container {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.health-title-icon {
  width: 64px;
  height: 64px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.75rem;
}

.health-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.025em;
}

.health-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0.25rem 0 0;
}

.health-date {
  display: inline-flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.health-date i {
  margin-right: 0.5rem;
}

.header-stats {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.header-stat {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius);
  padding: var(--space-3) var(--space-4);
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: var(--transition);
}

.header-stat:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Stat Cards */
.stat-card-mini {
  background-color: white;
  border-radius: var(--radius);
  padding: var(--space-4);
  height: 100%;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: var(--transition);
}

.stat-card-mini:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.stat-icon-mini {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stat-card-primary .stat-icon-mini {
  background-color: var(--health-bg-accent);
  color: var(--health-primary);
}

.stat-card-success .stat-icon-mini {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

.stat-card-warning .stat-icon-mini {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value-mini {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.stat-label-mini {
  font-size: 0.875rem;
  color: var(--health-text-secondary);
}

/* Content Cards */
.content-card {
  background-color: white;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  overflow: hidden;
  transition: var(--transition);
  border: none;
}

.content-card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: white;
  border-bottom: 1px solid var(--health-border-color);
  padding: var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h5 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-body {
  padding: var(--space-4);
}

/* Quick Actions */
.quick-action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-4);
}

.quick-action-card {
  background-color: white;
  border-radius: var(--radius);
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  text-align: center;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  border: 1px solid #f0f0f0;
}

.quick-action-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--health-border-color);
  background-color: var(--health-bg-accent);
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background-color: var(--health-bg-accent);
  color: var(--health-primary);
  transition: var(--transition);
}

.quick-action-card:hover .action-icon {
  background-color: var(--health-primary);
  color: white;
  transform: scale(1.1);
}

.action-label {
  font-weight: 600;
  color: var(--health-text-primary);
  transition: var(--transition);
}

.quick-action-card:hover .action-label {
  color: var(--health-primary-dark);
}

/* Tables */
.table {
  margin-bottom: 0;
}

.table th {
  background-color: #f9fafb;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--health-text-secondary);
  padding: var(--space-3) var(--space-4);
  border-top: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-color: #f0f0f0;
}

.table tr:hover {
  background-color: var(--health-bg-accent);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35em 0.65em;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge i {
  margin-right: 0.35em;
}

.status-pending {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning);
}

.status-approved {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

.status-rejected {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger);
}

.status-completed {
  background-color: rgba(3, 169, 244, 0.1);
  color: var(--info);
}

.status-low {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning);
}

.status-critical {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger);
}

.status-normal {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success);
}

/* Inventory Summary Cards */
.inventory-summary-card {
  background-color: white;
  border-radius: var(--radius);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition);
  border-left: 4px solid var(--health-primary);
}

.inventory-summary-card:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow);
}

.inventory-summary-left {
  display: flex;
  flex-direction: column;
}

.inventory-category {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--health-text-primary);
}

.inventory-count {
  font-size: 2rem;
  font-weight: 700;
  color: var(--health-primary);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.inventory-total {
  font-size: 0.875rem;
  color: var(--health-text-secondary);
}

.inventory-summary-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--space-2);
}

/* Stock Level Indicators */
.stock-level {
  width: 100%;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: 0.5rem 0;
}

.stock-level-bar {
  height: 100%;
  border-radius: var(--radius-full);
}

.stock-level-normal {
  background-color: var(--success);
}

.stock-level-low {
  background-color: var(--warning);
}

.stock-level-critical {
  background-color: var(--danger);
}

/* Expiring Items Section */
.expiring-item {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  border-radius: var(--radius);
  margin-bottom: var(--space-3);
  background-color: white;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border-left: 4px solid transparent;
}

.expiring-item:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow);
}

.expiring-soon {
  border-left-color: var(--warning);
}

.expiring-very-soon {
  border-left-color: var(--danger);
}

.expiring-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.expiring-soon .expiring-icon {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning);
}

.expiring-very-soon .expiring-icon {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger);
}

.expiring-content {
  flex: 1;
}

.expiring-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.expiring-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 0.875rem;
  color: var(--health-text-secondary);
}

.expiring-date {
  font-weight: 600;
}

.expiring-soon .expiring-date {
  color: var(--warning);
}

.expiring-very-soon .expiring-date {
  color: var(--danger);
}

.expiring-actions {
  margin-left: var(--space-3);
}

/* Notifications */
.notification-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.notification-item {
  background-color: white;
  border-radius: var(--radius);
  padding: var(--space-3) var(--space-4);
  box-shadow: var(--shadow-sm);
  border-left: 3px solid var(--health-primary-light);
  transition: var(--transition);
}

.notification-item:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow);
}

.notification-item.unread {
  border-left-color: var(--info);
  background-color: rgba(3, 169, 244, 0.05);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-2);
}

.notification-title {
  font-weight: 600;
  color: var(--health-text-primary);
  margin: 0;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--health-text-secondary);
}

.notification-message {
  font-size: 0.875rem;
  color: var(--health-text-secondary);
  margin-bottom: var(--space-2);
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-2);
}

/* Buttons */
.btn {
  border-radius: var(--radius);
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: var(--radius-sm);
}

.btn-primary {
  background-color: var(--health-primary);
  border-color: var(--health-primary);
}

.btn-primary:hover {
  background-color: var(--health-primary-dark);
  border-color: var(--health-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-primary {
  color: var(--health-primary);
  border-color: var(--health-primary);
}

.btn-outline-primary:hover {
  background-color: var(--health-primary);
  border-color: var(--health-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
  text-align: center;
}

.empty-state i {
  font-size: 3rem;
  color: #e0e0e0;
  margin-bottom: var(--space-3);
}

.empty-state-text {
  color: var(--health-text-secondary);
  font-size: 1rem;
  margin-bottom: var(--space-4);
}

/* Responsive Adjustments */
@media (max-width: 1199.98px) {
  .quick-action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991.98px) {
  .health-dashboard-header {
    padding: var(--space-4);
  }
  
  .health-title {
    font-size: 1.5rem;
  }
  
  .health-title-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
  
  .header-stats {
    flex-wrap: wrap;
  }
  
  .header-stat {
    flex: 0 0 calc(50% - 0.5rem);
  }
}

@media (max-width: 767.98px) {
  .container-fluid {
    padding: 1rem;
  }
  
  .health-dashboard-header::after {
    display: none;
  }
  
  .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .health-title-container {
    width: 100%;
  }
  
  .header-stats {
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .inventory-summary-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .inventory-summary-right {
    width: 100%;
    margin-top: var(--space-3);
    align-items: flex-start;
  }
}

@media (max-width: 575.98px) {
  .quick-action-grid {
    grid-template-columns: 1fr;
  }
  
  .header-stat {
    flex: 0 0 100%;
  }
  
  .stat-card-mini {
    margin-bottom: var(--space-3);
  }
}
