<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/audit_log.php');

// Ensure user is logged in and has appropriate permissions
requireLogin();
if (!hasRole('Superadmin')) {
    header('Location: /choims/access-denied.php');
    exit;
}

// Validate input
if (!isset($_POST['inventory_id']) || empty($_POST['inventory_id'])) {
    $_SESSION['error'] = "Invalid inventory ID.";
    header('Location: /choims/modules/inventory/list.php?show_deleted=1');
    exit;
}

$inventory_id = sanitizeInput($_POST['inventory_id']);

// Check if inventory exists and is deleted
$checkQuery = "SELECT * FROM consumable_inventory WHERE inventory_id = ? AND is_deleted = 1";
$stmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($stmt, 'i', $inventory_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    $_SESSION['error'] = "Inventory item not found or not marked as deleted.";
    header('Location: /choims/modules/inventory/list.php?show_deleted=1');
    exit;
}

$inventory = mysqli_fetch_assoc($result);

// Get old values for audit log
$oldValues = json_encode([
    'is_deleted' => 1
]);

// Perform restoration
$restoreQuery = "UPDATE consumable_inventory SET is_deleted = 0 WHERE inventory_id = ?";
$stmt = mysqli_prepare($conn, $restoreQuery);
mysqli_stmt_bind_param($stmt, 'i', $inventory_id);

if (mysqli_stmt_execute($stmt)) {
    // Log the action
    logAction(
        $conn,
        $_SESSION['user_id'],
        'Restored deleted inventory item',
        'inventory',
        $inventory_id,
        $oldValues,
        json_encode(['is_deleted' => 0])
    );
    
    $_SESSION['success'] = "Inventory item has been restored successfully.";
} else {
    $_SESSION['error'] = "Failed to restore inventory item: " . mysqli_error($conn);
}

// Redirect back to deleted inventory list
header('Location: /choims/modules/inventory/list.php?show_deleted=1');
exit;
?> 