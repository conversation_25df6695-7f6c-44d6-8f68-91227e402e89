<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Check if user has admin permissions
$auth = Auth::getInstance();
if (!$auth->hasRole(ROLE_SUPERADMIN) && !$auth->hasRole(ROLE_GODMODE)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$db = Database::getInstance()->getConnection();
$message = '';
$departments = [];

// Handle department actions: create, update, delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Create new department
        if ($_POST['action'] === 'create') {
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            $status = isset($_POST['status']) ? $_POST['status'] : 'active';
            
            if (empty($name)) {
                $message = displayAlert('Department name is required', 'danger');
            } else {
                try {
                    // Check if department name already exists
                    $stmt = $db->prepare("SELECT department_id FROM departments WHERE name = ?");
                    $stmt->execute([$name]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = displayAlert('Department name already exists', 'danger');
                    } else {
                        $stmt = $db->prepare("
                            INSERT INTO departments (name, description, status, created_at)
                            VALUES (?, ?, ?, NOW())
                        ");
                        $stmt->execute([$name, $description, $status]);
                        
                        $message = displayAlert('Department created successfully', 'success');
                    }
                } catch (PDOException $e) {
                    $message = displayAlert('Error creating department: ' . $e->getMessage(), 'danger');
                }
            }
        }
        
        // Update existing department
        else if ($_POST['action'] === 'update' && isset($_POST['department_id'])) {
            $departmentId = $_POST['department_id'];
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $description = isset($_POST['description']) ? trim($_POST['description']) : '';
            $status = isset($_POST['status']) ? $_POST['status'] : 'active';
            
            if (empty($name)) {
                $message = displayAlert('Department name is required', 'danger');
            } else {
                try {
                    // Check if the updated name already exists for another department
                    $stmt = $db->prepare("SELECT department_id FROM departments WHERE name = ? AND department_id != ?");
                    $stmt->execute([$name, $departmentId]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = displayAlert('Department name already exists', 'danger');
                    } else {
                        $stmt = $db->prepare("
                            UPDATE departments 
                            SET name = ?, description = ?, status = ?
                            WHERE department_id = ?
                        ");
                        $stmt->execute([$name, $description, $status, $departmentId]);
                        
                        $message = displayAlert('Department updated successfully', 'success');
                    }
                } catch (PDOException $e) {
                    $message = displayAlert('Error updating department: ' . $e->getMessage(), 'danger');
                }
            }
        }
        
        // Delete department
        else if ($_POST['action'] === 'delete' && isset($_POST['department_id'])) {
            $departmentId = $_POST['department_id'];
            
            try {
                // First check if there are users assigned to this department
                $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE department_id = ?");
                $stmt->execute([$departmentId]);
                $userCount = $stmt->fetchColumn();
                
                if ($userCount > 0) {
                    $message = displayAlert('Cannot delete department: There are ' . $userCount . ' users assigned to it', 'danger');
                } else {
                    // Also check if there are inventory items assigned to this department
                    $stmt = $db->prepare("SELECT COUNT(*) FROM inventory WHERE department_id = ?");
                    $stmt->execute([$departmentId]);
                    $inventoryCount = $stmt->fetchColumn();
                    
                    if ($inventoryCount > 0) {
                        $message = displayAlert('Cannot delete department: There are ' . $inventoryCount . ' inventory items assigned to it', 'danger');
                    } else {
                        // Safe to delete
                        $stmt = $db->prepare("DELETE FROM departments WHERE department_id = ?");
                        $stmt->execute([$departmentId]);
                        
                        $message = displayAlert('Department deleted successfully', 'success');
                    }
                }
            } catch (PDOException $e) {
                $message = displayAlert('Error deleting department: ' . $e->getMessage(), 'danger');
            }
        }
    }
}

// Get list of departments
try {
    $stmt = $db->query("
        SELECT d.*, 
               (SELECT COUNT(*) FROM users WHERE department_id = d.department_id) as user_count,
               (SELECT COUNT(*) FROM inventory WHERE department_id = d.department_id) as inventory_count
        FROM departments d
        ORDER BY d.name ASC
    ");
    $departments = $stmt->fetchAll();
} catch (PDOException $e) {
    $message = displayAlert('Error fetching departments: ' . $e->getMessage(), 'danger');
    $departments = [];
}

// Include header
require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Department Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDepartmentModal">
            <i class="fas fa-plus"></i> Add New Department
        </button>
    </div>
    
    <?php echo $message; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Departments</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="departmentsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Users</th>
                            <th>Inventory Items</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($departments as $dept): ?>
                        <tr>
                            <td><?php echo $dept['department_id']; ?></td>
                            <td><?php echo htmlspecialchars($dept['name']); ?></td>
                            <td><?php echo htmlspecialchars($dept['description']); ?></td>
                            <td><?php echo $dept['user_count']; ?></td>
                            <td><?php echo $dept['inventory_count']; ?></td>
                            <td>
                                <span class="badge bg-<?php echo $dept['status'] === 'active' ? 'success' : 'danger'; ?>">
                                    <?php echo ucfirst($dept['status']); ?>
                                </span>
                            </td>
                            <td><?php echo formatDate($dept['created_at']); ?></td>
                            <td>
                                <button class="btn btn-sm btn-info edit-dept-btn" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editDepartmentModal"
                                        data-department-id="<?php echo $dept['department_id']; ?>"
                                        data-name="<?php echo htmlspecialchars($dept['name']); ?>"
                                        data-description="<?php echo htmlspecialchars($dept['description']); ?>"
                                        data-status="<?php echo $dept['status']; ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                
                                <?php if ($dept['user_count'] == 0 && $dept['inventory_count'] == 0): ?>
                                <button class="btn btn-sm btn-danger delete-dept-btn"
                                        data-bs-toggle="modal"
                                        data-bs-target="#deleteDepartmentModal"
                                        data-department-id="<?php echo $dept['department_id']; ?>"
                                        data-name="<?php echo htmlspecialchars($dept['name']); ?>">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Department Modal -->
<div class="modal fade" id="createDepartmentModal" tabindex="-1" aria-labelledby="createDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createDepartmentModalLabel">Add New Department</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Department Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" selected>Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Department</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<div class="modal fade" id="editDepartmentModal" tabindex="-1" aria-labelledby="editDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDepartmentModalLabel">Edit Department</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" id="edit_department_id" name="department_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Department Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Department</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Department Modal -->
<div class="modal fade" id="deleteDepartmentModal" tabindex="-1" aria-labelledby="deleteDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteDepartmentModalLabel">Delete Department</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this department? This action cannot be undone.</p>
                <p>Department: <strong id="delete_dept_name"></strong></p>
            </div>
            <form method="post">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" id="delete_department_id" name="department_id">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Department</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#departmentsTable').DataTable();
    
    // Edit department button click
    const editDeptBtns = document.querySelectorAll('.edit-dept-btn');
    editDeptBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const departmentId = this.getAttribute('data-department-id');
            const name = this.getAttribute('data-name');
            const description = this.getAttribute('data-description');
            const status = this.getAttribute('data-status');
            
            document.getElementById('edit_department_id').value = departmentId;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_status').value = status;
        });
    });
    
    // Delete department confirmation
    const deleteDeptBtns = document.querySelectorAll('.delete-dept-btn');
    deleteDeptBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const departmentId = this.getAttribute('data-department-id');
            const name = this.getAttribute('data-name');
            
            document.getElementById('delete_department_id').value = departmentId;
            document.getElementById('delete_dept_name').textContent = name;
        });
    });
});
</script>

<?php
// Helper function for displaying alerts
function displayAlert($message, $type = 'info') {
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

require_once '../../templates/footer.php';
?> 