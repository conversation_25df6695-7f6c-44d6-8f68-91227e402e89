/* Modern Department Dashboard Styles */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-lighter: #81C784;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  
  /* Gradients */
  --green-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
  --blue-gradient: linear-gradient(135deg, #1976D2, #42A5F5);
  --orange-gradient: linear-gradient(135deg, #E65100, #FFA726);
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
}

/* Dashboard Container */
.dashboard-container {
  padding: var(--space-5) var(--space-4);
}

/* Dashboard Header */
.dashboard-header {
  background: var(--white);
  border-radius: var(--radius-md);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.dashboard-header-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  position: relative;
  z-index: 2;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.dashboard-title i {
  color: var(--primary);
}

.dashboard-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin: 0;
}

.dashboard-date {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  background: var(--gray-100);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  color: var(--gray-500);
  margin-top: var(--space-2);
  width: fit-content;
}

/* Stat Cards */
.stat-card {
  background: var(--white);
  border-radius: var(--radius);
  padding: var(--space-4);
  box-shadow: var(--shadow);
  height: 100%;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.stat-card-primary {
  border-top: 4px solid var(--info);
}

.stat-card-success {
  border-top: 4px solid var(--success);
}

.stat-card-warning {
  border-top: 4px solid var(--warning);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-bottom: var(--space-2);
}

.stat-card-primary .stat-icon {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.stat-card-success .stat-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-card-warning .stat-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
  margin: 0;
}

.stat-card-primary .stat-value {
  color: var(--info);
}

.stat-card-success .stat-value {
  color: var(--success);
}

.stat-card-warning .stat-value {
  color: var(--warning);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-500);
  margin: 0;
}

/* Content Cards */
.content-card {
  background: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  overflow: hidden;
  transition: var(--transition);
  border: none;
}

.content-card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h5 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header i {
  font-size: 1rem;
}

/* Quick Actions */
.quick-action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-4);
  padding: var(--space-4);
}

.quick-action-card {
  background: var(--white);
  border-radius: var(--radius);
  padding: var(--space-4);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  text-align: center;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.quick-action-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-lighter);
  background-color: var(--primary-bg);
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: var(--primary-bg);
  color: var(--primary);
  transition: var(--transition);
}

.quick-action-card:hover .action-icon {
  background: var(--primary);
  color: var(--white);
  transform: scale(1.1);
}

.action-label {
  font-weight: 600;
  color: var(--dark);
  transition: var(--transition);
}

.quick-action-card:hover .action-label {
  color: var(--primary-dark);
}

/* Tables */
.table-container {
  border-radius: var(--radius);
  overflow: hidden;
}

.table {
  margin-bottom: 0;
}

.table th {
  background: var(--gray-100);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--gray-500);
  padding: var(--space-3) var(--space-4);
  border-top: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
}

.table tr:hover {
  background-color: var(--gray-100);
}

/* Status Badges */
.status-badge {
  padding: 0.35em 0.65em;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Notifications */
.notification-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: var(--space-4);
}

.notification-item {
  background: var(--white);
  border-radius: var(--radius);
  padding: var(--space-3) var(--space-4);
  box-shadow: var(--shadow-sm);
  border-left: 3px solid var(--primary-light);
  transition: var(--transition-fast);
}

.notification-item:hover {
  transform: translateX(4px);
  box-shadow: var(--shadow);
}

.notification-item.unread {
  border-left-color: var(--info);
  background-color: rgba(59, 130, 246, 0.05);
}

.notification-title {
  font-weight: 600;
  color: var(--dark);
  margin-bottom: var(--space-1);
}

.notification-meta {
  font-size: 0.75rem;
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin-bottom: var(--space-2);
}

.notification-message {
  font-size: 0.875rem;
  color: var(--gray-500);
}

/* Transfers List */
.transfer-list {
  display: flex;
  flex-direction: column;
}

.transfer-item {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--gray-200);
  transition: var(--transition-fast);
}

.transfer-item:last-child {
  border-bottom: none;
}

.transfer-item:hover {
  background-color: var(--gray-100);
}

.transfer-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.transfer-icon.outgoing {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.transfer-icon.incoming {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.transfer-content {
  flex-grow: 1;
  min-width: 0;
}

.transfer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-1);
}

.transfer-title {
  font-weight: 600;
  color: var(--dark);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.transfer-date {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.transfer-description {
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  color: var(--gray-500);
}

.transfer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transfer-direction {
  font-size: 0.75rem;
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: 0.25em 0.5em;
  border-radius: var(--radius-full);
}

.transfer-direction.outgoing {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.transfer-direction.incoming {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
  text-align: center;
}

.empty-state i {
  font-size: 3rem;
  color: var(--gray-300);
  margin-bottom: var(--space-3);
}

.empty-state-text {
  color: var(--gray-500);
  font-size: 1rem;
}

/* Buttons */
.btn {
  border-radius: var(--radius);
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: var(--transition);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: var(--radius-sm);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: #0da271;
  border-color: #0da271;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-success {
  color: var(--success);
  border-color: var(--success);
}

.btn-outline-success:hover {
  background-color: var(--success);
  border-color: var(--success);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Responsive */
@media (max-width: 991.98px) {
  .quick-action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767.98px) {
  .dashboard-header {
    padding: var(--space-4);
  }
  
  .dashboard-title {
    font-size: 1.5rem;
  }
  
  .stat-value {
    font-size: 1.75rem;
  }
}

@media (max-width: 575.98px) {
  .quick-action-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-container {
    padding: var(--space-3);
  }
}
