/* Logistics Dashboard - Modern Rounded White Design */

:root {
  /* Modern Color Palette - Based on reference image */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --secondary: #1B5E20;
  --text-on-primary: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-accent: #f5f5f5;
  --border-color: #f0f0f0;
  --success: #4CAF50;
  --warning: #ff9800;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #E8F5E9;
  --dark: #1B5E20;

  /* Background Colors */
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --bg-pattern: #f9f9f9;

  /* Shadows & Effects */
  --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 12px 28px rgba(0, 0, 0, 0.15);
  --transition: all 0.2s ease;

  /* Border Radius */
  --radius-sm: 12px;
  --radius: 16px;
  --radius-lg: 20px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
}

/* Base styles and typography */
body {
  background-color: var(--bg-light);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: 0 1.5rem;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

/* Dashboard header section */
.dashboard-header {
  position: relative;
  background: #ffffff;
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 1.5rem 2rem 1rem;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  color: var(--text-primary);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.dashboard-header .title-container {
  display: flex;
  align-items: center;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.025em;
  white-space: nowrap;
}

.dashboard-title-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-light);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
}

.dashboard-date {
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.dashboard-date i {
  margin-right: 0.5rem;
  color: var(--primary);
}

.dashboard-content {
  margin-top: 0.5rem;
}

/* Dashboard actions */
.dashboard-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.7rem 1.25rem;
  background-color: var(--bg-white);
  color: var(--text-primary);
  border-radius: var(--radius);
  font-weight: 600;
  transition: all 0.2s ease;
  text-decoration: none;
  box-shadow: var(--shadow);
  font-size: 0.9rem;
}

.action-btn:hover {
  background-color: var(--bg-white);
  color: var(--primary);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.action-btn i {
  font-size: 1rem;
  color: var(--primary);
}

.action-primary {
  background-color: var(--primary);
  color: white;
}

.action-primary:hover {
  background-color: var(--primary-dark);
  color: white;
}

.action-primary i {
  color: white;
}

/* Quick stats overview */
.quick-stats {
  margin-top: 1rem;
  display: flex;
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 46px;
  height: 46px;
  border-radius: var(--radius);
  background-color: rgba(46, 125, 50, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--primary);
  box-shadow: 0 6px 12px rgba(46, 125, 50, 0.15);
  transition: all 0.2s ease;
  cursor: pointer;
}

.stat-icon:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(46, 125, 50, 0.2);
  background-color: rgba(46, 125, 50, 0.15);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Dashboard cards */
.dashboard-card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: var(--radius);
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card .card-body {
  padding: 1.75rem;
  flex: 1;
}

.dashboard-card .card-footer {
  background-color: var(--bg-white);
  border-top: 1px solid var(--border-color);
  padding: var(--space-3) var(--space-5);
  transition: var(--transition);
}

.dashboard-card:hover .card-footer {
  background-color: var(--bg-light);
}

/* Dashboard icons */
.dashboard-icon {
  width: 52px;
  height: 52px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.35rem;
  margin-left: auto;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

/* Icon colors */
.icon-assets {
  background-color: #e8f5e9;
  color: var(--primary);
}

.icon-inventory {
  background-color: #e3f2fd;
  color: var(--info);
}

.icon-warning {
  background-color: #fff3e0;
  color: var(--warning);
}

.icon-transfer {
  background-color: #f3e5f5;
  color: #9c27b0;
}

/* Card stats styling */
.stat-label {
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2.25rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-description {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.stat-trend {
  font-size: 0.875rem;
  font-weight: 600;
}

/* View details link */
.view-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  padding: 0.75rem 1rem;
}

.view-details:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.view-details i {
  transition: transform 0.2s ease;
}

.view-details:hover i {
  transform: translateX(4px);
}

/* Chart cards */
.chart-card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: var(--radius);
  position: relative;
}

/* Assets by Category chart card */
.chart-card:has(#assetsByCategoryChart) {
  background: var(--bg-white);
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.chart-card .card-header {
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.chart-card .card-body {
  padding: 1.75rem;
  flex: 1;
}

.chart-card .card-header i {
  margin-right: 0.5rem;
  color: var(--primary);
}

/* Low stock container */
.low-stock-container {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: var(--radius);
}

.low-stock-container:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.low-stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.75rem;
  border-bottom: 1px solid var(--border-color);
}

.low-stock-header-title {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.low-stock-header-title i {
  color: var(--warning);
}

.btn-view-all {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;
}

.btn-view-all:hover {
  color: var(--primary-dark);
}

.btn-view-all:hover i {
  transform: translateX(2px);
}

.low-stock-table {
  width: 100%;
  border-collapse: collapse;
}

.low-stock-table th {
  text-align: left;
  padding: 0.75rem 1.5rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #64748b;
  border-bottom: 1px solid var(--border-color);
}

.low-stock-table td {
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.low-stock-table tr:last-child td {
  border-bottom: none;
}

.low-stock-empty {
  padding: 3rem 1.5rem;
  text-align: center;
  color: #64748b;
}

.low-stock-empty i {
  font-size: 2.5rem;
  color: var(--success);
  margin-bottom: 1rem;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.03em;
  background-color: var(--bg-accent);
  color: var(--text-secondary);
}

.badge-pending {
  background-color: #fff8e1;
  color: #f59e0b;
}

.badge-approved {
  background-color: #e8f5e9;
  color: #10b981;
}

.badge-completed {
  background-color: #e8f5e9;
  color: #10b981;
}

.badge-rejected {
  background-color: #fee2e2;
  color: #ef4444;
}

/* Chart legend */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-label {
  font-weight: 500;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-state i {
  font-size: 2.5rem;
  color: #cbd5e1;
  margin-bottom: 1rem;
}

.empty-state p {
  color: #64748b;
  margin-bottom: 1.5rem;
}

/* Modern Icon Button */
.btn-icon-circle {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, #4CAF50, #2E7D32);
  color: white;
  border: none;
  box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3),
              0 4px 8px rgba(0, 0, 0, 0.1),
              inset 0 -2px 5px rgba(0, 0, 0, 0.1),
              inset 0 2px 5px rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  margin: 0 8px;
}

.btn-icon-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.btn-icon-circle:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 28px rgba(46, 125, 50, 0.4),
              0 8px 16px rgba(0, 0, 0, 0.15),
              inset 0 -2px 5px rgba(0, 0, 0, 0.1),
              inset 0 2px 5px rgba(255, 255, 255, 0.2);
}

.btn-icon-circle:hover::before {
  opacity: 1;
}

.btn-icon-circle:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(46, 125, 50, 0.3),
              0 3px 6px rgba(0, 0, 0, 0.1),
              inset 0 -1px 3px rgba(0, 0, 0, 0.1),
              inset 0 1px 3px rgba(255, 255, 255, 0.2);
}

.btn-icon-circle i {
  font-size: 1.5rem;
  color: white;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

/* Chart actions */
.chart-actions {
  display: flex;
  gap: 0.25rem;
}

.chart-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-sm);
  background-color: var(--bg-accent);
  color: var(--text-secondary);
  border: none;
}

.chart-actions .btn.active {
  background-color: var(--primary);
  color: white;
}

.chart-type-toggle {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: var(--radius-sm);
  background-color: var(--bg-accent);
  color: var(--text-secondary);
  border: none;
}

.chart-type-toggle.active {
  background-color: var(--primary);
  color: white;
}

/* Transfer management styles */
.nav-tabs {
    border-bottom: none;
    padding: 0 1.5rem;
    margin-top: 0.5rem;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    margin-right: 1rem;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link.active {
    color: var(--primary);
    border-bottom: 2px solid var(--primary);
    background-color: transparent;
}

.nav-tabs .nav-link:hover:not(.active) {
    border-bottom: 2px solid #e2e8f0;
}

.badge-direction {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    margin-right: 0.5rem;
}

.badge-incoming {
    background-color: #e8f5e9;
    color: var(--success);
}

.badge-outgoing {
    background-color: #e3f2fd;
    color: var(--info);
}

.action-required {
    background-color: #fffbeb;
}

.transfer-count {
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
}

.empty-state i {
    font-size: 2rem;
    color: #cbd5e1;
    margin-bottom: 1rem;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .stat-value {
    font-size: 1.75rem;
  }

  .dashboard-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .dashboard-header {
    padding: 1.5rem;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-title-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }
}

@media (max-width: 768px) {
  .dashboard-card,
  .chart-card,
  .low-stock-container {
    margin-bottom: var(--space-4);
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .low-stock-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .btn-view-all {
    align-self: flex-start;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .dashboard-actions {
    margin-top: 1rem;
    flex-wrap: wrap;
  }

  .quick-stats {
    margin-top: 1rem;
    flex-wrap: wrap;
  }
}

/* Animations */
.animate__animated.animate__fadeIn {
  animation-duration: 0.8s;
}

.animate__animated.animate__fadeInUp {
  animation-duration: 0.6s;
}

.animate__delay-1s {
  animation-delay: 0.2s;
}

.animate__delay-2s {
  animation-delay: 0.4s;
}

.animate__delay-3s {
  animation-delay: 0.6s;
}
