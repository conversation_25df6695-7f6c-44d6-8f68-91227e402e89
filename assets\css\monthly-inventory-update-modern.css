/* Monthly Inventory Update Modern CSS - Enhanced Version */

:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
  --primary-gradient-hover: linear-gradient(135deg, #1B5E20 0%, #2E7D32 100%);
  --primary-transparent: rgba(46, 125, 50, 0.1);
  --primary-transparent-hover: rgba(46, 125, 50, 0.2);

  --success: #10b981;
  --success-light: #d1fae5;
  --success-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);

  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --warning-gradient: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);

  --danger: #ef4444;
  --danger-light: #fee2e2;
  --danger-gradient: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);

  --info: #3b82f6;
  --info-light: #dbeafe;
  --info-gradient: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);

  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Shadows & Effects */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  --shadow-md: 0 12px 20px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 16px 28px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 20px 30px rgba(0, 0, 0, 0.18);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-outline: 0 0 0 3px rgba(46, 125, 50, 0.2);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);

  /* Border Radius */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius: 12px;
  --radius-md: 16px;
  --radius-lg: 24px;
  --radius-xl: 32px;
  --radius-full: 9999px;
}

/* Card Container */
.inventory-update-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.inventory-update-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-3px);
}

.inventory-update-card .card-header {
  background: white;
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.inventory-update-card .card-header .header-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
}

.inventory-update-card .card-header .header-title i {
  font-size: 1.25rem;
  color: var(--primary);
}

.inventory-update-card .card-body {
  padding: 1.5rem;
}

/* Progress Circle */
.monthly-update-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 1.5rem;
}

.progress-container {
  position: relative;
  width: 180px;
  height: 180px;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.progress-circle {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--gray-200);
  overflow: hidden;
}

.progress-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(
    var(--primary) 0% var(--percentage, 0%),
    rgba(229, 231, 235, 0.5) var(--percentage, 0%) 100%
  );
  transition: background 0.5s ease;
}

.progress-circle-inner {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  background: white;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.03) inset;
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.progress-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.progress-text {
  font-size: 1rem;
  color: var(--gray-500);
  font-weight: 500;
}

/* Stats Summary */
.stats-summary {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 1rem;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  flex: 1;
  padding: 1.25rem 1rem;
  border-radius: var(--radius-lg);
  background-color: var(--white);
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-500);
  margin-bottom: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

/* Color the values instead of borders */
.stat-item:nth-child(1) .stat-value {
  color: var(--primary);
}

.stat-item:nth-child(2) .stat-value {
  color: var(--success);
}

.stat-item:nth-child(3) .stat-value {
  color: var(--warning);
}

/* Status Table */
.inventory-status-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 1rem;
}

.inventory-status-table th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 1rem;
  text-align: left;
}

.inventory-status-table th:first-child {
  border-top-left-radius: var(--radius);
}

.inventory-status-table th:last-child {
  border-top-right-radius: var(--radius);
}

.inventory-status-table td {
  padding: 1rem;
  border-top: 1px solid var(--gray-200);
  vertical-align: middle;
}

.inventory-status-table tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius);
}

.inventory-status-table tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius);
}

.inventory-status-table tr:hover {
  background-color: var(--gray-100);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  letter-spacing: 0.03em;
}

.status-badge.completed {
  background-color: rgba(16, 185, 129, 0.15);
  color: var(--success);
}

.status-badge.completed::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success);
  margin-right: 0.5rem;
}

.status-badge.pending {
  background-color: rgba(245, 158, 11, 0.15);
  color: var(--warning);
}

.status-badge.pending::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--warning);
  margin-right: 0.5rem;
}

/* View Full Report Button */
.view-report-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--white);
  color: var(--primary);
  border-radius: var(--radius-full);
  font-weight: 500;
  transition: var(--transition);
  text-decoration: none;
  border: 1px solid var(--primary);
  box-shadow: var(--shadow-sm);
}

.view-report-btn:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .monthly-update-stats {
    padding: 1rem;
  }

  .progress-container {
    width: 140px;
    height: 140px;
  }

  .progress-value {
    font-size: 1.75rem;
  }

  .progress-text {
    font-size: 0.875rem;
  }

  .stats-summary {
    flex-direction: column;
    gap: 0.75rem;
  }

  .stat-item {
    padding: 0.75rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .inventory-status-table th,
  .inventory-status-table td {
    padding: 0.75rem;
  }
}

/* Enhanced Monthly Inventory Update Modal */
.inventory-modal {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: none;
  transform: scale(0.98);
  opacity: 0.8;
  transition: var(--transition);
}

.modal-animate-in {
  transform: scale(1);
  opacity: 1;
}

.inventory-modal-header {
  background: var(--primary-gradient);
  color: white;
  padding: 1.5rem;
  border-bottom: none;
  position: relative;
  overflow: hidden;
}

.inventory-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwYXR0ZXJuKSIvPjwvc3ZnPg==');
  opacity: 0.2;
  z-index: 0;
}

.modal-title-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.modal-icon-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.modal-icon-circle i {
  font-size: 1.5rem;
  color: white;
}

.inventory-modal .modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.inventory-modal-body {
  padding: 2rem;
  background-color: var(--white);
}

.inventory-intro {
  margin-bottom: 2rem;
  text-align: center;
  padding: 1rem;
  background-color: var(--gray-50);
  border-radius: var(--radius);
}

.inventory-intro-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.inventory-intro-text {
  color: var(--gray-700);
  margin-bottom: 0;
}

.inventory-alert {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: var(--radius);
  border: none;
  background-color: var(--success-light);
  margin-bottom: 1.5rem;
}

.alert-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--success);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.alert-icon i {
  font-size: 1.25rem;
  color: white;
}

.alert-content {
  flex-grow: 1;
}

.alert-heading {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--success);
  margin-bottom: 0.25rem;
}

.inventory-update-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Inventory Card */
.inventory-card {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition);
  margin-bottom: 1.5rem;
}

.inventory-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-3px);
}

.inventory-card-header {
  background-color: var(--white);
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.inventory-card-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: var(--gray-800);
}

.inventory-card-title i {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-sm);
  background: var(--primary-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  box-shadow: var(--shadow-sm);
}

.inventory-card-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-toggle-items {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  background-color: var(--white);
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: var(--radius-full);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition);
  cursor: pointer;
}

.btn-toggle-items:hover {
  background-color: var(--primary-transparent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-toggle-items.showing-all {
  background-color: var(--primary);
  color: white;
}

.inventory-card-body {
  padding: 0;
}

.inventory-table-container {
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
  position: relative;
}

.inventory-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.inventory-table th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem;
  text-align: left;
  border-bottom: 2px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.inventory-table td {
  padding: 1rem;
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
  font-size: 0.875rem;
  color: var(--gray-700);
}

.inventory-table tr:hover {
  background-color: var(--gray-50);
}

.inventory-table tr.row-changed {
  background-color: var(--primary-transparent);
  transition: background-color 0.3s ease;
}

.inventory-table tr.row-fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.sku-code {
  font-family: monospace;
  font-weight: 600;
  color: var(--gray-800);
  background-color: var(--gray-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-xs);
  font-size: 0.8rem;
}

.category-name {
  font-size: 0.8rem;
  color: var(--gray-600);
  background-color: var(--gray-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full);
}

.current-qty {
  font-weight: 600;
  color: var(--gray-800);
}

.quantity-input-wrapper {
  position: relative;
  transition: var(--transition);
}

.quantity-input-wrapper.input-focused {
  transform: scale(1.05);
}

.quantity-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  font-weight: 600;
  text-align: center;
  transition: var(--transition);
}

.quantity-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: var(--shadow-outline);
}

.quantity-input.qty-changed {
  background-color: var(--primary-transparent);
  border-color: var(--primary);
}

.quantity-input.qty-increased {
  background-color: var(--success-light);
  border-color: var(--success);
  color: var(--success);
}

.quantity-input.qty-decreased {
  background-color: var(--warning-light);
  border-color: var(--warning);
  color: var(--warning);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.4rem 0.75rem;
  font-size: 0.7rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  letter-spacing: 0.03em;
}

.status-badge.status-available {
  background-color: var(--success-light);
  color: var(--success);
}

.status-badge.status-low {
  background-color: var(--warning-light);
  color: var(--warning);
}

.status-badge.status-out {
  background-color: var(--danger-light);
  color: var(--danger);
}

.show-more-cell {
  text-align: center;
  padding: 1rem;
}

.btn-show-more {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.25rem;
  background-color: var(--white);
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: var(--radius-full);
  font-weight: 500;
  font-size: 0.875rem;
  transition: var(--transition);
  cursor: pointer;
}

.btn-show-more:hover {
  background-color: var(--primary-transparent);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Inventory Tip */
.inventory-tip {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background-color: var(--info-light);
  border-radius: var(--radius);
  margin-bottom: 1.5rem;
}

.tip-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--info);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tip-icon i {
  font-size: 1rem;
  color: white;
}

.tip-content {
  flex-grow: 1;
}

.tip-content h6 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--info);
  margin-bottom: 0.25rem;
}

.tip-content p {
  color: var(--gray-700);
  margin-bottom: 0;
  font-size: 0.9rem;
}

/* Notes Input */
.notes-group {
  margin-bottom: 1.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  display: block;
}

.notes-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  transition: var(--transition);
  resize: vertical;
  min-height: 100px;
}

.notes-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: var(--shadow-outline);
}

/* Confirm Checkbox */
.confirm-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--gray-50);
  border-radius: var(--radius);
}

.confirm-input {
  width: 20px;
  height: 20px;
  border-radius: var(--radius-xs);
  border: 2px solid var(--primary);
  cursor: pointer;
  margin-top: 0.25rem;
}

.confirm-label {
  flex-grow: 1;
  font-weight: 500;
  color: var(--gray-700);
  cursor: pointer;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.btn-review {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--white);
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: var(--radius-full);
  font-weight: 600;
  transition: var(--transition);
  text-decoration: none;
}

.btn-review:hover {
  background-color: var(--primary-transparent);
  transform: translateY(-3px);
  box-shadow: var(--shadow-sm);
}

.btn-complete {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  font-weight: 600;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  opacity: 0.7;
}

.btn-complete::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.btn-complete:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.btn-complete-active {
  opacity: 1;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-complete-active:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.btn-complete-active:hover::before {
  transform: translateX(100%);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

.slide-in-up {
  animation: slideInUp 0.5s ease forwards;
}

.pulse {
  animation: pulse 1.5s infinite;
}
