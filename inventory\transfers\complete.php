<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';
require_once '../../includes/websocket_helper.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get transfer ID
$transferId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$transferId) {
    setFlashMessage('error', 'Invalid transfer request');
    header("Location: index.php");
    exit;
}

try {
    // Fetch transfer details with all necessary information
    $stmt = $db->prepare("
        SELECT t.*,
               i.name as item_name, i.sku,
               cat.name as category_name,
               IF(fa.fixed_asset_id IS NOT NULL, 'fixed_asset', 'consumable') as item_type,
               COALESCE(fa.quantity, c.quantity) as current_quantity
        FROM transfers t
        JOIN items i ON t.item_id = i.item_id
        JOIN categories cat ON i.category_id = cat.category_id
        LEFT JOIN fixed_assets fa ON i.item_id = fa.item_id
        LEFT JOIN consumables c ON i.item_id = c.item_id
        WHERE t.transfer_id = ?
    ");
    $stmt->execute([$transferId]);
    $transfer = $stmt->fetch();

    if (!$transfer) {
        throw new Exception('Transfer request not found');
    }

    // Check if transfer can be completed
    if ($transfer['status'] !== 'approved') {
        setFlashMessage('error', 'Only approved transfers can be marked as completed');
        header("Location: view.php?id=" . $transferId);
        exit;
    }

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $db->beginTransaction();

        $currentUser = $auth->getCurrentUser();
        $completionRemarks = isset($_POST['completion_remarks']) ? $_POST['completion_remarks'] : '';
        $receivedBy = isset($_POST['received_by']) ? $_POST['received_by'] : '';
        $receiverDesignation = isset($_POST['receiver_designation']) ? $_POST['receiver_designation'] : '';

        // For consumables, verify quantity is still available
        if ($transfer['item_type'] === 'consumable') {
            if ($transfer['quantity'] > $transfer['current_quantity']) {
                throw new Exception('Insufficient quantity available for transfer');
            }
        }

        // Update transfer status
        $stmt = $db->prepare("
            UPDATE transfers
            SET status = 'completed',
                completed_by = ?,
                completion_date = NOW(),
                completion_remarks = ?,
                received_by = ?,
                receiver_designation = ?
            WHERE transfer_id = ?
        ");
        $stmt->execute([
            $currentUser['user_id'],
            $completionRemarks,
            $receivedBy,
            $receiverDesignation,
            $transferId
        ]);

        // Send WebSocket update
        $webSocketData = [
            'transfer_id' => $transferId,
            'status' => 'completed',
            'requires_himu_approval' => ($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply'),
            'show_notification' => true
        ];

        // Target users based on role and location
        $targetUsers = [];
        $targetRoles = [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS];

        if ($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply') {
            $targetRoles[] = ROLE_HIMU;
        }

        // Get users at source and destination locations
        $locationQuery = "SELECT user_id FROM users WHERE "
            . "(department_id = ? OR department_id = ? OR health_center_id = ? OR health_center_id = ?)";
        $locationStmt = $db->prepare($locationQuery);
        $locationStmt->execute([
            $transfer['source_department_id'],
            $transfer['destination_department_id'],
            $transfer['source_health_center_id'],
            $transfer['destination_health_center_id']
        ]);

        while ($user = $locationStmt->fetch()) {
            $targetUsers[] = $user['user_id'];
        }

        // Send the WebSocket update
        @sendWebSocketTransferUpdate($webSocketData, $targetUsers, $targetRoles);

        // Update item location and quantities
        if ($transfer['item_type'] === 'fixed_asset') {
            // Update fixed asset location
            $stmt = $db->prepare("
                UPDATE fixed_assets
                SET department_id = NULLIF(?, ''),
                    health_center_id = NULLIF(?, '')
                WHERE item_id = ?
            ");
            $stmt->execute([
                $transfer['destination_department_id'],
                $transfer['destination_health_center_id'],
                $transfer['item_id']
            ]);
        } else {
            // For consumables, create stock movement records
            // Deduct from source
            $stmt = $db->prepare("
                INSERT INTO stock_movements (
                    item_id, movement_type, quantity,
                    department_id, health_center_id,
                    reference_number, remarks, created_by
                ) VALUES (?, 'out', ?, NULLIF(?, ''), NULLIF(?, ''), ?, ?, ?)
            ");
            $stmt->execute([
                $transfer['item_id'],
                $transfer['quantity'],
                $transfer['source_department_id'],
                $transfer['source_health_center_id'],
                $transfer['reference_number'],
                'Transfer completed: ' . $transfer['reference_number'],
                $currentUser['user_id']
            ]);

            // Add to destination
            $stmt = $db->prepare("
                INSERT INTO stock_movements (
                    item_id, movement_type, quantity,
                    department_id, health_center_id,
                    reference_number, remarks, created_by
                ) VALUES (?, 'in', ?, NULLIF(?, ''), NULLIF(?, ''), ?, ?, ?)
            ");
            $stmt->execute([
                $transfer['item_id'],
                $transfer['quantity'],
                $transfer['destination_department_id'],
                $transfer['destination_health_center_id'],
                $transfer['reference_number'],
                'Transfer received: ' . $transfer['reference_number'],
                $currentUser['user_id']
            ]);

            // Update consumable quantities
            updateConsumableQuantities($db, $transfer['item_id']);
        }

        // Log the activity
        $auth->logActivity($currentUser['user_id'], 'complete', 'transfers', $transferId);

        $db->commit();
        setFlashMessage('success', 'Transfer marked as completed successfully');
        header("Location: view.php?id=" . $transferId);
        exit;
    }

} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error completing transfer: " . $e->getMessage());
    setFlashMessage('error', 'Error completing transfer: ' . $e->getMessage());
    header("Location: view.php?id=" . $transferId);
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Complete Transfer</h1>
        <div>
            <a href="view.php?id=<?php echo $transferId; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Transfer Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Transfer Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Reference Number:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Item:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['item_name']); ?><br>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['sku']); ?></small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Category:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['category_name']); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Quantity:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo number_format($transfer['quantity']); ?>
                            <?php if ($transfer['item_type'] === 'consumable'): ?>
                                <br><small class="text-muted">Current Stock: <?php echo number_format($transfer['current_quantity']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completion Form -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Completion Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="received_by" class="form-label required">Received By</label>
                            <input type="text" class="form-control" id="received_by" name="received_by" required>
                            <div class="invalid-feedback">
                                Please provide the name of the person who received the item(s).
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="receiver_designation" class="form-label required">Receiver's Designation</label>
                            <input type="text" class="form-control" id="receiver_designation" name="receiver_designation" required>
                            <div class="invalid-feedback">
                                Please provide the receiver's designation.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="completion_remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="completion_remarks" name="completion_remarks" rows="3"></textarea>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary"
                                    onclick="return confirm('Are you sure you want to mark this transfer as completed? This action cannot be undone.')">
                                <i class="fas fa-check-circle"></i> Mark as Completed
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>

<?php require_once '../../templates/footer.php'; ?>
