<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';
require_once '../../includes/websocket_helper.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_HIMU])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get transfer ID
$transferId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$transferId) {
    setFlashMessage('error', 'Invalid transfer request');
    header("Location: index.php");
    exit;
}

try {
    // Fetch transfer details
    $stmt = $db->prepare("
        SELECT t.*,
               i.name as item_name, i.sku,
               cat.name as category_name,
               src_dept.name as source_department,
               src_hc.name as source_health_center,
               dest_dept.name as destination_department,
               dest_hc.name as destination_health_center,
               req_user.full_name as requested_by_name,
               COALESCE(fa.quantity, c.quantity) as current_quantity,
               IF(fa.fixed_asset_id IS NOT NULL, 'fixed_asset', 'consumable') as item_type
        FROM transfers t
        JOIN items i ON t.item_id = i.item_id
        JOIN categories cat ON i.category_id = cat.category_id
        LEFT JOIN departments src_dept ON t.source_department_id = src_dept.department_id
        LEFT JOIN health_centers src_hc ON t.source_health_center_id = src_hc.health_center_id
        LEFT JOIN departments dest_dept ON t.destination_department_id = dest_dept.department_id
        LEFT JOIN health_centers dest_hc ON t.destination_health_center_id = dest_hc.health_center_id
        LEFT JOIN users req_user ON t.requested_by = req_user.user_id
        LEFT JOIN fixed_assets fa ON i.item_id = fa.item_id
        LEFT JOIN consumables c ON i.item_id = c.item_id
        WHERE t.transfer_id = ?
    ");
    $stmt->execute([$transferId]);
    $transfer = $stmt->fetch();

    if (!$transfer) {
        throw new Exception('Transfer request not found');
    }

    // Check if user can approve this transfer
    $currentUser = $auth->getCurrentUser();
    if (!canApproveTransfer($currentUser['role'], $transfer)) {
        setFlashMessage('error', 'You do not have permission to approve this transfer');
        header("Location: view.php?id=" . $transferId);
        exit;
    }

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $db->beginTransaction();

        $action = $_POST['action'];
        $remarks = $_POST['remarks'];

        if ($action === 'approve') {
            // For IT equipment/supply, HIMU must approve first
            if (($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply') &&
                $currentUser['role'] === ROLE_HIMU) {
                $newStatus = 'himu_approved';
            } else {
                $newStatus = 'approved';
            }

            // Update transfer status
            $stmt = $db->prepare("
                UPDATE transfers
                SET status = ?,
                    approval_remarks = ?,
                    approved_by = ?,
                    approval_date = NOW()
                WHERE transfer_id = ?
            ");
            $stmt->execute([$newStatus, $remarks, $currentUser['user_id'], $transferId]);

            // Send WebSocket update
            $webSocketData = [
                'transfer_id' => $transferId,
                'status' => $newStatus,
                'requires_himu_approval' => ($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply'),
                'show_notification' => true
            ];

            // Target users based on role and location
            $targetUsers = [$transfer['requested_by']];
            $targetRoles = [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS];

            if ($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply') {
                $targetRoles[] = ROLE_HIMU;
            }

            // Get users at source and destination locations
            $locationQuery = "SELECT user_id FROM users WHERE "
                . "(department_id = ? OR department_id = ? OR health_center_id = ? OR health_center_id = ?)";
            $locationStmt = $db->prepare($locationQuery);
            $locationStmt->execute([
                $transfer['source_department_id'],
                $transfer['destination_department_id'],
                $transfer['source_health_center_id'],
                $transfer['destination_health_center_id']
            ]);

            while ($user = $locationStmt->fetch()) {
                if (!in_array($user['user_id'], $targetUsers)) {
                    $targetUsers[] = $user['user_id'];
                }
            }

            // Send the WebSocket update
            @sendWebSocketTransferUpdate($webSocketData, $targetUsers, $targetRoles);

            // If fully approved, update item location
            if ($newStatus === 'approved') {
                if ($transfer['item_type'] === 'fixed_asset') {
                    $stmt = $db->prepare("
                        UPDATE fixed_assets
                        SET department_id = NULLIF(?, ''),
                            health_center_id = NULLIF(?, '')
                        WHERE item_id = ?
                    ");
                    $stmt->execute([
                        $transfer['destination_department_id'],
                        $transfer['destination_health_center_id'],
                        $transfer['item_id']
                    ]);
                } else {
                    // For consumables, create stock movement records
                    // Deduct from source
                    $stmt = $db->prepare("
                        INSERT INTO stock_movements (
                            item_id, movement_type, quantity,
                            department_id, health_center_id,
                            reference_number, remarks, created_by
                        ) VALUES (?, 'out', ?, NULLIF(?, ''), NULLIF(?, ''), ?, ?, ?)
                    ");
                    $stmt->execute([
                        $transfer['item_id'],
                        $transfer['quantity'],
                        $transfer['source_department_id'],
                        $transfer['source_health_center_id'],
                        $transfer['reference_number'],
                        'Transfer out: ' . $transfer['reference_number'],
                        $currentUser['user_id']
                    ]);

                    // Add to destination
                    $stmt = $db->prepare("
                        INSERT INTO stock_movements (
                            item_id, movement_type, quantity,
                            department_id, health_center_id,
                            reference_number, remarks, created_by
                        ) VALUES (?, 'in', ?, NULLIF(?, ''), NULLIF(?, ''), ?, ?, ?)
                    ");
                    $stmt->execute([
                        $transfer['item_id'],
                        $transfer['quantity'],
                        $transfer['destination_department_id'],
                        $transfer['destination_health_center_id'],
                        $transfer['reference_number'],
                        'Transfer in: ' . $transfer['reference_number'],
                        $currentUser['user_id']
                    ]);

                    // Update consumable quantities
                    updateConsumableQuantities($db, $transfer['item_id']);
                }
            }

            $message = $newStatus === 'approved' ? 'Transfer request approved' : 'Transfer request approved by HIMU';
        } else {
            // Reject transfer
            $stmt = $db->prepare("
                UPDATE transfers
                SET status = 'rejected',
                    approval_remarks = ?,
                    approved_by = ?,
                    approval_date = NOW()
                WHERE transfer_id = ?
            ");
            $stmt->execute([$remarks, $currentUser['user_id'], $transferId]);

            // Send WebSocket update
            $webSocketData = [
                'transfer_id' => $transferId,
                'status' => 'rejected',
                'requires_himu_approval' => ($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply'),
                'show_notification' => true
            ];

            // Target users based on role and location
            $targetUsers = [$transfer['requested_by']];
            $targetRoles = [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS];

            if ($transfer['category_name'] === 'IT Equipment' || $transfer['category_name'] === 'IT Supply') {
                $targetRoles[] = ROLE_HIMU;
            }

            // Send the WebSocket update
            @sendWebSocketTransferUpdate($webSocketData, $targetUsers, $targetRoles);

            $message = 'Transfer request rejected';
        }

        // Log the activity
        $auth->logActivity($currentUser['user_id'], $action, 'transfers', $transferId);

        $db->commit();
        setFlashMessage('success', $message);
        header("Location: view.php?id=" . $transferId);
        exit;
    }

} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error in transfer approval: " . $e->getMessage());
    setFlashMessage('error', 'Error processing transfer request');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Review Transfer Request</h1>
        <div>
            <a href="view.php?id=<?php echo $transferId; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Transfer Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Transfer Details</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Reference Number:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Item:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['item_name']); ?><br>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['sku']); ?></small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Category:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['category_name']); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Quantity:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo number_format($transfer['quantity']); ?>
                            <?php if ($transfer['item_type'] === 'consumable'): ?>
                                <br><small class="text-muted">Current Stock: <?php echo number_format($transfer['current_quantity']); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>From:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php
                            echo $transfer['source_department'] ?
                                htmlspecialchars($transfer['source_department']) :
                                ($transfer['source_health_center'] ?
                                    htmlspecialchars($transfer['source_health_center']) : 'N/A');
                            ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>To:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php
                            echo $transfer['destination_department'] ?
                                htmlspecialchars($transfer['destination_department']) :
                                ($transfer['destination_health_center'] ?
                                    htmlspecialchars($transfer['destination_health_center']) : 'N/A');
                            ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Requested By:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo htmlspecialchars($transfer['requested_by_name']); ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>Request Date:</strong>
                        </div>
                        <div class="col-md-8">
                            <?php echo formatDate($transfer['request_date']); ?>
                        </div>
                    </div>
                    <?php if ($transfer['remarks']): ?>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Request Remarks:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo nl2br(htmlspecialchars($transfer['remarks'])); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Approval Form -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Review Decision</h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="remarks" class="form-label required">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4" required></textarea>
                            <div class="invalid-feedback">
                                Please provide remarks for your decision.
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="approve" class="btn btn-success">
                                <i class="fas fa-check"></i> Approve Transfer
                            </button>
                            <button type="submit" name="action" value="reject" class="btn btn-danger"
                                    onclick="return confirm('Are you sure you want to reject this transfer request?')">
                                <i class="fas fa-times"></i> Reject Transfer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>

<?php
// Helper function to update consumable quantities
function updateConsumableQuantities($db, $itemId) {
    // Calculate total quantity from stock movements
    $stmt = $db->prepare("
        SELECT
            department_id,
            health_center_id,
            SUM(CASE WHEN movement_type = 'in' THEN quantity ELSE -quantity END) as total_quantity
        FROM stock_movements
        WHERE item_id = ?
        GROUP BY department_id, health_center_id
    ");
    $stmt->execute([$itemId]);
    $quantities = $stmt->fetchAll();

    // Update quantities in consumables table
    foreach ($quantities as $qty) {
        $stmt = $db->prepare("
            UPDATE consumables
            SET quantity = ?
            WHERE item_id = ?
            AND department_id = COALESCE(?, department_id)
            AND health_center_id = COALESCE(?, health_center_id)
        ");
        $stmt->execute([
            $qty['total_quantity'],
            $itemId,
            $qty['department_id'],
            $qty['health_center_id']
        ]);
    }
}

require_once '../../templates/footer.php';
?>
