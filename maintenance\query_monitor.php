<?php
// Query Performance Monitoring Tool
// Purpose: Monitor and analyze slow queries
// Access: GODMODE users only

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';

// Security check - only GODMODE admins should access this
if (!isset($_SESSION['user_id']) || !hasRole('godmode')) {
    header("Location: /choims/index.php");
    exit;
}

// Operations tracking
$success = [];
$errors = [];
$slowQueries = [];
$totalQueries = 0;
$avgQueryTime = 0;

// Action handling
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Handle query log actions
if ($action === 'enable_logging') {
    mysqli_query($conn, "SET GLOBAL slow_query_log = 'ON'");
    mysqli_query($conn, "SET GLOBAL long_query_time = 1"); // Log queries taking more than 1 second
    $success[] = "Slow query logging enabled. Queries taking more than 1 second will be logged.";
} elseif ($action === 'disable_logging') {
    mysqli_query($conn, "SET GLOBAL slow_query_log = 'OFF'");
    $success[] = "Slow query logging disabled.";
} elseif ($action === 'clear_logs') {
    // Get slow query log file location
    $result = mysqli_query($conn, "SHOW VARIABLES LIKE 'slow_query_log_file'");
    if ($row = mysqli_fetch_assoc($result)) {
        $logFile = $row['Value'];
        // Create empty file (clearing the log)
        @file_put_contents($logFile, '');
        $success[] = "Slow query log cleared.";
    } else {
        $errors[] = "Could not determine slow query log file location.";
    }
}

// Get slow query log status
$loggingEnabled = false;
$longQueryTime = 0;
$logFile = "";

$result = mysqli_query($conn, "SHOW VARIABLES LIKE 'slow_query_log'");
if ($row = mysqli_fetch_assoc($result)) {
    $loggingEnabled = ($row['Value'] === 'ON');
}

$result = mysqli_query($conn, "SHOW VARIABLES LIKE 'long_query_time'");
if ($row = mysqli_fetch_assoc($result)) {
    $longQueryTime = $row['Value'];
}

$result = mysqli_query($conn, "SHOW VARIABLES LIKE 'slow_query_log_file'");
if ($row = mysqli_fetch_assoc($result)) {
    $logFile = $row['Value'];
}

// Get server performance variables
$queryStats = [];

// Check if performance_schema is enabled
$result = mysqli_query($conn, "SHOW VARIABLES LIKE 'performance_schema'");
$performanceSchemaEnabled = false;
if ($row = mysqli_fetch_assoc($result)) {
    $performanceSchemaEnabled = ($row['Value'] === 'ON');
}

if ($performanceSchemaEnabled) {
    // Get top 20 slowest queries from performance_schema
    $slowQueryResult = mysqli_query($conn, "
        SELECT 
            DIGEST_TEXT as query_text,
            COUNT_STAR as execution_count,
            SUM_TIMER_WAIT/1000000000 as total_time_ms,
            AVG_TIMER_WAIT/1000000000 as avg_time_ms,
            MAX_TIMER_WAIT/1000000000 as max_time_ms
        FROM performance_schema.events_statements_summary_by_digest
        ORDER BY avg_time_ms DESC
        LIMIT 20
    ");
    
    if ($slowQueryResult) {
        while ($row = mysqli_fetch_assoc($slowQueryResult)) {
            $slowQueries[] = $row;
        }
    }
    
    // Get global query stats
    $totalResult = mysqli_query($conn, "
        SELECT 
            SUM(COUNT_STAR) as total_queries,
            SUM(SUM_TIMER_WAIT)/1000000000/SUM(COUNT_STAR) as avg_query_time_ms
        FROM performance_schema.events_statements_summary_by_digest
    ");
    
    if ($totalResult && $row = mysqli_fetch_assoc($totalResult)) {
        $totalQueries = $row['total_queries'];
        $avgQueryTime = $row['avg_query_time_ms'];
    }
    
    // Check if global_status table exists in performance_schema
    $tableExists = false;
    $tableCheck = mysqli_query($conn, "
        SELECT COUNT(*) as table_exists
        FROM information_schema.tables 
        WHERE table_schema = 'performance_schema' 
        AND table_name = 'global_status'
    ");
    
    if ($tableCheck && $row = mysqli_fetch_assoc($tableCheck)) {
        $tableExists = $row['table_exists'] > 0;
    }
    
    // Get additional performance metrics
    if ($tableExists) {
        $perfMetricsQuery = "
            SELECT variable_name, variable_value 
            FROM performance_schema.global_status 
            WHERE variable_name IN (
                'Com_select', 'Com_insert', 'Com_update', 'Com_delete',
                'Threads_connected', 'Threads_running',
                'Created_tmp_tables', 'Created_tmp_disk_tables',
                'Innodb_buffer_pool_read_requests', 'Innodb_buffer_pool_reads'
            )
        ";
    } else {
        // Fallback to SHOW GLOBAL STATUS for all MySQL versions
        $perfMetricsQuery = "
            SHOW GLOBAL STATUS WHERE Variable_name IN (
                'Com_select', 'Com_insert', 'Com_update', 'Com_delete',
                'Threads_connected', 'Threads_running',
                'Created_tmp_tables', 'Created_tmp_disk_tables',
                'Innodb_buffer_pool_read_requests', 'Innodb_buffer_pool_reads'
            )
        ";
    }
    
    $perfResult = mysqli_query($conn, $perfMetricsQuery);
    if ($perfResult) {
        while ($row = mysqli_fetch_assoc($perfResult)) {
            // Normalize variable name (case insensitive)
            $varName = isset($row['variable_name']) ? $row['variable_name'] : $row['Variable_name'];
            $varValue = isset($row['variable_value']) ? $row['variable_value'] : $row['Value'];
            $queryStats[$varName] = $varValue;
        }
    } else {
        // If query fails, try an alternative approach
        $errorMessage = mysqli_error($conn);
        $errors[] = "Failed to fetch performance metrics: " . $errorMessage;
        
        // Fallback to individual SHOW STATUS queries for critical metrics
        $statusVars = [
            'Threads_connected', 'Threads_running',
            'Created_tmp_tables', 'Created_tmp_disk_tables',
            'Com_select', 'Com_insert', 'Com_update', 'Com_delete',
            'Innodb_buffer_pool_read_requests', 'Innodb_buffer_pool_reads'
        ];
        
        foreach ($statusVars as $var) {
            $result = mysqli_query($conn, "SHOW GLOBAL STATUS LIKE '$var'");
            if ($result && $row = mysqli_fetch_assoc($result)) {
                $queryStats[$var] = $row['Value'];
            }
        }
    }
}

// Get table information
$tableResult = mysqli_query($conn, "
    SELECT 
        table_name, 
        table_rows, 
        data_length, 
        index_length
    FROM information_schema.tables
    WHERE table_schema = DATABASE()
    ORDER BY data_length DESC
");

$tables = [];
if ($tableResult) {
    while ($row = mysqli_fetch_assoc($tableResult)) {
        $tables[] = $row;
    }
}

// Calculate buffer pool hit ratio
$hitRatio = 0;
if (isset($queryStats['Innodb_buffer_pool_read_requests']) && 
    isset($queryStats['Innodb_buffer_pool_reads']) && 
    $queryStats['Innodb_buffer_pool_read_requests'] > 0) {
    
    $hitRatio = 100 * (1 - ($queryStats['Innodb_buffer_pool_reads'] / $queryStats['Innodb_buffer_pool_read_requests']));
}

// Include header and display the monitoring interface
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Query Performance Monitor</h1>
        <div>
            <a href="/choims/index.php" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
    
    <?php if (!empty($success)): ?>
        <?php foreach ($success as $message): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
        <?php foreach ($errors as $message): ?>
            <div class="alert alert-danger"><?php echo $message; ?></div>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <div class="row">
        <!-- Slow Query Logging Controls -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Slow Query Logging</h6>
                </div>
                <div class="card-body">
                    <p>Current Status: <strong><?php echo $loggingEnabled ? 'Enabled' : 'Disabled'; ?></strong></p>
                    <p>Threshold: <strong><?php echo $longQueryTime; ?> seconds</strong></p>
                    
                    <form method="post">
                        <?php if (!$loggingEnabled): ?>
                            <input type="hidden" name="action" value="enable_logging">
                            <button type="submit" class="btn btn-success">Enable Logging</button>
                        <?php else: ?>
                            <input type="hidden" name="action" value="disable_logging">
                            <button type="submit" class="btn btn-warning">Disable Logging</button>
                        <?php endif; ?>
                        
                        <button type="submit" name="action" value="clear_logs" class="btn btn-danger">Clear Logs</button>
                    </form>
                    
                    <?php if (!empty($logFile)): ?>
                        <div class="mt-3">
                            <small class="text-muted">Log File: <?php echo $logFile; ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Database Performance Summary -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">Performance Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Queries Tracked</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($totalQueries); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <p>Average Query Time: <strong><?php echo number_format($avgQueryTime, 4); ?> ms</strong></p>
                        
                        <?php if (isset($queryStats['Threads_connected'])): ?>
                            <p>Active Connections: <strong><?php echo $queryStats['Threads_connected']; ?></strong></p>
                        <?php endif; ?>
                        
                        <?php if (isset($queryStats['Threads_running'])): ?>
                            <p>Running Threads: <strong><?php echo $queryStats['Threads_running']; ?></strong></p>
                        <?php endif; ?>
                        
                        <?php if ($hitRatio > 0): ?>
                            <p>Buffer Pool Hit Ratio: <strong><?php echo number_format($hitRatio, 2); ?>%</strong></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Query Statistics -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">Query Operations</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <p>SELECT: <strong><?php echo isset($queryStats['Com_select']) ? number_format($queryStats['Com_select']) : 'N/A'; ?></strong></p>
                            <p>INSERT: <strong><?php echo isset($queryStats['Com_insert']) ? number_format($queryStats['Com_insert']) : 'N/A'; ?></strong></p>
                        </div>
                        <div class="col-6">
                            <p>UPDATE: <strong><?php echo isset($queryStats['Com_update']) ? number_format($queryStats['Com_update']) : 'N/A'; ?></strong></p>
                            <p>DELETE: <strong><?php echo isset($queryStats['Com_delete']) ? number_format($queryStats['Com_delete']) : 'N/A'; ?></strong></p>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <p>Temp Tables: <strong><?php echo isset($queryStats['Created_tmp_tables']) ? number_format($queryStats['Created_tmp_tables']) : 'N/A'; ?></strong></p>
                        <p>Disk Temp Tables: <strong><?php echo isset($queryStats['Created_tmp_disk_tables']) ? number_format($queryStats['Created_tmp_disk_tables']) : 'N/A'; ?></strong></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Slow Queries -->
        <div class="col-12 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Slowest Queries</h6>
                </div>
                <div class="card-body">
                    <?php if ($performanceSchemaEnabled && !empty($slowQueries)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="slowQueriesTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Query</th>
                                        <th>Executions</th>
                                        <th>Avg Time (ms)</th>
                                        <th>Max Time (ms)</th>
                                        <th>Total Time (ms)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($slowQueries as $query): ?>
                                        <tr>
                                            <td>
                                                <div style="max-width: 500px; overflow-x: auto;">
                                                    <code><?php echo htmlspecialchars($query['query_text']); ?></code>
                                                </div>
                                            </td>
                                            <td><?php echo number_format($query['execution_count']); ?></td>
                                            <td><?php echo number_format($query['avg_time_ms'], 2); ?></td>
                                            <td><?php echo number_format($query['max_time_ms'], 2); ?></td>
                                            <td><?php echo number_format($query['total_time_ms'], 2); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php elseif (!$performanceSchemaEnabled): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> Performance Schema is not enabled in your MySQL configuration. 
                            To enable detailed query statistics, add <code>performance_schema=ON</code> to your MySQL configuration file and restart the server.
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            No slow queries recorded yet. Enable slow query logging and run some queries to collect data.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Table Information -->
        <div class="col-12 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Table Statistics</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Table Actions:</div>
                            <a class="dropdown-item" href="/choims/maintenance/db_maintenance.php">Run Full Maintenance</a>
                            <a class="dropdown-item" href="/choims/maintenance/performance_optimize.php">Performance Optimization</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="tableStatsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Table Name</th>
                                    <th>Rows</th>
                                    <th>Data Size</th>
                                    <th>Index Size</th>
                                    <th>Total Size</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tables as $table): ?>
                                    <?php 
                                    $dataSize = round($table['data_length'] / (1024 * 1024), 2);
                                    $indexSize = round($table['index_length'] / (1024 * 1024), 2);
                                    $totalSize = $dataSize + $indexSize;
                                    ?>
                                    <tr>
                                        <td><?php echo $table['table_name']; ?></td>
                                        <td><?php echo number_format($table['table_rows']); ?></td>
                                        <td><?php echo $dataSize; ?> MB</td>
                                        <td><?php echo $indexSize; ?> MB</td>
                                        <td><?php echo $totalSize; ?> MB</td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Performance Recommendations -->
        <div class="col-12 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Performance Recommendations</h6>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <?php if ($hitRatio < 95 && $hitRatio > 0): ?>
                            <li class="list-group-item list-group-item-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i> 
                                Your buffer pool hit ratio is <?php echo number_format($hitRatio, 2); ?>%. Consider increasing innodb_buffer_pool_size for better performance.
                            </li>
                        <?php endif; ?>
                        
                        <?php if (isset($queryStats['Created_tmp_disk_tables']) && isset($queryStats['Created_tmp_tables']) &&
                                  $queryStats['Created_tmp_tables'] > 0 && 
                                  ($queryStats['Created_tmp_disk_tables'] / $queryStats['Created_tmp_tables']) > 0.25): ?>
                            <li class="list-group-item list-group-item-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i> 
                                Over 25% of temporary tables are created on disk. Consider increasing tmp_table_size and max_heap_table_size.
                            </li>
                        <?php endif; ?>
                        
                        <?php if (isset($queryStats['Threads_connected']) && $queryStats['Threads_connected'] > 50): ?>
                            <li class="list-group-item list-group-item-info">
                                <i class="fas fa-info-circle me-2"></i> 
                                You have <?php echo $queryStats['Threads_connected']; ?> active connections. Monitor for connection bottlenecks if this number grows.
                            </li>
                        <?php endif; ?>
                        
                        <li class="list-group-item list-group-item-info">
                            <i class="fas fa-info-circle me-2"></i> 
                            Add indexes to columns frequently used in WHERE, JOIN, and ORDER BY clauses.
                        </li>
                        
                        <li class="list-group-item list-group-item-info">
                            <i class="fas fa-info-circle me-2"></i> 
                            Implement caching for frequently accessed data to reduce database load.
                        </li>
                        
                        <li class="list-group-item list-group-item-info">
                            <i class="fas fa-info-circle me-2"></i> 
                            Ensure all list views use pagination with a reasonable page size (20-50 items).
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#slowQueriesTable').DataTable({
        "order": [[2, "desc"]], // Order by avg time column
        "pageLength": 10
    });
    
    $('#tableStatsTable').DataTable({
        "order": [[4, "desc"]], // Order by total size column
        "pageLength": 10
    });
});
</script>

<?php include_once '../includes/footer.php'; ?> 