/**
 * Mobile Form Optimizations
 * This file provides enhancements for forms on mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth < 768;
    
    if (isMobile) {
        optimizeFormsForMobile();
    }
    
    // Listen for window resize to apply mobile optimizations when needed
    window.addEventListener('resize', function() {
        const wasMobile = isMobile;
        const isMobileNow = window.innerWidth < 768;
        
        // Only reinitialize if we've crossed the mobile threshold
        if (wasMobile !== isMobileNow && isMobileNow) {
            optimizeFormsForMobile();
        }
    });
});

/**
 * Apply mobile-specific optimizations to forms
 */
function optimizeFormsForMobile() {
    // Find all forms
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        // Add mobile-specific class
        form.classList.add('mobile-optimized-form');
        
        // Optimize form layout
        optimizeFormLayout(form);
        
        // Enhance form controls
        enhanceFormControls(form);
        
        // Optimize form buttons
        optimizeFormButtons(form);
    });
    
    // Add mobile-specific styles
    addMobileFormStyles();
}

/**
 * Optimize form layout for mobile
 */
function optimizeFormLayout(form) {
    // Find form rows and columns
    const formRows = form.querySelectorAll('.form-row, .row');
    
    formRows.forEach(row => {
        // Add mobile-specific class
        row.classList.add('mobile-form-row');
        
        // Find columns
        const cols = row.querySelectorAll('[class*="col-"]');
        
        cols.forEach(col => {
            // Add mobile-specific class
            col.classList.add('mobile-form-col');
        });
    });
    
    // Find form groups
    const formGroups = form.querySelectorAll('.form-group, .mb-3');
    
    formGroups.forEach(group => {
        // Add mobile-specific class
        group.classList.add('mobile-form-group');
    });
}

/**
 * Enhance form controls for mobile
 */
function enhanceFormControls(form) {
    // Find all form controls
    const controls = form.querySelectorAll('input, select, textarea');
    
    controls.forEach(control => {
        // Add mobile-specific class
        control.classList.add('mobile-form-control');
        
        // Increase touch target size
        if (control.tagName === 'INPUT' && 
            (control.type === 'checkbox' || control.type === 'radio')) {
            // Create a wrapper for checkboxes and radios
            const wrapper = document.createElement('div');
            wrapper.className = 'mobile-input-wrapper';
            
            // Insert wrapper before the control
            control.parentNode.insertBefore(wrapper, control);
            
            // Move control into wrapper
            wrapper.appendChild(control);
            
            // Find associated label
            const id = control.id;
            if (id) {
                const label = document.querySelector(`label[for="${id}"]`);
                if (label) {
                    // Move label into wrapper
                    wrapper.appendChild(label);
                    
                    // Add mobile-specific class
                    label.classList.add('mobile-form-label');
                }
            }
        } else {
            // For other input types
            control.style.height = 'auto';
            control.style.minHeight = '44px';
            
            // Prevent iOS zoom on focus for text inputs
            if (control.tagName === 'INPUT' && 
                (control.type === 'text' || control.type === 'email' || 
                 control.type === 'password' || control.type === 'number' || 
                 control.type === 'search' || control.type === 'tel' || 
                 control.type === 'url')) {
                control.style.fontSize = '16px';
            }
        }
    });
    
    // Find all labels
    const labels = form.querySelectorAll('label:not(.mobile-form-label)');
    
    labels.forEach(label => {
        // Add mobile-specific class
        label.classList.add('mobile-form-label');
    });
}

/**
 * Optimize form buttons for mobile
 */
function optimizeFormButtons(form) {
    // Find all buttons
    const buttons = form.querySelectorAll('button, .btn');
    
    buttons.forEach(button => {
        // Add mobile-specific class
        button.classList.add('mobile-form-button');
        
        // Increase touch target size
        button.style.minHeight = '44px';
    });
    
    // Find button groups
    const buttonGroups = form.querySelectorAll('.btn-group');
    
    buttonGroups.forEach(group => {
        // Add mobile-specific class
        group.classList.add('mobile-button-group');
    });
}

/**
 * Add mobile-specific styles for forms
 */
function addMobileFormStyles() {
    // Create style element if it doesn't exist
    if (!document.getElementById('mobile-form-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-form-styles';
        
        style.textContent = `
            @media (max-width: 767.98px) {
                .mobile-optimized-form {
                    margin-bottom: 1rem;
                }
                
                .mobile-form-row {
                    display: flex;
                    flex-direction: column;
                    margin-right: 0;
                    margin-left: 0;
                }
                
                .mobile-form-col {
                    flex: 0 0 100%;
                    max-width: 100%;
                    padding-right: 0;
                    padding-left: 0;
                }
                
                .mobile-form-group {
                    margin-bottom: 1rem;
                }
                
                .mobile-form-control {
                    width: 100%;
                    min-height: 44px;
                    font-size: 16px !important;
                    padding: 0.5rem 0.75rem;
                }
                
                .mobile-form-label {
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: 500;
                }
                
                .mobile-input-wrapper {
                    display: flex;
                    align-items: center;
                    min-height: 44px;
                    margin-bottom: 0.5rem;
                }
                
                .mobile-input-wrapper input[type="checkbox"],
                .mobile-input-wrapper input[type="radio"] {
                    width: 24px;
                    height: 24px;
                    margin-right: 0.5rem;
                }
                
                .mobile-form-button {
                    display: block;
                    width: 100%;
                    min-height: 44px;
                    margin-bottom: 0.5rem;
                    padding: 0.5rem 1rem;
                }
                
                .mobile-button-group {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                }
                
                .mobile-button-group .btn {
                    width: 100%;
                    margin-bottom: 0.5rem;
                    border-radius: 0.25rem !important;
                }
                
                /* Fix for select2 and other custom select widgets */
                .select2-container {
                    width: 100% !important;
                }
                
                .select2-container .select2-selection {
                    min-height: 44px !important;
                }
                
                .select2-container .select2-selection__rendered {
                    line-height: 44px !important;
                }
                
                /* Fix for datepicker and other custom date widgets */
                .datepicker-dropdown {
                    width: 280px !important;
                    left: 50% !important;
                    transform: translateX(-50%) !important;
                }
                
                .datepicker table {
                    width: 100%;
                }
                
                .datepicker td, .datepicker th {
                    width: 40px;
                    height: 40px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }
}
