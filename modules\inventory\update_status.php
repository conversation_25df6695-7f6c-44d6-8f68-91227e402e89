<?php
// Start output buffering
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/audit_log.php');

// Ensure user is logged in
requireLogin();

// Get inventory ID and new status from URL parameters
$inventory_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$new_status = isset($_POST['status']) ? sanitizeInput($_POST['status']) : '';
$reason = isset($_POST['reason']) ? sanitizeInput($_POST['reason']) : '';
$redirect_url = isset($_POST['redirect_url']) ? $_POST['redirect_url'] : "/choims/modules/inventory/view.php?id=$inventory_id";

// Validate inputs
if ($inventory_id <= 0) {
    $_SESSION['error'] = "Invalid inventory ID";
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

if (empty($new_status)) {
    $_SESSION['error'] = "Status cannot be empty";
    header("Location: /choims/modules/inventory/view.php?id=$inventory_id");
    exit();
}

// Validate reason
if (empty($reason)) {
    $_SESSION['error'] = "Reason for status change is required";
    header("Location: /choims/modules/inventory/view.php?id=$inventory_id");
    exit();
}

// Validate status value
$allowed_statuses = ['Available', 'Low Stock', 'Out of Stock'];
if (!in_array($new_status, $allowed_statuses)) {
    $_SESSION['error'] = "Invalid status value";
    header("Location: /choims/modules/inventory/view.php?id=$inventory_id");
    exit();
}

// Check if the inventory item exists and user has permission to update it
$query = "SELECT ci.*, l.location_id FROM consumable_inventory ci JOIN locations l ON ci.location_id = l.location_id WHERE ci.inventory_id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $inventory_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['error'] = "Inventory item not found";
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

$inventory = mysqli_fetch_assoc($result);

// Check user permission (all roles can update status of items in their location)
$user_location_id = getUserLocationId();

// For logistics users, ensure they can only update items in their location
// For other non-godmode/superadmin roles, ensure they can only update items in their location
if ((hasRole('Logistics') && $inventory['location_id'] != $user_location_id) ||
    (!hasRole('GodMode', 'Superadmin', 'Logistics') && $inventory['location_id'] != $user_location_id)) {
    $_SESSION['error'] = "You don't have permission to update this inventory item";
    header("Location: /choims/modules/inventory/view.php?id=$inventory_id");
    exit();
}

// Get the old status value for audit log
$old_status = $inventory['status'];

// Update the inventory status
$update_query = "UPDATE consumable_inventory SET status = ?, updated_at = NOW() WHERE inventory_id = ?";
$update_stmt = mysqli_prepare($conn, $update_query);
mysqli_stmt_bind_param($update_stmt, 'si', $new_status, $inventory_id);

if (mysqli_stmt_execute($update_stmt)) {
    // Add to audit log with reason
    logAction($conn, $_SESSION['user_id'], "Update Status", "consumable_inventory", $inventory_id,
              json_encode(["status" => $old_status]),
              json_encode(["status" => $new_status, "reason" => $reason]));

    $_SESSION['success'] = "Inventory status updated successfully";
} else {
    $_SESSION['error'] = "Failed to update inventory status: " . mysqli_error($conn);
}

// Clear output buffer before redirect
ob_end_clean();

// Redirect back to the inventory view or specified redirect URL
header("Location: $redirect_url");
exit();