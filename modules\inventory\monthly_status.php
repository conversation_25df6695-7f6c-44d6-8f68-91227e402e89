<?php
// Monthly Inventory Status Handler
require_once '../../includes/header.php';

// Only allow logistics, superadmin, and godmode users to view all statuses
if (!hasRole('Logistics', 'Superadmin', 'GodMode')) {
    $_SESSION['error'] = "You don't have permission to view monthly inventory status.";
    header('Location: /choims/index.php');
    exit();
}

// Get current month and year or use specified values
$month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

// Validate month and year
if ($month < 1 || $month > 12) {
    $month = date('n');
}
if ($year < 2000 || $year > 2100) {
    $year = date('Y');
}

// Get all locations' status
$locations = getAllMonthlyInventoryUpdateStatus($conn, $month, $year);

// Calculate statistics
$totalLocations = count($locations);
$completedLocations = 0;
$pendingLocations = 0;

foreach ($locations as $location) {
    if ($location['status'] === MONTHLY_UPDATE_STATUS_COMPLETED) {
        $completedLocations++;
    } else {
        $pendingLocations++;
    }
}

$completionPercentage = $totalLocations > 0 ? round(($completedLocations / $totalLocations) * 100) : 0;

// Set page title for browser tab
$pageTitle = "Monthly Consumables Update Status";
$monthYearTitle = date('F Y', mktime(0, 0, 0, $month, 1, $year));

// Add custom CSS - original first, then our green theme to override
echo '<link rel="stylesheet" href="/choims/assets/css/monthly-status.css">';
// Add green theme CSS with higher priority
echo '<link rel="stylesheet" href="/choims/assets/css/monthly-status-green.css">';
// Add inline styles for additional customization
echo '<style>
body {
    background-color: #f8fafc;
}
.page-title h1 {
    color: #2E7D32;
}
.page-title i {
    color: #4CAF50;
}
.text-muted {
    color: #64748b !important;
}
</style>';
?>

<!-- Page Title -->
<div class="page-title fade-in">
    <h1 class="h3 mb-0">
        <i class="fas fa-calendar-check me-2"></i> Monthly Consumables Update Status
    </h1>
    <p class="lead">
        Tracking consumables updates for <span class="fw-bold"><?php echo $monthYearTitle; ?></span>
    </p>
</div>

<div class="d-flex justify-content-between align-items-center mb-4 fade-in" style="animation-delay: 0.1s;">
    <div class="filter-buttons">
        <form method="get" action="" class="d-flex gap-2">
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-calendar-alt"></i>
                </span>
                <select name="month" class="form-select border-start-0 border-end-0">
                    <?php for ($m = 1; $m <= 12; $m++): ?>
                        <option value="<?php echo $m; ?>" <?php echo $m == $month ? 'selected' : ''; ?>>
                            <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                        </option>
                    <?php endfor; ?>
                </select>
                <select name="year" class="form-select border-start-0 border-end-0">
                    <?php for ($y = date('Y') - 2; $y <= date('Y') + 1; $y++): ?>
                        <option value="<?php echo $y; ?>" <?php echo $y == $year ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
                <button type="submit" class="btn btn-dark">
                    <i class="fas fa-filter me-1"></i> Apply
                </button>
            </div>
        </form>
    </div>

    <div>
        <a href="/choims/dashboards/superadmin.php" class="btn btn-outline-dark">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4 fade-in" style="animation-delay: 0.3s;">
        <div class="stat-card">
            <div class="card-body">
                <div class="stat-label">Total Locations</div>
                <div class="stat-value"><?php echo $totalLocations; ?></div>
                <a href="#" class="stat-link" id="btnShowAll">View All Locations <i class="fas fa-arrow-right ms-1"></i></a>
                <div class="stat-icon-wrapper">
                    <i class="fas fa-hospital-alt"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 fade-in" style="animation-delay: 0.4s;">
        <div class="stat-card">
            <div class="card-body">
                <div class="stat-label">Completed Updates</div>
                <div class="stat-value"><?php echo $completedLocations; ?></div>
                <a href="#" class="stat-link" id="btnShowCompletedCard">View Completed <i class="fas fa-arrow-right ms-1"></i></a>
                <div class="stat-icon-wrapper">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 fade-in" style="animation-delay: 0.5s;">
        <div class="stat-card">
            <div class="card-body">
                <div class="stat-label">Completion Rate</div>
                <div class="stat-value"><?php echo $completionPercentage; ?>%</div>
                <div class="progress mt-2 mb-2">
                    <div class="progress-bar bg-dark" role="progressbar" style="width: <?php echo $completionPercentage; ?>%"></div>
                </div>
                <a href="#" class="stat-link" id="btnShowPendingCard">View Pending <i class="fas fa-arrow-right ms-1"></i></a>
                <div class="stat-icon-wrapper">
                    <i class="fas fa-percentage"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Locations Table -->
<div class="card content-card mb-4 fade-in" style="animation-delay: 0.6s;">
    <div class="card-header" style="background-color: #ffffff;">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-clipboard-list me-2"></i> Location Status
            </h5>
            <div class="filter-buttons">
                <button class="btn btn-sm btn-outline-dark" id="btnShowAll">
                    <i class="fas fa-list me-1"></i> All
                </button>
                <button class="btn btn-sm btn-outline-dark" id="btnShowCompleted">
                    <i class="fas fa-check-circle me-1"></i> Completed
                </button>
                <button class="btn btn-sm btn-outline-dark" id="btnShowPending">
                    <i class="fas fa-clock me-1"></i> Pending
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table" id="statusTable">
                <thead style="background-color: #ffffff !important;">
                    <tr>
                        <th style="background-color: #ffffff !important;">Location</th>
                        <th style="background-color: #ffffff !important;">Type</th>
                        <th style="background-color: #ffffff !important;">Status</th>
                        <th style="background-color: #ffffff !important;">Updated By</th>
                        <th style="background-color: #ffffff !important;">Updated At</th>
                        <th style="background-color: #ffffff !important;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($locations as $location): ?>
                        <tr class="status-row <?php echo $location['status'] === MONTHLY_UPDATE_STATUS_COMPLETED ? 'completed' : 'pending'; ?>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-2">
                                        <?php if ($location['status'] === MONTHLY_UPDATE_STATUS_COMPLETED): ?>
                                            <i class="fas fa-check-circle"></i>
                                        <?php else: ?>
                                            <i class="fas fa-clock text-secondary"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($location['location_name']); ?></strong>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($location['location_type']); ?></td>
                            <td>
                                <?php if ($location['status'] === MONTHLY_UPDATE_STATUS_COMPLETED): ?>
                                    <span class="badge bg-dark">Completed</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Pending</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($location['updated_by_name']): ?>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-edit me-2 text-muted"></i>
                                        <?php echo htmlspecialchars($location['updated_by_name']); ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($location['updated_at']): ?>
                                    <div class="d-flex align-items-center">
                                        <i class="far fa-calendar-alt me-2 text-muted"></i>
                                        <?php echo formatDateTime($location['updated_at']); ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">N/A</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="/choims/modules/inventory/list.php?location=<?php echo $location['location_id']; ?>" class="btn btn-sm btn-dark">
                                        <i class="fas fa-eye"></i> View Consumables
                                    </a>

                                    <?php if ($location['status'] === MONTHLY_UPDATE_STATUS_COMPLETED): ?>
                                        <a href="/choims/modules/reports/inventory_changes.php?location_id=<?php echo $location['location_id']; ?>&month=<?php echo $month; ?>&year=<?php echo $year; ?>" class="btn btn-sm btn-outline-dark">
                                            <i class="fas fa-chart-bar"></i> View Report
                                        </a>
                                    <?php endif; ?>

                                    <?php if ($location['status'] !== MONTHLY_UPDATE_STATUS_COMPLETED && hasRole('GodMode')): ?>
                                        <a href="/choims/modules/inventory/mark_completed.php?location_id=<?php echo $location['location_id']; ?>&month=<?php echo $month; ?>&year=<?php echo $year; ?>" class="btn btn-sm btn-dark">
                                            <i class="fas fa-check"></i> Mark Completed
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter buttons functionality - Table buttons
        document.getElementById('btnShowAll').addEventListener('click', function() {
            filterRows('all');
            highlightButton(this);
        });

        document.getElementById('btnShowCompleted').addEventListener('click', function() {
            filterRows('completed');
            highlightButton(this);
        });

        document.getElementById('btnShowPending').addEventListener('click', function() {
            filterRows('pending');
            highlightButton(this);
        });

        // Card links
        document.getElementById('btnShowAll').addEventListener('click', function() {
            filterRows('all');
            highlightButton(document.getElementById('btnShowAll'));
        });

        document.getElementById('btnShowCompletedCard').addEventListener('click', function(e) {
            e.preventDefault();
            filterRows('completed');
            highlightButton(document.getElementById('btnShowCompleted'));
        });

        document.getElementById('btnShowPendingCard').addEventListener('click', function(e) {
            e.preventDefault();
            filterRows('pending');
            highlightButton(document.getElementById('btnShowPending'));
        });

        // Helper functions
        function filterRows(type) {
            document.querySelectorAll('.status-row').forEach(row => {
                if (type === 'all') {
                    row.style.display = '';
                } else if (type === 'completed' && row.classList.contains('completed')) {
                    row.style.display = '';
                } else if (type === 'pending' && row.classList.contains('pending')) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function highlightButton(activeButton) {
            // Remove active class from all buttons
            document.querySelectorAll('.filter-buttons .btn').forEach(btn => {
                btn.classList.remove('active');

                // Reset styles
                if (btn.classList.contains('btn-outline-dark')) {
                    btn.classList.add('btn-outline-dark');
                    btn.classList.remove('btn-dark');
                }
            });

            // Add active class to clicked button
            activeButton.classList.add('active');

            // Change button style based on its type
            if (activeButton.classList.contains('btn-outline-dark')) {
                activeButton.classList.remove('btn-outline-dark');
                activeButton.classList.add('btn-dark');
            }
        }

        // Add ripple effect to buttons
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                const x = e.clientX - e.target.getBoundingClientRect().left;
                const y = e.clientY - e.target.getBoundingClientRect().top;

                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    });
</script>

<style>
/* Ripple effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.7);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
}

@keyframes ripple {
    to {
        transform: translate(-50%, -50%) scale(4);
        opacity: 0;
    }
}

/* Force white table headers and card header */
#statusTable thead,
#statusTable thead tr,
#statusTable thead th {
    background-color: #ffffff !important;
    color: #000000 !important;
}

/* Override any Bootstrap or other styles */
.table > thead > tr > th,
.table > thead > tr > td {
    background-color: #ffffff !important;
}

/* Override the blue gradient in monthly-status.css */
.content-card .card-header,
.card-header,
div.card-header {
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    color: #000000 !important;
    border-bottom: 1px solid #e0e0e0 !important;
}
</style>

<?php
// Include footer
require_once '../../includes/footer.php';
?>
