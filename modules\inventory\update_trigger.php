<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in with appropriate permissions
requireLogin();
if (!hasRole('GodMode', 'Superadmin')) {
    $_SESSION['error'] = "You don't have permission to update database triggers.";
    header("Location: /choims/index.php");
    exit();
}

// SQL to update the trigger
$triggerSQL = "
-- Drop the existing trigger
DROP TRIGGER IF EXISTS update_inventory_status;

-- Create the updated trigger with special handling for box unit type
DELIMITER $$
CREATE TRIGGER update_inventory_status BEFORE UPDATE ON consumable_inventory FOR EACH ROW 
BEGIN
    -- Get the unit of measure for this inventory item
    DECLARE unit_type VARCHAR(20);
    
    SELECT unit_of_measure INTO unit_type
    FROM sku_master
    WHERE sku_id = NEW.sku_id;
    
    -- Special handling for box unit type
    IF unit_type = 'box' THEN
        IF NEW.current_quantity <= 0 THEN
            SET NEW.status = 'Out of Stock';
        ELSEIF NEW.current_quantity <= NEW.low_stock_threshold THEN
            SET NEW.status = 'Low Stock';
        ELSE
            SET NEW.status = 'Available';
        END IF;
    ELSE
        -- Standard handling for other unit types
        IF NEW.current_quantity <= NEW.critical_threshold THEN
            SET NEW.status = 'Out of Stock';
        ELSEIF NEW.current_quantity <= NEW.low_stock_threshold THEN
            SET NEW.status = 'Low Stock';
        ELSE
            SET NEW.status = 'Available';
        END IF;
    END IF;
END
$$
DELIMITER ;
";

// Execute the SQL
$success = false;
$error = '';

try {
    // First drop the existing trigger
    $dropTriggerSQL = "DROP TRIGGER IF EXISTS update_inventory_status";
    if (!mysqli_query($conn, $dropTriggerSQL)) {
        throw new Exception("Error dropping existing trigger: " . mysqli_error($conn));
    }
    
    // Then create the new trigger (without DELIMITER statements which don't work in mysqli_query)
    $createTriggerSQL = "
    CREATE TRIGGER update_inventory_status BEFORE UPDATE ON consumable_inventory FOR EACH ROW 
    BEGIN
        -- Get the unit of measure for this inventory item
        DECLARE unit_type VARCHAR(20);
        
        SELECT unit_of_measure INTO unit_type
        FROM sku_master
        WHERE sku_id = NEW.sku_id;
        
        -- Special handling for box unit type
        IF unit_type = 'box' THEN
            IF NEW.current_quantity <= 0 THEN
                SET NEW.status = 'Out of Stock';
            ELSEIF NEW.current_quantity <= NEW.low_stock_threshold THEN
                SET NEW.status = 'Low Stock';
            ELSE
                SET NEW.status = 'Available';
            END IF;
        ELSE
            -- Standard handling for other unit types
            IF NEW.current_quantity <= NEW.critical_threshold THEN
                SET NEW.status = 'Out of Stock';
            ELSEIF NEW.current_quantity <= NEW.low_stock_threshold THEN
                SET NEW.status = 'Low Stock';
            ELSE
                SET NEW.status = 'Available';
            END IF;
        END IF;
    END
    ";
    
    if (!mysqli_query($conn, $createTriggerSQL)) {
        throw new Exception("Error creating new trigger: " . mysqli_error($conn));
    }
    
    $success = true;
    
    // Log the action
    logDetailedAction($conn, $_SESSION['user_id'], 'update', 'database_trigger', 0, [
        'entity_name' => 'update_inventory_status',
        'changes_summary' => "Updated inventory status trigger to handle box unit type differently",
        'new_values' => json_encode(['trigger_sql' => $createTriggerSQL])
    ]);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Database Trigger</title>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4>Update Database Trigger</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                The inventory status trigger has been successfully updated to handle box unit type differently.
                            </div>
                            <p>Now products with unit type "box" will only be marked as "Out of Stock" when their quantity is 0.</p>
                            <p>Products with 1 box will be properly shown as "Available" or "Low Stock" based on the low stock threshold.</p>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Error updating trigger: <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <a href="/choims/modules/inventory/list.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i> Return to Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>
