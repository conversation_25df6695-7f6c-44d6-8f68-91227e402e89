/* Bold & Creative Assets Report Page Styling */
:root {
  /* Bold Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --accent: #FF5722;
  --accent-light: #FF8A65;
  --success: #00C853;
  --warning: #FFD600;
  --danger: #F44336;
  --info: #00B0FF;
  --white: #ffffff;
  --dark: #1e293b;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Gradients */
  --primary-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
  --accent-gradient: linear-gradient(135deg, var(--accent), var(--accent-light));
  --success-gradient: linear-gradient(135deg, var(--success), #69F0AE);
  --warning-gradient: linear-gradient(135deg, var(--warning), #FFFF00);
  --danger-gradient: linear-gradient(135deg, var(--danger), #FF8A80);
  --info-gradient: linear-gradient(135deg, var(--info), #80D8FF);

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 15px 25px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15);

  /* 3D Effects */
  --shadow-3d: 0 8px 24px rgba(0, 0, 0, 0.12), 0 16px 32px rgba(0, 0, 0, 0.1);
  --shadow-3d-hover: 0 16px 32px rgba(0, 0, 0, 0.15), 0 24px 48px rgba(0, 0, 0, 0.12);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.5rem;
  --radius-xl: 2rem;
  --radius-full: 9999px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  --space-10: 4rem;

  /* Transitions */
  --transition-fast: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Page Container */
.assets-report-container {
  padding: var(--space-5) var(--space-4);
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding: var(--space-5);
  background: var(--primary-gradient);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-3d);
  position: relative;
  overflow: hidden;
  animation: scaleIn 0.5s ease-out;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/choims/assets/img/pattern.svg');
  background-size: cover;
  opacity: 0.1;
  z-index: 0;
}

.page-header > div {
  position: relative;
  z-index: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--white);
  margin-bottom: var(--space-2);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  font-size: 2.2rem;
  background: var(--white);
  color: var(--primary);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow);
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin-bottom: 0;
  font-weight: 500;
  max-width: 600px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-3);
}

.btn {
  font-weight: 600;
  padding: 0.8rem 1.8rem;
  border-radius: var(--radius-md);
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  box-shadow: var(--shadow);
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: var(--transition);
}

.btn:hover::after {
  opacity: 1;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(1px);
}

.btn-sm {
  padding: 0.5rem 1.2rem;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--white);
  color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--white);
  color: var(--primary-dark);
}

.btn-success {
  background: var(--success-gradient);
  color: var(--white);
}

.btn-info {
  background: var(--info-gradient);
  color: var(--white);
}

.btn i {
  font-size: 1.2em;
}

/* Cards */
.card {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-3d);
  transition: var(--transition);
  overflow: hidden;
  background: var(--white);
  position: relative;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-3d-hover);
}

.card-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--primary), transparent);
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--accent);
  font-size: 1.4rem;
}

.card-body {
  padding: var(--space-5);
}

/* Stat Cards */
.stat-card {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-bounce);
  background: var(--white);
  box-shadow: var(--shadow-3d);
  animation: fadeIn 0.5s ease-out forwards;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-3d-hover);
}

.stat-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8px;
  border-radius: 10px 0 0 10px;
}

.stat-card.primary::before {
  background: var(--primary-gradient);
}

.stat-card.success::before {
  background: var(--success-gradient);
}

.stat-card.warning::before {
  background: var(--warning-gradient);
}

.stat-card.danger::before {
  background: var(--danger-gradient);
}

.stat-card .card-body {
  padding: var(--space-5);
  position: relative;
  z-index: 1;
}

.stat-card .icon-circle {
  height: 4rem;
  width: 4rem;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-bounce);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.stat-card:hover .icon-circle {
  transform: scale(1.1) rotate(5deg);
}

.stat-card.primary .icon-circle {
  background: var(--primary-gradient);
  color: var(--white);
}

.stat-card.success .icon-circle {
  background: var(--success-gradient);
  color: var(--white);
}

.stat-card.warning .icon-circle {
  background: var(--warning-gradient);
  color: var(--white);
}

.stat-card.danger .icon-circle {
  background: var(--danger-gradient);
  color: var(--white);
}

.stat-card .icon-circle i {
  font-size: 1.8rem;
  position: relative;
  z-index: 1;
}

.stat-card .text-xs {
  font-size: 0.875rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
}

.stat-card .h3 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.stat-card.primary .text-xs {
  color: var(--primary);
}

.stat-card.success .text-xs {
  color: var(--success);
}

.stat-card.warning .text-xs {
  color: var(--warning);
}

.stat-card.danger .text-xs {
  color: var(--danger);
}

/* Chart Cards */
.chart-card {
  height: 100%;
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: var(--space-3);
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  transition: var(--transition);
}

.legend-item:hover {
  transform: translateX(5px);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

/* Filter Card */
.filter-card {
  margin-bottom: var(--space-5);
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: 0.1s;
  opacity: 0;
}

.filter-card .card-body {
  padding: var(--space-4);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  align-items: flex-end;
}

.filter-form .form-group {
  flex: 1;
  min-width: 200px;
}

.filter-form .btn {
  height: 42px;
}

.filter-card .form-control,
.filter-card .form-select {
  border-radius: var(--radius);
  padding: 0.6rem 1rem;
  border: 2px solid var(--gray-300);
  font-size: 0.95rem;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  background-color: var(--white);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.filter-card .form-label {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.filter-card .input-group .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Quick Filter Buttons */
.quick-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.quick-filter-btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  background: var(--gray-100);
  border: none;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.quick-filter-btn:hover {
  background: var(--primary-bg);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.quick-filter-btn.active {
  background: var(--primary);
  color: var(--white);
}

/* Table Styling */
.table-card {
  margin-top: var(--space-5);
  animation: fadeIn 0.5s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

.table {
  width: 100%;
  margin-bottom: 0;
}

.table th {
  background: linear-gradient(to right, rgba(46, 125, 50, 0.1), rgba(46, 125, 50, 0.05));
  border-top: none;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: var(--space-4);
  color: var(--primary-dark);
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--primary-gradient);
  opacity: 0.5;
}

.table td {
  padding: var(--space-4);
  vertical-align: middle;
  border-top: none;
  border-bottom: 1px solid var(--gray-200);
  font-size: 0.95rem;
}

.table-hover tbody tr {
  transition: var(--transition);
  position: relative;
}

.table-hover tbody tr:hover {
  background-color: rgba(46, 125, 50, 0.05);
  transform: translateX(5px);
}

.table-hover tbody tr:hover td:first-child {
  position: relative;
}

.table-hover tbody tr:hover td:first-child::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-gradient);
}

.table a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  position: relative;
}

.table a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: var(--transition);
}

.table a:hover {
  color: var(--primary-dark);
}

.table a:hover::after {
  width: 100%;
}

/* Status Badges */
.badge {
  padding: 0.5em 0.8em;
  font-weight: 600;
  font-size: 0.75rem;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.badge-success {
  background: var(--success-gradient);
  color: var(--white);
}

.badge-warning {
  background: var(--warning-gradient);
  color: var(--dark);
}

.badge-danger {
  background: var(--danger-gradient);
  color: var(--white);
}

.badge-info {
  background: var(--info-gradient);
  color: var(--white);
}

.badge-primary {
  background: var(--primary-gradient);
  color: var(--white);
}

.badge-secondary {
  background: linear-gradient(135deg, #607D8B, #90A4AE);
  color: var(--white);
}

.badge-info {
  background: var(--info-gradient);
  color: var(--white);
}

/* DataTables Custom Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: var(--space-4);
}

.dataTables_wrapper .dataTables_length select {
  border: 2px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.4rem 0.8rem;
  margin: 0 var(--space-2);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.dataTables_wrapper .dataTables_length select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.dataTables_wrapper .dataTables_filter input {
  border: 2px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.6rem 1rem;
  margin-left: var(--space-2);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  width: 250px;
}

.dataTables_wrapper .dataTables_filter input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 0.5rem 1rem;
  margin: 0 var(--space-1);
  border-radius: var(--radius);
  border: none !important;
  background: var(--gray-100) !important;
  color: var(--gray-700) !important;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--primary-bg) !important;
  color: var(--primary) !important;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--primary-gradient) !important;
  color: var(--white) !important;
  border: none !important;
}

.dataTables_wrapper .dataTables_info {
  font-size: 0.875rem;
  color: var(--gray-500);
  padding-top: var(--space-4);
}

/* Floating Action Button */
.floating-action-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-bounce);
  z-index: 1000;
  border: none;
  cursor: pointer;
}

.floating-action-btn i {
  font-size: 1.5rem;
}

.floating-action-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: var(--shadow-xl);
}

/* Tooltips */
.custom-tooltip {
  position: relative;
}

.custom-tooltip:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--dark);
  color: var(--white);
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  white-space: nowrap;
  box-shadow: var(--shadow);
  z-index: 1000;
  margin-bottom: var(--space-2);
}

.custom-tooltip:hover::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 8px solid transparent;
  border-top-color: var(--dark);
  margin-bottom: -8px;
  z-index: 1000;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
    padding: var(--space-4);
  }

  .page-title {
    font-size: 2rem;
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-start;
  }

  .card-header {
    padding: var(--space-3) var(--space-4);
  }

  .card-body {
    padding: var(--space-4);
  }

  .stat-card .h3 {
    font-size: 2rem;
  }

  .stat-card .icon-circle {
    width: 3.5rem;
    height: 3.5rem;
  }

  .stat-card .icon-circle i {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .assets-report-container {
    padding: var(--space-4) var(--space-3);
  }

  .page-title {
    font-size: 1.75rem;
  }

  .page-title i {
    width: 40px;
    height: 40px;
    font-size: 1.8rem;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .action-buttons .btn {
    flex: 1;
    min-width: 120px;
  }

  .btn {
    padding: 0.6rem 1.2rem;
  }

  .table td, .table th {
    padding: var(--space-3);
  }

  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_filter,
  .dataTables_wrapper .dataTables_info,
  .dataTables_wrapper .dataTables_paginate {
    text-align: left;
    float: none;
    display: block;
    margin-bottom: var(--space-3);
  }

  .dataTables_wrapper .dataTables_filter input {
    width: 100%;
    margin-left: 0;
    margin-top: var(--space-2);
  }

  .floating-action-btn {
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
  }
}

/* Animation Classes */
.animate__animated {
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeIn;
}

.animate__delay-1 {
  animation-delay: 0.1s;
}

.animate__delay-2 {
  animation-delay: 0.2s;
}

.animate__delay-3 {
  animation-delay: 0.3s;
}

.animate__delay-4 {
  animation-delay: 0.4s;
}

.animate__delay-5 {
  animation-delay: 0.5s;
}
