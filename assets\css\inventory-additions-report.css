/* Modern UI Styles for Inventory Additions Report */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  
  /* Gradients */
  --green-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
}

/* Page Layout */
.container-fluid {
  padding: var(--space-5) var(--space-4);
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Title */
.page-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  font-size: 1.6rem;
  color: var(--primary);
}

/* Report Tabs */
.nav-tabs {
  border-bottom: 1px solid var(--gray-200);
  margin-bottom: var(--space-5);
  display: flex;
  gap: var(--space-2);
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  border-radius: 0;
  padding: var(--space-3) var(--space-5);
  font-weight: 600;
  color: var(--gray-500);
  transition: var(--transition-fast);
  font-size: 1rem;
}

.nav-tabs .nav-link:hover {
  border-bottom-color: rgba(27, 94, 32, 0.3);
  background-color: rgba(27, 94, 32, 0.05);
  color: var(--primary);
}

.nav-tabs .nav-link.active {
  color: var(--primary-dark);
  background-color: transparent;
  border-bottom-color: var(--primary-dark);
}

.nav-tabs .nav-link i {
  margin-right: var(--space-2);
}

/* Stats Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-5);
}

.stat-card {
  background-color: var(--white);
  border-radius: var(--radius);
  padding: var(--space-4);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon i {
  font-size: 1.4rem;
  color: var(--white);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--gray-500);
}

/* Stat Card Colors */
.stat-primary .stat-icon {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

.stat-primary .stat-value {
  color: var(--primary-dark);
}

.stat-success .stat-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-success .stat-value {
  color: #059669;
}

.stat-warning .stat-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-warning .stat-value {
  color: #d97706;
}

.stat-info .stat-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-info .stat-value {
  color: #2563eb;
}

.stat-danger .stat-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.stat-danger .stat-value {
  color: #dc2626;
}

/* Filter & Content Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  border: none;
  overflow: hidden;
  margin-bottom: var(--space-5);
  transition: var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

.card-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--green-gradient);
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin: 0;
  display: flex;
  align-items: center;
}

.card-title i {
  margin-right: var(--space-2);
  color: var(--primary);
}

.card-body {
  padding: var(--space-5);
}

/* Form Elements */
.form-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-control, .form-select {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: var(--space-2) var(--space-3);
  transition: var(--transition-fast);
  height: auto;
  font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

/* Button Styles */
.btn {
  font-weight: 600;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius);
  transition: var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
}

/* Tables */
.table {
  width: 100%;
  margin-bottom: var(--space-4);
  color: var(--dark);
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  font-weight: 600;
  padding: var(--space-3) var(--space-4);
  border-bottom: 2px solid var(--gray-200);
  color: var(--primary-dark);
  white-space: nowrap;
  background-color: rgba(46, 125, 50, 0.05);
}

.table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
  background-color: rgba(46, 125, 50, 0.05);
}

/* Print Styles */
@media print {
  /* Hide everything by default */
  body * {
    visibility: hidden;
  }
  
  /* Only show the report content */
  #reportContent, 
  #reportContent * {
    visibility: visible;
  }
  
  /* Position the report at the top of the page */
  #reportContent {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 20px;
    margin: 0;
    box-shadow: none;
    border: none;
  }
  
  /* Force show print-only elements */
  .d-print-block {
    display: block !important;
  }
  
  /* Elements to hide in print */
  .no-print,
  .sidebar,
  .navbar,
  nav,
  .nav-tabs,
  .card-header,
  .filter-toggle,
  #filterCollapse,
  .stats-container,
  button,
  footer,
  .page-header,
  header,
  .btn,
  .topbar {
    display: none !important;
  }
  
  /* Print styling */
  .table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .table th {
    background-color: #f2f2f2 !important;
    color: #000 !important;
    border-bottom: 1px solid #ddd;
  }
  
  .table td {
    border-bottom: 1px solid #eee;
  }
  
  /* Reset transforms and shadows */
  .card {
    box-shadow: none !important;
    transform: none !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }
  
  /* Remove animations */
  .animate__fadeIn,
  .animate__fadeInUp,
  .animate__faster {
    animation: none !important;
  }
  
  /* Ensure text is black */
  body {
    color: #000 !important;
    background: #fff !important;
  }
  
  /* Chart size */
  .chart-container {
    max-height: 300px;
    page-break-inside: avoid;
  }
  
  /* Add page breaks where needed */
  .card {
    page-break-inside: avoid;
  }
  
  /* Ensure all content is visible */
  .table-responsive {
    overflow: visible !important;
  }
  
  /* Show report header with larger title */
  .report-header {
    margin-bottom: 2cm;
    text-align: center;
  }

  .report-header h2 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .report-header p {
    font-size: 14px;
    line-height: 1.5;
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card-body {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .nav-tabs .nav-link {
    padding: var(--space-2) var(--space-3);
  }
  
  .page-title {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .card-header, .card-body {
    padding: var(--space-3);
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: var(--space-3);
  }
  
  .stat-icon {
    margin-bottom: var(--space-2);
  }
}

/* Animation Classes */
.animate__fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate__fadeInUp {
  animation: fadeInUp 0.5s ease forwards;
}

.animate__faster {
  animation-duration: 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Toggle button for filter collapse */
.filter-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  text-align: left;
  padding: var(--space-3) var(--space-4);
  background-color: transparent;
  border: none;
  font-weight: 600;
  color: var(--primary-dark);
  transition: var(--transition-fast);
}

.filter-toggle:hover {
  background-color: rgba(46, 125, 50, 0.05);
}

.filter-toggle i {
  transition: var(--transition-fast);
}

.filter-toggle:not(.collapsed) i {
  transform: rotate(180deg);
}

/* Chart container */
.chart-container {
  height: 300px;
  position: relative;
} 