<?php
// Include necessary files
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/config.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');

// Set content type to JSON
header('Content-Type: application/json');

// Ensure user is logged in
if (!isLoggedIn()) {
    echo json_encode(['error' => 'Authentication required']);
    exit();
}

// Ensure user has logistics role
if (!hasRole('Logistics')) {
    echo json_encode(['error' => 'Insufficient permissions']);
    exit();
}

// Check if asset_id is provided
if (!isset($_POST['asset_id']) || empty($_POST['asset_id'])) {
    echo json_encode(['error' => 'Asset ID is required']);
    exit();
}

$asset_id = sanitizeInput($_POST['asset_id']);

// Query to check required fields
$query = "SELECT local_mr, receipt_type, assigned_to FROM fixed_assets WHERE asset_id = ?";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    echo json_encode(['error' => 'Asset not found']);
    exit();
}

$asset = mysqli_fetch_assoc($result);

// Check for missing fields
$missingFields = [];
if (empty($asset['local_mr'])) $missingFields[] = 'local_mr';
if (empty($asset['receipt_type'])) $missingFields[] = 'receipt_type';
if (empty($asset['assigned_to'])) $missingFields[] = 'assigned_to';

// Return results
echo json_encode([
    'asset_id' => $asset_id,
    'missingFields' => $missingFields,
    'hasRequiredFields' => empty($missingFields)
]); 