<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/db.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div class="alert alert-danger">Access denied. You do not have permission to view maintenance records.</div>';
    exit;
}

// Get record ID from request
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div class="alert alert-danger">Invalid request. Maintenance record ID is required.</div>';
    exit;
}

$record_id = sanitizeInput($_GET['id']);

// Get maintenance record details
$recordQuery = "
    SELECT mr.*, fa.asset_name, fa.serial_number, sm.sku_name, c.category_name, l.location_name,
           u.username as technician_name, u.full_name as technician_full_name,
           cu.username as created_by_username
    FROM maintenance_records mr
    JOIN fixed_assets fa ON mr.asset_id = fa.asset_id
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    LEFT JOIN users u ON mr.technician_id = u.user_id
    LEFT JOIN users cu ON mr.created_by = cu.user_id
    WHERE mr.record_id = ? AND c.category_id IN (2, 3)
";

$recordStmt = mysqli_prepare($conn, $recordQuery);
mysqli_stmt_bind_param($recordStmt, 'i', $record_id);
mysqli_stmt_execute($recordStmt);
$recordResult = mysqli_stmt_get_result($recordStmt);

// Check if record exists
if (mysqli_num_rows($recordResult) === 0) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<div class="alert alert-danger">Maintenance record not found or not an office/medical equipment record.</div>';
    exit;
}

$record = mysqli_fetch_assoc($recordResult);

// Determine status and type icons
$statusIcon = 'fas fa-clock';
$statusClass = 'status-scheduled';
$typeIcon = 'fas fa-tools';

switch ($record['status']) {
    case 'In Progress':
        $statusIcon = 'fas fa-spinner fa-spin';
        $statusClass = 'status-in-progress';
        break;
    case 'Completed':
        $statusIcon = 'fas fa-check-circle';
        $statusClass = 'status-completed';
        break;
    case 'Cancelled':
        $statusIcon = 'fas fa-times-circle';
        $statusClass = 'status-cancelled';
        break;
}

switch ($record['maintenance_type']) {
    case 'Preventive Maintenance':
        $typeIcon = 'fas fa-shield-alt';
        break;
    case 'Corrective Maintenance':
        $typeIcon = 'fas fa-wrench';
        break;
    case 'Repair':
        $typeIcon = 'fas fa-tools';
        break;
    case 'Upgrade':
        $typeIcon = 'fas fa-arrow-up';
        break;
    case 'Inspection':
        $typeIcon = 'fas fa-search';
        break;
    case 'Calibration':
        $typeIcon = 'fas fa-balance-scale';
        break;
    case 'Parts Replacement':
        $typeIcon = 'fas fa-exchange-alt';
        break;
    case 'Cleaning':
        $typeIcon = 'fas fa-broom';
        break;
    case 'Other':
        $typeIcon = 'fas fa-cog';
        break;
}

// Format the modal content as HTML
$html = '
<div class="modal-dialog modal-dialog-centered">
    <div class="modal-content modern-modal">
        <div class="modal-header">
            <h5 class="modal-title" id="viewMaintenanceModalLabel' . $record['record_id'] . '">
                <div class="d-flex align-items-center">
                    <div class="modal-icon ' . $statusClass . '">
                        <i class="' . $typeIcon . '"></i>
                    </div>
                    <span class="ms-2">Maintenance Record</span>
                </div>
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="maintenance-status-banner ' . $statusClass . ' mb-4">
                <div class="status-icon">
                    <i class="' . $statusIcon . '"></i>
                </div>
                <div class="status-content">
                    <h6 class="status-title">' . $record['status'] . '</h6>
                    <p class="status-subtitle">' . $record['maintenance_type'] . '</p>
                </div>
            </div>

            <div class="maintenance-details">
                <div class="detail-section">
                    <h6 class="detail-title">Asset Information</h6>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">Asset Name</span>
                            <span class="detail-value">' . $record['asset_name'] . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Category</span>
                            <span class="detail-value">' . $record['category_name'] . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Serial Number</span>
                            <span class="detail-value">' . ($record['serial_number'] ? $record['serial_number'] : 'N/A') . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Location</span>
                            <span class="detail-value">' . $record['location_name'] . '</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h6 class="detail-title">Maintenance Information</h6>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">Date</span>
                            <span class="detail-value">' . formatDate($record['maintenance_date']) . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type</span>
                            <span class="detail-value">' . $record['maintenance_type'] . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Cost</span>
                            <span class="detail-value">' . ($record['cost'] ? formatCurrency($record['cost']) : 'N/A') . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Technician</span>
                            <span class="detail-value">';

if (!empty($record['technician_name'])) {
    $html .= $record['technician_name'];
    if (!empty($record['technician_full_name'])) {
        $html .= ' (' . $record['technician_full_name'] . ')';
    }
} else {
    $html .= 'Not assigned';
}

$html .= '</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h6 class="detail-title">Description</h6>
                    <div class="detail-description">
                        ' . nl2br($record['description']) . '
                    </div>
                </div>

                <div class="detail-section">
                    <h6 class="detail-title">Record Information</h6>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">Record ID</span>
                            <span class="detail-value">' . $record['record_id'] . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Created By</span>
                            <span class="detail-value">' . $record['created_by_username'] . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Created At</span>
                            <span class="detail-value">' . formatDateTime($record['created_at']) . '</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Last Updated</span>
                            <span class="detail-value">' . formatDateTime($record['updated_at']) . '</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer border-0 pt-0">
            <button type="button" class="btn btn-soft-secondary" data-bs-dismiss="modal">
                <i class="fas fa-times me-1"></i> Close
            </button>';

if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics')) {
    $html .= '
            <a href="/choims/modules/logistics/edit_maintenance.php?id=' . $record['record_id'] . '" class="btn btn-soft-primary">
                <i class="fas fa-edit me-1"></i> Edit Record
            </a>';
}

$html .= '
        </div>
    </div>
</div>';

// Add debugging information
$debug = "<!-- Debug info: Modal content for record ID: $record_id -->";

// Return the HTML with proper content type
header('Content-Type: text/html; charset=utf-8');
echo $debug . $html;
?>
