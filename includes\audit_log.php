<?php
/**
 * Logs an action to the audit trail
 * 
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action Description of the action
 * @param string $table_name Name of the table being modified
 * @param int $record_id ID of the record being modified
 * @param string $old_values JSON string of old values
 * @param string $new_values JSON string of new values
 * @return bool True if logging was successful, false otherwise
 */
function logAction($conn, $user_id, $action, $table_name, $record_id, $old_values, $new_values) {
    // Sanitize inputs
    $user_id = intval($user_id);
    $action = mysqli_real_escape_string($conn, $action);
    $table_name = mysqli_real_escape_string($conn, $table_name);
    $record_id = intval($record_id);
    $old_values = mysqli_real_escape_string($conn, $old_values);
    $new_values = mysqli_real_escape_string($conn, $new_values);
    
    // Prepare the insert query
    $query = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, log_time) 
              VALUES (?, ?, ?, ?, ?, ?, NOW())";
    
    $stmt = mysqli_prepare($conn, $query);
    if (!$stmt) {
        return false;
    }
    
    mysqli_stmt_bind_param($stmt, 'isssss', 
        $user_id, 
        $action, 
        $table_name, 
        $record_id, 
        $old_values, 
        $new_values
    );
    
    $result = mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);
    
    return $result;
}

/**
 * Retrieves audit log entries for a specific record
 * 
 * @param mysqli $conn Database connection
 * @param string $table_name Name of the table
 * @param int $record_id ID of the record
 * @param int $limit Maximum number of entries to return (default: 10)
 * @return array Array of audit log entries
 */
function getAuditLog($conn, $table_name, $record_id, $limit = 10) {
    // Sanitize inputs
    $table_name = mysqli_real_escape_string($conn, $table_name);
    $record_id = intval($record_id);
    $limit = intval($limit);
    
    $query = "SELECT al.*, u.full_name as user_name 
              FROM audit_logs al 
              JOIN users u ON al.user_id = u.user_id 
              WHERE al.entity_type = ? AND al.entity_id = ? 
              ORDER BY al.log_time DESC 
              LIMIT ?";
    
    $stmt = mysqli_prepare($conn, $query);
    if (!$stmt) {
        return [];
    }
    
    mysqli_stmt_bind_param($stmt, 'sii', $table_name, $record_id, $limit);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $entries = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $entries[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    return $entries;
} 