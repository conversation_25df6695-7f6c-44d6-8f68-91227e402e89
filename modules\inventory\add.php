<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in and has appropriate role
requireLogin();
requireRole('Logistics');

// Get SKUs for dropdown
$skuQuery = "
    SELECT s.sku_id, s.sku_code, s.sku_name, c.category_name 
    FROM sku_master s
    JOIN categories c ON s.category_id = c.category_id
    WHERE s.item_type = 'Consumable'
    ORDER BY c.category_name, s.sku_name
";
$skuResult = mysqli_query($conn, $skuQuery);

// Get locations for dropdown
$locationQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
$locationResult = mysqli_query($conn, $locationQuery);

// Get suppliers for dropdown
$supplierQuery = "SELECT supplier_id, supplier_name FROM suppliers ORDER BY supplier_name";
$supplierResult = mysqli_query($conn, $supplierQuery);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate inputs
    $sku_id = sanitizeInput($_POST['sku_id']);
    $location_id = sanitizeInput($_POST['location_id']);
    $current_quantity = sanitizeInput($_POST['current_quantity']);
    $min_quantity = sanitizeInput($_POST['min_quantity']);
    $low_stock_threshold = sanitizeInput($_POST['low_stock_threshold']);
    $critical_threshold = sanitizeInput($_POST['critical_threshold']);
    $supplier_id = !empty($_POST['supplier_id']) ? sanitizeInput($_POST['supplier_id']) : null;
    $unit_cost = !empty($_POST['unit_cost']) ? sanitizeInput($_POST['unit_cost']) : null;
    $reference_document = !empty($_POST['reference_document']) ? sanitizeInput($_POST['reference_document']) : null;
    $remarks = !empty($_POST['remarks']) ? sanitizeInput($_POST['remarks']) : null;
    
    // Check if inventory already exists for this SKU and location
    $checkQuery = "SELECT inventory_id FROM consumable_inventory WHERE sku_id = ? AND location_id = ?";
    $checkStmt = mysqli_prepare($conn, $checkQuery);
    mysqli_stmt_bind_param($checkStmt, 'ii', $sku_id, $location_id);
    mysqli_stmt_execute($checkStmt);
    $checkResult = mysqli_stmt_get_result($checkStmt);
    
    if (mysqli_num_rows($checkResult) > 0) {
        $error = "An inventory item for this SKU already exists at this location.";
    } else {
        // Begin transaction
        mysqli_begin_transaction($conn);
        
        try {
            // Determine status based on quantity thresholds
            $status = 'Available';
            if ($current_quantity <= $critical_threshold) {
                $status = 'Out of Stock';
            } elseif ($current_quantity <= $low_stock_threshold) {
                $status = 'Low Stock';
            }
            
            // Insert inventory record
            $inventoryQuery = "
                INSERT INTO consumable_inventory (
                    sku_id, location_id, current_quantity, min_quantity,
                    status, low_stock_threshold, critical_threshold, last_restock_date,
                    created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), NOW()
                )
            ";
            $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
            mysqli_stmt_bind_param(
                $inventoryStmt, 
                'iiiiiii',
                $sku_id, $location_id, $current_quantity, $min_quantity,
                $status, $low_stock_threshold, $critical_threshold
            );
            mysqli_stmt_execute($inventoryStmt);
            
            // Get newly created inventory_id
            $inventory_id = mysqli_insert_id($conn);
            
            // Record transaction if quantity > 0
            if ($current_quantity > 0) {
                $transactionQuery = "
                    INSERT INTO consumable_transactions (
                        inventory_id, transaction_type, quantity, 
                        destination_location_id, reference_document, supplier_id,
                        unit_cost, remarks, performed_by, transaction_date
                    ) VALUES (
                        ?, 'Stock In', ?, ?, ?, ?, ?, ?, ?, NOW()
                    )
                ";
                $transactionStmt = mysqli_prepare($conn, $transactionQuery);
                mysqli_stmt_bind_param(
                    $transactionStmt,
                    'iiissisi',
                    $inventory_id, $current_quantity, $location_id,
                    $reference_document, $supplier_id, $unit_cost,
                    $remarks, $_SESSION['user_id']
                );
                mysqli_stmt_execute($transactionStmt);
            }
            
            // Create audit log
            createAuditLog(
                $_SESSION['user_id'],
                'create',
                'consumable_inventory',
                $inventory_id,
                null,
                json_encode([
                    'sku_id' => $sku_id,
                    'location_id' => $location_id,
                    'current_quantity' => $current_quantity,
                    'min_quantity' => $min_quantity,
                    'status' => $status,
                    'low_stock_threshold' => $low_stock_threshold,
                    'critical_threshold' => $critical_threshold
                ])
            );
            
            // Commit transaction
            mysqli_commit($conn);
            
            // Redirect to inventory list
            header("Location: /choims/modules/inventory/list.php?success=1");
            exit();
            
        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($conn);
            $error = "Error creating inventory item: " . $e->getMessage();
        }
    }
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Add New Inventory Item</h1>
        <a href="/choims/modules/inventory/list.php" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Inventory Information</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="sku_id" class="form-label">Item (SKU) *</label>
                            <select class="form-select" id="sku_id" name="sku_id" required>
                                <option value="">Select Item</option>
                                <?php
                                $currentCategory = '';
                                while ($sku = mysqli_fetch_assoc($skuResult)):
                                    if ($currentCategory != $sku['category_name']) {
                                        if ($currentCategory != '') {
                                            echo '</optgroup>';
                                        }
                                        echo '<optgroup label="' . $sku['category_name'] . '">';
                                        $currentCategory = $sku['category_name'];
                                    }
                                ?>
                                    <option value="<?php echo $sku['sku_id']; ?>">
                                        <?php echo $sku['sku_code']; ?> - <?php echo $sku['sku_name']; ?>
                                    </option>
                                <?php 
                                endwhile;
                                if ($currentCategory != '') {
                                    echo '</optgroup>';
                                }
                                ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="location_id" class="form-label">Location *</label>
                            <select class="form-select" id="location_id" name="location_id" required>
                                <option value="">Select Location</option>
                                <?php
                                // If user is not Logistics, restrict to their location
                                if (!hasRole('Logistics')) {
                                    $userLocationId = getUserLocationId();
                                    $locationQuery = "SELECT location_id, location_name FROM locations WHERE location_id = ?";
                                    $locationStmt = mysqli_prepare($conn, $locationQuery);
                                    mysqli_stmt_bind_param($locationStmt, 'i', $userLocationId);
                                    mysqli_stmt_execute($locationStmt);
                                    $locationResult = mysqli_stmt_get_result($locationStmt);
                                }
                                
                                while ($location = mysqli_fetch_assoc($locationResult)):
                                ?>
                                    <option value="<?php echo $location['location_id']; ?>">
                                        <?php echo $location['location_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="current_quantity" class="form-label">Current Quantity *</label>
                            <input type="number" class="form-control" id="current_quantity" name="current_quantity" min="0" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="min_quantity" class="form-label">Minimum Quantity *</label>
                            <input type="number" class="form-control" id="min_quantity" name="min_quantity" min="0" required>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="low_stock_threshold" class="form-label">Low Stock Threshold *</label>
                            <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold" min="0" value="10" required>
                            <small class="form-text text-muted">When quantity falls below this level, item will be marked as 'Low Stock'</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="critical_threshold" class="form-label">Critical Threshold *</label>
                            <input type="number" class="form-control" id="critical_threshold" name="critical_threshold" min="0" value="3" required>
                            <small class="form-text text-muted">When quantity falls below this level, item will be marked as 'Out of Stock'</small>
                        </div>
                        
                        <div id="initialStockDiv">
                            <h6 class="mt-4 mb-3">Initial Stock Information</h6>
                            
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">Supplier</label>
                                <select class="form-select" id="supplier_id" name="supplier_id">
                                    <option value="">Select Supplier</option>
                                    <?php while ($supplier = mysqli_fetch_assoc($supplierResult)): ?>
                                        <option value="<?php echo $supplier['supplier_id']; ?>">
                                            <?php echo $supplier['supplier_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="unit_cost" class="form-label">Unit Cost</label>
                                <input type="number" step="0.01" class="form-control" id="unit_cost" name="unit_cost">
                            </div>
                            
                            <div class="mb-3">
                                <label for="reference_document" class="form-label">Reference Document</label>
                                <input type="text" class="form-control" id="reference_document" name="reference_document" 
                                       placeholder="e.g., Invoice #, Delivery Receipt #">
                            </div>
                            
                            <div class="mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Inventory Item
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                    <a href="/choims/modules/inventory/list.php" class="btn btn-danger">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide/show initial stock div based on current quantity
    const currentQuantityInput = document.getElementById('current_quantity');
    const initialStockDiv = document.getElementById('initialStockDiv');
    
    currentQuantityInput.addEventListener('input', function() {
        if (parseInt(this.value) > 0) {
            initialStockDiv.style.display = 'block';
        } else {
            initialStockDiv.style.display = 'none';
        }
    });
    
    // Trigger the event handler on page load
    currentQuantityInput.dispatchEvent(new Event('input'));
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>