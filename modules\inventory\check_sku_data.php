<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Check if unit_of_measure column exists
$checkColumnQuery = "SHOW COLUMNS FROM sku_master LIKE 'unit_of_measure'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);

if (mysqli_num_rows($checkColumnResult) == 0) {
    echo "Column 'unit_of_measure' does not exist in sku_master table.<br>";
} else {
    echo "Column 'unit_of_measure' exists in sku_master table.<br>";

    // Check data in the table
    $dataQuery = "SELECT sku_id, sku_code, sku_name, unit_of_measure FROM sku_master LIMIT 10";
    $dataResult = mysqli_query($conn, $dataQuery);

    echo "<h3>Sample SKU Data:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>SKU ID</th><th>SKU Code</th><th>SKU Name</th><th>Unit of Measure</th></tr>";

    while ($row = mysqli_fetch_assoc($dataResult)) {
        echo "<tr>";
        echo "<td>" . $row['sku_id'] . "</td>";
        echo "<td>" . $row['sku_code'] . "</td>";
        echo "<td>" . $row['sku_name'] . "</td>";
        echo "<td>" . $row['unit_of_measure'] . "</td>";
        echo "</tr>";
    }

    echo "</table>";
}

// Close connection
mysqli_close($conn);
?>
