<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has Superadmin permission
if (!$auth->hasRole(ROLE_SUPERADMIN)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$currentUser = $auth->getCurrentUser();

// Initialize arrays to avoid undefined variable errors
$recentActivities = [];
$pendingTransfers = [];
$departmentStats = [];
$healthCenterStats = [];

try {
    // Fetch system-wide statistics
    $stmt = $db->query("
        SELECT 
            (SELECT COUNT(*) FROM fixed_assets) as total_assets,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'in_use') as assets_in_use,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'under_repair') as assets_under_repair,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'defective') as defective_assets,
            (SELECT COUNT(DISTINCT item_id) FROM inventory WHERE quantity > 0) as consumables_in_stock,
            (SELECT COUNT(DISTINCT item_id) FROM inventory WHERE quantity <= minimum_stock) as low_stock_items,
            (SELECT COUNT(*) FROM departments) as total_departments,
            (SELECT COUNT(*) FROM health_centers) as total_health_centers,
            (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users
    ");
    $systemStats = $stmt->fetch();

    // Fetch pending transfers requiring any approval
    $stmt = $db->query("
        SELECT tr.*, 
               i.name as item_name,
               CONCAT(u.first_name, ' ', u.last_name) as requester_name,
               sd.name as source_dept,
               shc.name as source_hc,
               dd.name as dest_dept,
               dhc.name as dest_hc,
               c.name as category_name
        FROM transfer_requests tr
        JOIN items i ON tr.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN users u ON tr.requested_by = u.user_id
        LEFT JOIN departments sd ON tr.source_department_id = sd.department_id
        LEFT JOIN health_centers shc ON tr.source_health_center_id = shc.health_center_id
        LEFT JOIN departments dd ON tr.destination_department_id = dd.department_id
        LEFT JOIN health_centers dhc ON tr.destination_health_center_id = dhc.health_center_id
        WHERE tr.status = 'pending'
        ORDER BY tr.created_at DESC
        LIMIT 5
    ");
    $pendingTransfers = $stmt->fetchAll();

    // Fix table name - audit_log vs audit_logs
    try {
        // First try with audit_logs (plural)
        $stmt = $db->query("
            SELECT al.*,
                   CONCAT(u.first_name, ' ', u.last_name) as user_name,
                   u.role
            FROM audit_logs al
            JOIN users u ON al.user_id = u.user_id
            ORDER BY al.created_at DESC
            LIMIT 10
        ");
        $recentActivities = $stmt->fetchAll();
    } catch (PDOException $e) {
        // If that fails, try with audit_log (singular)
        try {
            $stmt = $db->query("
                SELECT al.*,
                       CONCAT(u.first_name, ' ', u.last_name) as user_name,
                       u.role
                FROM audit_log al
                JOIN users u ON al.user_id = u.user_id
                ORDER BY al.created_at DESC
                LIMIT 10
            ");
            $recentActivities = $stmt->fetchAll();
        } catch (PDOException $e2) {
            // Both attempts failed, set empty array
            $recentActivities = [];
            error_log("Error fetching audit logs: " . $e2->getMessage());
        }
    }

    // Fetch department statistics
    $stmt = $db->query("
        SELECT d.name as department_name,
               (SELECT COUNT(*) FROM fixed_assets fa WHERE fa.department_id = d.department_id) as asset_count,
               (SELECT COUNT(DISTINCT inv.item_id) FROM inventory inv WHERE inv.department_id = d.department_id) as item_count
        FROM departments d
        ORDER BY d.name
        LIMIT 5
    ");
    $departmentStats = $stmt->fetchAll();

    // Fetch health center statistics
    $stmt = $db->query("
        SELECT hc.name as center_name,
               (SELECT COUNT(*) FROM fixed_assets fa WHERE fa.health_center_id = hc.health_center_id) as asset_count,
               (SELECT COUNT(DISTINCT inv.item_id) FROM inventory inv WHERE inv.health_center_id = hc.health_center_id) as item_count
        FROM health_centers hc
        ORDER BY hc.name
        LIMIT 5
    ");
    $healthCenterStats = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error in Superadmin dashboard: " . $e->getMessage());
    setFlashMessage('error', 'Error loading dashboard data');
}

require_once '../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Superadmin Dashboard</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Export Reports
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="../reports/system-overview.php">System Overview Report</a></li>
                <li><a class="dropdown-item" href="../reports/inventory.php">Full Inventory Report</a></li>
                <li><a class="dropdown-item" href="../reports/user-activity.php">User Activity Report</a></li>
                <li><a class="dropdown-item" href="../reports/department-summary.php">Department Summary</a></li>
                <li><a class="dropdown-item" href="../reports/health-center-summary.php">Health Center Summary</a></li>
            </ul>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="../admin/users/manage.php" class="btn btn-primary">
                            <i class="fas fa-users"></i> Manage Users
                        </a>
                        <a href="../admin/departments/manage.php" class="btn btn-success">
                            <i class="fas fa-building"></i> Manage Departments
                        </a>
                        <a href="../admin/health-centers/manage.php" class="btn btn-info">
                            <i class="fas fa-clinic-medical"></i> Manage Health Centers
                        </a>
                        <a href="../admin/categories/manage.php" class="btn btn-warning">
                            <i class="fas fa-tags"></i> Manage Categories
                        </a>
                        <a href="../admin/settings.php" class="btn btn-secondary">
                            <i class="fas fa-cog"></i> System Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <!-- System Users -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Active Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($systemStats['active_users']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Departments & Centers -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Locations
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($systemStats['total_departments'] + $systemStats['total_health_centers']); ?>
                            </div>
                            <div class="text-xs text-gray-600">
                                <?php echo $systemStats['total_departments']; ?> Departments, 
                                <?php echo $systemStats['total_health_centers']; ?> Health Centers
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Assets -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Assets
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($systemStats['total_assets']); ?>
                            </div>
                            <div class="text-xs text-gray-600">
                                <?php echo number_format($systemStats['assets_in_use']); ?> In Use, 
                                <?php echo number_format($systemStats['assets_under_repair']); ?> Under Repair
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-laptop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consumables -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Consumables
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($systemStats['consumables_in_stock']); ?>
                            </div>
                            <div class="text-xs text-danger">
                                <?php echo number_format($systemStats['low_stock_items']); ?> Low Stock Items
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Content Column -->
        <div class="col-lg-6 mb-4">
            <!-- System Management Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Management</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/users/list.php" class="btn btn-info btn-block">
                                <i class="fas fa-users mr-2"></i> User Management
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/admin/locations.php" class="btn btn-info btn-block">
                                <i class="fas fa-map-marker-alt mr-2"></i> Locations
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/admin/categories.php" class="btn btn-info btn-block">
                                <i class="fas fa-tags mr-2"></i> Categories
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/admin/sku.php" class="btn btn-info btn-block">
                                <i class="fas fa-barcode mr-2"></i> SKU Management
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/admin/suppliers.php" class="btn btn-info btn-block">
                                <i class="fas fa-truck mr-2"></i> Suppliers
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/admin/sources.php" class="btn btn-info btn-block">
                                <i class="fas fa-building mr-2"></i> Sources
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/assets/list.php?show_deleted=1" class="btn btn-warning btn-block">
                                <i class="fas fa-trash-restore mr-2"></i> Deleted Assets
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/modules/inventory/list.php?show_deleted=1" class="btn btn-warning btn-block">
                                <i class="fas fa-trash-restore mr-2"></i> Deleted Inventory
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/choims/setup_deleted_items.php" class="btn btn-danger btn-block">
                                <i class="fas fa-database mr-2"></i> Setup Soft Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-xl-8 col-lg-7">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history mr-1"></i>
                        Recent System Activities
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php foreach ($recentActivities as $activity): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">
                                    <?php echo htmlspecialchars($activity['user_name']); ?>
                                    <small class="text-muted">[<?php echo htmlspecialchars($activity['role']); ?>]</small>
                                </h6>
                                <p class="timeline-text">
                                    <?php echo htmlspecialchars($activity['action']); ?>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo formatDate($activity['created_at']); ?>
                                    </small>
                                </p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../admin/audit-log.php" class="btn btn-primary btn-sm">
                            View Full Activity Log
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Transfers -->
        <div class="col-xl-4 col-lg-5">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock mr-1"></i>
                        Pending Transfers
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Item</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingTransfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>">
                                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                    <td>
                                        <?php
                                        $status = [];
                                        if ($transfer['requires_himu_approval']) {
                                            $status[] = "HIMU: " . ucfirst($transfer['himu_approval_status']);
                                        }
                                        $status[] = "Logistics: " . ucfirst($transfer['logistics_approval_status']);
                                        echo implode("<br>", $status);
                                        ?>
                                    </td>
                                    <td>
                                        <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/transfers/index.php" class="btn btn-warning btn-sm">
                            View All Transfers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Statistics -->
    <div class="row">
        <!-- Department Statistics -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-building mr-1"></i>
                        Department Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Fixed Assets</th>
                                    <th>Consumables</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($departmentStats as $dept): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                    <td><?php echo number_format($dept['asset_count']); ?></td>
                                    <td><?php echo number_format($dept['item_count']); ?></td>
                                    <td>
                                        <a href="../inventory/departments/view.php?id=<?php echo $dept['department_id']; ?>" 
                                           class="btn btn-success btn-sm">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../admin/departments/manage.php" class="btn btn-success btn-sm">
                            Manage Departments
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Health Center Statistics -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-clinic-medical mr-1"></i>
                        Health Center Statistics
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Health Center</th>
                                    <th>Fixed Assets</th>
                                    <th>Consumables</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($healthCenterStats as $center): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($center['center_name']); ?></td>
                                    <td><?php echo number_format($center['asset_count']); ?></td>
                                    <td><?php echo number_format($center['item_count']); ?></td>
                                    <td>
                                        <a href="../inventory/health-centers/view.php?id=<?php echo $center['health_center_id']; ?>" 
                                           class="btn btn-info btn-sm">
                                            View
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../admin/health-centers/manage.php" class="btn btn-info btn-sm">
                            Manage Health Centers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Timeline Styles */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #4e73df;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px #4e73df;
}

.timeline-content {
    background-color: #f8f9fc;
    padding: 15px;
    border-radius: 4px;
}

.timeline-title {
    margin: 0 0 10px;
    color: #4e73df;
}

.timeline-text {
    margin: 0;
    color: #5a5c69;
}
</style>

<?php require_once '../templates/footer.php'; ?>
