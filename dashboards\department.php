<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireRole('Department');

// Get user's location
$userLocationId = $_SESSION['location_id'];
$locationQuery = "SELECT * FROM locations WHERE location_id = ?";
$locationStmt = mysqli_prepare($conn, $locationQuery);
mysqli_stmt_bind_param($locationStmt, 'i', $userLocationId);
mysqli_stmt_execute($locationStmt);
$locationResult = mysqli_stmt_get_result($locationStmt);
$location = mysqli_fetch_assoc($locationResult);

// Check if it's time for monthly inventory update
$showMonthlyUpdateButton = isTimeForMonthlyInventoryUpdate(); // Show only 2 days before month end
$hasCompletedMonthlyUpdate = false;

if ($showMonthlyUpdateButton) {
    // Check if this location has already completed their monthly update
    $hasCompletedMonthlyUpdate = hasCompletedMonthlyInventoryUpdate($conn, $userLocationId);
}

// Generate CSRF token for the monthly update form
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get asset counts for user's location
$assetsQuery = "
    SELECT COUNT(*) as total
    FROM fixed_assets
    WHERE current_location_id = ? AND is_active = 1 AND (is_deleted = 0 OR is_deleted IS NULL)
";
$assetsStmt = mysqli_prepare($conn, $assetsQuery);
mysqli_stmt_bind_param($assetsStmt, 'i', $userLocationId);
mysqli_stmt_execute($assetsStmt);
$assetsResult = mysqli_stmt_get_result($assetsStmt);
$assetsCount = mysqli_fetch_assoc($assetsResult)['total'];

// Get consumable inventory count
$inventoryQuery = "
    SELECT COUNT(*) as total
    FROM consumable_inventory
    WHERE location_id = ?
";
$inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
mysqli_stmt_bind_param($inventoryStmt, 'i', $userLocationId);
mysqli_stmt_execute($inventoryStmt);
$inventoryResult = mysqli_stmt_get_result($inventoryStmt);
$inventoryCount = mysqli_fetch_assoc($inventoryResult)['total'];

// Get pending transfers count (both individual and batch transfers)
$transfersQuery = "
    SELECT
        (SELECT COUNT(*) FROM transfers
         WHERE (source_location_id = ? OR destination_location_id = ?)
         AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) +
        (SELECT COUNT(*) FROM batch_transfers
         WHERE (source_location_id = ? OR destination_location_id = ?)
         AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) AS total
";
$transfersStmt = mysqli_prepare($conn, $transfersQuery);
mysqli_stmt_bind_param($transfersStmt, 'iiii', $userLocationId, $userLocationId, $userLocationId, $userLocationId);
mysqli_stmt_execute($transfersStmt);
$transfersResult = mysqli_stmt_get_result($transfersStmt);
$pendingTransfersCount = mysqli_fetch_assoc($transfersResult)['total'];

// Get low stock items at user's location
$lowStockQuery = "
    SELECT i.inventory_id, s.sku_name, i.current_quantity, i.low_stock_threshold, i.critical_threshold, i.status
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    WHERE i.location_id = ? AND (i.status = 'Low Stock' OR i.status = 'Out of Stock')
    ORDER BY
        CASE
            WHEN i.status = 'Out of Stock' THEN 1
            WHEN i.status = 'Low Stock' THEN 2
            ELSE 3
        END,
        s.sku_name ASC
    LIMIT 10
";
$lowStockStmt = mysqli_prepare($conn, $lowStockQuery);
mysqli_stmt_bind_param($lowStockStmt, 'i', $userLocationId);
mysqli_stmt_execute($lowStockStmt);
$lowStockResult = mysqli_stmt_get_result($lowStockStmt);

// Get recent department activity logs
$activityQuery = "
        SELECT al.*, u.full_name
    FROM audit_logs al
    LEFT JOIN users u ON al.user_id = u.user_id
    WHERE (
        (al.entity_type = 'Fixed Asset' AND (
            SELECT fa.current_location_id FROM fixed_assets fa WHERE fa.asset_id = al.entity_id
        ) = ?)
        OR
        (al.entity_type = 'Consumable Inventory' AND (
            SELECT ci.location_id FROM consumable_inventory ci WHERE ci.inventory_id = al.entity_id
        ) = ?)
        OR
        (al.entity_type = 'Transfer' AND (
            SELECT t.source_location_id FROM transfers t WHERE t.transfer_id = al.entity_id
        ) = ? OR (
            SELECT t.destination_location_id FROM transfers t WHERE t.transfer_id = al.entity_id
        ) = ?)
    )
    ORDER BY al.log_time DESC
    LIMIT 5
";
$activityStmt = mysqli_prepare($conn, $activityQuery);
mysqli_stmt_bind_param($activityStmt, 'iiii', $userLocationId, $userLocationId, $userLocationId, $userLocationId);
mysqli_stmt_execute($activityStmt);
$activityResult = mysqli_stmt_get_result($activityStmt);

// Get transfers waiting for acceptance (where this location is the destination)
$pendingTransfersQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        l1.location_name AS source_location,
        u1.full_name AS initiated_by,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT c.requires_himu_approval FROM fixed_assets a JOIN sku_master s ON a.sku_id = s.sku_id JOIN categories c ON s.category_id = c.category_id WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT c.requires_himu_approval FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id JOIN categories c ON s.category_id = c.category_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 0
        END AS requires_himu_approval
    FROM transfers t
    JOIN locations l1 ON t.source_location_id = l1.location_id
    JOIN users u1 ON t.initiated_by = u1.user_id
    WHERE t.destination_location_id = ?
    AND (
        t.status = 'Approved by HIMU'
        OR
        (t.status = 'Approved by Logistics' AND
         NOT EXISTS (
             SELECT 1 FROM fixed_assets a
             JOIN sku_master s ON a.sku_id = s.sku_id
             JOIN categories c ON s.category_id = c.category_id
             WHERE a.asset_id = t.asset_id AND c.requires_himu_approval = 1
         )
         AND
         NOT EXISTS (
             SELECT 1 FROM consumable_inventory ci
             JOIN sku_master s ON ci.sku_id = s.sku_id
             JOIN categories c ON s.category_id = c.category_id
             WHERE ci.inventory_id = t.inventory_id AND c.requires_himu_approval = 1
         )
        )
    )
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        l1.location_name AS source_location,
        u1.full_name AS initiated_by,
        b.requires_himu_approval
    FROM batch_transfers b
    JOIN locations l1 ON b.source_location_id = l1.location_id
    JOIN users u1 ON b.initiated_by = u1.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.destination_location_id = ?
    AND (
        b.status = 'Approved by HIMU'
        OR
        (b.status = 'Approved by Logistics' AND b.requires_himu_approval = 0)
    )
    GROUP BY b.batch_id
    LIMIT 5)

    ORDER BY transfer_date DESC
";
$pendingTransfersStmt = mysqli_prepare($conn, $pendingTransfersQuery);
mysqli_stmt_bind_param($pendingTransfersStmt, 'ii', $userLocationId, $userLocationId);
mysqli_stmt_execute($pendingTransfersStmt);
$pendingTransfersResult = mysqli_stmt_get_result($pendingTransfersStmt);

// Recent transfers to/from this location
$transfersQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM transfers t
    JOIN locations l1 ON t.source_location_id = l1.location_id
    JOIN locations l2 ON t.destination_location_id = l2.location_id
    JOIN users u1 ON t.initiated_by = u1.user_id
    WHERE t.source_location_id = ? OR t.destination_location_id = ?
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM batch_transfers b
    JOIN locations l1 ON b.source_location_id = l1.location_id
    JOIN locations l2 ON b.destination_location_id = l2.location_id
    JOIN users u1 ON b.initiated_by = u1.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.source_location_id = ? OR b.destination_location_id = ?
    GROUP BY b.batch_id
    LIMIT 5)

    ORDER BY transfer_date DESC
    LIMIT 5
";
$transfersStmt = mysqli_prepare($conn, $transfersQuery);
mysqli_stmt_bind_param($transfersStmt, 'iiii', $userLocationId, $userLocationId, $userLocationId, $userLocationId);
mysqli_stmt_execute($transfersStmt);
$transfersResult = mysqli_stmt_get_result($transfersStmt);

// Get latest system notifications for this location
$notificationsQuery = "
    SELECT notification_id, notification_type, title, message, is_read, created_at
    FROM notifications
    WHERE user_id = ? OR (related_entity = 'location' AND related_id = ?)
    ORDER BY created_at DESC
    LIMIT 5
";
$notificationsStmt = mysqli_prepare($conn, $notificationsQuery);
mysqli_stmt_bind_param($notificationsStmt, 'ii', $_SESSION['user_id'], $userLocationId);
mysqli_stmt_execute($notificationsStmt);
$notificationsResult = mysqli_stmt_get_result($notificationsStmt);
?>

<!-- Custom CSS for the redesigned dashboard -->
<link rel="stylesheet" href="/choims/assets/css/monthly-inventory-update-modern.css">
<style>
    /* Dashboard redesign with modern variables */
    :root {
        --primary-green: #2E7D32;
        --light-green: #4CAF50;
        --lighter-green: #8BC34A;
        --dark-green: #1B5E20;
        --accent-green: #00C853;
        --green-hover: #388E3C;
        --green-gradient: linear-gradient(135deg, var(--primary-green), var(--light-green));
        --green-text: #1B5E20;
        --green-border: #81C784;
        --green-bg-light: #E8F5E9;
        --shadow-sm: 0 2px 8px rgba(0,0,0,0.08);
        --shadow-md: 0 6px 16px rgba(0,0,0,0.1);
        --shadow-lg: 0 12px 28px rgba(0,0,0,0.15);
        --radius-sm: 8px;
        --radius-md: 12px;
        --radius-lg: 16px;
    }

    /* Dashboard header with enhanced design */
    .dashboard-header {
        background: var(--green-gradient);
        color: white;
        border-radius: var(--radius-md);
        padding: 1.75rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 300px;
        height: 100%;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMwMCAwSDBWMzAwSDMwMFYwWiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4wNSIvPjxjaXJjbGUgY3g9IjE1MCIgY3k9IjE1MCIgcj0iMTAwIiBmaWxsPSJ3aGl0ZSIgZmlsbC1vcGFjaXR5PSIwLjA1Ii8+PGNpcmNsZSBjeD0iMTUwIiBjeT0iMTUwIiByPSI1MCIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4wNSIvPjwvc3ZnPg==');
        background-size: cover;
        opacity: 0.2;
        z-index: 0;
    }

    /* Add more header patterns and visual elements */
    .dashboard-header::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 40%;
        background: linear-gradient(0deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%);
        z-index: 0;
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.25rem;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .dashboard-subtitle {
        opacity: 0.9;
        font-weight: 500;
        letter-spacing: 0.3px;
        position: relative;
        z-index: 1;
        font-size: 1.05rem;
        max-width: 80%;
        margin-bottom: 0.5rem;
    }

    .dashboard-stats-summary {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .dashboard-stat {
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(5px);
        border-radius: var(--radius-sm);
        padding: 0.6rem 1rem;
        border: 1px solid rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .dashboard-stat i {
        font-size: 1.1rem;
    }

    .dashboard-stat-value {
        font-weight: 700;
        margin-right: 0.25rem;
    }

    .dashboard-header-buttons {
        display: flex;
        gap: 0.75rem;
    }

    .dashboard-header-button {
        background: rgba(255,255,255,0.15);
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255,255,255,0.2);
        color: white;
        transition: all 0.3s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.6rem 1rem;
        border-radius: var(--radius-sm);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .dashboard-header-button:hover {
        background: rgba(255,255,255,0.25);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        color: white;
        text-decoration: none;
    }

    .dashboard-header-button i {
        font-size: 1rem;
    }

    /* Quick action cards with enhanced design */
    .content-card {
        border-radius: var(--radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        margin-bottom: 1.5rem;
        border: none;
        transition: all 0.3s ease;
    }

    .content-card:hover {
        box-shadow: var(--shadow-md);
    }

    .content-card .card-header {
        background: white;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        font-weight: 600;
        color: #333;
        padding: 1rem 1.25rem;
    }

    .quick-action-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }

    .quick-action-card {
        background: white;
        border-radius: var(--radius-md);
        border: 1px solid rgba(0,0,0,0.05);
        padding: 1.25rem 1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }

    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-md);
        border-color: var(--green-border);
        background-color: var(--green-bg-light);
    }

    .action-icon {
        width: 60px;
        height: 60px;
        background-color: var(--green-bg-light);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        color: var(--primary-green);
        font-size: 1.5rem;
        transition: all 0.3s ease;
    }

    .quick-action-card:hover .action-icon {
        background-color: var(--primary-green);
        color: white;
        transform: scale(1.1);
    }

    .action-label {
        font-size: 0.95rem;
        font-weight: 600;
        color: #333;
        text-align: center;
        transition: all 0.3s ease;
    }

    .quick-action-card:hover .action-label {
        color: var(--green-text);
    }

    /* Responsive adjustments */
    @media (max-width: 767.98px) {
        .quick-action-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    .stat-card {
        border-radius: var(--radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
        height: 100%;
        border: none;
        position: relative;
        z-index: 1;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-md);
    }

    .stat-card .card-body {
        padding: 1.75rem;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        z-index: 2;
    }

    .stat-card-primary::before {
        background: linear-gradient(90deg, #2196F3, #42A5F5);
    }

    .stat-card-success::before {
        background: linear-gradient(90deg, #4CAF50, #66BB6A);
    }

    .stat-card-warning::before {
        background: linear-gradient(90deg, #FF9800, #FFA726);
    }

    .stat-card-primary .card-body {
        background: linear-gradient(45deg, rgba(33, 150, 243, 0.05), rgba(66, 165, 245, 0.1));
    }

    .stat-card-success .card-body {
        background: linear-gradient(45deg, rgba(76, 175, 80, 0.05), rgba(102, 187, 106, 0.1));
    }

    .stat-card-warning .card-body {
        background: linear-gradient(45deg, rgba(255, 152, 0, 0.05), rgba(255, 167, 38, 0.1));
    }

    .stat-icon-wrapper {
        position: absolute;
        right: 1.25rem;
        bottom: 1.25rem;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        opacity: 0.85;
        transition: all 0.3s ease;
    }

    .stat-card:hover .stat-icon-wrapper {
        transform: scale(1.1);
        opacity: 1;
    }

    .stat-card-primary .stat-icon-wrapper {
        background: rgba(33, 150, 243, 0.15);
        color: #2196F3;
    }

    .stat-card-success .stat-icon-wrapper {
        background: rgba(76, 175, 80, 0.15);
        color: #4CAF50;
    }

    .stat-card-warning .stat-icon-wrapper {
        background: rgba(255, 152, 0, 0.15);
        color: #FF9800;
    }

    .stat-icon {
        font-size: 1.75rem;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.75rem;
    }

    .stat-label {
        text-transform: uppercase;
        font-size: 0.85rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        opacity: 0.85;
        margin-bottom: 0.5rem;
    }

    .stat-link {
        display: inline-flex;
        align-items: center;
        margin-top: 0.75rem;
        font-size: 0.85rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .stat-link i {
        margin-left: 0.3rem;
        transition: transform 0.2s ease;
    }

    .stat-link:hover i {
        transform: translateX(3px);
    }

    .stat-card-primary .stat-link {
        color: #2196F3;
    }

    .stat-card-success .stat-link {
        color: #4CAF50;
    }

    .stat-card-warning .stat-link {
        color: #FF9800;
    }

    .notification-item {
        padding: 12px 15px;
        border-left: 3px solid #4CAF50;
        background-color: white;
        margin-bottom: 10px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.2s ease;
    }

    .notification-item:hover {
        background-color: #f9f9f9;
        transform: translateX(3px);
    }

    .notification-item.unread {
        border-left-color: #2196F3;
        background-color: #f0f8ff;
    }

    .notification-title {
        font-weight: 600;
        margin-bottom: 2px;
        color: #333;
    }

    .notification-meta {
        font-size: 0.75rem;
        color: #666;
    }

    .notification-message {
        font-size: 0.9rem;
        margin-top: 5px;
        color: #555;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }

    .table th {
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        color: #555;
        background-color: #f9f9f9;
    }

    .responsive-status {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .responsive-status i {
        margin-right: 4px;
    }

    .responsive-status.outgoing {
        background-color: #ffebee;
        color: #d32f2f;
    }

    .responsive-status.incoming {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    /* Compact header styles */
    .dashboard-header-compact {
        padding: 1.5rem;
        background: var(--green-gradient);
        color: white;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-md);
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header-compact::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 100%;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMwMCAwSDBWMzAwSDMwMFYwWiIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4wNSIvPjxjaXJjbGUgY3g9IjE1MCIgY3k9IjE1MCIgcj0iMTAwIiBmaWxsPSJ3aGl0ZSIgZmlsbC1vcGFjaXR5PSIwLjA1Ii8+PGNpcmNsZSBjeD0iMTUwIiBjeT0iMTUwIiByPSI1MCIgZmlsbD0id2hpdGUiIGZpbGwtb3BhY2l0eT0iMC4wNSIvPjwvc3ZnPg==');
        background-size: cover;
        opacity: 0.2;
        z-index: 0;
    }

    .dashboard-header-compact::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 40%;
        background: linear-gradient(0deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%);
        z-index: 0;
    }

    .dashboard-title-compact {
        font-size: 1.75rem;
        font-weight: 700;
        color: white;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .dashboard-subtitle-compact {
        font-size: 1rem;
        color: rgba(255,255,255,0.9);
        margin-bottom: 0.75rem;
        position: relative;
        z-index: 1;
    }

    .dashboard-date {
        font-size: 0.9rem;
        color: rgba(255,255,255,0.8);
        position: relative;
        z-index: 1;
        background: rgba(255,255,255,0.15);
        display: inline-block;
        padding: 0.4rem 0.75rem;
        border-radius: 20px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    /* Mini stat cards */
    .stat-card-mini {
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 1.5rem;
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-md);
        height: 100%;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card-mini:hover {
        transform: translateY(-3px);
    }

    .stat-card-primary {
        background: linear-gradient(135deg, #1976D2, #42A5F5);
        color: white;
    }

    .stat-card-success {
        background: linear-gradient(135deg, #2E7D32, #66BB6A);
        color: white;
    }

    .stat-card-warning {
        background: linear-gradient(135deg, #E65100, #FFA726);
        color: white;
    }

    .stat-card-mini::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100%;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNTAiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuMSIvPjwvc3ZnPg==');
        background-size: cover;
        opacity: 0.2;
        z-index: 0;
    }

    .stat-icon-mini {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        position: relative;
        z-index: 1;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .stat-content {
        position: relative;
        z-index: 1;
    }

    .stat-value-mini {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1.2;
        margin-bottom: 0.25rem;
        color: white;
    }

    .stat-label-mini {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }
</style>

<!-- Include modern notifications CSS -->
<link rel="stylesheet" href="/choims/dashboards/notifications-modern.css">

<div class="container-fluid py-4">
    <!-- Compact Dashboard Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="dashboard-header-compact">
                <?php
                // Extract first name from full name
                $firstName = '';
                if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                    $nameParts = explode(' ', $_SESSION['full_name']);
                    $firstName = $nameParts[0];
                }
                ?>
                <h1 class="dashboard-title-compact mb-0">
                    <i class="fas fa-hospital me-2"></i> Hi, <?php echo htmlspecialchars($firstName); ?>
                </h1>
                <p class="dashboard-subtitle-compact">Department Inventory Management</p>
                <div class="dashboard-date">
                    <i class="fas fa-calendar-alt me-1"></i> <?php echo date('F j, Y'); ?>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-md-4">
                    <div class="stat-card-mini stat-card-primary">
                        <div class="stat-icon-mini"><i class="fas fa-laptop"></i></div>
                        <div class="stat-content">
                            <div class="stat-value-mini"><?php echo $assetsCount; ?></div>
                            <div class="stat-label-mini">Assets</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card-mini stat-card-success">
                        <div class="stat-icon-mini"><i class="fas fa-boxes"></i></div>
                        <div class="stat-content">
                            <div class="stat-value-mini"><?php echo $inventoryCount; ?></div>
                            <div class="stat-label-mini">Consumables</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stat-card-mini stat-card-warning">
                        <div class="stat-icon-mini"><i class="fas fa-exchange-alt"></i></div>
                        <div class="stat-content">
                            <div class="stat-value-mini"><?php echo $pendingTransfersCount; ?></div>
                            <div class="stat-label-mini">Transfers</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($showMonthlyUpdateButton && !$hasCompletedMonthlyUpdate): ?>
    <!-- Monthly Inventory Update Alert -->
    <div class="alert alert-warning animate__animated animate__pulse animate__infinite" style="border-left: 4px solid #f6c23e;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-calendar-check me-2"></i> Monthly Inventory Update Required</h5>
                <p class="mb-0">Please update your consumables inventory for the month of <?php echo date('F Y'); ?>.</p>
            </div>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                <i class="fas fa-clipboard-check me-1"></i> Update Now
            </button>
        </div>
    </div>
    <?php elseif ($showMonthlyUpdateButton && $hasCompletedMonthlyUpdate): ?>
    <!-- Monthly Inventory Update Completed Alert -->
    <div class="alert alert-success" style="border-left: 4px solid #1cc88a;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-check-circle me-2"></i> Monthly Inventory Update Completed</h5>
                <p class="mb-0">Thank you for completing your inventory update for <?php echo date('F Y'); ?>.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Enhanced Quick Actions Section -->
    <div class="card shadow content-card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-bolt me-2 text-warning"></i> Quick Actions</h5>
        </div>
        <div class="card-body py-4">
            <div class="quick-action-grid">
                <a href="/choims/modules/transfers/create.php" class="quick-action-card text-decoration-none">
                    <div class="action-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="action-label">Initiate Transfer</div>
                </a>

                <a href="/choims/modules/inventory/list.php" class="quick-action-card text-decoration-none">
                    <div class="action-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="action-label">View Consumables</div>
                </a>

                <a href="/choims/modules/assets/list.php" class="quick-action-card text-decoration-none">
                    <div class="action-icon">
                        <i class="fas fa-laptop"></i>
                    </div>
                    <div class="action-label">Manage Assets</div>
                </a>

                <a href="/choims/modules/transfers/list.php" class="quick-action-card text-decoration-none">
                    <div class="action-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="action-label">View Transfers</div>
                </a>

                <?php if ($showMonthlyUpdateButton): ?>
                <a href="#" class="quick-action-card text-decoration-none <?php echo $hasCompletedMonthlyUpdate ? 'bg-success text-white' : 'bg-warning text-dark'; ?>" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                    <div class="action-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="action-label">
                        <?php echo $hasCompletedMonthlyUpdate ? 'Update Completed' : 'Monthly Update'; ?>
                    </div>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Main Dashboard Content -->
    <div class="row">
        <!-- Left Column: Stats & Alerts -->
        <div class="col-lg-8">
            <!-- Transfers Waiting for Confirmation -->
            <div class="card content-card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-truck-loading me-2 text-warning"></i> Transfers Waiting for Confirmation
                    </h5>
                    <a href="/choims/modules/transfers/list.php" class="btn btn-sm btn-outline-primary">
                        View All Transfers
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (mysqli_num_rows($pendingTransfersResult) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Transfer</th>
                                        <th>Date</th>
                                        <th>From</th>
                                        <th>Items</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($transfer = mysqli_fetch_assoc($pendingTransfersResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'bg-success' : 'bg-primary'; ?> mb-1">
                                                        <?php echo $transfer['transfer_type']; ?>
                                                    </span>
                                                    <small class="text-muted">
                                                        <?php echo !empty($transfer['transaction_code']) ? $transfer['transaction_code'] : 'N/A'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td><?php echo formatDate($transfer['transfer_date']); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['source_location']); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo ($transfer['status'] == 'Approved by Logistics') ? 'bg-info' : 'bg-primary'; ?> status-badge">
                                                    <?php echo $transfer['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($transfer['transfer_type'] == 'Batch'): ?>
                                                        <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>

                                                        <?php if ($transfer['status'] == 'Approved by HIMU' || ($transfer['status'] == 'Approved by Logistics' && !$transfer['requires_himu_approval'])): ?>
                                                            <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>&action=receive" class="btn btn-success">
                                                                <i class="fas fa-check"></i> Confirm
                                                            </a>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>

                                                        <?php if ($transfer['status'] == 'Approved by HIMU' || ($transfer['status'] == 'Approved by Logistics' && !$transfer['requires_himu_approval'])): ?>
                                                            <a href="/choims/modules/transfers/approve.php?id=<?php echo $transfer['id']; ?>" class="btn btn-success">
                                                                <i class="fas fa-check"></i> Confirm
                                                            </a>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info m-3">
                            <i class="fas fa-info-circle me-2"></i> No transfers are currently waiting for confirmation.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Low Stock Items -->
            <div class="card content-card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                        Low Stock Items
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (mysqli_num_rows($lowStockResult) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th class="text-center">Quantity</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-end">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($item = mysqli_fetch_assoc($lowStockResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-box me-2 text-muted"></i>
                                                    <span><?php echo $item['sku_name']; ?></span>
                                                </div>
                                            </td>
                                            <td class="text-center"><?php echo $item['current_quantity']; ?></td>
                                            <td class="text-center">
                                                <?php
                                                if ($item['status'] == 'Out of Stock') {
                                                    echo '<span class="badge bg-danger status-badge">Out of Stock</span>';
                                                } else {
                                                    echo '<span class="badge bg-warning status-badge">Low Stock</span>';
                                                }
                                                ?>
                                            </td>
                                            <td class="text-end">
                                                <a href="/choims/modules/inventory/view.php?id=<?php echo $item['inventory_id']; ?>" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success m-3">
                            <i class="fas fa-check-circle me-2"></i> All items are adequately stocked.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Assets & Top Inventory Items -->
            <div class="row">
                <!-- Assets -->
                <div class="col-md-6 mb-4">
                    <div class="card content-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-laptop me-2 text-primary"></i> Recent Assets
                            </h5>
                            <a href="/choims/modules/assets/list.php" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body p-0">
                            <?php
                            // Get most recent assets
                            $recentAssetsQuery = "
                                SELECT fa.*, s.sku_name
                                FROM fixed_assets fa
                                JOIN sku_master s ON fa.sku_id = s.sku_id
                                WHERE fa.current_location_id = ? AND fa.is_active = 1 AND (fa.is_deleted = 0 OR fa.is_deleted IS NULL)
                                ORDER BY fa.created_at DESC
                                LIMIT 5
                            ";
                            $recentAssetsStmt = mysqli_prepare($conn, $recentAssetsQuery);
                            mysqli_stmt_bind_param($recentAssetsStmt, 'i', $userLocationId);
                            mysqli_stmt_execute($recentAssetsStmt);
                            $recentAssetsResult = mysqli_stmt_get_result($recentAssetsStmt);

                            if (mysqli_num_rows($recentAssetsResult) > 0):
                            ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Asset</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($asset = mysqli_fetch_assoc($recentAssetsResult)): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <span class="text-muted small me-2">#<?php echo $asset['asset_id']; ?></span>
                                                            <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" class="text-decoration-none">
                                                                <?php echo $asset['asset_name']; ?>
                                                            </a>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $asset['sku_name']; ?></td>
                                                    <td>
                                                        <?php
                                                        $statusClass = '';
                                                        switch ($asset['status']) {
                                                            case 'In use':
                                                                $statusClass = 'bg-primary';
                                                                break;
                                                            case 'Available':
                                                                $statusClass = 'bg-success';
                                                                break;
                                                            case 'Under Repair':
                                                                $statusClass = 'bg-warning';
                                                                break;
                                                            case 'Defective':
                                                                $statusClass = 'bg-danger';
                                                                break;
                                                            default:
                                                                $statusClass = 'bg-secondary';
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $statusClass; ?> status-badge"><?php echo $asset['status']; ?></span>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info m-3">
                                    <i class="fas fa-info-circle me-2"></i> No assets available.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Inventory -->
                <div class="col-md-6 mb-4">
                    <div class="card content-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-boxes me-2 text-success"></i> Top Inventory Items
                            </h5>
                            <a href="/choims/modules/inventory/list.php" class="btn btn-sm btn-outline-success">View All</a>
                        </div>
                        <div class="card-body p-0">
                            <?php
                            // Get top inventory items by quantity
                            $topInventoryQuery = "
                                SELECT i.*, s.sku_name
                                FROM consumable_inventory i
                                JOIN sku_master s ON i.sku_id = s.sku_id
                                WHERE i.location_id = ?
                                ORDER BY i.current_quantity DESC
                                LIMIT 5
                            ";
                            $topInventoryStmt = mysqli_prepare($conn, $topInventoryQuery);
                            mysqli_stmt_bind_param($topInventoryStmt, 'i', $userLocationId);
                            mysqli_stmt_execute($topInventoryStmt);
                            $topInventoryResult = mysqli_stmt_get_result($topInventoryStmt);

                            if (mysqli_num_rows($topInventoryResult) > 0):
                            ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Item</th>
                                                <th class="text-center">Quantity</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($item = mysqli_fetch_assoc($topInventoryResult)): ?>
                                                <tr>
                                                    <td><?php echo $item['sku_name']; ?></td>
                                                    <td class="text-center">
                                                        <span class="fw-bold"><?php echo $item['current_quantity']; ?></span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        if ($item['status'] == 'Available') {
                                                            echo '<span class="badge bg-success status-badge">Available</span>';
                                                        } elseif ($item['status'] == 'Low Stock') {
                                                            echo '<span class="badge bg-warning status-badge">Low Stock</span>';
                                                        } else {
                                                            echo '<span class="badge bg-danger status-badge">Out of Stock</span>';
                                                        }
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info m-3">
                                    <i class="fas fa-info-circle me-2"></i> No inventory items available.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Notifications & Activity -->
        <div class="col-lg-4">
            <!-- Modern System Notifications Area -->
            <div class="notifications-card">
                <div class="notifications-header">
                    <h5 class="notifications-title">
                        <i class="fas fa-bell"></i> System Notifications
                    </h5>
                    <a href="#" class="notifications-view-all">View All</a>
                </div>
                <div class="notifications-body">
                    <?php if (mysqli_num_rows($notificationsResult) > 0): ?>
                        <div class="notifications-list">
                            <?php while ($notification = mysqli_fetch_assoc($notificationsResult)): ?>
                                <?php
                                    // Determine notification icon based on type
                                    $iconClass = 'fas fa-info-circle';
                                    $iconType = '';

                                    if (stripos($notification['title'], 'transfer') !== false) {
                                        $iconClass = 'fas fa-exchange-alt';
                                        $iconType = 'transfer';
                                    } elseif (stripos($notification['title'], 'approved') !== false ||
                                              stripos($notification['title'], 'completed') !== false) {
                                        $iconClass = 'fas fa-check-circle';
                                        $iconType = 'success';
                                    } elseif (stripos($notification['title'], 'rejected') !== false ||
                                              stripos($notification['title'], 'low stock') !== false) {
                                        $iconClass = 'fas fa-exclamation-triangle';
                                        $iconType = 'warning';
                                    }
                                ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>">
                                    <?php if (!$notification['is_read']): ?>
                                        <div class="notification-badge"></div>
                                    <?php endif; ?>
                                    <div class="notification-icon <?php echo $iconType; ?>">
                                        <i class="<?php echo $iconClass; ?>"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">
                                            <?php echo htmlspecialchars($notification['title']); ?>
                                        </div>
                                        <div class="notification-message">
                                            <?php echo htmlspecialchars($notification['message']); ?>
                                        </div>
                                        <div class="notification-meta">
                                            <div class="notification-time">
                                                <i class="far fa-clock"></i>
                                                <?php echo date('M d, g:i A', strtotime($notification['created_at'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-notifications">
                            <i class="fas fa-check-circle"></i>
                            <p class="empty-notifications-text">You're all caught up! No new notifications at this time.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Transfers Summary -->
            <div class="card content-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-exchange-alt me-2 text-info"></i> Recent Transfers
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (mysqli_num_rows($transfersResult) > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php while($transfer = mysqli_fetch_assoc($transfersResult)): ?>
                                <div class="list-group-item border-0 py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <?php if ($transfer['source_location'] == $location['location_name']): ?>
                                                <div class="avatar-sm rounded-circle bg-danger text-white d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-arrow-right"></i>
                                                </div>
                                            <?php else: ?>
                                                <div class="avatar-sm rounded-circle bg-success text-white d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-arrow-left"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <span class="badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'bg-success' : 'bg-primary'; ?> me-2">
                                                    <?php echo $transfer['transfer_type']; ?>
                                                </span>
                                                <small class="text-muted"><?php echo formatDate($transfer['transfer_date']); ?></small>
                                            </div>
                                            <p class="mb-1 text-truncate">
                                                <?php echo htmlspecialchars($transfer['item_name']); ?>
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <?php
                                                    if ($transfer['source_location'] == $location['location_name']) {
                                                        echo '<span class="responsive-status outgoing"><i class="fas fa-arrow-right"></i> To: ' . htmlspecialchars($transfer['destination_location']) . '</span>';
                                                    } else {
                                                        echo '<span class="responsive-status incoming"><i class="fas fa-arrow-left"></i> From: ' . htmlspecialchars($transfer['source_location']) . '</span>';
                                                    }
                                                    ?>
                                                </small>
                                                <span class="badge <?php
                                                    switch ($transfer['status']) {
                                                        case 'Pending': echo 'bg-warning'; break;
                                                        case 'Approved by Logistics': echo 'bg-info'; break;
                                                        case 'Approved by HIMU': echo 'bg-info'; break;
                                                        case 'Completed': echo 'bg-success'; break;
                                                        case 'Rejected': echo 'bg-danger'; break;
                                                        default: echo 'bg-secondary';
                                                    }
                                                ?> status-badge">
                                                    <?php echo $transfer['status']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                        <div class="card-footer text-center py-3">
                            <a href="/choims/modules/transfers/list.php" class="text-decoration-none">View All Transfers <i class="fas fa-arrow-right ms-1"></i></a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info m-3">
                            <i class="fas fa-info-circle me-2"></i> No recent transfers.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 1rem;
}
</style>

<?php if ($showMonthlyUpdateButton): ?>
<!-- Monthly Inventory Update Modal -->
<div class="modal fade" id="monthlyUpdateModal" tabindex="-1" aria-labelledby="monthlyUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content inventory-modal">
            <div class="modal-header inventory-modal-header">
                <div class="modal-title-wrapper">
                    <div class="modal-icon-circle">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h5 class="modal-title" id="monthlyUpdateModalLabel">
                        Monthly Inventory Update - <?php echo date('F Y'); ?>
                    </h5>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body inventory-modal-body">
                <?php if ($hasCompletedMonthlyUpdate): ?>
                    <div class="alert alert-success inventory-alert">
                        <div class="alert-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="alert-heading">Update Complete!</h6>
                            <p class="mb-0">You have already completed your monthly inventory update for <?php echo date('F Y'); ?>.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="inventory-intro">
                        <h5 class="inventory-intro-title">Time for your monthly inventory check</h5>
                        <p class="inventory-intro-text">
                            Please confirm that you have reviewed and updated your consumables inventory for the month of <?php echo date('F Y'); ?>.
                            This will help ensure accurate inventory tracking and planning.
                        </p>
                    </div>

                    <form action="/choims/modules/inventory/monthly_update.php" method="post" class="inventory-update-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="update_quantities" value="1">

                    <!-- Inventory Items to Update -->
                    <div class="inventory-card">
                        <div class="inventory-card-header">
                            <div class="inventory-card-title">
                                <i class="fas fa-edit"></i>
                                <span>Update Inventory Quantities</span>
                            </div>
                        </div>
                        <div class="inventory-card-body">
                            <div class="inventory-table-container">
                                <table class="inventory-table" id="inventoryItemsTable">
                                    <thead>
                                        <tr>
                                            <th>SKU Code</th>
                                            <th>Item Name</th>
                                            <th>Category</th>
                                            <th>Current Qty</th>
                                            <th>New Qty</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Get inventory items for this location
                                        $itemsQuery = "
                                            SELECT
                                                i.inventory_id,
                                                i.current_quantity,
                                                i.status,
                                                s.sku_id,
                                                s.sku_code,
                                                s.sku_name,
                                                c.category_name
                                            FROM
                                                consumable_inventory i
                                            JOIN
                                                sku_master s ON i.sku_id = s.sku_id
                                            JOIN
                                                categories c ON s.category_id = c.category_id
                                            WHERE
                                                i.location_id = ?
                                            ORDER BY
                                                c.category_name, s.sku_name
                                        ";
                                        $itemsStmt = mysqli_prepare($conn, $itemsQuery);
                                        mysqli_stmt_bind_param($itemsStmt, 'i', $userLocationId);
                                        mysqli_stmt_execute($itemsStmt);
                                        $itemsResult = mysqli_stmt_get_result($itemsStmt);

                                        $rowCount = 0;

                                        while ($item = mysqli_fetch_assoc($itemsResult)):
                                            $rowCount++;
                                        ?>
                                        <tr class="inventory-item-row">
                                            <td><span class="sku-code"><?php echo $item['sku_code']; ?></span></td>
                                            <td><?php echo $item['sku_name']; ?></td>
                                            <td><span class="category-name"><?php echo $item['category_name']; ?></span></td>
                                            <td>
                                                <span class="current-qty"><?php echo $item['current_quantity']; ?></span>
                                            </td>
                                            <td>
                                                <input type="hidden" name="inventory_ids[]" value="<?php echo $item['inventory_id']; ?>">
                                                <input type="hidden" name="old_quantities[]" value="<?php echo $item['current_quantity']; ?>">
                                                <div class="quantity-input-wrapper">
                                                    <input type="number" name="new_quantities[]" class="quantity-input new-qty"
                                                           value="<?php echo $item['current_quantity']; ?>" min="0">
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($item['status'] == 'Available'): ?>
                                                    <span class="status-badge status-available">Available</span>
                                                <?php elseif ($item['status'] == 'Low Stock'): ?>
                                                    <span class="status-badge status-low">Low Stock</span>
                                                <?php else: ?>
                                                    <span class="status-badge status-out">Out of Stock</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="inventory-tip">
                        <div class="tip-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="tip-content">
                            <h6>Tip</h6>
                            <p>Update the quantities above to reflect your current inventory. Any changes will be recorded in the audit log.</p>
                        </div>
                    </div>

                    <div class="form-group notes-group">
                        <label for="notes" class="form-label">Notes or Comments (Optional)</label>
                        <textarea class="form-control notes-input" id="notes" name="notes" rows="3" placeholder="Enter any notes about your inventory update..."></textarea>
                    </div>

                    <div class="confirm-checkbox">
                        <input class="confirm-input" type="checkbox" id="confirmUpdate" required>
                        <label class="confirm-label" for="confirmUpdate">
                            I confirm that I have reviewed and updated the inventory for <?php echo $location['location_name']; ?> for the month of <?php echo date('F Y'); ?>.
                        </label>
                    </div>

                    <div class="form-actions">
                        <a href="/choims/modules/inventory/list.php" class="btn-review">
                            <i class="fas fa-list"></i> Review Inventory First
                        </a>
                        <button type="submit" class="btn-complete" id="submitUpdate" disabled>
                            <i class="fas fa-check-circle"></i> Complete Monthly Update
                        </button>
                    </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Enhanced inventory update modal interactions
    document.addEventListener('DOMContentLoaded', function() {
        const confirmCheckbox = document.getElementById('confirmUpdate');
        const submitButton = document.getElementById('submitUpdate');
        const inventoryItemRows = document.querySelectorAll('.inventory-item-row');

        // Enable/disable submit button based on checkbox with animation
        if (confirmCheckbox && submitButton) {
            confirmCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    submitButton.disabled = false;
                    submitButton.classList.add('btn-complete-active');
                } else {
                    submitButton.disabled = true;
                    submitButton.classList.remove('btn-complete-active');
                }
            });
        }

        // Enhanced quantity input interactions
        const newQtyInputs = document.querySelectorAll('.new-qty');
        if (newQtyInputs) {
            newQtyInputs.forEach(input => {
                // Add focus effects
                input.addEventListener('focus', function() {
                    this.closest('.quantity-input-wrapper').classList.add('input-focused');
                });

                input.addEventListener('blur', function() {
                    this.closest('.quantity-input-wrapper').classList.remove('input-focused');
                });

                // Highlight changed quantities with animation
                input.addEventListener('change', function() {
                    const row = this.closest('tr');
                    const currentQty = parseInt(row.querySelector('.current-qty').textContent);
                    const newQty = parseInt(this.value);

                    if (newQty !== currentQty) {
                        // Add changed state
                        this.classList.add('qty-changed');
                        row.classList.add('row-changed');

                        // Add visual indicator for increase/decrease
                        if (newQty > currentQty) {
                            this.classList.add('qty-increased');
                            this.classList.remove('qty-decreased');
                        } else if (newQty < currentQty) {
                            this.classList.add('qty-decreased');
                            this.classList.remove('qty-increased');
                        }
                    } else {
                        // Remove all change indicators
                        this.classList.remove('qty-changed', 'qty-increased', 'qty-decreased');
                        row.classList.remove('row-changed');
                    }
                });
            });
        }

        // Add animation to the modal when it opens
        const monthlyUpdateModal = document.getElementById('monthlyUpdateModal');
        if (monthlyUpdateModal) {
            monthlyUpdateModal.addEventListener('shown.bs.modal', function() {
                const modalContent = this.querySelector('.modal-content');
                modalContent.classList.add('modal-animate-in');
            });
        }
    });
</script>
<?php endif; ?>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>