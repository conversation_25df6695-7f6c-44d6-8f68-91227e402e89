<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get asset ID from URL
$assetId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$assetId) {
    setFlashMessage('error', 'Invalid asset ID');
    header("Location: index.php");
    exit;
}

// Fetch asset details
try {
    $query = "
        SELECT fa.*, i.name as item_name, i.sku, i.specifications, i.unit_cost,
               c.name as category_name,
               s.name as source_name,
               sup.name as supplier_name,
               u.full_name as assigned_to_name,
               d.name as department_name,
               hc.name as health_center_name
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN sources s ON fa.source_id = s.source_id
        JOIN suppliers sup ON fa.supplier_id = sup.supplier_id
        LEFT JOIN users u ON fa.assigned_to = u.user_id
        LEFT JOIN departments d ON fa.department_id = d.department_id
        LEFT JOIN health_centers hc ON fa.health_center_id = hc.health_center_id
        WHERE fa.asset_id = ?
    ";
    $stmt = $db->prepare($query);
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();

    if (!$asset) {
        setFlashMessage('error', 'Asset not found');
        header("Location: index.php");
        exit;
    }

    // Fetch transfer history
    $query = "
        SELECT tr.*, 
               sd.name as source_dept_name,
               shc.name as source_hc_name,
               dd.name as dest_dept_name,
               dhc.name as dest_hc_name,
               u.full_name as requested_by_name
        FROM transfer_requests tr
        LEFT JOIN departments sd ON tr.source_department_id = sd.department_id
        LEFT JOIN health_centers shc ON tr.source_health_center_id = shc.health_center_id
        LEFT JOIN departments dd ON tr.destination_department_id = dd.department_id
        LEFT JOIN health_centers dhc ON tr.destination_health_center_id = dhc.health_center_id
        JOIN users u ON tr.requested_by = u.user_id
        WHERE tr.item_id = ?
        ORDER BY tr.created_at DESC
    ";
    $stmt = $db->prepare($query);
    $stmt->execute([$asset['item_id']]);
    $transfers = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching asset details: " . $e->getMessage());
    setFlashMessage('error', 'Error loading asset details');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Fixed Asset Details</h1>
        <div>
            <a href="edit.php?id=<?php echo $assetId; ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Asset Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Asset Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="35%">SKU:</th>
                                    <td><?php echo htmlspecialchars($asset['sku']); ?></td>
                                </tr>
                                <tr>
                                    <th>Item Name:</th>
                                    <td><?php echo htmlspecialchars($asset['item_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Category:</th>
                                    <td><?php echo htmlspecialchars($asset['category_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Serial Number:</th>
                                    <td><?php echo htmlspecialchars($asset['serial_number']); ?></td>
                                </tr>
                                <tr>
                                    <th>Model:</th>
                                    <td><?php echo htmlspecialchars($asset['model']); ?></td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $asset['status'] === 'in_use' ? 'success' : 
                                                ($asset['status'] === 'available' ? 'primary' : 
                                                ($asset['status'] === 'under_repair' ? 'warning' : 'danger')); 
                                        ?>">
                                            <?php echo ucwords(str_replace('_', ' ', $asset['status'])); ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="35%">Source:</th>
                                    <td><?php echo htmlspecialchars($asset['source_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Supplier:</th>
                                    <td><?php echo htmlspecialchars($asset['supplier_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Unit Cost:</th>
                                    <td><?php echo formatCurrency($asset['unit_cost']); ?></td>
                                </tr>
                                <tr>
                                    <th>Purchase Date:</th>
                                    <td><?php echo formatDate($asset['purchase_date']); ?></td>
                                </tr>
                                <tr>
                                    <th>Warranty Expiry:</th>
                                    <td><?php echo $asset['warranty_expiry'] ? formatDate($asset['warranty_expiry']) : 'N/A'; ?></td>
                                </tr>
                                <tr>
                                    <th>Receipt Details:</th>
                                    <td>
                                        <?php echo ucwords(str_replace('_', ' ', $asset['receipt_type'])); ?>
                                        <?php if ($asset['series_number']): ?>
                                            (<?php echo htmlspecialchars($asset['series_number']); ?>)
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if ($asset['specifications']): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Specifications:</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($asset['specifications'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($asset['remarks']): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Remarks:</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($asset['remarks'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Assignment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Assignment Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="25%">Assigned To:</th>
                            <td><?php echo $asset['assigned_to_name'] ? htmlspecialchars($asset['assigned_to_name']) : 'Not Assigned'; ?></td>
                        </tr>
                        <tr>
                            <th>Location:</th>
                            <td>
                                <?php
                                if ($asset['department_name']) {
                                    echo 'Department: ' . htmlspecialchars($asset['department_name']);
                                } elseif ($asset['health_center_name']) {
                                    echo 'Health Center: ' . htmlspecialchars($asset['health_center_name']);
                                } else {
                                    echo 'Not Assigned';
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Local MR:</th>
                            <td><?php echo $asset['local_mr'] ? htmlspecialchars($asset['local_mr']) : 'N/A'; ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Transfer History -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Transfer History</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($transfers)): ?>
                        <p class="text-muted">No transfer history found.</p>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($transfers as $transfer): ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-<?php 
                                        echo $transfer['status'] === 'pending' ? 'warning' : 
                                            ($transfer['status'] === 'approved' ? 'success' : 
                                            ($transfer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                    ?>"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-date">
                                            <?php echo formatDate($transfer['created_at']); ?>
                                        </div>
                                        <h6>Transfer Request</h6>
                                        <p>
                                            From: <?php 
                                                echo $transfer['source_dept_name'] ? 
                                                    htmlspecialchars($transfer['source_dept_name']) : 
                                                    ($transfer['source_hc_name'] ? 
                                                        htmlspecialchars($transfer['source_hc_name']) : 'N/A'); 
                                            ?><br>
                                            To: <?php 
                                                echo $transfer['dest_dept_name'] ? 
                                                    htmlspecialchars($transfer['dest_dept_name']) : 
                                                    ($transfer['dest_hc_name'] ? 
                                                        htmlspecialchars($transfer['dest_hc_name']) : 'N/A'); 
                                            ?><br>
                                            Status: <span class="badge bg-<?php 
                                                echo $transfer['status'] === 'pending' ? 'warning' : 
                                                    ($transfer['status'] === 'approved' ? 'success' : 
                                                    ($transfer['status'] === 'rejected' ? 'danger' : 'secondary')); 
                                            ?>">
                                                <?php echo ucfirst($transfer['status']); ?>
                                            </span><br>
                                            Requested by: <?php echo htmlspecialchars($transfer['requested_by_name']); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 15px;
    height: 15px;
    border-radius: 50%;
}

.timeline-content {
    position: relative;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.timeline-date {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 5px;
}
</style>

<?php require_once '../../templates/footer.php'; ?>
