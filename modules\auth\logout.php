<?php
// Use relative paths
$base_path = dirname(dirname(dirname(__FILE__)));
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/detailed_audit_log.php');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Store user_id and other session data before destroying session
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$username = isset($_SESSION['username']) ? $_SESSION['username'] : null;
$location_id = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// Get location name if location_id is available
$location_name = null;
if ($location_id) {
    $location_query = "SELECT location_name FROM locations WHERE location_id = ?";
    $location_stmt = mysqli_prepare($conn, $location_query);
    mysqli_stmt_bind_param($location_stmt, 'i', $location_id);
    mysqli_stmt_execute($location_stmt);
    $location_result = mysqli_stmt_get_result($location_stmt);
    if ($location = mysqli_fetch_assoc($location_result)) {
        $location_name = $location['location_name'];
    }
    mysqli_stmt_close($location_stmt);
}

// Destroy session
session_destroy();

// Log logout activity if we had a user id
if ($user_id) {
    try {
        // Log to regular audit log
        logActivity($conn, 'Logout', 'User', $user_id, null, null);

        // Log to detailed audit system
        // Include IP address and location in the new_values
        $new_values = [
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'location_id' => $location_id,
            'location_name' => $location_name
        ];

        // Create a user object with the username for the summary
        $user = ['username' => $username];

        // Call logUserAction with the location name
        logUserAction($conn, $user_id, 'logout', $user_id, null, $new_values);
    } catch (Exception $e) {
        // Just continue with logout even if logging fails
        error_log("Error logging logout: " . $e->getMessage());
    }
}

// Redirect to login page
header('Location: /choims/modules/auth/login.php');
exit();
?>