<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Initialize variables
$categories = [];
$errors = [];
$formData = [
    'category_id' => 0,
    'name' => '',
    'code' => '',
    'description' => '',
    'status' => 'active'
];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $formData = [
        'category_id' => isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0,
        'name' => isset($_POST['name']) ? trim($_POST['name']) : '',
        'code' => isset($_POST['code']) ? trim(strtoupper($_POST['code'])) : '',
        'description' => isset($_POST['description']) ? trim($_POST['description']) : '',
        'status' => isset($_POST['status']) ? $_POST['status'] : 'active'
    ];

    // Validate form data
    if (empty($formData['name'])) {
        $errors[] = 'Name is required';
    }

    if (empty($formData['code'])) {
        $errors[] = 'Code is required';
    } elseif (strlen($formData['code']) > 20) {
        $errors[] = 'Code must be 20 characters or less';
    }

    // Check if code is unique
    if (!empty($formData['code'])) {
        try {
            $stmt = $db->prepare("SELECT category_id FROM categories WHERE code = ? AND category_id != ?");
            $stmt->execute([$formData['code'], $formData['category_id']]);
            if ($stmt->rowCount() > 0) {
                $errors[] = 'Code already exists';
            }
        } catch (PDOException $e) {
            error_log("Error checking category code: " . $e->getMessage());
            $errors[] = 'Database error occurred';
        }
    }

    // If no errors, process the operation
    if (empty($errors)) {
        try {
            // Check if it's an insert or update operation
            if ($formData['category_id'] > 0) {
                // Update existing category
                $stmt = $db->prepare("
                    UPDATE categories 
                    SET name = ?, code = ?, description = ?, status = ?
                    WHERE category_id = ?
                ");
                $result = $stmt->execute([
                    $formData['name'],
                    $formData['code'],
                    $formData['description'],
                    $formData['status'],
                    $formData['category_id']
                ]);

                if ($result) {
                    $auth->logActivity('Updated category', 'categories', $formData['category_id']);
                    setFlashMessage('success', 'Category updated successfully');
                } else {
                    $errors[] = 'Failed to update category';
                }
            } else {
                // Insert new category
                $stmt = $db->prepare("
                    INSERT INTO categories (name, code, description, status)
                    VALUES (?, ?, ?, ?)
                ");
                $result = $stmt->execute([
                    $formData['name'],
                    $formData['code'],
                    $formData['description'],
                    $formData['status']
                ]);

                if ($result) {
                    $categoryId = $db->lastInsertId();
                    $auth->logActivity('Added new category', 'categories', $categoryId);
                    setFlashMessage('success', 'Category added successfully');
                    // Reset form data for new entry
                    $formData = [
                        'category_id' => 0,
                        'name' => '',
                        'code' => '',
                        'description' => '',
                        'status' => 'active'
                    ];
                } else {
                    $errors[] = 'Failed to add category';
                }
            }
        } catch (PDOException $e) {
            error_log("Error processing category: " . $e->getMessage());
            $errors[] = 'Database error occurred';
        }
    }
}

// Handle delete operation
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $categoryId = (int)$_GET['id'];
    
    try {
        // Check if category is in use
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM items WHERE category_id = ?");
        $stmt->execute([$categoryId]);
        $itemCount = $stmt->fetch()['count'];
        
        if ($itemCount > 0) {
            setFlashMessage('error', 'Cannot delete category as it is being used by ' . $itemCount . ' item(s)');
        } else {
            // Delete the category
            $stmt = $db->prepare("DELETE FROM categories WHERE category_id = ?");
            $result = $stmt->execute([$categoryId]);
            
            if ($result) {
                $auth->logActivity('Deleted category', 'categories', $categoryId);
                setFlashMessage('success', 'Category deleted successfully');
            } else {
                setFlashMessage('error', 'Failed to delete category');
            }
        }
    } catch (PDOException $e) {
        error_log("Error deleting category: " . $e->getMessage());
        setFlashMessage('error', 'Database error occurred while deleting category');
    }
    
    // Redirect to avoid form resubmission
    header("Location: " . BASE_URL . "/admin/inventory/categories.php");
    exit;
}

// Handle edit operation
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $categoryId = (int)$_GET['id'];
    
    try {
        $stmt = $db->prepare("SELECT * FROM categories WHERE category_id = ?");
        $stmt->execute([$categoryId]);
        
        if ($category = $stmt->fetch()) {
            $formData = [
                'category_id' => $category['category_id'],
                'name' => $category['name'],
                'code' => $category['code'],
                'description' => $category['description'],
                'status' => $category['status']
            ];
        } else {
            setFlashMessage('error', 'Category not found');
        }
    } catch (PDOException $e) {
        error_log("Error fetching category: " . $e->getMessage());
        setFlashMessage('error', 'Database error occurred while fetching category');
    }
}

// Fetch all categories
try {
    $stmt = $db->query("SELECT * FROM categories ORDER BY name ASC");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    setFlashMessage('error', 'Database error occurred while fetching categories');
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Category Management</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Inventory Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <?php echo $formData['category_id'] > 0 ? 'Edit Category' : 'Add New Category'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <input type="hidden" name="category_id" value="<?php echo $formData['category_id']; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($formData['name']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="code" class="form-label">Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="code" name="code" 
                                   value="<?php echo htmlspecialchars($formData['code']); ?>" required
                                   maxlength="20" placeholder="e.g. IT-EQUIP">
                            <small class="text-muted">Unique code, max 20 characters</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($formData['description']); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo $formData['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo $formData['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo $formData['category_id'] > 0 ? 'Update Category' : 'Add Category'; ?>
                            </button>
                            
                            <?php if ($formData['category_id'] > 0): ?>
                                <a href="categories.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Categories List</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($categories)): ?>
                        <p class="text-muted text-center">No categories found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Code</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($categories as $category): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($category['name']); ?></td>
                                            <td><code><?php echo htmlspecialchars($category['code']); ?></code></td>
                                            <td>
                                                <?php if (!empty($category['description'])): ?>
                                                    <?php echo htmlspecialchars(strlen($category['description']) > 50 ? substr($category['description'], 0, 50) . '...' : $category['description']); ?>
                                                <?php else: ?>
                                                    <em class="text-muted">No description</em>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $category['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo ucfirst($category['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="categories.php?action=edit&id=<?php echo $category['category_id']; ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <a href="categories.php?action=delete&id=<?php echo $category['category_id']; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('Are you sure you want to delete this category? This action cannot be undone.')"
                                                       title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../templates/footer.php'; ?> 