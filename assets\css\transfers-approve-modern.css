/* Modern Transfers Approve Page Styling */
:root {
  /* Colors - <PERSON> Green Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --secondary: #81C784;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Dashboard Header */
.dashboard-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--primary-gradient);
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.dashboard-subtitle {
  color: var(--gray-500);
  font-size: 0.9rem;
}

.dashboard-actions {
  display: flex;
  gap: var(--space-3);
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  color: var(--white);
  background: var(--primary-gradient);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 20px rgba(46, 125, 50, 0.25), 0 6px 6px rgba(46, 125, 50, 0.22), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  transform: translateY(-3px);
  box-shadow: 0 15px 25px rgba(46, 125, 50, 0.3), 0 10px 10px rgba(46, 125, 50, 0.2), inset 0 -2px 5px rgba(0, 0, 0, 0.2);
  color: var(--white);
  text-decoration: none;
}

.action-btn:hover::after {
  opacity: 1;
}

.action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(78, 115, 223, 0.3), inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-btn i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

/* Transfer Info Card */
.transfer-info-card {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  overflow: hidden;
  animation: fadeIn 0.5s ease;
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-header i {
  color: var(--primary);
  font-size: 1.2rem;
}

.card-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--gray-800);
  font-size: 1.1rem;
}

.card-body {
  padding: var(--space-5);
}

/* Transfer Details */
.transfer-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-5);
}

.detail-group {
  margin-bottom: var(--space-4);
}

.detail-label {
  font-weight: 500;
  color: var(--gray-500);
  font-size: 0.85rem;
  margin-bottom: var(--space-1);
}

.detail-value {
  color: var(--gray-800);
  font-size: 0.95rem;
}

.detail-value.highlight {
  color: var(--primary);
  font-weight: 500;
}

/* Transfer Flow */
.transfer-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--space-5) 0;
  padding: var(--space-4);
  background-color: var(--primary-ultra-soft);
  border-radius: var(--radius);
}

.location-box {
  flex: 1;
  text-align: center;
  padding: var(--space-3);
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
}

.location-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--space-1);
}

.location-type {
  font-size: 0.8rem;
  color: var(--gray-500);
}

.transfer-arrow {
  color: var(--primary);
  font-size: 1.5rem;
  margin: 0 var(--space-3);
}

/* Approval Form Card */
.approval-form-card {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: var(--space-5);
  animation: fadeIn 0.5s ease;
}

.form-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.form-header i {
  color: var(--primary);
  font-size: 1.2rem;
}

.form-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--gray-800);
  font-size: 1.1rem;
}

.form-body {
  padding: var(--space-5);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--gray-800);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.form-control:focus {
  color: var(--gray-800);
  background-color: var(--white);
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-hint {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
  font-size: 0.8rem;
  color: var(--gray-500);
}

.form-hint i {
  color: var(--primary);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-5);
}

.action-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius);
  background-color: var(--gray-100);
  transition: var(--transition-fast);
}

.action-option:hover {
  background-color: var(--gray-200);
}

.action-option.approve {
  background-color: rgba(0, 200, 83, 0.1);
}

.action-option.approve:hover {
  background-color: rgba(0, 200, 83, 0.15);
}

.action-option.reject {
  background-color: rgba(255, 82, 82, 0.1);
}

.action-option.reject:hover {
  background-color: rgba(255, 82, 82, 0.15);
}

.action-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 600;
  color: var(--gray-800);
}

.action-header i {
  font-size: 1.1rem;
}

.action-option.approve .action-header i {
  color: var(--success);
}

.action-option.reject .action-header i {
  color: var(--danger);
}

.btn-approve, .btn-reject, .btn-receive {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: var(--radius);
  transition: var(--transition);
  width: 100%;
}

.btn-approve {
  background-color: var(--success);
  color: var(--white);
  box-shadow: 0 4px 6px rgba(0, 200, 83, 0.2);
}

.btn-approve:hover {
  background-color: #00b248;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 200, 83, 0.3);
}

.btn-reject {
  background-color: var(--danger);
  color: var(--white);
  box-shadow: 0 4px 6px rgba(255, 82, 82, 0.2);
}

.btn-reject:hover {
  background-color: #ff1744;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(255, 82, 82, 0.3);
}

.btn-receive {
  background-color: var(--info);
  color: var(--white);
  box-shadow: 0 4px 6px rgba(77, 208, 225, 0.2);
}

.btn-receive:hover {
  background-color: #00bcd4;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(77, 208, 225, 0.3);
}

/* Timeline Card */
.timeline-card {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: var(--space-5);
  animation: fadeIn 0.5s ease;
}

.timeline-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.timeline-header i {
  color: var(--primary);
  font-size: 1.2rem;
}

.timeline-header h5 {
  margin: 0;
  font-weight: 600;
  color: var(--gray-800);
  font-size: 1.1rem;
}

.timeline-body {
  padding: var(--space-5);
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0.5rem;
  width: 2px;
  background-color: var(--gray-200);
}

.timeline-item {
  position: relative;
  padding-bottom: var(--space-5);
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -2rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--white);
  border: 2px solid var(--gray-300);
  z-index: 1;
}

.timeline-item.completed::before {
  background-color: var(--success);
  border-color: var(--success);
}

.timeline-item.pending::before {
  background-color: var(--warning);
  border-color: var(--warning);
}

.timeline-item.rejected::before {
  background-color: var(--danger);
  border-color: var(--danger);
}

.timeline-date {
  font-size: 0.8rem;
  color: var(--gray-500);
  margin-bottom: var(--space-1);
}

.timeline-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--space-2);
}

.timeline-content {
  color: var(--gray-700);
  font-size: 0.9rem;
}

.timeline-notes {
  margin-top: var(--space-2);
  padding: var(--space-3);
  background-color: var(--gray-100);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  color: var(--gray-700);
}

/* Notes Box */
.notes-box {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background-color: var(--gray-100);
  border-radius: var(--radius);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.notes-box i {
  color: var(--primary);
  font-size: 1.1rem;
  margin-top: var(--space-1);
}

.notes-content {
  flex: 1;
}

.notes-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--space-2);
}

.notes-text {
  color: var(--gray-700);
  font-size: 0.9rem;
  white-space: pre-line;
}

/* Alert Styling */
.alert {
  padding: var(--space-4) var(--space-5);
  margin-bottom: var(--space-5);
  border: none;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  animation: fadeIn 0.5s ease;
}

.alert i {
  font-size: 1.2rem;
}

.alert-success {
  background-color: rgba(0, 200, 83, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(255, 82, 82, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(255, 213, 79, 0.1);
  color: #f57c00;
}

.alert-info {
  background-color: rgba(77, 208, 225, 0.1);
  color: var(--info);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .dashboard-actions {
    width: 100%;
  }

  .transfer-details {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--space-4);
  }

  .card-header, .form-header, .timeline-header {
    padding: var(--space-3) var(--space-4);
  }

  .card-body, .form-body, .timeline-body {
    padding: var(--space-4);
  }

  .transfer-flow {
    flex-direction: column;
    gap: var(--space-3);
  }

  .transfer-arrow {
    transform: rotate(90deg);
  }

  .action-btn {
    padding: 0.7rem 1.25rem;
    font-size: 0.9rem;
  }

  .action-btn i {
    margin-right: 0.5rem;
  }
}
