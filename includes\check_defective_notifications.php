<?php
/**
 * Check for defective asset notifications
 *
 * This script checks for new defective asset notifications for HIMU users
 * and returns them as JSON.
 */

// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/db_connect.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Ensure user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Ensure user has HIMU role
if (strtolower($_SESSION['role']) !== 'himu') {
    echo json_encode(['success' => false, 'message' => 'User does not have HIMU role']);
    exit;
}

// Get user ID
$user_id = $_SESSION['user_id'];

// Get last notification ID from request
$last_id = isset($_GET['last_id']) ? intval($_GET['last_id']) : 0;

// Query for new defective asset notifications (IT equipment only for HIMU users)
$query = "
    SELECT n.*, fa.asset_name
    FROM notifications n
    LEFT JOIN fixed_assets fa ON n.related_entity = 'asset' AND n.related_id = fa.asset_id
    LEFT JOIN sku_master sm ON fa.sku_id = sm.sku_id
    LEFT JOIN categories c ON sm.category_id = c.category_id
    WHERE n.user_id = ?
    AND n.notification_type = 'asset_defective'
    AND n.is_read = 0
    AND n.notification_id > ?
    AND c.category_id = 1 -- Only IT Equipment (category_id = 1) for HIMU users
    ORDER BY n.created_at DESC
    LIMIT 5
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'ii', $user_id, $last_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Prepare response
$notifications = [];
while ($row = mysqli_fetch_assoc($result)) {
    $notifications[] = $row;
}

// Get the highest notification ID
$max_id = 0;
if (!empty($notifications)) {
    $max_id = $notifications[0]['notification_id'];
}

// Return response
echo json_encode([
    'success' => true,
    'new_notifications' => count($notifications),
    'notifications' => $notifications,
    'max_id' => $max_id
]);
