<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has HIMU permission
if (!$auth->hasRole(ROLE_HIMU)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$currentUser = $auth->getCurrentUser();

try {
    // Fetch IT inventory statistics
    $stmt = $db->query("
        SELECT 
            (SELECT COUNT(*) FROM fixed_assets fa
             JOIN items i ON fa.item_id = i.item_id
             JOIN categories c ON i.category_id = c.category_id
             WHERE c.code IN ('ITE')) as total_it_equipment,
            
            (SELECT COUNT(*) FROM fixed_assets fa
             JOIN items i ON fa.item_id = i.item_id
             JOIN categories c ON i.category_id = c.category_id
             WHERE c.code IN ('ITE') AND fa.status = 'under_repair') as equipment_under_repair,
            
            (SELECT COUNT(DISTINCT inv.item_id) FROM inventory inv
             JOIN items i ON inv.item_id = i.item_id
             JOIN categories c ON i.category_id = c.category_id
             WHERE c.code IN ('ITS') AND inv.quantity > 0) as it_supplies_in_stock,
            
            (SELECT COUNT(DISTINCT inv.item_id) FROM inventory inv
             JOIN items i ON inv.item_id = i.item_id
             JOIN categories c ON i.category_id = c.category_id
             WHERE c.code IN ('ITS') AND inv.quantity <= inv.minimum_stock) as low_it_supplies
    ");
    $inventoryStats = $stmt->fetch();

    // Fetch pending IT transfers requiring HIMU approval
    $stmt = $db->query("
        SELECT tr.*, 
               i.name as item_name,
               CONCAT(u.first_name, ' ', u.last_name) as requester_name,
               sd.name as source_dept,
               shc.name as source_hc,
               dd.name as dest_dept,
               dhc.name as dest_hc,
               c.name as category_name
        FROM transfer_requests tr
        JOIN items i ON tr.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN users u ON tr.requested_by = u.user_id
        LEFT JOIN departments sd ON tr.source_department_id = sd.department_id
        LEFT JOIN health_centers shc ON tr.source_health_center_id = shc.health_center_id
        LEFT JOIN departments dd ON tr.destination_department_id = dd.department_id
        LEFT JOIN health_centers dhc ON tr.destination_health_center_id = dhc.health_center_id
        WHERE c.code IN ('ITE', 'ITS')
        AND tr.requires_himu_approval = 1
        AND tr.himu_approval_status = 'pending'
        ORDER BY tr.created_at DESC
        LIMIT 5
    ");
    $pendingTransfers = $stmt->fetchAll();

    // Fetch low IT supplies
    $stmt = $db->query("
        SELECT i.name, inv.quantity, inv.minimum_stock,
               COALESCE(d.name, hc.name) as location
        FROM inventory inv
        JOIN items i ON inv.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        LEFT JOIN departments d ON inv.department_id = d.department_id
        LEFT JOIN health_centers hc ON inv.health_center_id = hc.health_center_id
        WHERE c.code = 'ITS'
        AND inv.quantity <= inv.minimum_stock
        ORDER BY inv.quantity ASC
        LIMIT 5
    ");
    $lowStockAlerts = $stmt->fetchAll();

    // Fetch equipment under repair
    $stmt = $db->query("
        SELECT i.name, fa.serial_number, fa.model,
               COALESCE(d.name, hc.name) as location
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        LEFT JOIN departments d ON fa.department_id = d.department_id
        LEFT JOIN health_centers hc ON fa.health_center_id = hc.health_center_id
        WHERE c.code = 'ITE'
        AND fa.status = 'under_repair'
        ORDER BY fa.updated_at DESC
        LIMIT 5
    ");
    $underRepairEquipment = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error in HIMU dashboard: " . $e->getMessage());
    setFlashMessage('error', 'Error loading dashboard data');
}

require_once '../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">HIMU Dashboard</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Export Reports
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="../reports/it-inventory.php">IT Inventory Report</a></li>
                <li><a class="dropdown-item" href="../reports/it-transfers.php">IT Transfer Report</a></li>
                <li><a class="dropdown-item" href="../reports/maintenance.php">Equipment Maintenance Report</a></li>
            </ul>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <!-- IT Equipment Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total IT Equipment
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['total_it_equipment']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-laptop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Equipment Under Repair -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Under Repair
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['equipment_under_repair']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tools fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- IT Supplies -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                IT Supplies In Stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['it_supplies_in_stock']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low IT Supplies -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Low IT Supplies
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['low_it_supplies']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Pending IT Transfers -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock mr-1"></i>
                        Pending IT Transfers
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingTransfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>">
                                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                    <td><?php echo htmlspecialchars($transfer['category_name']); ?></td>
                                    <td>
                                        <?php 
                                        echo htmlspecialchars(isset($transfer['source_dept']) ? $transfer['source_dept'] : isset($transfer['source_hc']) ? $transfer['source_hc'] : 'N/A');
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        echo htmlspecialchars(isset($transfer['dest_dept']) ? $transfer['dest_dept'] : isset($transfer['dest_hc']) ? $transfer['dest_hc'] : 'N/A');
                                        ?>
                                    </td>
                                    <td>
                                        <a href="../inventory/transfers/approve.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            Review
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/transfers/pending.php" class="btn btn-primary btn-sm">
                            View All Pending Transfers
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Equipment Under Repair -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-tools mr-1"></i>
                        Equipment Under Repair
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Equipment</th>
                                    <th>Serial Number</th>
                                    <th>Model</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($underRepairEquipment as $equipment): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($equipment['name']); ?></td>
                                    <td><?php echo htmlspecialchars($equipment['serial_number']); ?></td>
                                    <td><?php echo htmlspecialchars($equipment['model']); ?></td>
                                    <td><?php echo htmlspecialchars($equipment['location']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/fixed-assets/maintenance.php" class="btn btn-warning btn-sm">
                            View Maintenance Records
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low IT Supplies -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        Low IT Supplies
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Location</th>
                                    <th>Current Stock</th>
                                    <th>Min. Stock</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockAlerts as $alert): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($alert['name']); ?></td>
                                    <td><?php echo htmlspecialchars($alert['location']); ?></td>
                                    <td class="text-danger"><?php echo number_format($alert['quantity']); ?></td>
                                    <td><?php echo number_format($alert['minimum_stock']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/consumables/it-supplies.php" class="btn btn-danger btn-sm">
                            View All IT Supplies
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../templates/footer.php'; ?>
