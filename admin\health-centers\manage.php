<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Check if user has admin permissions
$auth = Auth::getInstance();
if (!$auth->hasRole(ROLE_SUPERADMIN) && !$auth->hasRole(ROLE_GODMODE)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$db = Database::getInstance()->getConnection();
$message = '';
$healthCenters = [];

// Handle health center actions: create, update, delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Create new health center
        if ($_POST['action'] === 'create') {
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $location = isset($_POST['location']) ? trim($_POST['location']) : '';
            $contactPerson = isset($_POST['contact_person']) ? trim($_POST['contact_person']) : '';
            $contactNumber = isset($_POST['contact_number']) ? trim($_POST['contact_number']) : '';
            $email = isset($_POST['email']) ? trim($_POST['email']) : '';
            $status = isset($_POST['status']) ? $_POST['status'] : 'active';
            
            if (empty($name)) {
                $message = displayAlert('Health center name is required', 'danger');
            } else {
                try {
                    // Check if health center name already exists
                    $stmt = $db->prepare("SELECT health_center_id FROM health_centers WHERE name = ?");
                    $stmt->execute([$name]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = displayAlert('Health center name already exists', 'danger');
                    } else {
                        $stmt = $db->prepare("
                            INSERT INTO health_centers (name, location, contact_person, contact_number, email, status, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, NOW())
                        ");
                        $stmt->execute([$name, $location, $contactPerson, $contactNumber, $email, $status]);
                        
                        $message = displayAlert('Health center created successfully', 'success');
                    }
                } catch (PDOException $e) {
                    $message = displayAlert('Error creating health center: ' . $e->getMessage(), 'danger');
                }
            }
        }
        
        // Update existing health center
        else if ($_POST['action'] === 'update' && isset($_POST['health_center_id'])) {
            $healthCenterId = $_POST['health_center_id'];
            $name = isset($_POST['name']) ? trim($_POST['name']) : '';
            $location = isset($_POST['location']) ? trim($_POST['location']) : '';
            $contactPerson = isset($_POST['contact_person']) ? trim($_POST['contact_person']) : '';
            $contactNumber = isset($_POST['contact_number']) ? trim($_POST['contact_number']) : '';
            $email = isset($_POST['email']) ? trim($_POST['email']) : '';
            $status = isset($_POST['status']) ? $_POST['status'] : 'active';
            
            if (empty($name)) {
                $message = displayAlert('Health center name is required', 'danger');
            } else {
                try {
                    // Check if the updated name already exists for another health center
                    $stmt = $db->prepare("SELECT health_center_id FROM health_centers WHERE name = ? AND health_center_id != ?");
                    $stmt->execute([$name, $healthCenterId]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = displayAlert('Health center name already exists', 'danger');
                    } else {
                        $stmt = $db->prepare("
                            UPDATE health_centers 
                            SET name = ?, location = ?, contact_person = ?, contact_number = ?, email = ?, status = ?
                            WHERE health_center_id = ?
                        ");
                        $stmt->execute([$name, $location, $contactPerson, $contactNumber, $email, $status, $healthCenterId]);
                        
                        $message = displayAlert('Health center updated successfully', 'success');
                    }
                } catch (PDOException $e) {
                    $message = displayAlert('Error updating health center: ' . $e->getMessage(), 'danger');
                }
            }
        }
        
        // Delete health center
        else if ($_POST['action'] === 'delete' && isset($_POST['health_center_id'])) {
            $healthCenterId = $_POST['health_center_id'];
            
            try {
                // First check if there are users assigned to this health center
                $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE health_center_id = ?");
                $stmt->execute([$healthCenterId]);
                $userCount = $stmt->fetchColumn();
                
                if ($userCount > 0) {
                    $message = displayAlert('Cannot delete health center: There are ' . $userCount . ' users assigned to it', 'danger');
                } else {
                    // Also check if there are inventory items or transactions related to this health center
                    $stmt = $db->prepare("SELECT COUNT(*) FROM inventory WHERE health_center_id = ?");
                    $stmt->execute([$healthCenterId]);
                    $inventoryCount = $stmt->fetchColumn();
                    
                    if ($inventoryCount > 0) {
                        $message = displayAlert('Cannot delete health center: There are ' . $inventoryCount . ' inventory items assigned to it', 'danger');
                    } else {
                        // Safe to delete
                        $stmt = $db->prepare("DELETE FROM health_centers WHERE health_center_id = ?");
                        $stmt->execute([$healthCenterId]);
                        
                        $message = displayAlert('Health center deleted successfully', 'success');
                    }
                }
            } catch (PDOException $e) {
                $message = displayAlert('Error deleting health center: ' . $e->getMessage(), 'danger');
            }
        }
    }
}

// Get list of health centers
try {
    $stmt = $db->query("
        SELECT hc.*, 
               (SELECT COUNT(*) FROM users WHERE health_center_id = hc.health_center_id) as user_count,
               (SELECT COUNT(*) FROM inventory WHERE health_center_id = hc.health_center_id) as inventory_count
        FROM health_centers hc
        ORDER BY hc.name ASC
    ");
    $healthCenters = $stmt->fetchAll();
} catch (PDOException $e) {
    $message = displayAlert('Error fetching health centers: ' . $e->getMessage(), 'danger');
    $healthCenters = [];
}

// Include header
require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Health Center Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createHealthCenterModal">
            <i class="fas fa-plus"></i> Add New Health Center
        </button>
    </div>
    
    <?php echo $message; ?>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Health Centers</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="healthCentersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Location</th>
                            <th>Contact Person</th>
                            <th>Contact</th>
                            <th>Users</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($healthCenters as $center): ?>
                        <tr>
                            <td><?php echo $center['health_center_id']; ?></td>
                            <td><?php echo htmlspecialchars($center['name']); ?></td>
                            <td><?php echo htmlspecialchars($center['location']); ?></td>
                            <td><?php echo htmlspecialchars($center['contact_person']); ?></td>
                            <td>
                                <?php if (!empty($center['contact_number'])): ?>
                                <div><i class="fas fa-phone"></i> <?php echo htmlspecialchars($center['contact_number']); ?></div>
                                <?php endif; ?>
                                <?php if (!empty($center['email'])): ?>
                                <div><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($center['email']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $center['user_count']; ?></td>
                            <td>
                                <span class="badge bg-<?php echo $center['status'] === 'active' ? 'success' : 'danger'; ?>">
                                    <?php echo ucfirst($center['status']); ?>
                                </span>
                            </td>
                            <td><?php echo formatDate($center['created_at']); ?></td>
                            <td>
                                <button class="btn btn-sm btn-info edit-hc-btn" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editHealthCenterModal"
                                        data-hc-id="<?php echo $center['health_center_id']; ?>"
                                        data-name="<?php echo htmlspecialchars($center['name']); ?>"
                                        data-location="<?php echo htmlspecialchars($center['location']); ?>"
                                        data-contact-person="<?php echo htmlspecialchars($center['contact_person']); ?>"
                                        data-contact-number="<?php echo htmlspecialchars($center['contact_number']); ?>"
                                        data-email="<?php echo htmlspecialchars($center['email']); ?>"
                                        data-status="<?php echo $center['status']; ?>">
                                    <i class="fas fa-edit"></i>
                                </button>
                                
                                <?php if ($center['user_count'] == 0 && $center['inventory_count'] == 0): ?>
                                <button class="btn btn-sm btn-danger delete-hc-btn"
                                        data-bs-toggle="modal"
                                        data-bs-target="#deleteHealthCenterModal"
                                        data-hc-id="<?php echo $center['health_center_id']; ?>"
                                        data-name="<?php echo htmlspecialchars($center['name']); ?>">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Health Center Modal -->
<div class="modal fade" id="createHealthCenterModal" tabindex="-1" aria-labelledby="createHealthCenterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createHealthCenterModalLabel">Add New Health Center</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Health Center Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="location" name="location">
                    </div>
                    
                    <div class="mb-3">
                        <label for="contact_person" class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person">
                    </div>
                    
                    <div class="mb-3">
                        <label for="contact_number" class="form-label">Contact Number</label>
                        <input type="text" class="form-control" id="contact_number" name="contact_number">
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" selected>Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Health Center</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Health Center Modal -->
<div class="modal fade" id="editHealthCenterModal" tabindex="-1" aria-labelledby="editHealthCenterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editHealthCenterModalLabel">Edit Health Center</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" id="edit_health_center_id" name="health_center_id">
                    
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Health Center Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_location" class="form-label">Location</label>
                        <input type="text" class="form-control" id="edit_location" name="location">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_contact_person" class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_contact_number" class="form-label">Contact Number</label>
                        <input type="text" class="form-control" id="edit_contact_number" name="contact_number">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Health Center</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Health Center Modal -->
<div class="modal fade" id="deleteHealthCenterModal" tabindex="-1" aria-labelledby="deleteHealthCenterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteHealthCenterModalLabel">Delete Health Center</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this health center? This action cannot be undone.</p>
                <p>Health Center: <strong id="delete_hc_name"></strong></p>
            </div>
            <form method="post">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" id="delete_health_center_id" name="health_center_id">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Health Center</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#healthCentersTable').DataTable();
    
    // Edit health center button click
    const editHCBtns = document.querySelectorAll('.edit-hc-btn');
    editHCBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const hcId = this.getAttribute('data-hc-id');
            const name = this.getAttribute('data-name');
            const location = this.getAttribute('data-location');
            const contactPerson = this.getAttribute('data-contact-person');
            const contactNumber = this.getAttribute('data-contact-number');
            const email = this.getAttribute('data-email');
            const status = this.getAttribute('data-status');
            
            document.getElementById('edit_health_center_id').value = hcId;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_location').value = location;
            document.getElementById('edit_contact_person').value = contactPerson;
            document.getElementById('edit_contact_number').value = contactNumber;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_status').value = status;
        });
    });
    
    // Delete health center confirmation
    const deleteHCBtns = document.querySelectorAll('.delete-hc-btn');
    deleteHCBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const hcId = this.getAttribute('data-hc-id');
            const name = this.getAttribute('data-name');
            
            document.getElementById('delete_health_center_id').value = hcId;
            document.getElementById('delete_hc_name').textContent = name;
        });
    });
});
</script>

<?php
// Helper function for displaying alerts
function displayAlert($message, $type = 'info') {
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

require_once '../../templates/footer.php';
?> 