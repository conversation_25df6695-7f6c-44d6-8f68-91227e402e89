<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern stock in CSS
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-stock-in-modern.css">';

// Ensure user is logged in
requireLogin();

// Prevent superadmin from accessing this page (direct URL protection)
if (strtolower($_SESSION['role']) === 'superadmin') {
    // Redirect to list view instead
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

// Initialize variables
$userLocationId = getUserLocationId();
$selectedSku = '';
$inventoryItem = null;

// Get all consumable inventory items for the user's location
$inventoryQuery = "
    SELECT
        i.inventory_id, i.sku_id, i.current_quantity, i.low_stock_threshold, i.critical_threshold,
        i.status, l.location_name, s.sku_code, s.sku_name, c.category_name
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON i.location_id = l.location_id
    WHERE i.location_id = ?
    ORDER BY s.sku_code
";

// For logistics users, show consumable SKUs in their location
// Use a more direct query that gets all consumable items
if (strtolower($_SESSION['role']) === 'logistics' || strtolower($_SESSION['role']) === 'godmode') {
    // Get consumable SKUs for the user's location
    $inventoryQuery = "
        SELECT
            s.sku_id, s.sku_code, s.sku_name, c.category_name, c.category_id,
            i.inventory_id,
            i.current_quantity,
            i.low_stock_threshold,
            i.critical_threshold,
            i.status,
            l.location_name
        FROM sku_master s
        JOIN categories c ON s.category_id = c.category_id
        JOIN consumable_inventory i ON s.sku_id = i.sku_id
        JOIN locations l ON i.location_id = l.location_id
        WHERE i.location_id = ? AND
              (s.item_type = 'Consumable' OR
               c.category_name IN ('Office Supply', 'Medical Supply', 'IT Supply'))
        ORDER BY c.category_name, s.sku_code
    ";
    $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
    if (!$inventoryStmt) {
        die("Error in SQL prepare: " . mysqli_error($conn));
    }

    mysqli_stmt_bind_param($inventoryStmt, 'i', $userLocationId);
    if (!mysqli_stmt_execute($inventoryStmt)) {
        die("Error executing query: " . mysqli_stmt_error($inventoryStmt));
    }
} else {
    $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
    if (!$inventoryStmt) {
        die("Error in SQL prepare: " . mysqli_error($conn));
    }

    mysqli_stmt_bind_param($inventoryStmt, 'i', $userLocationId);
    if (!mysqli_stmt_execute($inventoryStmt)) {
        die("Error executing query: " . mysqli_stmt_error($inventoryStmt));
    }
}

$inventoryResult = mysqli_stmt_get_result($inventoryStmt);
if (!$inventoryResult) {
    die("Error getting result: " . mysqli_error($conn));
}

// Add debug count
$item_count = mysqli_num_rows($inventoryResult);

// Get suppliers for dropdown
$supplierQuery = "SELECT supplier_id, supplier_name FROM suppliers ORDER BY supplier_name";
$supplierResult = mysqli_query($conn, $supplierQuery);

// If a product is selected, get its details
if (isset($_GET['sku_id']) && !empty($_GET['sku_id'])) {
    $selectedSku = sanitizeInput($_GET['sku_id']);

    // For logistics users, only allow access to inventory items in their location
    if (strtolower($_SESSION['role']) === 'logistics' || strtolower($_SESSION['role']) === 'godmode') {
        $itemQuery = "
            SELECT
                s.sku_id, s.sku_code, s.sku_name, c.category_name, c.category_id,
                i.inventory_id,
                i.current_quantity,
                i.low_stock_threshold,
                i.critical_threshold,
                i.status,
                l.location_name,
                s.unit_of_measure
            FROM sku_master s
            JOIN categories c ON s.category_id = c.category_id
            JOIN consumable_inventory i ON s.sku_id = i.sku_id
            JOIN locations l ON i.location_id = l.location_id
            WHERE s.sku_id = ? AND i.location_id = ?
        ";
        $itemStmt = mysqli_prepare($conn, $itemQuery);
        mysqli_stmt_bind_param($itemStmt, 'ii', $selectedSku, $userLocationId);
    } else {
        $itemQuery = "
            SELECT
                i.inventory_id, i.current_quantity, i.low_stock_threshold, i.critical_threshold,
                i.status, l.location_name, s.sku_code, s.sku_name, c.category_name, s.sku_id,
                s.unit_of_measure
            FROM consumable_inventory i
            JOIN sku_master s ON i.sku_id = s.sku_id
            JOIN categories c ON s.category_id = c.category_id
            JOIN locations l ON i.location_id = l.location_id
            WHERE i.location_id = ? AND s.sku_id = ?
        ";
        $itemStmt = mysqli_prepare($conn, $itemQuery);
        mysqli_stmt_bind_param($itemStmt, 'ii', $userLocationId, $selectedSku);
    }

    mysqli_stmt_execute($itemStmt);
    $itemResult = mysqli_stmt_get_result($itemStmt);

    if (mysqli_num_rows($itemResult) > 0) {
        $inventoryItem = mysqli_fetch_assoc($itemResult);
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate inputs
    $inventory_id = sanitizeInput($_POST['inventory_id']);
    $quantity = sanitizeInput($_POST['quantity']);
    $unit_type = sanitizeInput($_POST['unit_type']);
    $supplier_id = !empty($_POST['supplier_id']) ? sanitizeInput($_POST['supplier_id']) : null;
    $unit_cost = !empty($_POST['unit_cost']) ? sanitizeInput($_POST['unit_cost']) : null;
    $reference_document = !empty($_POST['reference_document']) ? sanitizeInput($_POST['reference_document']) : null;
    $notes = !empty($_POST['notes']) ? sanitizeInput($_POST['notes']) : null;

    // Validate that quantity is > 0
    if ($quantity <= 0) {
        $error = "Quantity must be greater than zero.";
    } else {
        // Begin transaction
        mysqli_begin_transaction($conn);

        try {
            // Get current quantity
            $currentQuery = "SELECT current_quantity, sku_id, low_stock_threshold, critical_threshold FROM consumable_inventory WHERE inventory_id = ?";
            $currentStmt = mysqli_prepare($conn, $currentQuery);
            mysqli_stmt_bind_param($currentStmt, 'i', $inventory_id);
            mysqli_stmt_execute($currentStmt);
            $currentResult = mysqli_stmt_get_result($currentStmt);
            $currentData = mysqli_fetch_assoc($currentResult);

            $newQuantity = $currentData['current_quantity'] + $quantity;

            // Get the unit of measure for this inventory item
            $unitTypeQuery = "SELECT unit_of_measure FROM sku_master WHERE sku_id = ?";
            $unitTypeStmt = mysqli_prepare($conn, $unitTypeQuery);
            mysqli_stmt_bind_param($unitTypeStmt, 'i', $currentData['sku_id']);
            mysqli_stmt_execute($unitTypeStmt);
            $unitTypeResult = mysqli_stmt_get_result($unitTypeStmt);
            $unitTypeData = mysqli_fetch_assoc($unitTypeResult);
            $unitOfMeasure = $unitTypeData['unit_of_measure'] ?? '';

            // Determine status based on new quantity and unit type
            $status = 'Available';

            // Special handling for box unit type - only mark as Out of Stock if quantity is 0
            if ($unitOfMeasure === 'box') {
                if ($newQuantity <= 0) {
                    $status = 'Out of Stock';
                } elseif ($newQuantity <= $currentData['low_stock_threshold']) {
                    $status = 'Low Stock';
                }
            } else {
                // Standard handling for other unit types
                if ($newQuantity <= $currentData['critical_threshold']) {
                    $status = 'Out of Stock';
                } elseif ($newQuantity <= $currentData['low_stock_threshold']) {
                    $status = 'Low Stock';
                }
            }

            // Update inventory
            $updateQuery = "
                UPDATE consumable_inventory
                SET current_quantity = ?, status = ?, last_restock_date = NOW(), updated_at = NOW()
                WHERE inventory_id = ?
            ";
            $updateStmt = mysqli_prepare($conn, $updateQuery);
            mysqli_stmt_bind_param($updateStmt, 'isi', $newQuantity, $status, $inventory_id);
            mysqli_stmt_execute($updateStmt);

            // Record transaction
            $transactionQuery = "
                INSERT INTO consumable_transactions (
                    inventory_id, transaction_type, quantity,
                    destination_location_id, reference_document, supplier_id,
                    unit_cost, remarks, performed_by, transaction_date
                ) VALUES (
                    ?, 'Stock In', ?, ?, ?, ?, ?, ?, ?, NOW()
                )
            ";
            $transactionStmt = mysqli_prepare($conn, $transactionQuery);

            $remarks = "Unit Type: " . $unit_type;
            if (!empty($notes)) {
                $remarks .= ". Notes: " . $notes;
            }

            mysqli_stmt_bind_param(
                $transactionStmt,
                'iiissids',
                $inventory_id, $quantity, $userLocationId,
                $reference_document, $supplier_id, $unit_cost,
                $remarks, $_SESSION['user_id']
            );
            mysqli_stmt_execute($transactionStmt);

            // Create audit log
            createAuditLog(
                $_SESSION['user_id'],
                'update',
                'consumable_inventory',
                $inventory_id,
                json_encode(['current_quantity' => $currentData['current_quantity']]),
                json_encode(['current_quantity' => $newQuantity])
            );

            // Get transaction ID
            $transaction_id = mysqli_insert_id($conn);

            // Get SKU details for detailed logging
            $skuQuery = "SELECT sm.sku_name, sm.sku_code, c.category_name, l.location_name
                         FROM sku_master sm
                         JOIN categories c ON sm.category_id = c.category_id
                         JOIN consumable_inventory ci ON sm.sku_id = ci.sku_id
                         JOIN locations l ON ci.location_id = l.location_id
                         WHERE sm.sku_id = ?";
            $skuStmt = mysqli_prepare($conn, $skuQuery);
            mysqli_stmt_bind_param($skuStmt, 'i', $currentData['sku_id']);
            mysqli_stmt_execute($skuStmt);
            $skuResult = mysqli_stmt_get_result($skuStmt);
            $skuData = mysqli_fetch_assoc($skuResult);

            // Create detailed audit log for inventory update
            $old_values = [
                'current_quantity' => $currentData['current_quantity']
            ];
            $new_values = [
                'current_quantity' => $newQuantity,
                'status' => $status
            ];
            logConsumableAction($conn, $_SESSION['user_id'], 'update', $inventory_id, $old_values, $new_values);

            // Create detailed audit log for stock transaction
            $transaction_data = [
                'inventory_id' => $inventory_id,
                'transaction_type' => 'Stock In',
                'quantity' => $quantity,
                'destination_location_id' => $userLocationId,
                'destination_location_name' => $skuData['location_name'] ?? 'Unknown',
                'reference_document' => $reference_document,
                'supplier_id' => $supplier_id,
                'unit_cost' => $unit_cost,
                'remarks' => $remarks,
                'item_name' => $skuData['sku_name'] ?? 'Unknown',
                'item_sku' => $skuData['sku_code'] ?? 'Unknown',
                'item_category' => $skuData['category_name'] ?? 'Unknown'
            ];
            logStockTransaction($conn, $_SESSION['user_id'], 'stock_in', $transaction_id, $transaction_data);

            // Commit transaction
            mysqli_commit($conn);

            // Success message and redirect
            $_SESSION['success_message'] = "Added " . $quantity . " units to inventory successfully!";
            if (headers_sent()) {
                echo '<script>window.location.href = "/choims/modules/inventory/stock_in.php?success=1";</script>';
                exit();
            } else {
                header("Location: /choims/modules/inventory/stock_in.php?success=1");
                exit();
            }

        } catch (Exception $e) {
            // Rollback transaction on error
            mysqli_rollback($conn);
            $error = "Error adding stock: " . $e->getMessage();
        }
    }
}
?>

<div class="container-fluid">
    <!-- Page heading -->
    <div class="page-header mb-4 animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title"><i class="fas fa-plus-circle"></i> Add Stock</h1>
                <p class="page-subtitle">Add inventory items to your current stock</p>
            </div>
            <div class="col-md-4 text-md-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/choims/dashboards/logistics.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="/choims/modules/inventory/list.php">Inventory</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Stock In</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="d-flex w-100 justify-content-between align-items-center">
            <div><?php echo isset($_SESSION['success_message']) ? $_SESSION['success_message'] : 'Stock added successfully!'; ?></div>
            <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="d-flex w-100 justify-content-between align-items-center">
            <div><?php echo $error; ?></div>
            <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Product Selection Card -->
    <div class="card animate__animated animate__fadeInUp animate__faster">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-box"></i>
                Select Product
            </div>
        </div>
        <div class="card-body">
            <?php if (strtolower($_SESSION['role']) === 'logistics' || strtolower($_SESSION['role']) === 'godmode'): ?>
                <div class="alert alert-info d-flex align-items-center mb-3">
                    <div class="alert-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div>
                        Showing all consumable inventory items (<?php echo $item_count; ?> items found).
                    </div>
                </div>
            <?php endif; ?>

            <form method="get" action="" class="row g-3" id="productSelectForm">
                <div class="col-md-6">
                    <label for="sku_id" class="form-label fw-medium">Choose Product</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-search text-primary"></i></span>
                        <select class="form-select rounded-end-3 border-start-0" id="sku_id" name="sku_id" onchange="this.form.submit()">
                            <option value="">Select a Product</option>
                            <?php
                            $currentCategory = '';
                            mysqli_data_seek($inventoryResult, 0);

                            if ($item_count > 0) {
                                while ($item = mysqli_fetch_assoc($inventoryResult)):
                                    if ($currentCategory != $item['category_name']) {
                                        if ($currentCategory != '') {
                                            echo '</optgroup>';
                                        }
                                        echo '<optgroup label="' . htmlspecialchars($item['category_name']) . '">';
                                        $currentCategory = $item['category_name'];
                                    }
                            ?>
                                    <option value="<?php echo $item['sku_id']; ?>" <?php echo ($selectedSku == $item['sku_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($item['sku_code']); ?> - <?php echo htmlspecialchars($item['sku_name']); ?>
                                    </option>
                            <?php
                                endwhile;
                                if ($currentCategory != '') {
                                    echo '</optgroup>';
                                }
                            } else {
                                echo '<option disabled>No consumable items found</option>';
                            }
                            ?>
                        </select>
                    </div>
                    <?php if (strtolower($_SESSION['role']) === 'logistics' || strtolower($_SESSION['role']) === 'godmode'): ?>
                    <div class="form-text text-success small"><i class="fas fa-info-circle me-1"></i> As a logistics user, you can see consumable inventory items in your location, including IT supplies.</div>
                    <?php else: ?>
                    <div class="form-text small">Select the product you want to add stock to</div>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <?php if ($inventoryItem): ?>
    <!-- Product Info & Stock In Form -->
    <div class="row">
        <!-- Left Column: Product Info -->
        <div class="col-md-4">
            <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.1s;">
                <div class="card-header">
                    <div class="card-header-title">
                        <i class="fas fa-info-circle"></i>
                        Product Information
                    </div>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="product-avatar">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                        <h5 class="mb-1"><?php echo $inventoryItem['sku_name']; ?></h5>
                        <p class="mb-0 text-muted"><?php echo $inventoryItem['sku_code']; ?></p>
                        <span class="badge bg-info bg-opacity-10 rounded-pill px-3 py-2 mt-2">
                            <i class="fas fa-tag me-1"></i>
                            <?php echo $inventoryItem['category_name']; ?>
                        </span>
                    </div>

                    <div class="border-top pt-3">
                        <ul class="product-info-list">
                            <li class="product-info-item">
                                <span class="product-info-label">Current Stock</span>
                                <span class="product-info-value"><?php echo $inventoryItem['current_quantity']; ?></span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Status</span>
                                <?php
                                $statusBadgeClass = 'bg-success bg-opacity-10 text-success';
                                $statusIcon = 'fa-check-circle';
                                switch ($inventoryItem['status']) {
                                    case 'Available':
                                        $statusBadgeClass = 'bg-success bg-opacity-10 text-success';
                                        $statusIcon = 'fa-check-circle';
                                        break;
                                    case 'Low Stock':
                                        $statusBadgeClass = 'bg-warning bg-opacity-10 text-warning';
                                        $statusIcon = 'fa-exclamation-circle';
                                        break;
                                    case 'Out of Stock':
                                        $statusBadgeClass = 'bg-danger bg-opacity-10 text-danger';
                                        $statusIcon = 'fa-times-circle';
                                        break;
                                    default:
                                        $statusBadgeClass = 'bg-secondary bg-opacity-10 text-secondary';
                                        $statusIcon = 'fa-question-circle';
                                }
                                ?>
                                <span class="badge rounded-pill <?php echo $statusBadgeClass; ?> px-3 py-2">
                                    <i class="fas <?php echo $statusIcon; ?> me-1"></i>
                                    <?php echo $inventoryItem['status']; ?>
                                </span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Low Stock Threshold</span>
                                <span class="product-info-value"><?php echo $inventoryItem['low_stock_threshold']; ?></span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Critical Threshold</span>
                                <span class="product-info-value"><?php echo $inventoryItem['critical_threshold']; ?></span>
                            </li>
                            <li class="product-info-item">
                                <span class="product-info-label">Location</span>
                                <span class="product-info-value"><?php echo $inventoryItem['location_name']; ?></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Stock In Form -->
        <div class="col-md-8">
            <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.2s;">
                <div class="card-header">
                    <div class="card-header-title">
                        <i class="fas fa-plus-circle"></i>
                        Add Stock
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($inventoryItem['inventory_id'] == 0): ?>
                    <div class="alert alert-info d-flex align-items-center">
                        <div class="alert-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div>
                            You need to add this item to inventory before you can add stock to it.
                        </div>
                    </div>
                    <?php else: ?>
                    <form method="post" action="" id="stockInForm">
                        <input type="hidden" name="inventory_id" value="<?php echo $inventoryItem['inventory_id']; ?>">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="stock-calculator mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="stock-calculator-icon">
                                            <i class="fas fa-calculator"></i>
                                        </div>
                                        <div>
                                            <div class="stock-calculator-label">Stock After Addition</div>
                                            <div class="stock-calculator-value" id="newStockTotal"><?php echo $inventoryItem['current_quantity']; ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="quantity" class="form-label fw-medium">Quantity to Add <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-sort-numeric-up text-primary"></i></span>
                                    <input type="number" class="form-control border-start-0 border-end-0" id="quantity" name="quantity" min="1" placeholder="Enter quantity" required>
                                    <select class="form-select rounded-end-3" id="unit_type" name="unit_type" style="max-width: 120px;">
                                        <?php
                                        $selectedUnit = isset($inventoryItem['unit_of_measure']) ? $inventoryItem['unit_of_measure'] : 'pcs';

                                        // Check if unit_types table exists
                                        $checkTableQuery = "SHOW TABLES LIKE 'unit_types'";
                                        $checkTableResult = mysqli_query($conn, $checkTableQuery);

                                        if (mysqli_num_rows($checkTableResult) > 0) {
                                            // Table exists, fetch unit types from database
                                            $unitTypesQuery = "SELECT unit_type FROM unit_types ORDER BY unit_type";
                                            $unitTypesResult = mysqli_query($conn, $unitTypesQuery);

                                            $unitOptions = [];
                                            while ($unitType = mysqli_fetch_assoc($unitTypesResult)) {
                                                $unitOptions[] = $unitType['unit_type'];
                                            }
                                        } else {
                                            // Table doesn't exist, use hardcoded values
                                            $unitOptions = ['pcs', 'box', 'pack', 'bottle', 'roll', 'set', 'gallon', 'ream'];
                                        }

                                        // If the unit from database is not in our list, add it
                                        if (!in_array($selectedUnit, $unitOptions)) {
                                            $unitOptions[] = $selectedUnit;
                                        }

                                        foreach ($unitOptions as $unit) {
                                            $selected = ($unit == $selectedUnit) ? 'selected' : '';
                                            echo "<option value=\"{$unit}\" {$selected}>{$unit}</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <div class="form-text small">Enter the quantity you want to add to inventory</div>
                            </div>
                            <div class="col-md-6">
                                <label for="unit_cost" class="form-label fw-medium">Unit Cost</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-dollar-sign text-primary"></i></span>
                                    <input type="number" class="form-control rounded-end-3 border-start-0" id="unit_cost" name="unit_cost" min="0" step="0.01" placeholder="Enter unit cost">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="supplier_id" class="form-label fw-medium">Supplier</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-building text-primary"></i></span>
                                    <select class="form-select rounded-end-3 border-start-0" id="supplier_id" name="supplier_id">
                                        <option value="">Select Supplier</option>
                                        <?php mysqli_data_seek($supplierResult, 0); ?>
                                        <?php while ($supplier = mysqli_fetch_assoc($supplierResult)): ?>
                                            <option value="<?php echo $supplier['supplier_id']; ?>">
                                                <?php echo $supplier['supplier_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="reference_document" class="form-label fw-medium">Reference Document</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-file-invoice text-primary"></i></span>
                                    <input type="text" class="form-control rounded-end-3 border-start-0" id="reference_document" name="reference_document"
                                           placeholder="Invoice #, PO #, etc.">
                                </div>
                            </div>
                            <div class="col-12">
                                <label for="notes" class="form-label fw-medium">Notes</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-sticky-note text-primary"></i></span>
                                    <textarea class="form-control rounded-end-3 border-start-0" id="notes" name="notes" rows="2" placeholder="Add any additional notes or comments..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-3 mt-4">
                            <button type="submit" class="btn btn-primary px-4 py-2">
                                <i class="fas fa-plus-circle me-2"></i> Add Stock
                            </button>
                            <button type="reset" class="btn btn-outline-secondary px-4 py-2">
                                <i class="fas fa-undo me-1"></i> Reset
                            </button>
                        </div>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- JavaScript for Dynamic Stock Calculation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show loading animation
    document.querySelectorAll('.animate__animated').forEach(function(element, index) {
        element.style.opacity = '0';
        setTimeout(function() {
            element.style.opacity = '1';
        }, 100 * index);
    });

    const quantityInput = document.getElementById('quantity');
    const newStockTotal = document.getElementById('newStockTotal');
    const currentStock = <?php echo $inventoryItem ? $inventoryItem['current_quantity'] : 0; ?>;

    if (quantityInput && newStockTotal) {
        quantityInput.addEventListener('input', function() {
            const quantity = parseInt(this.value) || 0;
            newStockTotal.textContent = currentStock + quantity;

            // Change color based on new total
            if (currentStock + quantity <= <?php echo $inventoryItem ? $inventoryItem['critical_threshold'] : 0; ?>) {
                newStockTotal.className = 'stock-calculator-value text-danger';
            } else if (currentStock + quantity <= <?php echo $inventoryItem ? $inventoryItem['low_stock_threshold'] : 0; ?>) {
                newStockTotal.className = 'stock-calculator-value text-warning';
            } else {
                newStockTotal.className = 'stock-calculator-value text-success';
            }
        });
    }

    // Enhanced select options
    if (typeof jQuery !== 'undefined' && jQuery.fn.select2) {
        jQuery('#sku_id').select2({
            placeholder: 'Select a product',
            theme: 'classic'
        });

        jQuery('#supplier_id').select2({
            placeholder: 'Select a supplier',
            theme: 'classic'
        });
    }

    // Make unit type dropdown read-only if a product is selected
    const unitTypeSelect = document.getElementById('unit_type');
    if (unitTypeSelect) {
        // Disable the dropdown to prevent changes
        unitTypeSelect.disabled = true;

        // Add a visual indicator that it's read-only
        unitTypeSelect.parentElement.classList.add('opacity-75');

        // Add a tooltip to explain why it's read-only
        unitTypeSelect.title = "Unit type is set based on the product's configuration and cannot be changed";
    }

    // Add dismiss functionality to alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const closeButton = alert.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                alert.classList.add('animate__fadeOutUp');
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }
    });
});
</script>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>