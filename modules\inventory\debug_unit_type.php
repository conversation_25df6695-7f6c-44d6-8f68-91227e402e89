<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Get SKU ID from URL
$skuId = isset($_GET['sku_id']) ? intval($_GET['sku_id']) : 0;

if ($skuId > 0) {
    // Get SKU details
    $skuQuery = "SELECT sku_id, sku_code, sku_name, unit_of_measure FROM sku_master WHERE sku_id = ?";
    $skuStmt = mysqli_prepare($conn, $skuQuery);
    mysqli_stmt_bind_param($skuStmt, 'i', $skuId);
    mysqli_stmt_execute($skuStmt);
    $skuResult = mysqli_stmt_get_result($skuStmt);

    if ($sku = mysqli_fetch_assoc($skuResult)) {
        echo "<h2>SKU Details</h2>";
        echo "<table border='1'>";
        echo "<tr><th>SKU ID</th><th>SKU Code</th><th>SKU Name</th><th>Unit of Measure</th></tr>";
        echo "<tr>";
        echo "<td>" . $sku['sku_id'] . "</td>";
        echo "<td>" . $sku['sku_code'] . "</td>";
        echo "<td>" . $sku['sku_name'] . "</td>";
        echo "<td>" . $sku['unit_of_measure'] . "</td>";
        echo "</tr>";
        echo "</table>";

        // Get transactions for this SKU
        $transactionQuery = "
            SELECT ct.transaction_id, ct.transaction_type, ct.quantity, ct.remarks, ct.transaction_date
            FROM consumable_transactions ct
            JOIN consumable_inventory ci ON ct.inventory_id = ci.inventory_id
            WHERE ci.sku_id = ?
            ORDER BY ct.transaction_date DESC
            LIMIT 10
        ";

        $transactionStmt = mysqli_prepare($conn, $transactionQuery);
        mysqli_stmt_bind_param($transactionStmt, 'i', $skuId);
        mysqli_stmt_execute($transactionStmt);
        $transactionResult = mysqli_stmt_get_result($transactionStmt);

        echo "<h2>Recent Transactions</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Transaction ID</th><th>Type</th><th>Quantity</th><th>Remarks</th><th>Date</th></tr>";

        while ($transaction = mysqli_fetch_assoc($transactionResult)) {
            echo "<tr>";
            echo "<td>" . $transaction['transaction_id'] . "</td>";
            echo "<td>" . $transaction['transaction_type'] . "</td>";
            echo "<td>" . $transaction['quantity'] . "</td>";
            echo "<td>" . $transaction['remarks'] . "</td>";
            echo "<td>" . $transaction['transaction_date'] . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>SKU not found.</p>";
    }
} else {
    // List all SKUs
    $skusQuery = "SELECT sku_id, sku_code, sku_name, unit_of_measure FROM sku_master ORDER BY sku_id DESC LIMIT 20";
    $skusResult = mysqli_query($conn, $skusQuery);

    echo "<h2>Recent SKUs</h2>";
    echo "<table border='1'>";
    echo "<tr><th>SKU ID</th><th>SKU Code</th><th>SKU Name</th><th>Unit of Measure</th><th>Action</th></tr>";

    while ($sku = mysqli_fetch_assoc($skusResult)) {
        echo "<tr>";
        echo "<td>" . $sku['sku_id'] . "</td>";
        echo "<td>" . $sku['sku_code'] . "</td>";
        echo "<td>" . $sku['sku_name'] . "</td>";
        echo "<td>" . $sku['unit_of_measure'] . "</td>";
        echo "<td><a href='?sku_id=" . $sku['sku_id'] . "'>View Details</a></td>";
        echo "</tr>";
    }

    echo "</table>";
}

// Close connection
mysqli_close($conn);
?>
