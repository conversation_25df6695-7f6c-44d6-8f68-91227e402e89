/**
 * Modern Forms - React-like UI enhancements
 * For City Health Office Inventory Management System
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize floating labels for all form controls
    initFloatingLabels();
    
    // Add shadow effect on card hover
    initCardHoverEffects();
    
    // Add ripple effect on buttons
    initButtonRippleEffect();
});

/**
 * Initialize floating labels behavior
 */
function initFloatingLabels() {
    // Find all form controls in floating containers
    const formControls = document.querySelectorAll('.form-floating input, .form-floating select, .form-floating textarea');
    
    formControls.forEach(control => {
        // Set initial state based on value
        updateLabelState(control);
        
        // Add event listeners
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            updateLabelState(this);
        });
        
        control.addEventListener('input', function() {
            updateLabelState(this);
        });
        
        // For select elements, handle change events
        if (control.tagName === 'SELECT') {
            control.addEventListener('change', function() {
                updateLabelState(this);
            });
        }
    });
}

/**
 * Update the visual state of a control's label based on its value
 */
function updateLabelState(control) {
    if (control.value) {
        control.classList.add('has-value');
    } else {
        control.classList.remove('has-value');
    }
}

/**
 * Add subtle shadow animation on card hover
 */
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.react-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.12)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}

/**
 * Add material design-inspired ripple effect to buttons
 */
function initButtonRippleEffect() {
    const buttons = document.querySelectorAll('.react-btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Add CSS styles for the ripple effect
 */
(function addRippleStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .react-btn {
            position: relative;
            overflow: hidden;
        }
        
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            width: 100px;
            height: 100px;
            margin-top: -50px;
            margin-left: -50px;
            animation: ripple 0.6s linear;
            transform: scale(0);
            pointer-events: none;
        }
        
        .react-btn-secondary .ripple-effect {
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        @keyframes ripple {
            to {
                transform: scale(3);
                opacity: 0;
            }
        }
        
        .form-floating.focused label {
            color: var(--primary-green, #2E7D32) !important;
        }
    `;
    document.head.appendChild(style);
})(); 