/**
 * Logistics Defective Asset Notification Handler
 * 
 * This script handles notifications for defective office and medical equipment with sound alerts
 * specifically for Logistics roles.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run this for Logistics users
    if (typeof userRole !== 'undefined' && userRole.toLowerCase() === 'logistics') {
        // Check for defective asset notifications
        checkForLogisticsDefectiveNotifications();
        
        // Check every 30 seconds
        setInterval(checkForLogisticsDefectiveNotifications, 30000);
    }
    
    /**
     * Check for new defective office and medical equipment notifications
     */
    function checkForLogisticsDefectiveNotifications() {
        fetch('/choims/includes/check_logistics_defective_notifications.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.new_notifications > 0) {
                    // Play notification sound with higher priority
                    playDefectiveNotificationSound();
                    
                    // Show toast notification for each defective asset
                    data.notifications.forEach(notification => {
                        showDefectiveToast(notification);
                    });
                }
            })
            .catch(error => {
                console.error('Error checking for logistics defective notifications:', error);
            });
    }
    
    /**
     * Play a notification sound for defective assets
     * This uses a more urgent sound than regular notifications
     */
    function playDefectiveNotificationSound() {
        // Create audio element if it doesn't exist
        if (!window.defectiveNotificationSound) {
            window.defectiveNotificationSound = new Audio('/choims/assets/sounds/defective-alert.mp3');
            // Set volume higher than regular notifications
            window.defectiveNotificationSound.volume = 0.8;
        }
        
        // Play the sound
        try {
            window.defectiveNotificationSound.play();
        } catch (e) {
            console.log('Could not play defective notification sound:', e);
            // Fallback to regular notification sound
            if (typeof playNotificationSound === 'function') {
                playNotificationSound();
            }
        }
    }
    
    /**
     * Show a toast notification for a defective asset
     */
    function showDefectiveToast(notification) {
        // Create toast container if it doesn't exist
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        // Create a unique ID for this toast
        const toastId = 'toast-defective-' + Math.random().toString(36).substr(2, 9);
        
        // Get the first line of the message for the toast
        const messageLines = notification.message.split('\n');
        const shortMessage = messageLines[0];
        const hasMoreDetails = messageLines.length > 1;
        
        // Create toast HTML
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="15000">
                <div class="toast-header bg-danger text-white">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong class="me-auto">${notification.title}</strong>
                    <small>${new Date(notification.created_at).toLocaleTimeString()}</small>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body" style="word-wrap: break-word; overflow-wrap: break-word; word-break: break-word; white-space: normal; hyphens: auto; max-width: 300px;">
                    ${shortMessage}
                    ${hasMoreDetails ? '<div class="mt-1 small text-muted">Click for more details</div>' : ''}
                    <div class="mt-2">
                        <a href="/choims/modules/assets/view.php?id=${notification.related_id}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye"></i> View Asset
                        </a>
                        <a href="/choims/modules/logistics/maintenance.php" class="btn btn-sm btn-warning">
                            <i class="fas fa-tools"></i> Maintenance
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        // Add toast to container
        toastContainer.innerHTML += toastHtml;
        
        // Initialize and show the toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // Make toast clickable to view notification details
        toastElement.addEventListener('click', function(e) {
            // Don't trigger if clicking on close button or action buttons
            if (e.target.classList.contains('btn-close') || 
                e.target.closest('.btn') || 
                e.target.tagName === 'A' || 
                e.target.tagName === 'BUTTON' || 
                e.target.closest('a') || 
                e.target.closest('button')) {
                return;
            }
            
            // Navigate to asset view page
            window.location.href = `/choims/modules/assets/view.php?id=${notification.related_id}`;
        });
        
        // Mark notification as read when toast is closed
        toastElement.addEventListener('hidden.bs.toast', function() {
            fetch('/choims/modules/notifications/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `notification_id=${notification.notification_id}`
            });
        });
    }
    
    /**
     * Create a toast container if it doesn't exist
     */
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1080';
        document.body.appendChild(container);
        return container;
    }
});
