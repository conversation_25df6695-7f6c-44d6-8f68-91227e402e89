/* Enhanced Notifications Widget Styles */
:root {
  --notification-radius: 16px;
  --notification-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  --notification-shadow-hover: 0 15px 35px rgba(0, 0, 0, 0.12);
  --notification-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  --notification-bg: #ffffff;
  --notification-border: rgba(0, 0, 0, 0.05);
  --notification-unread: #f0f9ff;
  --notification-unread-border: #3b82f6;
  --notification-text: #333333;
  --notification-text-secondary: #6b7280;
  --notification-success: #10b981;
  --notification-warning: #f59e0b;
  --notification-danger: #ef4444;
  --notification-info: #3b82f6;
  --notification-primary: #2E7D32;
  --notification-header-bg: linear-gradient(135deg, #f9fafb, #ffffff);
}

/* Main Notification Card */
.notifications-widget {
  background: var(--notification-bg);
  border-radius: var(--notification-radius);
  box-shadow: var(--notification-shadow);
  overflow: hidden;
  border: none;
  margin-bottom: 1.5rem;
  transition: var(--notification-transition);
  position: relative;
}

.notifications-widget:hover {
  box-shadow: var(--notification-shadow-hover);
  transform: translateY(-3px);
}

/* Notification Header */
.notifications-widget .card-header {
  background: var(--notification-header-bg);
  border-bottom: 1px solid var(--notification-border);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-widget .card-header > div {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notifications-widget .card-header i {
  font-size: 1.2rem;
  color: var(--notification-primary);
  background-color: rgba(46, 125, 50, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--notification-transition);
}

.notifications-widget:hover .card-header i {
  transform: scale(1.1);
}

.notifications-widget .card-header .notification-count {
  background-color: var(--notification-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.15rem 0.5rem;
  border-radius: 20px;
  margin-left: 0.5rem;
}

/* Notification Body */
.notifications-widget .card-body {
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

.notifications-widget .card-body::-webkit-scrollbar {
  width: 6px;
}

.notifications-widget .card-body::-webkit-scrollbar-track {
  background: transparent;
}

.notifications-widget .card-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

/* Notification List */
.notifications-list {
  display: flex;
  flex-direction: column;
}

/* Notification Item */
.notification-item {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--notification-border);
  transition: var(--notification-transition);
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.notification-item.unread {
  background-color: var(--notification-unread);
  border-left: 4px solid var(--notification-unread-border);
}

.notification-item.unread:hover {
  background-color: rgba(59, 130, 246, 0.08);
}

/* Notification Icon */
.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1rem;
  transition: var(--notification-transition);
}

.notification-item:hover .notification-icon {
  transform: scale(1.1);
}

.notification-icon.info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--notification-info);
}

.notification-icon.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--notification-success);
}

.notification-icon.warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--notification-warning);
}

.notification-icon.danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--notification-danger);
}

.notification-icon.transfer {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

/* Notification Content */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.notification-title {
  font-weight: 600;
  color: var(--notification-text);
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--notification-text-secondary);
  white-space: nowrap;
  margin-left: 0.5rem;
}

.notification-message {
  color: var(--notification-text-secondary);
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.notification-action-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--notification-text-secondary);
  border: none;
  cursor: pointer;
  transition: var(--notification-transition);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.notification-action-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--notification-text);
}

.notification-action-btn i {
  font-size: 0.7rem;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--notification-info);
}

/* Empty State */
.empty-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-notifications-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

.empty-notifications-icon i {
  font-size: 2.5rem;
  color: #d1d5db;
}

.empty-notifications-title {
  font-weight: 600;
  color: var(--notification-text);
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.empty-notifications-text {
  color: var(--notification-text-secondary);
  font-size: 0.95rem;
  max-width: 250px;
  line-height: 1.5;
}

/* Footer */
.notifications-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--notification-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb;
}

.notifications-footer-actions {
  display: flex;
  gap: 0.75rem;
}

.notifications-footer-btn {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  background-color: white;
  color: var(--notification-text-secondary);
  border: 1px solid var(--notification-border);
  cursor: pointer;
  transition: var(--notification-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.notifications-footer-btn:hover {
  background-color: #f3f4f6;
  color: var(--notification-text);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.notifications-footer-btn i {
  font-size: 0.85rem;
}

.notifications-footer-btn.primary {
  background-color: var(--notification-primary);
  color: white;
  border: none;
}

.notifications-footer-btn.primary:hover {
  background-color: #1b5e20;
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(209, 213, 219, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(209, 213, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(209, 213, 219, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-item {
  animation: fadeIn 0.3s ease-out forwards;
  opacity: 0;
}

.notification-item:nth-child(1) { animation-delay: 0.1s; }
.notification-item:nth-child(2) { animation-delay: 0.2s; }
.notification-item:nth-child(3) { animation-delay: 0.3s; }
.notification-item:nth-child(4) { animation-delay: 0.4s; }
.notification-item:nth-child(5) { animation-delay: 0.5s; }

/* Responsive Adjustments */
@media (max-width: 768px) {
  .notifications-widget .card-header {
    padding: 1rem 1.25rem;
  }
  
  .notification-item {
    padding: 1rem 1.25rem;
  }
  
  .notification-icon {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
  
  .notifications-footer {
    padding: 0.75rem 1.25rem;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .notifications-footer-actions {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .notification-item {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .notification-icon {
    margin-bottom: 0.25rem;
  }
  
  .notification-header {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .notification-time {
    margin-left: 0;
  }
}
