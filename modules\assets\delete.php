<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in and has appropriate permissions
requireLogin();
if (!hasRole('Logistics')) {
    header('Location: /choims/access-denied.php');
    exit;
}

// Check if the fixed_assets table has the is_deleted column
$checkColumnQuery = "SHOW COLUMNS FROM fixed_assets LIKE 'is_deleted'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);

// If is_deleted column doesn't exist, add it
if (mysqli_num_rows($checkColumnResult) == 0) {
    $addColumnQuery = "ALTER TABLE fixed_assets ADD COLUMN is_deleted TINYINT(1) NOT NULL DEFAULT 0";
    if (!mysqli_query($conn, $addColumnQuery)) {
        die("Error adding is_deleted column: " . mysqli_error($conn));
    }
}

// Validate input
if (!isset($_POST['asset_id']) || empty($_POST['asset_id'])) {
    $_SESSION['error'] = "Invalid asset ID.";
    header('Location: /choims/modules/assets/list.php');
    exit;
}

$asset_id = sanitizeInput($_POST['asset_id']);

// Check if asset exists
$checkQuery = "SELECT * FROM fixed_assets WHERE asset_id = ?";
$stmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    $_SESSION['error'] = "Asset not found.";
    header('Location: /choims/modules/assets/list.php');
    exit;
}

$asset = mysqli_fetch_assoc($result);

// Get old values for audit log
$oldValues = json_encode([
    'is_deleted' => 0
]);

// Perform soft delete
$deleteQuery = "UPDATE fixed_assets SET is_deleted = 1 WHERE asset_id = ?";
$stmt = mysqli_prepare($conn, $deleteQuery);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);

if (mysqli_stmt_execute($stmt)) {
    // Log the action in both audit logs
    logAction(
        $conn,
        $_SESSION['user_id'],
        'Deleted asset',
        'asset',
        $asset_id,
        $oldValues,
        json_encode(['is_deleted' => 1])
    );

    // Log to detailed audit logs
    logFixedAssetAction(
        $conn,
        $_SESSION['user_id'],
        'delete',
        $asset_id,
        json_decode($oldValues, true),
        ['is_deleted' => 1]
    );

    // Get asset details for notification
    $assetDetailsQuery = "SELECT fa.asset_name, fa.serial_number, sm.sku_name, c.category_name, l.location_name
                          FROM fixed_assets fa
                          JOIN sku_master sm ON fa.sku_id = sm.sku_id
                          JOIN categories c ON sm.category_id = c.category_id
                          JOIN locations l ON fa.current_location_id = l.location_id
                          WHERE fa.asset_id = ?";
    $detailsStmt = mysqli_prepare($conn, $assetDetailsQuery);
    mysqli_stmt_bind_param($detailsStmt, 'i', $asset_id);
    mysqli_stmt_execute($detailsStmt);
    $detailsResult = mysqli_stmt_get_result($detailsStmt);
    $assetDetails = mysqli_fetch_assoc($detailsResult);

    // Create special notification for superadmins
    $notificationTitle = "ALERT: Asset Deleted";
    $notificationMessage = "Asset '{$assetDetails['asset_name']}' ({$assetDetails['sku_name']}) has been deleted by {$_SESSION['username']}\n";
    $notificationMessage .= "Category: {$assetDetails['category_name']}\n";
    $notificationMessage .= "Serial Number: {$assetDetails['serial_number']}\n";
    $notificationMessage .= "Location: {$assetDetails['location_name']}\n";
    $notificationMessage .= "Time: " . date('Y-m-d H:i:s');

    // Insert notification for all superadmins
    $notifyQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id, is_read, created_at)
                    SELECT user_id, 'asset_deleted', ?, ?, 'asset', ?, 0, NOW()
                    FROM users
                    WHERE LOWER(role) IN ('superadmin', 'godmode')";
    $notifyStmt = mysqli_prepare($conn, $notifyQuery);
    mysqli_stmt_bind_param($notifyStmt, 'ssi', $notificationTitle, $notificationMessage, $asset_id);
    mysqli_stmt_execute($notifyStmt);

    $_SESSION['success'] = "Asset has been deleted successfully.";
} else {
    $_SESSION['error'] = "Failed to delete asset: " . mysqli_error($conn);
}

// Redirect back to asset list
header('Location: /choims/modules/assets/list.php');
exit;
?>