<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in
requireLogin();

// Initialize response
$response = ['exists' => false];

// Check if required parameters are provided
if (isset($_GET['sku_id']) && isset($_GET['location_id'])) {
    $sku_id = (int)$_GET['sku_id'];
    $location_id = (int)$_GET['location_id'];
    
    // Check if this SKU already exists in inventory at this location
    $checkQuery = "SELECT inventory_id FROM consumable_inventory WHERE sku_id = ? AND location_id = ?";
    $checkStmt = mysqli_prepare($conn, $checkQuery);
    mysqli_stmt_bind_param($checkStmt, 'ii', $sku_id, $location_id);
    mysqli_stmt_execute($checkStmt);
    $checkResult = mysqli_stmt_get_result($checkStmt);
    
    if (mysqli_num_rows($checkResult) > 0) {
        $response['exists'] = true;
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit;
?>
