<?php
session_start();
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/detailed_audit_log.php';

// Check if user is logged in and has superadmin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || ($_SESSION['role'] !== 'superadmin' && $_SESSION['role'] !== 'godmode')) {
    $_SESSION['error'] = "You don't have permission to access this page.";
    header("Location: ../../index.php");
    exit();
}

// Log activity
logActivity($_SESSION['user_id'], 'Accessed Suppliers Management Page');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_supplier'])) {
        $supplier_name = trim($_POST['supplier_name']);
        $contact_person = trim($_POST['contact_person']);
        $contact_number = trim($_POST['contact_number']);
        $email = trim($_POST['email']);
        $address = trim($_POST['address']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Validation
        if (empty($supplier_name)) {
            $_SESSION['error'] = "Supplier name is required.";
        } else {
            // Check if supplier name already exists
            $check_sql = "SELECT supplier_id FROM suppliers WHERE supplier_name = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("s", $supplier_name);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Supplier with this name already exists.";
            } else {
                // Insert new supplier
                $sql = "INSERT INTO suppliers (supplier_name, contact_person, contact_number, email, address, is_active) VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sssssi", $supplier_name, $contact_person, $contact_number, $email, $address, $is_active);

                if ($stmt->execute()) {
                    $supplier_id = $stmt->insert_id;
                    $_SESSION['success'] = "Supplier added successfully.";

                    // Log to regular audit log
                    logActivity($_SESSION['user_id'], 'Added new supplier: ' . $supplier_name);

                    // Log to detailed audit system
                    $new_values = [
                        'supplier_name' => $supplier_name,
                        'contact_person' => $contact_person,
                        'contact_number' => $contact_number,
                        'email' => $email,
                        'address' => $address,
                        'is_active' => $is_active
                    ];
                    logSupplierAction($conn, $_SESSION['user_id'], 'create', $supplier_id, null, $new_values);
                } else {
                    $_SESSION['error'] = "Error adding supplier: " . $conn->error;
                }

                $stmt->close();
            }
            $check_stmt->close();
        }
    } elseif (isset($_POST['edit_supplier'])) {
        $supplier_id = $_POST['supplier_id'];
        $supplier_name = trim($_POST['supplier_name']);
        $contact_person = trim($_POST['contact_person']);
        $contact_number = trim($_POST['contact_number']);
        $email = trim($_POST['email']);
        $address = trim($_POST['address']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Validation
        if (empty($supplier_name)) {
            $_SESSION['error'] = "Supplier name is required.";
        } else {
            // Check if supplier name already exists (excluding current supplier)
            $check_sql = "SELECT supplier_id FROM suppliers WHERE supplier_name = ? AND supplier_id != ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("si", $supplier_name, $supplier_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Supplier with this name already exists.";
            } else {
                // Update supplier
                $sql = "UPDATE suppliers SET supplier_name = ?, contact_person = ?, contact_number = ?, email = ?, address = ?, is_active = ? WHERE supplier_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sssssii", $supplier_name, $contact_person, $contact_number, $email, $address, $is_active, $supplier_id);

                // Get old supplier data for logging
                $old_data_sql = "SELECT * FROM suppliers WHERE supplier_id = ?";
                $old_data_stmt = $conn->prepare($old_data_sql);
                $old_data_stmt->bind_param("i", $supplier_id);
                $old_data_stmt->execute();
                $old_data_result = $old_data_stmt->get_result();
                $old_data = $old_data_result->fetch_assoc();
                $old_data_stmt->close();

                if ($stmt->execute()) {
                    $_SESSION['success'] = "Supplier updated successfully.";

                    // Log to regular audit log
                    logActivity($_SESSION['user_id'], 'Updated supplier: ' . $supplier_name);

                    // Log to detailed audit system
                    $old_values = [
                        'supplier_name' => $old_data['supplier_name'],
                        'contact_person' => $old_data['contact_person'],
                        'contact_number' => $old_data['contact_number'],
                        'email' => $old_data['email'],
                        'address' => $old_data['address'],
                        'is_active' => $old_data['is_active']
                    ];

                    $new_values = [
                        'supplier_name' => $supplier_name,
                        'contact_person' => $contact_person,
                        'contact_number' => $contact_number,
                        'email' => $email,
                        'address' => $address,
                        'is_active' => $is_active
                    ];

                    logSupplierAction($conn, $_SESSION['user_id'], 'update', $supplier_id, $old_values, $new_values);
                } else {
                    $_SESSION['error'] = "Error updating supplier: " . $conn->error;
                }

                $stmt->close();
            }
            $check_stmt->close();
        }
    } elseif (isset($_POST['toggle_supplier'])) {
        $supplier_id = $_POST['supplier_id'];
        $current_status = $_POST['current_status'];
        $new_status = $current_status == 1 ? 0 : 1;

        // Update supplier status
        $sql = "UPDATE suppliers SET is_active = ? WHERE supplier_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $new_status, $supplier_id);

        // Get supplier details for logging
        $supplier_sql = "SELECT * FROM suppliers WHERE supplier_id = ?";
        $supplier_stmt = $conn->prepare($supplier_sql);
        $supplier_stmt->bind_param("i", $supplier_id);
        $supplier_stmt->execute();
        $supplier_result = $supplier_stmt->get_result();
        $supplier_data = $supplier_result->fetch_assoc();
        $supplier_stmt->close();

        if ($stmt->execute()) {
            $_SESSION['success'] = "Supplier status updated successfully.";
            $status_text = $new_status == 1 ? 'activated' : 'deactivated';

            // Log to regular audit log
            logActivity($_SESSION['user_id'], 'Supplier ' . $supplier_data['supplier_name'] . ' ' . $status_text);

            // Log to detailed audit system
            $old_values = [
                'is_active' => $current_status
            ];

            $new_values = [
                'is_active' => $new_status
            ];

            logSupplierAction($conn, $_SESSION['user_id'], 'update', $supplier_id, $old_values, $new_values);
        } else {
            $_SESSION['error'] = "Error updating supplier status: " . $conn->error;
        }

        $stmt->close();
    }

    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Get all suppliers
$sql = "SELECT * FROM suppliers ORDER BY supplier_name ASC";
$result = $conn->query($sql);
$suppliers = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $suppliers[] = $row;
    }
}

// Include header
include_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>Suppliers Management</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                        <i class="fas fa-plus-circle me-1"></i> Add Supplier
                    </button>

                    <div class="table-responsive">
                        <table id="suppliersTable" class="table table-bordered table-striped">
                            <thead class="bg-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Supplier Name</th>
                                    <th>Contact Person</th>
                                    <th>Contact Number</th>
                                    <th>Email</th>
                                    <th>Address</th>
                                    <th>Status</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($suppliers as $supplier): ?>
                                <tr>
                                    <td><?php echo $supplier['supplier_id']; ?></td>
                                    <td><?php echo htmlspecialchars($supplier['supplier_name']); ?></td>
                                    <td><?php echo htmlspecialchars(isset($supplier['contact_person']) ? $supplier['contact_person'] : ''); ?></td>
                                    <td><?php echo htmlspecialchars(isset($supplier['contact_number']) ? $supplier['contact_number'] : ''); ?></td>
                                    <td><?php echo htmlspecialchars(isset($supplier['email']) ? $supplier['email'] : ''); ?></td>
                                    <td><?php echo htmlspecialchars(isset($supplier['address']) ? $supplier['address'] : ''); ?></td>
                                    <td>
                                        <?php if ($supplier['is_active'] == 1): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y h:i A', strtotime($supplier['created_at'])); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning edit-supplier"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editSupplierModal"
                                                data-id="<?php echo $supplier['supplier_id']; ?>"
                                                data-name="<?php echo htmlspecialchars($supplier['supplier_name']); ?>"
                                                data-contact="<?php echo htmlspecialchars(isset($supplier['contact_person']) ? $supplier['contact_person'] : ''); ?>"
                                                data-number="<?php echo htmlspecialchars(isset($supplier['contact_number']) ? $supplier['contact_number'] : ''); ?>"
                                                data-email="<?php echo htmlspecialchars(isset($supplier['email']) ? $supplier['email'] : ''); ?>"
                                                data-address="<?php echo htmlspecialchars(isset($supplier['address']) ? $supplier['address'] : ''); ?>"
                                                data-active="<?php echo $supplier['is_active']; ?>">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>

                                        <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to <?php echo $supplier['is_active'] == 1 ? 'deactivate' : 'activate'; ?> this supplier?');">
                                            <input type="hidden" name="supplier_id" value="<?php echo $supplier['supplier_id']; ?>">
                                            <input type="hidden" name="current_status" value="<?php echo $supplier['is_active']; ?>">
                                            <button type="submit" name="toggle_supplier" class="btn btn-sm <?php echo $supplier['is_active'] == 1 ? 'btn-danger' : 'btn-success'; ?>">
                                                <i class="fas <?php echo $supplier['is_active'] == 1 ? 'fa-ban' : 'fa-check-circle'; ?>"></i>
                                                <?php echo $supplier['is_active'] == 1 ? 'Deactivate' : 'Activate'; ?>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Supplier Modal -->
<div class="modal fade" id="addSupplierModal" tabindex="-1" aria-labelledby="addSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addSupplierModalLabel"><i class="fas fa-plus-circle me-2"></i>Add New Supplier</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="supplier_name" class="form-label">Supplier Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="supplier_name" name="supplier_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact_person" class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="contact_person" name="contact_person">
                    </div>
                    <div class="mb-3">
                        <label for="contact_number" class="form-label">Contact Number</label>
                        <input type="text" class="form-control" id="contact_number" name="contact_number">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="add_supplier" class="btn btn-primary">Add Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Supplier Modal -->
<div class="modal fade" id="editSupplierModal" tabindex="-1" aria-labelledby="editSupplierModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="editSupplierModalLabel"><i class="fas fa-edit me-2"></i>Edit Supplier</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_supplier_id" name="supplier_id">
                    <div class="mb-3">
                        <label for="edit_supplier_name" class="form-label">Supplier Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_supplier_name" name="supplier_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_contact_person" class="form-label">Contact Person</label>
                        <input type="text" class="form-control" id="edit_contact_person" name="contact_person">
                    </div>
                    <div class="mb-3">
                        <label for="edit_contact_number" class="form-label">Contact Number</label>
                        <input type="text" class="form-control" id="edit_contact_number" name="contact_number">
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">Address</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="2"></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="edit_supplier" class="btn btn-warning">Update Supplier</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#suppliersTable').DataTable({
        order: [[1, 'asc']],
        pageLength: 10,
        responsive: true
    });

    // Handle edit supplier modal data
    const editButtons = document.querySelectorAll('.edit-supplier');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const contact = this.getAttribute('data-contact');
            const number = this.getAttribute('data-number');
            const email = this.getAttribute('data-email');
            const address = this.getAttribute('data-address');
            const active = this.getAttribute('data-active');

            document.getElementById('edit_supplier_id').value = id;
            document.getElementById('edit_supplier_name').value = name;
            document.getElementById('edit_contact_person').value = contact;
            document.getElementById('edit_contact_number').value = number;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_address').value = address;
            document.getElementById('edit_is_active').checked = active === '1';
        });
    });
});
</script>

<?php
// Include footer
include_once '../../includes/footer.php';
?>