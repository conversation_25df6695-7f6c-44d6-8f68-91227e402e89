<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in and has appropriate role
requireLogin();
requireRole('GodMode', 'Superadmin');

// Initialize variables
$category_name = '';
$description = '';
$requires_himu_approval = 0;
$error_message = '';
$success_message = '';

// Process form submission for adding/editing category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_category'])) {
    // Get form data
    $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : null;
    $category_name = sanitizeInput($_POST['category_name']);
    $description = sanitizeInput($_POST['description']);
    $requires_himu_approval = isset($_POST['requires_himu_approval']) ? 1 : 0;
    
    // Validate form data
    $errors = [];
    
    if (empty($category_name)) {
        $errors[] = "Category name is required.";
    }
    
    // If there are no errors, save the category
    if (empty($errors)) {
        if ($category_id) {
            // Update existing category
            $query = "
                UPDATE categories 
                SET category_name = ?, description = ?, requires_himu_approval = ? 
                WHERE category_id = ?
            ";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'ssii', $category_name, $description, $requires_himu_approval, $category_id);
            
            if (mysqli_stmt_execute($stmt)) {
                // Create audit log
                createAuditLog(
                    $_SESSION['user_id'], 
                    "Category updated", 
                    'category', 
                    $category_id, 
                    null, 
                    json_encode([
                        'category_name' => $category_name,
                        'description' => $description,
                        'requires_himu_approval' => $requires_himu_approval
                    ])
                );
                
                $success_message = "Category updated successfully.";
                
                // Reset form values
                $category_name = '';
                $description = '';
                $requires_himu_approval = 0;
            } else {
                $error_message = "Error updating category: " . mysqli_error($conn);
            }
        } else {
            // Create new category
            $query = "
                INSERT INTO categories (category_name, description, requires_himu_approval, created_at) 
                VALUES (?, ?, ?, NOW())
            ";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'ssi', $category_name, $description, $requires_himu_approval);
            
            if (mysqli_stmt_execute($stmt)) {
                $new_category_id = mysqli_insert_id($conn);
                
                // Create audit log
                createAuditLog(
                    $_SESSION['user_id'], 
                    "Category created", 
                    'category', 
                    $new_category_id, 
                    null, 
                    json_encode([
                        'category_name' => $category_name,
                        'description' => $description,
                        'requires_himu_approval' => $requires_himu_approval
                    ])
                );
                
                $success_message = "Category created successfully.";
                
                // Reset form values
                $category_name = '';
                $description = '';
                $requires_himu_approval = 0;
            } else {
                $error_message = "Error creating category: " . mysqli_error($conn);
            }
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
}

// Delete category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_category'])) {
    $category_id = (int)$_POST['category_id'];
    
    // Check if category is in use (has SKUs)
    $checkQuery = "SELECT COUNT(*) as count FROM sku_master WHERE category_id = ?";
    $stmt = mysqli_prepare($conn, $checkQuery);
    mysqli_stmt_bind_param($stmt, 'i', $category_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row = mysqli_fetch_assoc($result);
    
    if ($row['count'] > 0) {
        $error_message = "Cannot delete category because it has associated SKUs.";
    } else {
        // Delete the category
        $query = "DELETE FROM categories WHERE category_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'i', $category_id);
        
        if (mysqli_stmt_execute($stmt)) {
            // Create audit log
            createAuditLog(
                $_SESSION['user_id'], 
                "Category deleted", 
                'category', 
                $category_id, 
                null, 
                null
            );
            
            $success_message = "Category deleted successfully.";
        } else {
            $error_message = "Error deleting category: " . mysqli_error($conn);
        }
    }
}

// Get all categories for display
$query = "SELECT * FROM categories ORDER BY category_name";
$result = mysqli_query($conn, $query);
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Category Management</h1>
        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#categoryModal">
            <i class="fas fa-plus"></i> Add New Category
        </button>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">System Categories</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="categoriesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Category Name</th>
                            <th>Description</th>
                            <th>HIMU Approval</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($category = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td><?php echo $category['category_id']; ?></td>
                                    <td><?php echo htmlspecialchars($category['category_name']); ?></td>
                                    <td><?php echo htmlspecialchars($category['description'] ?: 'N/A'); ?></td>
                                    <td>
                                        <?php if ($category['requires_himu_approval']): ?>
                                            <span class="badge bg-primary">Required</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Not Required</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm editCategoryBtn" 
                                                data-category='<?php echo json_encode($category); ?>'>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" action="" style="display:inline;" 
                                              onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.');">
                                            <input type="hidden" name="category_id" value="<?php echo $category['category_id']; ?>">
                                            <input type="hidden" name="delete_category" value="1">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center">No categories found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalLabel">Add New Category</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="categoryForm" method="POST" action="">
                    <input type="hidden" id="category_id" name="category_id" value="">
                    <div class="form-group">
                        <label for="category_name">Category Name *</label>
                        <input type="text" class="form-control" id="category_name" name="category_name" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group form-check">
                        <input type="checkbox" class="form-check-input" id="requires_himu_approval" name="requires_himu_approval">
                        <label class="form-check-label" for="requires_himu_approval">
                            Requires HIMU Approval
                        </label>
                        <small class="form-text text-muted">
                            If checked, transfers of items in this category will require HIMU approval.
                        </small>
                    </div>
                    <input type="hidden" name="save_category" value="1">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" form="categoryForm" class="btn btn-primary">Save Category</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#categoriesTable').DataTable({
        pageLength: 25
    });
    
    // Handle edit category button
    const editButtons = document.querySelectorAll('.editCategoryBtn');
    editButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const categoryData = JSON.parse(this.getAttribute('data-category'));
            
            // Set modal title
            document.getElementById('categoryModalLabel').textContent = 'Edit Category';
            
            // Populate form fields
            document.getElementById('category_id').value = categoryData.category_id;
            document.getElementById('category_name').value = categoryData.category_name;
            document.getElementById('description').value = categoryData.description || '';
            document.getElementById('requires_himu_approval').checked = categoryData.requires_himu_approval == 1;
            
            // Show the modal
            $('#categoryModal').modal('show');
        });
    });
    
    // Reset form when adding new category
    const addButton = document.querySelector('[data-target="#categoryModal"]');
    addButton.addEventListener('click', function() {
        document.getElementById('categoryModalLabel').textContent = 'Add New Category';
        document.getElementById('categoryForm').reset();
        document.getElementById('category_id').value = '';
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>