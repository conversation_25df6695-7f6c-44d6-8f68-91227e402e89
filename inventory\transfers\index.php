<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_HIMU, ROLE_DEPARTMENT, ROLE_HEALTH_CENTER])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Initialize filters
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$categoryFilter = isset($_GET['category']) ? $_GET['category'] : '';
$dateFromFilter = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$dateToFilter = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$searchQuery = isset($_GET['search']) ? trim($_GET['search']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    // Base query
    $query = "
        SELECT t.*, 
               i.name as item_name, i.sku,
               cat.name as category_name,
               src_dept.name as source_department,
               src_hc.name as source_health_center,
               dest_dept.name as destination_department,
               dest_hc.name as destination_health_center,
               req_user.full_name as requested_by,
               app_user.full_name as approved_by
        FROM transfers t
        JOIN items i ON t.item_id = i.item_id
        JOIN categories cat ON i.category_id = cat.category_id
        LEFT JOIN departments src_dept ON t.source_department_id = src_dept.department_id
        LEFT JOIN health_centers src_hc ON t.source_health_center_id = src_hc.health_center_id
        LEFT JOIN departments dest_dept ON t.destination_department_id = dest_dept.department_id
        LEFT JOIN health_centers dest_hc ON t.destination_health_center_id = dest_hc.health_center_id
        LEFT JOIN users req_user ON t.requested_by = req_user.user_id
        LEFT JOIN users app_user ON t.approved_by = app_user.user_id
        WHERE 1=1
    ";

    // Role-based filters
    $params = [];
    $currentUser = $auth->getCurrentUser();
    
    if ($currentUser['role'] === ROLE_DEPARTMENT) {
        $query .= " AND (t.source_department_id = ? OR t.destination_department_id = ?)";
        $params[] = $currentUser['department_id'];
        $params[] = $currentUser['department_id'];
    } elseif ($currentUser['role'] === ROLE_HEALTH_CENTER) {
        $query .= " AND (t.source_health_center_id = ? OR t.destination_health_center_id = ?)";
        $params[] = $currentUser['health_center_id'];
        $params[] = $currentUser['health_center_id'];
    } elseif ($currentUser['role'] === ROLE_HIMU) {
        $query .= " AND (cat.name IN ('IT Equipment', 'IT Supply') OR t.requires_himu_approval = 1)";
    }

    // Apply filters
    if ($statusFilter) {
        $query .= " AND t.status = ?";
        $params[] = $statusFilter;
    }
    if ($categoryFilter) {
        $query .= " AND cat.category_id = ?";
        $params[] = $categoryFilter;
    }
    if ($dateFromFilter) {
        $query .= " AND DATE(t.request_date) >= ?";
        $params[] = $dateFromFilter;
    }
    if ($dateToFilter) {
        $query .= " AND DATE(t.request_date) <= ?";
        $params[] = $dateToFilter;
    }
    if ($searchQuery) {
        $query .= " AND (i.name LIKE ? OR i.sku LIKE ? OR t.reference_number LIKE ?)";
        $params[] = "%$searchQuery%";
        $params[] = "%$searchQuery%";
        $params[] = "%$searchQuery%";
    }

    // Count total records for pagination
    $countQuery = str_replace("t.*, i.name as item_name, i.sku,
               cat.name as category_name,
               src_dept.name as source_department,
               src_hc.name as source_health_center,
               dest_dept.name as destination_department,
               dest_hc.name as destination_health_center,
               req_user.full_name as requested_by,
               app_user.full_name as approved_by", "COUNT(*) as total", $query);
    $stmt = $db->prepare($countQuery);
    $stmt->execute($params);
    $totalRecords = $stmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $limit);

    // Add sorting and pagination to main query
    $query .= " ORDER BY t.request_date DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;

    // Execute main query
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $transfers = $stmt->fetchAll();

    // Fetch categories for filter
    $stmt = $db->query("SELECT category_id, name FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching transfers: " . $e->getMessage());
    setFlashMessage('error', 'Error loading transfers');
    $transfers = [];
    $totalPages = 0;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Transfer Management</h1>
        <div>
            <a href="request.php" class="btn btn-primary">
                <i class="fas fa-exchange-alt"></i> New Transfer Request
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                        <option value="approved" <?php echo $statusFilter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                        <option value="rejected" <?php echo $statusFilter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                        <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                        <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['category_id']; ?>"
                                    <?php echo $categoryFilter == $category['category_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo $dateFromFilter; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo $dateToFilter; ?>">
                </div>
                <div class="col-md-2">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($searchQuery); ?>" 
                           placeholder="Search...">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Transfers List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($transfers)): ?>
                <p class="text-muted text-center py-4">No transfers found.</p>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Item</th>
                                <th>Category</th>
                                <th>From</th>
                                <th>To</th>
                                <th>Quantity</th>
                                <th>Status</th>
                                <th>Request Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transfers as $transfer): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($transfer['reference_number']); ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($transfer['item_name']); ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($transfer['sku']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($transfer['category_name']); ?></td>
                                    <td>
                                        <?php 
                                        echo $transfer['source_department'] ? 
                                            htmlspecialchars($transfer['source_department']) : 
                                            ($transfer['source_health_center'] ? 
                                                htmlspecialchars($transfer['source_health_center']) : 'N/A'); 
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        echo $transfer['destination_department'] ? 
                                            htmlspecialchars($transfer['destination_department']) : 
                                            ($transfer['destination_health_center'] ? 
                                                htmlspecialchars($transfer['destination_health_center']) : 'N/A'); 
                                        ?>
                                    </td>
                                    <td><?php echo number_format($transfer['quantity']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $transfer['status'] === 'approved' ? 'success' : 
                                                ($transfer['status'] === 'pending' ? 'warning' : 
                                                    ($transfer['status'] === 'rejected' ? 'danger' : 
                                                        ($transfer['status'] === 'completed' ? 'info' : 'secondary'))); 
                                        ?>">
                                            <?php echo ucfirst($transfer['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo formatDate($transfer['request_date']); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="view.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($transfer['status'] === 'pending'): ?>
                                                <?php if (canApproveTransfer($currentUser['role'], $transfer)): ?>
                                                    <a href="approve.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                                       class="btn btn-sm btn-success" title="Review">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if ($transfer['requested_by'] === $currentUser['user_id']): ?>
                                                    <a href="cancel.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('Are you sure you want to cancel this transfer?')"
                                                       title="Cancel">
                                                        <i class="fas fa-times"></i>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $statusFilter; ?>&category=<?php echo $categoryFilter; ?>&date_from=<?php echo $dateFromFilter; ?>&date_to=<?php echo $dateToFilter; ?>&search=<?php echo urlencode($searchQuery); ?>">
                                        Previous
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $statusFilter; ?>&category=<?php echo $categoryFilter; ?>&date_from=<?php echo $dateFromFilter; ?>&date_to=<?php echo $dateToFilter; ?>&search=<?php echo urlencode($searchQuery); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $statusFilter; ?>&category=<?php echo $categoryFilter; ?>&date_from=<?php echo $dateFromFilter; ?>&date_to=<?php echo $dateToFilter; ?>&search=<?php echo urlencode($searchQuery); ?>">
                                        Next
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php 
// Helper function to determine if user can approve transfer
function canApproveTransfer($userRole, $transfer) {
    if ($userRole === ROLE_GODMODE || $userRole === ROLE_SUPERADMIN) {
        return true;
    }
    
    if ($userRole === ROLE_HIMU && 
        ($transfer['category_name'] === 'IT Equipment' || 
         $transfer['category_name'] === 'IT Supply' || 
         $transfer['requires_himu_approval'])) {
        return true;
    }
    
    if ($userRole === ROLE_LOGISTICS && 
        !($transfer['category_name'] === 'IT Equipment' || 
          $transfer['category_name'] === 'IT Supply')) {
        return true;
    }
    
    return false;
}

require_once '../../templates/footer.php'; 
?>
