<?php
// Monthly Inventory Update Handler
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Ensure user is logged in
requireLogin();

// Allow all users with a location to update their inventory
// GodMode can update any location's inventory
if (!hasRole('GodMode') && !getUserLocationId()) {
    $_SESSION['error'] = "You don't have permission to update monthly inventory.";
    header('Location: /choims/index.php');
    exit();
}

// Get user's location ID
$locationId = getUserLocationId();

if (!$locationId) {
    $_SESSION['error'] = "No location associated with your account.";
    header('Location: /choims/index.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error'] = "Invalid request. Please try again.";
        header('Location: /choims/index.php');
        exit();
    }

    // Get current month and year
    $month = date('n'); // 1-12
    $year = date('Y');

    // Get notes from form
    $notes = isset($_POST['notes']) ? sanitizeInput($_POST['notes']) : '';

    // Process inventory quantity updates if provided
    $inventoryUpdated = false;
    $updatedItemsCount = 0;

    // Debug information
    $debugInfo = [];
    $debugInfo['post_data'] = $_POST;
    $debugInfo['has_update_quantities'] = isset($_POST['update_quantities']);
    $debugInfo['update_quantities_value'] = isset($_POST['update_quantities']) ? $_POST['update_quantities'] : 'not set';
    $debugInfo['has_inventory_ids'] = isset($_POST['inventory_ids']);
    $debugInfo['has_old_quantities'] = isset($_POST['old_quantities']);
    $debugInfo['has_new_quantities'] = isset($_POST['new_quantities']);

    // Log debug information
    logActivity($conn, "Monthly Update Debug", "debug", 0, null, json_encode($debugInfo));

    if (isset($_POST['update_quantities']) && $_POST['update_quantities'] == 1) {
        if (isset($_POST['inventory_ids']) && isset($_POST['old_quantities']) && isset($_POST['new_quantities'])) {
            $inventoryIds = $_POST['inventory_ids'];
            $oldQuantities = $_POST['old_quantities'];
            $newQuantities = $_POST['new_quantities'];

            // Debug arrays
            $debugArrays = [];
            $debugArrays['inventory_ids'] = $inventoryIds;
            $debugArrays['old_quantities'] = $oldQuantities;
            $debugArrays['new_quantities'] = $newQuantities;
            $debugArrays['count'] = count($inventoryIds);

            // Log debug arrays
            logActivity($conn, "Monthly Update Arrays", "debug", 0, null, json_encode($debugArrays));

            // Loop through each inventory item
            for ($i = 0; $i < count($inventoryIds); $i++) {
                $inventoryId = intval($inventoryIds[$i]);
                $oldQuantity = intval($oldQuantities[$i]);
                $newQuantity = intval($newQuantities[$i]);

                // Only update if quantity has changed
                if ($oldQuantity !== $newQuantity) {
                    // Get current inventory data for audit log
                    $inventoryQuery = "SELECT i.*, s.sku_name, s.sku_code, c.category_name, l.location_name
                                      FROM consumable_inventory i
                                      JOIN sku_master s ON i.sku_id = s.sku_id
                                      JOIN categories c ON s.category_id = c.category_id
                                      JOIN locations l ON i.location_id = l.location_id
                                      WHERE i.inventory_id = ?";
                    $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
                    mysqli_stmt_bind_param($inventoryStmt, 'i', $inventoryId);
                    mysqli_stmt_execute($inventoryStmt);
                    $inventoryResult = mysqli_stmt_get_result($inventoryStmt);
                    $inventoryData = mysqli_fetch_assoc($inventoryResult);

                    // Update inventory quantity
                    $updateInventoryQuery = "UPDATE consumable_inventory
                                           SET current_quantity = ?, updated_at = NOW()
                                           WHERE inventory_id = ?";
                    $updateInventoryStmt = mysqli_prepare($conn, $updateInventoryQuery);
                    mysqli_stmt_bind_param($updateInventoryStmt, 'ii', $newQuantity, $inventoryId);
                    $updateSuccess = mysqli_stmt_execute($updateInventoryStmt);

                    if ($updateSuccess) {
                        $inventoryUpdated = true;
                        $updatedItemsCount++;

                        // Debug update success
                        $updateDebug = [
                            'inventory_id' => $inventoryId,
                            'old_quantity' => $oldQuantity,
                            'new_quantity' => $newQuantity,
                            'update_success' => $updateSuccess,
                            'mysqli_error' => mysqli_error($conn),
                            'affected_rows' => mysqli_affected_rows($conn),
                            'updated_items_count' => $updatedItemsCount
                        ];
                        logActivity($conn, "Monthly Update Item Success", "debug", $inventoryId, null, json_encode($updateDebug));

                        // Create audit log entry
                        $oldValues = [
                            'current_quantity' => $oldQuantity,
                            'status' => $inventoryData['status']
                        ];

                        $newValues = [
                            'current_quantity' => $newQuantity,
                            // Status will be updated by the database trigger
                        ];

                        // Log the inventory update
                        logActivity($conn, "Monthly Inventory Update", "consumable_inventory", $inventoryId,
                                   json_encode($oldValues), json_encode($newValues));

                        // Create detailed audit log with more information
                        $auditLogQuery = "INSERT INTO audit_logs
                                        (user_id, action, entity_type, entity_id, old_values, new_values, ip_address, log_time)
                                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
                        $auditLogStmt = mysqli_prepare($conn, $auditLogQuery);

                        $action = "Monthly Inventory Quantity Update";
                        $entityType = "inventory";
                        $ipAddress = $_SERVER['REMOTE_ADDR'];

                        $detailedOldValues = json_encode([
                            'item_name' => $inventoryData['sku_name'],
                            'sku_code' => $inventoryData['sku_code'],
                            'category' => $inventoryData['category_name'],
                            'location' => $inventoryData['location_name'],
                            'quantity' => $oldQuantity,
                            'status' => $inventoryData['status'],
                            'update_type' => 'monthly_update'
                        ]);

                        $detailedNewValues = json_encode([
                            'item_name' => $inventoryData['sku_name'],
                            'sku_code' => $inventoryData['sku_code'],
                            'category' => $inventoryData['category_name'],
                            'location' => $inventoryData['location_name'],
                            'quantity' => $newQuantity,
                            'update_type' => 'monthly_update'
                        ]);

                        mysqli_stmt_bind_param($auditLogStmt, 'ississs',
                            $_SESSION['user_id'],
                            $action,
                            $entityType,
                            $inventoryId,
                            $detailedOldValues,
                            $detailedNewValues,
                            $ipAddress
                        );
                        mysqli_stmt_execute($auditLogStmt);
                    }
                }
            }
        }
    }

    // Check if there's an existing record
    $checkQuery = "SELECT update_id FROM monthly_inventory_updates
                  WHERE location_id = ? AND month = ? AND year = ?";
    $checkStmt = mysqli_prepare($conn, $checkQuery);
    mysqli_stmt_bind_param($checkStmt, 'iii', $locationId, $month, $year);
    mysqli_stmt_execute($checkStmt);
    $checkResult = mysqli_stmt_get_result($checkStmt);

    if (mysqli_num_rows($checkResult) > 0) {
        // Update existing record
        $row = mysqli_fetch_assoc($checkResult);
        $updateId = $row['update_id'];

        $updateQuery = "UPDATE monthly_inventory_updates
                       SET status = ?, updated_by = ?, updated_at = NOW(), notes = ?
                       WHERE update_id = ?";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        $status = MONTHLY_UPDATE_STATUS_COMPLETED;
        mysqli_stmt_bind_param($updateStmt, 'sisi', $status, $_SESSION['user_id'], $notes, $updateId);
        $success = mysqli_stmt_execute($updateStmt);
    } else {
        // Insert new record
        $insertQuery = "INSERT INTO monthly_inventory_updates
                       (location_id, month, year, status, updated_by, updated_at, notes)
                       VALUES (?, ?, ?, ?, ?, NOW(), ?)";
        $insertStmt = mysqli_prepare($conn, $insertQuery);
        $status = MONTHLY_UPDATE_STATUS_COMPLETED;
        mysqli_stmt_bind_param($insertStmt, 'iiisis', $locationId, $month, $year, $status, $_SESSION['user_id'], $notes);
        $success = mysqli_stmt_execute($insertStmt);
    }

    if ($success) {
        // Clear cache for this location's inventory status
        invalidateCache('monthly_inventory', $locationId);

        // Create notification for logistics and superadmin users
        $locationName = getUserLocation($conn, $locationId);
        $notificationTitle = "Monthly Inventory Update Completed";
        $notificationMessage = "$locationName has completed their monthly inventory update for " . date('F Y');

        // Create notification for logistics users
        $logisticsQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                          SELECT user_id, 'monthly_inventory', ?, ?, 'location', ?
                          FROM users
                          WHERE LOWER(role) IN ('logistics')";
        $logisticsStmt = mysqli_prepare($conn, $logisticsQuery);
        mysqli_stmt_bind_param($logisticsStmt, 'ssi', $notificationTitle, $notificationMessage, $locationId);
        mysqli_stmt_execute($logisticsStmt);

        // Create notification for superadmin users
        $adminQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
                      SELECT user_id, 'monthly_inventory', ?, ?, 'location', ?
                      FROM users
                      WHERE LOWER(role) IN ('superadmin', 'godmode')";
        $adminStmt = mysqli_prepare($conn, $adminQuery);
        mysqli_stmt_bind_param($adminStmt, 'ssi', $notificationTitle, $notificationMessage, $locationId);
        mysqli_stmt_execute($adminStmt);

        // Log the action
        logActivity($conn, "Monthly Inventory Update", "location", $locationId, null, json_encode([
            'month' => $month,
            'year' => $year,
            'status' => $status,
            'notes' => $notes
        ]));

        if ($inventoryUpdated) {
            $_SESSION['success'] = "Monthly inventory update completed successfully. $updatedItemsCount inventory item" . ($updatedItemsCount > 1 ? "s" : "") . " updated.";
        } else {
            $_SESSION['success'] = "Monthly inventory update completed successfully. No inventory quantities were changed.";
        }
    } else {
        $_SESSION['error'] = "Failed to update monthly inventory: " . mysqli_error($conn);
    }

    // Redirect back to appropriate dashboard based on role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else if (hasRole('Logistics')) {
        header('Location: /choims/dashboards/logistics.php');
    } else if (hasRole('HIMU')) {
        header('Location: /choims/dashboards/himu.php');
    } else if (hasRole('Superadmin') || hasRole('GodMode')) {
        header('Location: /choims/dashboards/superadmin.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit();
}

// If not a POST request, redirect to appropriate dashboard based on role
if (hasRole('HealthCenter')) {
    header('Location: /choims/dashboards/health_center.php');
} else if (hasRole('Department')) {
    header('Location: /choims/dashboards/department.php');
} else if (hasRole('Logistics')) {
    header('Location: /choims/dashboards/logistics.php');
} else if (hasRole('HIMU')) {
    header('Location: /choims/dashboards/himu.php');
} else if (hasRole('Superadmin') || hasRole('GodMode')) {
    header('Location: /choims/dashboards/superadmin.php');
} else {
    header('Location: /choims/index.php');
}
exit();
?>
