/* Modern Add Product Page Styling */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Page Header */
.page-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  color: var(--primary);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-bottom: 0;
}

.breadcrumb {
  margin-bottom: 0;
  justify-content: flex-end;
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--gray-500);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: var(--space-5);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
}

.card-header i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.25rem 1rem;
}

.form-floating > label {
  padding: 1rem;
  color: var(--gray-500);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
  color: var(--primary);
}

.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
}

textarea.form-control {
  min-height: 120px;
}

.form-floating > textarea.form-control {
  height: 120px;
}

.input-group-text {
  background-color: var(--primary-bg);
  border: 1px solid var(--gray-300);
  color: var(--primary-dark);
}

.invalid-feedback {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-lg {
  padding: 0.75rem 1.75rem;
  font-size: 1rem;
}

/* Alerts */
.alert {
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  margin-right: var(--space-4);
}

.alert-success .alert-icon {
  background-color: rgba(16, 185, 129, 0.2);
}

.alert-danger .alert-icon {
  background-color: rgba(239, 68, 68, 0.2);
}

.alert-warning .alert-icon {
  background-color: rgba(245, 158, 11, 0.2);
}

.alert-info .alert-icon {
  background-color: rgba(59, 130, 246, 0.2);
}

/* Status Badges */
.badge {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
}

.badge i {
  margin-right: 0.25rem;
}

.badge.bg-primary.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-success.bg-opacity-10 {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: #10b981 !important;
}

.badge.bg-warning.bg-opacity-10 {
  background-color: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
}

.badge.bg-danger.bg-opacity-10 {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.badge.bg-info.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-secondary.bg-opacity-10 {
  background-color: rgba(107, 114, 128, 0.1) !important;
  color: #6b7280 !important;
}

/* Select2 Styling */
.select2-container--default .select2-selection--single,
.select2-container--classic .select2-selection--single {
  height: calc(3.5rem + 2px) !important;
  padding: 1.25rem 1rem 0.25rem !important;
  font-size: 1rem !important;
  border: 1px solid var(--gray-300) !important;
  border-radius: var(--radius-md) !important;
  background-color: var(--white) !important;
  transition: all 0.2s ease !important;
  box-shadow: none !important;
  overflow: hidden !important;
}

.select2-container--default .select2-selection--single:hover,
.select2-container--classic .select2-selection--single:hover {
  border-color: var(--primary-light) !important;
}

.select2-container--default .select2-selection--single:focus,
.select2-container--classic .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--classic.select2-container--focus .select2-selection--single {
  border-color: var(--primary-light) !important;
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25) !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--classic .select2-selection--single .select2-selection__rendered {
  color: var(--dark) !important;
  line-height: 1.5 !important;
  padding-left: 0 !important;
  padding-right: 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow,
.select2-container--classic .select2-selection--single .select2-selection__arrow {
  height: 3.5rem !important;
  top: 0 !important;
  right: 0.75rem !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b,
.select2-container--classic .select2-selection--single .select2-selection__arrow b {
  border-color: var(--primary) transparent transparent transparent !important;
  border-width: 6px 6px 0 6px !important;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b,
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent var(--primary) transparent !important;
  border-width: 0 6px 6px 6px !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-container--classic .select2-results__option--highlighted[aria-selected] {
  background-color: var(--primary-bg) !important;
  color: var(--primary) !important;
}

.select2-container--default .select2-results__option[aria-selected=true],
.select2-container--classic .select2-results__option[aria-selected=true] {
  background-color: var(--primary) !important;
  color: white !important;
}

.select2-dropdown {
  margin-top: 5px !important;
  border: 1px solid var(--gray-300) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-md) !important;
  padding: 10px !important;
  z-index: 1056 !important; /* Higher than Bootstrap modals */
}

.select2-dropdown-rounded {
  border-radius: var(--radius-md) !important;
  overflow: hidden !important;
}

.select2-container--open .select2-dropdown--below {
  border-top: none !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.select2-search--dropdown {
  padding: 10px !important;
}

.select2-search--dropdown .select2-search__field {
  padding: 10px 15px !important;
  border: 1px solid var(--gray-300) !important;
  border-radius: var(--radius-md) !important;
  height: 45px !important;
  box-shadow: var(--shadow-sm) !important;
}

.select2-search--dropdown .select2-search__field:focus {
  border-color: var(--primary-light) !important;
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25) !important;
  outline: none !important;
}

.select2-results__option {
  padding: 12px 15px !important;
  border-radius: var(--radius-md) !important;
  margin-bottom: 2px !important;
  transition: all 0.2s ease !important;
}

.select2-results__option--manual-entry {
  border: 1px dashed var(--primary-light) !important;
  background-color: var(--primary-bg) !important;
  color: var(--primary) !important;
  font-weight: 500 !important;
  cursor: pointer !important;
}

.select2-results__option--manual-entry:hover {
  background-color: rgba(76, 175, 80, 0.2) !important;
}

/* SKU Selection Container */
.sku-selection-container {
  position: relative !important;
  width: 100% !important;
}

/* Force Select2 container to have rounded corners */
.sku-selection-container .select2-container {
  width: 100% !important;
  display: block !important;
}

/* Custom class for rounded select */
.rounded-select + .select2-container--default .select2-selection--single,
.rounded-select + .select2-container--classic .select2-selection--single {
  border-radius: var(--radius-md) !important;
  height: calc(3.5rem + 2px) !important;
  padding: 1.25rem 1rem 0.25rem !important;
}

.select2-selection-rounded {
  border-radius: var(--radius-md) !important;
}

/* Fix for form-floating with Select2 */
.form-floating .select2-container {
  padding-top: 1.625rem !important;
  padding-bottom: 0.625rem !important;
}

.form-floating .select2-container .select2-selection--single {
  height: calc(3.5rem + 2px) !important;
  line-height: 1.25 !important;
}

.form-floating .select2-container--default .select2-selection--single .select2-selection__rendered,
.form-floating .select2-container--classic .select2-selection--single .select2-selection__rendered {
  padding-top: 0 !important;
  line-height: 1.5 !important;
  color: var(--dark) !important;
}

/* Form alignment fixes */
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.25rem 1rem 0.25rem;
}

.form-floating > label {
  padding: 1rem;
  color: var(--gray-500);
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Tips Card */
.tips-card {
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
}

.tips-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.tips-card-title i {
  color: var(--primary);
}

.tips-card ul {
  margin-bottom: 0;
  padding-left: 1.5rem;
}

.tips-card li {
  margin-bottom: var(--space-2);
  color: var(--gray-500);
}

.tips-card li:last-child {
  margin-bottom: 0;
}

/* Status Levels Card */
.status-levels-card {
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.status-level {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-3);
}

.status-level:last-child {
  margin-bottom: 0;
}

.status-level-badge {
  margin-right: var(--space-3);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-level-text {
  font-size: 0.875rem;
  color: var(--gray-500);
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-header {
    padding: var(--space-4);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .card-header, .card-body {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem;
  }

  .btn-lg {
    padding: 0.6rem 1.5rem;
  }
}
