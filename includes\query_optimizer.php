<?php
/**
 * Query Optimizer for CHOIMS
 * 
 * This file provides functions to optimize database queries for better performance
 * under heavy loads.
 */

/**
 * Optimize a query by adding LIMIT and pagination
 * 
 * @param string $query Original SQL query
 * @param int $page Page number (1-based)
 * @param int $perPage Items per page
 * @return string Optimized query
 */
function optimizeQueryWithPagination($query, $page = 1, $perPage = 20) {
    // Ensure valid pagination parameters
    $page = max(1, intval($page));
    $perPage = max(1, intval($perPage));
    $offset = ($page - 1) * $perPage;
    
    // Check if query already has LIMIT clause
    if (stripos($query, ' LIMIT ') !== false) {
        // Replace existing LIMIT with our pagination
        $query = preg_replace('/\s+LIMIT\s+\d+(\s*,\s*\d+)?/i', " LIMIT $offset, $perPage", $query);
    } else {
        // Add LIMIT clause
        $query .= " LIMIT $offset, $perPage";
    }
    
    return $query;
}

/**
 * Get total count for a paginated query
 * 
 * @param string $query Original SQL query
 * @param object $conn Database connection
 * @return int Total count
 */
function getQueryTotalCount($query, $conn) {
    // Remove existing LIMIT clause if any
    $countQuery = preg_replace('/\s+LIMIT\s+\d+(\s*,\s*\d+)?/i', '', $query);
    
    // Convert to COUNT query
    if (stripos($countQuery, 'SELECT COUNT') !== false) {
        // Already a count query
        return executeCountQuery($countQuery, $conn);
    }
    
    // Check if it's a simple query or has GROUP BY/HAVING
    if (stripos($countQuery, ' GROUP BY ') !== false || stripos($countQuery, ' HAVING ') !== false) {
        // For complex queries, wrap in a subquery
        $countQuery = "SELECT COUNT(*) as total FROM ($countQuery) as subquery";
    } else {
        // For simple queries, replace columns with COUNT(*)
        $fromPos = stripos($countQuery, ' FROM ');
        if ($fromPos !== false) {
            $countQuery = "SELECT COUNT(*) as total " . substr($countQuery, $fromPos);
        }
    }
    
    return executeCountQuery($countQuery, $conn);
}

/**
 * Execute a count query and return the result
 * 
 * @param string $countQuery SQL count query
 * @param object $conn Database connection
 * @return int Count result
 */
function executeCountQuery($countQuery, $conn) {
    $result = mysqli_query($conn, $countQuery);
    if ($result && $row = mysqli_fetch_assoc($result)) {
        return intval($row['total']);
    }
    return 0;
}

/**
 * Optimize a SELECT query by selecting only needed columns
 * 
 * @param string $query Original SQL query
 * @param array $neededColumns Array of needed columns
 * @return string Optimized query
 */
function optimizeQueryColumns($query, array $neededColumns) {
    if (empty($neededColumns)) {
        return $query;
    }
    
    // Check if it's a SELECT query
    if (stripos($query, 'SELECT ') !== 0) {
        return $query;
    }
    
    // Find the position of FROM
    $fromPos = stripos($query, ' FROM ');
    if ($fromPos === false) {
        return $query;
    }
    
    // Replace columns with needed columns
    $columnsStr = implode(', ', $neededColumns);
    return "SELECT $columnsStr " . substr($query, $fromPos);
}

/**
 * Add query hints for optimization
 * 
 * @param string $query Original SQL query
 * @param array $hints Array of MySQL hints
 * @return string Query with hints
 */
function addQueryHints($query, array $hints) {
    if (empty($hints)) {
        return $query;
    }
    
    // Check if it's a SELECT query
    if (stripos($query, 'SELECT ') !== 0) {
        return $query;
    }
    
    // Format hints
    $hintsStr = implode(' ', $hints);
    
    // Add hints after SELECT
    return "SELECT $hintsStr " . substr($query, 7);
}

/**
 * Cache query results
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param object $conn Database connection
 * @param int $ttl Cache time to live in seconds
 * @return array Query results
 */
function cachedQuery($query, array $params, $conn, $ttl = 300) {
    // Get cache instance
    $cache = Cache::getInstance();
    
    // Generate cache key
    $cacheKey = 'query_' . md5($query . serialize($params));
    
    // Try to get from cache
    $results = $cache->get($cacheKey);
    if ($results !== null) {
        return $results;
    }
    
    // Execute query
    if (empty($params)) {
        // Simple query without parameters
        $result = mysqli_query($conn, $query);
        if (!$result) {
            return [];
        }
        
        $results = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $results[] = $row;
        }
    } else {
        // Prepared statement with parameters
        $stmt = mysqli_prepare($conn, $query);
        if (!$stmt) {
            return [];
        }
        
        // Bind parameters
        $types = '';
        $bindParams = [];
        
        foreach ($params as $param) {
            if (is_int($param)) {
                $types .= 'i';
            } elseif (is_float($param)) {
                $types .= 'd';
            } elseif (is_string($param)) {
                $types .= 's';
            } else {
                $types .= 'b';
            }
            $bindParams[] = $param;
        }
        
        // Create reference array for call_user_func_array
        $bindParamsRef = [];
        $bindParamsRef[] = $stmt;
        $bindParamsRef[] = $types;
        
        foreach ($bindParams as $key => $value) {
            $bindParamsRef[] = &$bindParams[$key];
        }
        
        call_user_func_array('mysqli_stmt_bind_param', $bindParamsRef);
        
        // Execute and get results
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $results = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $results[] = $row;
        }
        
        mysqli_stmt_close($stmt);
    }
    
    // Cache results
    $cache->set($cacheKey, $results, $ttl);
    
    return $results;
}

/**
 * Optimize and execute a query with caching and pagination
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param object $conn Database connection
 * @param array $options Options array
 * @return array Results with pagination info
 */
function optimizedQuery($query, array $params, $conn, array $options = []) {
    // Default options
    $defaultOptions = [
        'page' => 1,
        'perPage' => 20,
        'cache' => true,
        'cacheTTL' => 300,
        'columns' => [],
        'hints' => []
    ];
    
    $options = array_merge($defaultOptions, $options);
    
    // Optimize query
    if (!empty($options['columns'])) {
        $query = optimizeQueryColumns($query, $options['columns']);
    }
    
    if (!empty($options['hints'])) {
        $query = addQueryHints($query, $options['hints']);
    }
    
    // Get total count for pagination
    $totalCount = 0;
    if ($options['page'] > 0 && $options['perPage'] > 0) {
        $totalCount = getQueryTotalCount($query, $conn);
        $query = optimizeQueryWithPagination($query, $options['page'], $options['perPage']);
    }
    
    // Execute query with caching if enabled
    $results = $options['cache'] 
        ? cachedQuery($query, $params, $conn, $options['cacheTTL'])
        : executeQuery($query, $params, $conn);
    
    // Return results with pagination info
    return [
        'data' => $results,
        'pagination' => [
            'total' => $totalCount,
            'page' => $options['page'],
            'perPage' => $options['perPage'],
            'totalPages' => $options['perPage'] > 0 ? ceil($totalCount / $options['perPage']) : 1
        ]
    ];
}

/**
 * Execute a query without caching
 * 
 * @param string $query SQL query
 * @param array $params Query parameters
 * @param object $conn Database connection
 * @return array Query results
 */
function executeQuery($query, array $params, $conn) {
    if (empty($params)) {
        // Simple query without parameters
        $result = mysqli_query($conn, $query);
        if (!$result) {
            return [];
        }
        
        $results = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $results[] = $row;
        }
        
        return $results;
    }
    
    // Prepared statement with parameters
    $stmt = mysqli_prepare($conn, $query);
    if (!$stmt) {
        return [];
    }
    
    // Bind parameters
    $types = '';
    $bindParams = [];
    
    foreach ($params as $param) {
        if (is_int($param)) {
            $types .= 'i';
        } elseif (is_float($param)) {
            $types .= 'd';
        } elseif (is_string($param)) {
            $types .= 's';
        } else {
            $types .= 'b';
        }
        $bindParams[] = $param;
    }
    
    // Create reference array for call_user_func_array
    $bindParamsRef = [];
    $bindParamsRef[] = $stmt;
    $bindParamsRef[] = $types;
    
    foreach ($bindParams as $key => $value) {
        $bindParamsRef[] = &$bindParams[$key];
    }
    
    call_user_func_array('mysqli_stmt_bind_param', $bindParamsRef);
    
    // Execute and get results
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $results = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $results[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    
    return $results;
}
?>
