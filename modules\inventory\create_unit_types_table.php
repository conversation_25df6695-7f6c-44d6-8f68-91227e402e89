<?php
// Include database connection
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Check if unit_types table exists
$checkTableQuery = "SHOW TABLES LIKE 'unit_types'";
$checkTableResult = mysqli_query($conn, $checkTableQuery);

if (mysqli_num_rows($checkTableResult) == 0) {
    // Table doesn't exist, create it
    $createTableQuery = "
        CREATE TABLE unit_types (
            unit_type_id INT PRIMARY KEY AUTO_INCREMENT,
            unit_type VARCHAR(50) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INT,
            FOREIGN KEY (created_by) REFERENCES users(user_id)
        )
    ";
    
    if (mysqli_query($conn, $createTableQuery)) {
        echo "Table 'unit_types' created successfully.<br>";
        
        // Insert default unit types
        $defaultUnitTypes = ['pcs', 'box', 'pack', 'bottle', 'roll', 'set', 'gallon', 'ream'];
        $insertCount = 0;
        
        foreach ($defaultUnitTypes as $defaultType) {
            $insertDefaultQuery = "INSERT INTO unit_types (unit_type, created_by) VALUES (?, ?)";
            $insertDefaultStmt = mysqli_prepare($conn, $insertDefaultQuery);
            mysqli_stmt_bind_param($insertDefaultStmt, 'si', $defaultType, $_SESSION['user_id']);
            
            if (mysqli_stmt_execute($insertDefaultStmt)) {
                $insertCount++;
            }
        }
        
        echo "Inserted {$insertCount} default unit types.<br>";
    } else {
        echo "Error creating table: " . mysqli_error($conn) . "<br>";
    }
} else {
    echo "Table 'unit_types' already exists.<br>";
}

// Show all unit types
$unitTypesQuery = "SELECT * FROM unit_types ORDER BY unit_type";
$unitTypesResult = mysqli_query($conn, $unitTypesQuery);

if (mysqli_num_rows($unitTypesResult) > 0) {
    echo "<h3>Current Unit Types</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Unit Type</th><th>Created At</th></tr>";
    
    while ($unitType = mysqli_fetch_assoc($unitTypesResult)) {
        echo "<tr>";
        echo "<td>" . $unitType['unit_type_id'] . "</td>";
        echo "<td>" . $unitType['unit_type'] . "</td>";
        echo "<td>" . $unitType['created_at'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "No unit types found.<br>";
}

// Close connection
mysqli_close($conn);
?>
