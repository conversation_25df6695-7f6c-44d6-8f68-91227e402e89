<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_DEPARTMENT, ROLE_HEALTH_CENTER])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get transfer ID
$transferId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
if (!$transferId) {
    setFlashMessage('error', 'Invalid transfer request');
    header("Location: index.php");
    exit;
}

try {
    // Fetch transfer details
    $stmt = $db->prepare("
        SELECT t.*, i.name as item_name
        FROM transfers t
        JOIN items i ON t.item_id = i.item_id
        WHERE t.transfer_id = ?
    ");
    $stmt->execute([$transferId]);
    $transfer = $stmt->fetch();

    if (!$transfer) {
        throw new Exception('Transfer request not found');
    }

    // Check if transfer can be cancelled
    $currentUser = $auth->getCurrentUser();
    
    // Only allow cancellation if:
    // 1. Transfer is pending
    // 2. User is the requester OR has GODMODE/SUPERADMIN role
    if ($transfer['status'] !== 'pending') {
        setFlashMessage('error', 'Only pending transfers can be cancelled');
        header("Location: view.php?id=" . $transferId);
        exit;
    }

    if ($transfer['requested_by'] !== $currentUser['user_id'] && 
        !in_array($currentUser['role'], [ROLE_GODMODE, ROLE_SUPERADMIN])) {
        setFlashMessage('error', 'You do not have permission to cancel this transfer');
        header("Location: view.php?id=" . $transferId);
        exit;
    }

    // Process cancellation
    $db->beginTransaction();

    // Update transfer status
    $stmt = $db->prepare("
        UPDATE transfers 
        SET status = 'cancelled',
            cancelled_by = ?,
            cancelled_date = NOW(),
            cancellation_remarks = ?
        WHERE transfer_id = ?
    ");
    $stmt->execute([
        $currentUser['user_id'],
        'Cancelled by ' . $currentUser['full_name'],
        $transferId
    ]);

    // Log the activity
    $auth->logActivity($currentUser['user_id'], 'cancel', 'transfers', $transferId);

    $db->commit();
    setFlashMessage('success', 'Transfer request cancelled successfully');

} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Error cancelling transfer: " . $e->getMessage());
    setFlashMessage('error', 'Error cancelling transfer request');
}

// Redirect back to the view page
header("Location: view.php?id=" . $transferId);
exit;
?>
