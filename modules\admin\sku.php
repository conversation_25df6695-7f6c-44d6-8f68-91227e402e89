<?php
// Enable detailed error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start a session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Debug information - store in session to avoid headers already sent issues
$_SESSION['debug_info'] = [
    'time' => date('Y-m-d H:i:s'),
    'session_id' => session_id(),
    'session_data' => $_SESSION,
    'get_data' => $_GET,
    'post_data' => $_POST,
    'server' => [
        'php_self' => $_SERVER['PHP_SELF'],
        'request_uri' => $_SERVER['REQUEST_URI'],
        'http_referer' => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'Not set'
    ]
];

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has superadmin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) ||
    (strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode')) {
    $_SESSION['error'] = "You don't have permission to access this page. Role: " .
                          (isset($_SESSION['role']) ? $_SESSION['role'] : 'Not set');

    // Store debug info before redirect
    $_SESSION['debug_redirect'] = "Redirected from SKU page due to permission check.";

    header("Location: ../../index.php");
    exit();
}

// Log activity
logActivity($conn, 'Accessed SKU Management Page', 'admin', null);

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_sku'])) {
        $category_id = $_POST['category_id'];
        $sku_code = trim($_POST['sku_code']);
        $sku_name = trim($_POST['sku_name']);
        $description = trim($_POST['description']);
        $unit_of_measure = trim($_POST['unit_of_measure']);

        // Validation
        if (empty($sku_code) || empty($sku_name) || empty($unit_of_measure)) {
            $_SESSION['error'] = "SKU Code, SKU Name, and Unit of Measure are required fields.";
        } else {
            // Check if SKU code already exists
            $check_sql = "SELECT sku_id FROM sku_master WHERE sku_code = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("s", $sku_code);
            $check_stmt->execute();
            $result = $check_stmt->get_result();

            if ($result->num_rows > 0) {
                $_SESSION['error'] = "SKU with this code already exists.";
            } else {
                // Insert new SKU
                $sql = "INSERT INTO sku_master (category_id, sku_code, sku_name, description, unit_of_measure) VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("issss", $category_id, $sku_code, $sku_name, $description, $unit_of_measure);

                if ($stmt->execute()) {
                    $_SESSION['success'] = "SKU added successfully.";
                    logActivity($conn, 'Added new SKU: ' . $sku_code . ' - ' . $sku_name, 'sku', $stmt->insert_id);
                } else {
                    $_SESSION['error'] = "Error adding SKU: " . $conn->error;
                }

                $stmt->close();
            }
            $check_stmt->close();
        }
    } elseif (isset($_POST['edit_sku'])) {
        $sku_id = $_POST['sku_id'];
        $category_id = $_POST['category_id'];
        $sku_name = trim($_POST['sku_name']);
        $description = trim($_POST['description']);
        $unit_of_measure = trim($_POST['unit_of_measure']);

        // Validation
        if (empty($sku_name) || empty($unit_of_measure)) {
            $_SESSION['error'] = "SKU Name and Unit of Measure are required fields.";
        } else {
            // Get the existing SKU code
            $get_code_sql = "SELECT sku_code FROM sku_master WHERE sku_id = ?";
            $get_code_stmt = $conn->prepare($get_code_sql);
            $get_code_stmt->bind_param("i", $sku_id);
            $get_code_stmt->execute();
            $code_result = $get_code_stmt->get_result();
            $existing_sku = $code_result->fetch_assoc();
            $sku_code = $existing_sku['sku_code'];
            $get_code_stmt->close();

            // Update SKU - but keep the original SKU code
            $sql = "UPDATE sku_master SET category_id = ?, sku_name = ?, description = ?, unit_of_measure = ? WHERE sku_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("isssi", $category_id, $sku_name, $description, $unit_of_measure, $sku_id);

            if ($stmt->execute()) {
                $_SESSION['success'] = "SKU updated successfully.";
                logActivity($conn, 'Updated SKU: ' . $sku_code . ' - ' . $sku_name, 'sku', $sku_id);
            } else {
                $_SESSION['error'] = "Error updating SKU: " . $conn->error;
            }

            $stmt->close();
        }
    } elseif (isset($_POST['delete_sku'])) {
        $sku_id = $_POST['sku_id'];

        // Check if SKU is in use
        $check_sql = "SELECT COUNT(*) as count FROM fixed_assets WHERE sku_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $sku_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $count = $result->fetch_assoc()['count'];

        if ($count > 0) {
            $_SESSION['error'] = "Cannot delete SKU because it is being used by assets.";
        } else {
            // Get SKU details for logging before deletion
            $name_sql = "SELECT sku_code, sku_name FROM sku_master WHERE sku_id = ?";
            $name_stmt = $conn->prepare($name_sql);
            $name_stmt->bind_param("i", $sku_id);
            $name_stmt->execute();
            $name_result = $name_stmt->get_result();
            $sku_info = $name_result->fetch_assoc();
            $name_stmt->close();

            // Delete SKU
            $sql = "DELETE FROM sku_master WHERE sku_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $sku_id);

            if ($stmt->execute()) {
                $_SESSION['success'] = "SKU deleted successfully.";
                logActivity($conn, 'Deleted SKU: ' . $sku_info['sku_code'] . ' - ' . $sku_info['sku_name'], 'sku', $sku_id);
            } else {
                $_SESSION['error'] = "Error deleting SKU: " . $conn->error;
            }

            $stmt->close();
        }
        $check_stmt->close();
    }

    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Get all categories
$cat_sql = "SELECT * FROM categories ORDER BY category_name ASC";
$cat_result = $conn->query($cat_sql);
$categories = [];
if ($cat_result && $cat_result->num_rows > 0) {
    while ($row = $cat_result->fetch_assoc()) {
        $categories[] = $row;
    }
}

// Define category code mapping
$category_codes = [
    1 => 'IE', // IT Equipment
    2 => 'OE', // Office Equipment
    3 => 'ME', // Medical Equipment
    4 => 'IS', // IT Supply
    5 => 'OS', // Office Supply
    6 => 'MS'  // Medical Supply
];

// Get all SKUs with category names
$sql = "SELECT s.*, c.category_name, IFNULL(a.asset_count, 0) as asset_count
        FROM sku_master s
        LEFT JOIN categories c ON s.category_id = c.category_id
        LEFT JOIN (
            SELECT sku_id, COUNT(*) as asset_count
            FROM fixed_assets
            GROUP BY sku_id
        ) a ON s.sku_id = a.sku_id
        ORDER BY s.sku_code ASC";
$result = $conn->query($sql);
$skus = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $skus[] = $row;
    }
}

// Function to get the next SKU number for a category
function getNextSkuNumber($conn, $category_id, $category_codes) {
    // Escape category code to prevent SQL injection
    $category_code = $category_codes[$category_id];
    $pattern = $category_code . '-%';

    $sql = "SELECT MAX(SUBSTRING_INDEX(sku_code, '-', -1)) as max_num
            FROM sku_master
            WHERE sku_code LIKE ?";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $pattern);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    if ($row['max_num'] === null) {
        // No SKUs yet for this category
        return $category_code . '-0001';
    } else {
        // Increment the last number
        $next_num = intval($row['max_num']) + 1;
        return $category_code . '-' . str_pad($next_num, 4, '0', STR_PAD_LEFT);
    }
}

// Include header
include_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-barcode me-2"></i>SKU Management</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php
                    // Find similar SKUs
                    $similar_sql = "SELECT a.sku_id as id1, a.sku_code as code1,
                                          b.sku_id as id2, b.sku_code as code2
                                   FROM sku_master a
                                   JOIN sku_master b ON a.sku_id < b.sku_id
                                   WHERE (a.sku_code LIKE CONCAT(b.sku_code, '%') OR b.sku_code LIKE CONCAT(a.sku_code, '%'))
                                   AND a.category_id = b.category_id";
                    $similar_result = $conn->query($similar_sql);
                    $similar_count = $similar_result ? $similar_result->num_rows : 0;
                    ?>

                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSkuModal">
                            <i class="fas fa-plus-circle me-1"></i> Add SKU
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table id="skuTable" class="table table-bordered table-striped">
                            <thead class="bg-light">
                                <tr>
                                    <th>ID</th>
                                    <th>SKU Code</th>
                                    <th>SKU Name</th>
                                    <th>Category</th>
                                    <th>Unit of Measure</th>
                                    <th>Description</th>
                                    <th>Assets Count</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($skus as $sku): ?>
                                <tr>
                                    <td><?php echo $sku['sku_id']; ?></td>
                                    <td><?php echo htmlspecialchars($sku['sku_code']); ?></td>
                                    <td><?php echo htmlspecialchars($sku['sku_name']); ?></td>
                                    <td><?php echo htmlspecialchars($sku['category_name']); ?></td>
                                    <td><?php echo htmlspecialchars(isset($sku['unit_of_measure']) ? $sku['unit_of_measure'] : 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars(isset($sku['description']) ? $sku['description'] : ''); ?></td>
                                    <td>
                                        <?php if ($sku['asset_count'] > 0): ?>
                                            <span class="badge bg-info"><?php echo $sku['asset_count']; ?> asset(s)</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No assets</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y h:i A', strtotime($sku['created_at'])); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning edit-sku"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editSkuModal"
                                                data-id="<?php echo $sku['sku_id']; ?>"
                                                data-category="<?php echo $sku['category_id']; ?>"
                                                data-code="<?php echo htmlspecialchars($sku['sku_code']); ?>"
                                                data-name="<?php echo htmlspecialchars($sku['sku_name']); ?>"
                                                data-uom="<?php echo htmlspecialchars(isset($sku['unit_of_measure']) ? $sku['unit_of_measure'] : ''); ?>"
                                                data-description="<?php echo htmlspecialchars(isset($sku['description']) ? $sku['description'] : ''); ?>">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>

                                        <?php if ($sku['asset_count'] == 0): ?>
                                        <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this SKU? This action cannot be undone.');">
                                            <input type="hidden" name="sku_id" value="<?php echo $sku['sku_id']; ?>">
                                            <button type="submit" name="delete_sku" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash-alt"></i> Delete
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add SKU Modal -->
<div class="modal fade" id="addSkuModal" tabindex="-1" aria-labelledby="addSkuModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addSkuModalLabel"><i class="fas fa-plus-circle me-2"></i>Add New SKU</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                        <select class="form-select" id="category_id" name="category_id" required onchange="updateSkuCode()">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['category_id']; ?>"><?php echo htmlspecialchars($category['category_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="sku_code" class="form-label">SKU Code <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="sku_code" name="sku_code" readonly required>
                        <small class="text-muted">The SKU code will be automatically generated in the format XX-0001</small>
                    </div>
                    <div class="mb-3">
                        <label for="sku_name" class="form-label">SKU Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="sku_name" name="sku_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="unit_of_measure" class="form-label">Unit of Measure <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="unit_of_measure" name="unit_of_measure" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="add_sku" class="btn btn-primary">Add SKU</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit SKU Modal -->
<div class="modal fade" id="editSkuModal" tabindex="-1" aria-labelledby="editSkuModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editSkuModalLabel"><i class="fas fa-edit me-2"></i>Edit SKU</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <input type="hidden" name="sku_id" id="edit_sku_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_category_id" class="form-label">Category <span class="text-danger">*</span></label>
                        <select class="form-select" id="edit_category_id" name="category_id" required>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['category_id']; ?>"><?php echo htmlspecialchars($category['category_name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_sku_code" class="form-label">SKU Code <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_sku_code" name="sku_code" readonly required>
                        <small class="text-muted">SKU codes follow the format XX-0001 and cannot be changed</small>
                    </div>
                    <div class="mb-3">
                        <label for="edit_sku_name" class="form-label">SKU Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_sku_name" name="sku_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_unit_of_measure" class="form-label">Unit of Measure <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_unit_of_measure" name="unit_of_measure" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="edit_sku" class="btn btn-primary">Update SKU</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#skuTable').DataTable({
        order: [[1, 'asc']],
        pageLength: 10,
        responsive: true
    });

    // Handle edit SKU modal data
    const editButtons = document.querySelectorAll('.edit-sku');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const category = this.getAttribute('data-category');
            const code = this.getAttribute('data-code');
            const name = this.getAttribute('data-name');
            const uom = this.getAttribute('data-uom');
            const description = this.getAttribute('data-description');

            document.getElementById('edit_sku_id').value = id;
            document.getElementById('edit_category_id').value = category;
            document.getElementById('edit_sku_code').value = code;
            document.getElementById('edit_sku_name').value = name;
            document.getElementById('edit_unit_of_measure').value = uom;
            document.getElementById('edit_description').value = description;
        });
    });
});

function updateSkuCode() {
    const categoryId = document.getElementById('category_id').value;
    if (categoryId) {
        // Make an AJAX call to get the next SKU code for this category
        fetch(`get_next_sku_code.php?category_id=${categoryId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('sku_code').value = data.sku_code;
            })
            .catch(error => {
                console.error('Error fetching SKU code:', error);
            });
    } else {
        document.getElementById('sku_code').value = '';
    }
}
</script>

<?php
// Include footer
include_once '../../includes/footer.php';
?>