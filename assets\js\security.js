/**
 * Security measures for CHOIMS
 * Prevents browser inspection and protects content
 */

// Configuration
const securityConfig = {
    disableRightClick: true,
    disableDevTools: false,  // Disable the aggressive dev tools detection
    disableSelection: false, // Allow text selection
    disableCopy: false,      // Allow copying
    disablePrint: true,
    disableSave: true,
    disableViewSource: true,
    showWarningMessage: true,
    warningMessage: 'This action is not allowed for security reasons.',
    warningShown: false,     // Track if warning was already shown
    warningCooldown: 5000    // Cooldown between warnings (5 seconds)
};

// Initialize security measures
function initSecurity() {
    if (securityConfig.disableRightClick) {
        disableRightClick();
    }

    if (securityConfig.disableDevTools) {
        detectDevTools();
    }

    if (securityConfig.disableSelection) {
        disableSelection();
    }

    if (securityConfig.disableCopy) {
        disableCopy();
    }

    if (securityConfig.disablePrint) {
        disablePrint();
    }

    if (securityConfig.disableSave) {
        disableSave();
    }

    if (securityConfig.disableViewSource) {
        disableViewSource();
    }
}

// Disable right-click context menu
function disableRightClick() {
    document.addEventListener('contextmenu', function(e) {
        // Allow right-click on form elements
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
            return true;
        }

        e.preventDefault();
        if (securityConfig.showWarningMessage) {
            showWarning('Right-click inspection is disabled for security reasons.');
        }
        return false;
    });
}

// Detect and respond to developer tools
function detectDevTools() {
    // This function is now disabled in the config
    // Keeping the function as a placeholder in case it needs to be re-enabled later
    console.log('Developer tools detection is disabled');
}

// Disable text selection
function disableSelection() {
    document.addEventListener('selectstart', function(e) {
        // Allow selection in form inputs and textareas
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return true;
        }
        e.preventDefault();
        return false;
    });

    // Add CSS to disable selection
    const style = document.createElement('style');
    style.textContent = `
        body:not(input):not(textarea) {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    `;
    document.head.appendChild(style);
}

// Disable copy functionality
function disableCopy() {
    document.addEventListener('copy', function(e) {
        // Allow copy in form inputs and textareas
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return true;
        }
        e.preventDefault();
        if (securityConfig.showWarningMessage) {
            showWarning('Copying content is not allowed.');
        }
        return false;
    });
}

// Disable print functionality
function disablePrint() {
    window.addEventListener('keydown', function(e) {
        // Detect Ctrl+P or Command+P
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 80) {
            e.preventDefault();
            if (securityConfig.showWarningMessage) {
                showWarning('Printing is not allowed.');
            }
            return false;
        }
    });
}

// Disable save functionality
function disableSave() {
    window.addEventListener('keydown', function(e) {
        // Detect Ctrl+S or Command+S
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) {
            e.preventDefault();
            if (securityConfig.showWarningMessage) {
                showWarning('Saving this page is not allowed.');
            }
            return false;
        }
    });
}

// Disable view source functionality
function disableViewSource() {
    window.addEventListener('keydown', function(e) {
        // Detect Ctrl+U or Command+U
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 85) {
            e.preventDefault();
            if (securityConfig.showWarningMessage) {
                showWarning('Viewing source is not allowed.');
            }
            return false;
        }
    });
}

// Show warning message
function showWarning(message = securityConfig.warningMessage) {
    // Check if warning is in cooldown period to prevent spam
    if (securityConfig.warningShown) {
        return;
    }

    // Set warning shown flag and start cooldown
    securityConfig.warningShown = true;
    setTimeout(() => {
        securityConfig.warningShown = false;
    }, securityConfig.warningCooldown);

    // Create toast notification
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0';
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // Initialize Bootstrap toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });

    bsToast.show();
}

// Create toast container if it doesn't exist
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// Initialize security measures when DOM is loaded
document.addEventListener('DOMContentLoaded', initSecurity);
