<?php
// Reset IDs Script
// Purpose: Reset auto-increment counters for specified tables
// This is useful when importing real data after testing

// Include necessary files
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/config/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Security check - only godmode users should access this
if (!isset($_SESSION['user_id']) || !hasRole('godmode')) {
    header("Location: /choims/index.php");
    exit;
}

// Define tables that can be reset
$availableTables = [
    'transfers' => 'Transfers',
    'batch_transfers' => 'Batch Transfers',
    'batch_transfer_assets' => 'Batch Transfer Assets',
    'batch_transfer_inventory' => 'Batch Transfer Inventory',
    'fixed_assets' => 'Fixed Assets',
    'consumable_inventory' => 'Consumable Inventory',
    'consumable_transactions' => 'Consumable Transactions',
    'maintenance_records' => 'Maintenance Records'
];

// Initialize variables
$success = [];
$errors = [];
$selectedTables = [];

// Check if form was submitted
$confirmed = isset($_POST['confirm']) && $_POST['confirm'] === 'yes';

// Get selected tables
if (isset($_POST['tables']) && is_array($_POST['tables'])) {
    foreach ($_POST['tables'] as $table) {
        if (array_key_exists($table, $availableTables)) {
            $selectedTables[] = $table;
        }
    }
}

// Display the form if not confirmed or no tables selected
if (!$confirmed || empty($selectedTables)) {
    include_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
    ?>
    <div class="container mt-5">
        <div class="card shadow">
            <div class="card-header bg-danger text-white">
                <h3><i class="fas fa-exclamation-triangle me-2"></i>Reset Table IDs</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h4><i class="fas fa-exclamation-circle me-2"></i>Warning!</h4>
                    <p>This tool will <strong>permanently delete all data</strong> from the selected tables and reset their auto-increment counters to 1.</p>
                    <p>This action <strong>cannot be undone</strong>. Only use this tool before importing real data.</p>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <h5>Errors:</h5>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <h5>Success:</h5>
                        <ul class="mb-0">
                            <?php foreach ($success as $message): ?>
                                <li><?php echo htmlspecialchars($message); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form method="post" onsubmit="return confirm('Are you absolutely sure you want to delete ALL data from the selected tables and reset their IDs? This action CANNOT be undone!');">
                    <div class="mb-4">
                        <h5>Select tables to reset:</h5>
                        <div class="row">
                            <?php foreach ($availableTables as $tableName => $displayName): ?>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" name="tables[]" value="<?php echo $tableName; ?>" id="check_<?php echo $tableName; ?>">
                                        <label class="form-check-label" for="check_<?php echo $tableName; ?>">
                                            <?php echo $displayName; ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                        <label class="form-check-label" for="confirmCheck">
                            I understand that this will permanently delete all data from the selected tables and reset their IDs to 1
                        </label>
                    </div>

                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-danger">Reset Selected Tables</button>
                    <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
    <?php
    include_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
    exit;
}

// If confirmed and tables selected, proceed with resetting
try {
    // Start transaction
    mysqli_begin_transaction($conn);

    // Disable foreign key checks temporarily
    mysqli_query($conn, "SET FOREIGN_KEY_CHECKS = 0");

    // Process each selected table
    foreach ($selectedTables as $table) {
        try {
            // Truncate the table (faster than DELETE and resets auto-increment)
            $truncateQuery = "TRUNCATE TABLE `$table`";
            $truncateResult = mysqli_query($conn, $truncateQuery);

            if ($truncateResult) {
                $success[] = "Successfully reset table: $table";
            } else {
                throw new Exception(mysqli_error($conn));
            }
        } catch (Exception $e) {
            // If truncate fails, try DELETE and ALTER TABLE
            try {
                // Delete all records
                $deleteQuery = "DELETE FROM `$table`";
                $deleteResult = mysqli_query($conn, $deleteQuery);

                if (!$deleteResult) {
                    throw new Exception(mysqli_error($conn));
                }

                // Reset auto-increment
                $resetQuery = "ALTER TABLE `$table` AUTO_INCREMENT = 1";
                $resetResult = mysqli_query($conn, $resetQuery);

                if ($resetResult) {
                    $success[] = "Successfully reset table: $table (using DELETE and ALTER TABLE)";
                } else {
                    throw new Exception(mysqli_error($conn));
                }
            } catch (Exception $innerEx) {
                $errors[] = "Error resetting $table: " . $innerEx->getMessage();
            }
        }
    }

    // Re-enable foreign key checks
    mysqli_query($conn, "SET FOREIGN_KEY_CHECKS = 1");

    // Commit or rollback based on errors
    if (empty($errors)) {
        mysqli_commit($conn);
    } else {
        mysqli_rollback($conn);
        $success = []; // Clear success messages if we rolled back
        $errors[] = "Transaction rolled back due to errors.";
    }
} catch (Exception $e) {
    // Rollback on any exception
    mysqli_rollback($conn);
    $errors[] = "Transaction error: " . $e->getMessage();
}

// Display results
include_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
?>
<div class="container mt-5">
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h3><i class="fas fa-database me-2"></i>Reset Table IDs - Results</h3>
        </div>
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5>Errors:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <h5>Success:</h5>
                    <ul class="mb-0">
                        <?php foreach ($success as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="mt-4">
                <a href="/choims/maintenance/reset_ids.php" class="btn btn-primary">
                    <i class="fas fa-redo me-2"></i> Reset More Tables
                </a>
                <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i> Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>
