/* Combined MR Report Styles */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-lighter: #81C784;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate__animated {
  animation-duration: 0.5s;
  animation-fill-mode: both;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__slideInUp {
  animation-name: slideInUp;
}

.animate__delay-1 {
  animation-delay: 0.1s;
}

.animate__delay-2 {
  animation-delay: 0.2s;
}

.animate__delay-3 {
  animation-delay: 0.3s;
}

/* Page Container */
.container-fluid {
  padding: var(--space-5);
  background-color: var(--light);
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-5);
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.page-subtitle {
  color: var(--gray-600);
  font-size: 1rem;
  margin-top: 0;
}

/* View Toggle */
.view-toggle {
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  padding: 0.25rem;
  display: inline-flex;
  margin-bottom: var(--space-4);
}

.view-toggle-btn {
  border: none;
  background: transparent;
  color: var(--gray-700);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: var(--transition-fast);
}

.view-toggle-btn.active {
  background-color: var(--white);
  color: var(--primary);
  box-shadow: var(--shadow-sm);
}

/* Export Button */
.export-btn {
  background-color: var(--success);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 0.625rem 1.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn:hover {
  background-color: #0ca678;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Alert */
.alert {
  border-radius: var(--radius);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  border: none;
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border-left: 4px solid var(--info);
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--info);
  color: white;
  border-radius: var(--radius-full);
  margin-right: var(--space-3);
}

.alert-link {
  color: var(--info);
  font-weight: 600;
  text-decoration: underline;
}

/* Filter Section */
.filter-section {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow);
}

.filter-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: var(--space-4);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.625rem 1rem;
  font-size: 0.95rem;
  transition: var(--transition-fast);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
}

/* MR Report Card */
.mr-report-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
}

.mr-report-header {
  background-color: var(--primary);
  color: white;
  padding: var(--space-4) var(--space-5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mr-report-title {
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.mr-report-body {
  padding: 0;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.03em;
  padding: var(--space-3) var(--space-4);
  border-top: none;
  border-bottom: 2px solid var(--gray-200);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
  font-size: 0.9rem;
}

.table tr:hover {
  background-color: var(--gray-100);
}

/* Status Badges */
.badge {
  padding: 0.35em 0.65em;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-full);
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.badge-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.badge-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.badge-secondary {
  background-color: rgba(107, 114, 128, 0.1);
  color: var(--gray-600);
}

/* Pagination */
.pagination {
  margin-top: var(--space-4);
  justify-content: center;
}

.page-link {
  color: var(--primary);
  border: 1px solid var(--gray-300);
  padding: 0.5rem 0.75rem;
  margin: 0 0.25rem;
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.page-link:hover {
  background-color: var(--primary-bg);
  color: var(--primary-dark);
  border-color: var(--primary-lighter);
}

.page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
}

.page-item.disabled .page-link {
  color: var(--gray-400);
  background-color: var(--gray-100);
  border-color: var(--gray-300);
}

/* Loading Indicator */
.spinner-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--white);
}

.spinner-border {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .container-fluid {
    padding: var(--space-4);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .filter-section {
    padding: var(--space-4);
  }
  
  .mr-report-header {
    padding: var(--space-3) var(--space-4);
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.5rem;
  }
  
  .table td, .table th {
    padding: var(--space-2) var(--space-3);
  }
}

/* Tab Styles */
.nav-tabs {
  border-bottom: 1px solid var(--gray-300);
  margin-bottom: var(--space-4);
}

.nav-tabs .nav-link {
  color: var(--gray-600);
  border: none;
  border-bottom: 2px solid transparent;
  padding: var(--space-3) var(--space-4);
  font-weight: 500;
  transition: var(--transition-fast);
}

.nav-tabs .nav-link:hover {
  color: var(--primary);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--primary);
  border-color: var(--primary);
  background-color: transparent;
}

/* Print Styles */
@media print {
  .btn, .filter-section, .pagination, .dataTables_filter, .dataTables_length, .dataTables_paginate {
    display: none !important;
  }
  
  .mr-report-card {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
  
  .mr-report-header {
    background-color: var(--gray-100) !important;
    color: var(--dark) !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .table th {
    background-color: var(--gray-100) !important;
    color: var(--dark) !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  .badge {
    border: 1px solid currentColor;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
