<?php
// Application configuration
define('APP_NAME', 'PCHIMS');
define('BASE_URL', 'http://localhost/choims');

// User roles
define('ROLE_GODMODE', 'godmode');
define('ROLE_SUPERADMIN', 'superadmin');
define('ROLE_LOGISTICS', 'logistics');
define('ROLE_HIMU', 'himu');
define('ROLE_DEPARTMENT', 'department');
define('ROLE_HEALTH_CENTER', 'health_center');

// Error reporting - Production settings
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in production
ini_set('log_errors', 1); // Log errors instead
ini_set('error_log', __DIR__ . '/../logs/error.log'); // Set error log path

// Session configuration
// Only set session parameters if session hasn't been started yet
if (session_status() == PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', 0); // Disabled for HTTP
}

// Include required files
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Get database connection
$db = getDBConnection();
