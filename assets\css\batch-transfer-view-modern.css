/* 
 * Modern React-like UI Styles for Batch Transfer View Page
 * CHOIMS - City Health Office Inventory Management System
 */

:root {
  /* Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  
  /* Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-all: all 0.2s ease;
  
  /* Gradients */
  --green-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease forwards;
}

/* Page Container */
.batch-view-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.5s ease;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--green-gradient);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--primary);
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: none;
  transition: var(--transition-all);
  margin-bottom: 1.5rem;
  overflow: hidden;
  animation: slideInUp 0.5s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: 1rem 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  margin: 0;
  font-weight: 600;
  color: var(--primary);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-title i {
  color: var(--primary);
}

.card-body {
  padding: 1.25rem;
}

/* Status Badge */
.status-badge {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.pending {
  background-color: #fff8e1;
  color: #f59e0b;
}

.status-badge.approved {
  background-color: #e3f2fd;
  color: #3b82f6;
}

.status-badge.completed {
  background-color: #e8f5e9;
  color: #10b981;
}

.status-badge.rejected {
  background-color: #fee2e2;
  color: #ef4444;
}

/* Buttons */
.btn {
  font-weight: 500;
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  transition: var(--transition-all);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background-color: var(--gray-200);
  border-color: var(--gray-200);
  color: var(--gray-700);
}

.btn-secondary:hover {
  background-color: var(--gray-300);
  border-color: var(--gray-300);
  color: var(--gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

.btn-success:hover {
  background-color: #0da271;
  border-color: #0da271;
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-danger {
  background-color: var(--danger);
  border-color: var(--danger);
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Alerts */
.alert {
  border-radius: var(--radius-lg);
  padding: 1rem 1.25rem;
  border: none;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: #ecfdf5;
  color: #065f46;
}

.alert-danger {
  background-color: #fef2f2;
  color: #991b1b;
}

/* Info Tables */
.info-table {
  width: 100%;
  margin-bottom: 1rem;
}

.info-table th {
  font-weight: 600;
  color: var(--gray-600);
  padding: 0.75rem 0;
  width: 40%;
  vertical-align: top;
  border-bottom: 1px solid var(--gray-200);
}

.info-table td {
  padding: 0.75rem 0;
  color: var(--gray-800);
  border-bottom: 1px solid var(--gray-200);
}

.info-table tr:last-child th,
.info-table tr:last-child td {
  border-bottom: none;
}

.info-section {
  margin-bottom: 1.5rem;
}

.info-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--gray-200);
}

/* Horizontal Timeline */
.timeline-container {
  margin: 2rem 0;
}

.timeline {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  padding: 0;
  margin: 0;
  list-style: none;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 24px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--gray-300);
  z-index: 1;
}

.timeline-step {
  position: relative;
  z-index: 2;
  flex: 1;
  text-align: center;
  padding: 0 0.5rem;
}

.timeline-step-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--white);
  border: 2px solid var(--gray-300);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem;
  color: var(--gray-500);
  font-size: 1.25rem;
  transition: var(--transition-all);
}

.timeline-step.active .timeline-step-icon {
  background-color: var(--primary-bg);
  border-color: var(--primary);
  color: var(--primary);
}

.timeline-step.completed .timeline-step-icon {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.timeline-step-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--gray-600);
  margin-bottom: 0.25rem;
}

.timeline-step.active .timeline-step-title {
  color: var(--primary);
}

.timeline-step.completed .timeline-step-title {
  color: var(--gray-800);
}

.timeline-step-date {
  font-size: 0.8rem;
  color: var(--gray-500);
}

.timeline-step-content {
  font-size: 0.85rem;
  color: var(--gray-500);
  max-width: 150px;
  margin: 0 auto;
}

.timeline-step-content.pending {
  font-style: italic;
  color: var(--gray-400);
}

/* Data Tables */
.table-container {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
}

.data-table {
  width: 100%;
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.data-table th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--gray-200);
}

.data-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tbody tr {
  transition: var(--transition-all);
}

.data-table tbody tr:hover {
  background-color: var(--gray-50);
}

/* DataTables Custom Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length select {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.25rem 1.5rem 0.25rem 0.5rem;
}

.dataTables_wrapper .dataTables_filter input {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: var(--radius);
  border: 1px solid var(--gray-300);
  padding: 0.25rem 0.75rem;
  margin: 0 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--gray-800) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white !important;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }
  
  .timeline {
    overflow-x: auto;
    padding-bottom: 1rem;
  }
  
  .timeline-step {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .info-table th {
    width: 50%;
  }
  
  .timeline-step {
    min-width: 120px;
  }
}
