<?php
// Ensure user is logged in and has appropriate permissions
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/config/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');
requireLogin();
requireRole('Logistics', 'Department', 'HealthCenter');

// Check if POST request and has inventory_id
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['inventory_id'])) {
    $inventory_id = sanitizeInput($_POST['inventory_id']);
    $transaction_type = sanitizeInput($_POST['transaction_type']);
    $quantity = sanitizeInput($_POST['quantity']);
    $supplier_id = !empty($_POST['supplier_id']) ? sanitizeInput($_POST['supplier_id']) : null;
    $reference_document = !empty($_POST['reference_document']) ? sanitizeInput($_POST['reference_document']) : null;
    $unit_cost = !empty($_POST['unit_cost']) ? sanitizeInput($_POST['unit_cost']) : null;
    $remarks = !empty($_POST['remarks']) ? sanitizeInput($_POST['remarks']) : null;

    // Get inventory details to check permissions
    $inventoryQuery = "SELECT * FROM consumable_inventory WHERE inventory_id = ?";
    $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
    mysqli_stmt_bind_param($inventoryStmt, 'i', $inventory_id);
    mysqli_stmt_execute($inventoryStmt);
    $inventoryResult = mysqli_stmt_get_result($inventoryStmt);

    if (mysqli_num_rows($inventoryResult) == 0) {
        // Inventory not found
        header("Location: /choims/modules/inventory/list.php?error=not_found");
        exit();
    }

    $inventory = mysqli_fetch_assoc($inventoryResult);

    // Check if user has permission for this location
    // Logistics users can only modify inventory in their assigned location
    if ((hasRole('Logistics') && getUserLocationId() != $inventory['location_id']) ||
        (!hasRole('Logistics') && getUserLocationId() != $inventory['location_id'])) {
        // User doesn't have permission
        header("Location: /choims/modules/inventory/list.php?error=permission");
        exit();
    }

    // Calculate new quantity
    $new_quantity = $inventory['current_quantity'];
    if ($transaction_type == 'Stock In') {
        $new_quantity += $quantity;
    } else if ($transaction_type == 'Stock Out') {
        $new_quantity -= $quantity;
        if ($new_quantity < 0) {
            // Cannot go below zero
            $new_quantity = 0;
        }
    } else if ($transaction_type == 'Adjustment') {
        $new_quantity = $quantity; // Direct adjustment to specified quantity
    }

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // Update inventory quantity
        $updateQuery = "
            UPDATE consumable_inventory
            SET current_quantity = ?,
                last_restock_date = CASE WHEN ? = 'Stock In' THEN NOW() ELSE last_restock_date END,
                updated_at = NOW()
            WHERE inventory_id = ?
        ";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'isi', $new_quantity, $transaction_type, $inventory_id);
        mysqli_stmt_execute($updateStmt);

        // Determine source and destination based on transaction type
        $source_location_id = null;
        $destination_location_id = null;

        if ($transaction_type == 'Stock In') {
            $destination_location_id = $inventory['location_id'];
        } else if ($transaction_type == 'Stock Out') {
            $source_location_id = $inventory['location_id'];
        } else if ($transaction_type == 'Adjustment') {
            // For adjustments, we use the same location for continuity
            if ($new_quantity > $inventory['current_quantity']) {
                $destination_location_id = $inventory['location_id'];
            } else {
                $source_location_id = $inventory['location_id'];
            }
        }

        // Record transaction
        $transactionQuery = "
            INSERT INTO consumable_transactions (
                inventory_id, transaction_type, quantity, source_location_id,
                destination_location_id, reference_document, supplier_id,
                unit_cost, remarks, performed_by, transaction_date
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )
        ";
        $transactionStmt = mysqli_prepare($conn, $transactionQuery);
        mysqli_stmt_bind_param(
            $transactionStmt,
            'issiisissi',
            $inventory_id, $transaction_type, $quantity, $source_location_id,
            $destination_location_id, $reference_document, $supplier_id,
            $unit_cost, $remarks, $_SESSION['user_id']
        );
        mysqli_stmt_execute($transactionStmt);

        // Get transaction ID
        $transaction_id = mysqli_insert_id($conn);

        // Create detailed audit log
        $transaction_data = [
            'inventory_id' => $inventory_id,
            'transaction_type' => $transaction_type,
            'quantity' => $quantity,
            'supplier_id' => $supplier_id,
            'reference_document' => $reference_document,
            'unit_cost' => $unit_cost,
            'remarks' => $remarks,
            'old_quantity' => $inventory['current_quantity'],
            'new_quantity' => $new_quantity,
            'item_name' => $inventory['sku_name'],
            'item_sku' => $inventory['sku_code']
        ];

        // Log to both systems for backward compatibility
        createAuditLog(
            $_SESSION['user_id'],
            'update',
            'consumable_inventory',
            $inventory_id,
            json_encode(['current_quantity' => $inventory['current_quantity']]),
            json_encode(['current_quantity' => $new_quantity])
        );

        // Determine action type based on transaction type
        $action_type = ($transaction_type == 'Stock In') ? 'stock_in' : 'stock_out';
        if ($transaction_type == 'Adjustment') {
            $action_type = ($new_quantity > $inventory['current_quantity']) ? 'stock_in' : 'stock_out';
        }

        // Log the detailed transaction
        logStockTransaction($conn, $_SESSION['user_id'], $action_type, $transaction_id, $transaction_data);

        // Commit transaction
        mysqli_commit($conn);

        // Redirect back to inventory view
        header("Location: /choims/modules/inventory/view.php?id={$inventory_id}&success=1");
        exit();

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error = "Error updating inventory: " . $e->getMessage();
    }
} else {
    // Not a valid POST request
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

// Only include header.php if we haven't redirected yet
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
?>