<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern stock logs CSS
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-stock-logs-modern.css">';

// Add custom CSS for clickable rows
echo '<style>
    .clickable-row {
        transition: all 0.2s ease;
        position: relative;
    }
    .clickable-row:after {
        content: "\\f0c9";
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #2E7D32;
        opacity: 0.3;
        transition: all 0.2s ease;
    }
    .table-hover-custom {
        background-color: rgba(46, 125, 50, 0.05) !important;
        box-shadow: 0 0 5px rgba(46, 125, 50, 0.2);
        transform: translateY(-1px);
    }
    .table-hover-custom:after {
        opacity: 1;
        right: 15px;
    }
    .clickable-row:active {
        background-color: rgba(46, 125, 50, 0.1) !important;
        transform: translateY(0);
    }
    .clickable-row td {
        padding: 0.75rem 1rem;
    }
</style>';

// Ensure user is logged in and has appropriate role
requireLogin();
requireRole('GodMode', 'Superadmin', 'Logistics');

// Initialize filters
$filter_location = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
$filter_transaction_type = isset($_GET['transaction_type']) ? sanitizeInput($_GET['transaction_type']) : '';
$filter_sku = isset($_GET['sku']) ? sanitizeInput($_GET['sku']) : '';
$filter_start_date = isset($_GET['start_date']) ? sanitizeInput($_GET['start_date']) : '';
$filter_end_date = isset($_GET['end_date']) ? sanitizeInput($_GET['end_date']) : '';

// Base query
$query = "
    SELECT
        t.transaction_id,
        t.transaction_date,
        t.transaction_type,
        t.quantity,
        t.reference_document,
        t.remarks,
        s.sku_code,
        s.sku_name,
        c.category_name,
        src.location_name as source_location,
        dest.location_name as destination_location,
        u.full_name as performed_by,
        sup.supplier_name
    FROM consumable_transactions t
    JOIN consumable_inventory i ON t.inventory_id = i.inventory_id
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    LEFT JOIN locations src ON t.source_location_id = src.location_id
    LEFT JOIN locations dest ON t.destination_location_id = dest.location_id
    LEFT JOIN users u ON t.performed_by = u.user_id
    LEFT JOIN suppliers sup ON t.supplier_id = sup.supplier_id
    WHERE 1=1
";

// Apply filters
$params = [];
$param_types = "";

// User role based filtering - limit to user's location unless they have special roles
if (!hasRole('Logistics', 'Superadmin', 'GodMode')) {
    $userLocationId = getUserLocationId();
    $query .= " AND (i.location_id = ? OR t.source_location_id = ? OR t.destination_location_id = ?)";
    $params[] = $userLocationId;
    $params[] = $userLocationId;
    $params[] = $userLocationId;
    $param_types .= "iii";
}

// Location filter
if (!empty($filter_location)) {
    $query .= " AND (i.location_id = ? OR t.source_location_id = ? OR t.destination_location_id = ?)";
    $params[] = $filter_location;
    $params[] = $filter_location;
    $params[] = $filter_location;
    $param_types .= "iii";
}

// Transaction type filter
if (!empty($filter_transaction_type)) {
    $query .= " AND t.transaction_type = ?";
    $params[] = $filter_transaction_type;
    $param_types .= "s";
}

// SKU filter
if (!empty($filter_sku)) {
    $query .= " AND s.sku_id = ?";
    $params[] = $filter_sku;
    $param_types .= "i";
}

// Date range filters
if (!empty($filter_start_date)) {
    $query .= " AND t.transaction_date >= ?";
    $params[] = $filter_start_date . " 00:00:00";
    $param_types .= "s";
}

if (!empty($filter_end_date)) {
    $query .= " AND t.transaction_date <= ?";
    $params[] = $filter_end_date . " 23:59:59";
    $param_types .= "s";
}

// Order by transaction date descending
$query .= " ORDER BY t.transaction_date DESC";

// Prepare and execute the query
$stmt = mysqli_prepare($conn, $query);

if ($stmt) {
    if (!empty($params)) {
        // Create a dynamic call to bind_param
        $bind_params = array($stmt, $param_types);
        foreach ($params as $param) {
            $bind_params[] = &$params[array_search($param, $params)];
        }
        call_user_func_array('mysqli_stmt_bind_param', $bind_params);
    }
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
} else {
    $error = "Database query error: " . mysqli_error($conn);
}

// Get locations for filter dropdown
$locationQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
$locationResult = mysqli_query($conn, $locationQuery);

// Get SKUs for filter dropdown
$skuQuery = "
    SELECT DISTINCT s.sku_id, s.sku_code, s.sku_name, c.category_name
    FROM sku_master s
    JOIN categories c ON s.category_id = c.category_id
    JOIN consumable_inventory i ON s.sku_id = i.sku_id
    ORDER BY c.category_name, s.sku_name
";
$skuResult = mysqli_query($conn, $skuQuery);
?>

<div class="container-fluid">
    <!-- Page heading -->
    <div class="page-header mb-4 animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title"><i class="fas fa-history"></i> Stock Transaction Logs</h1>
                <p class="page-subtitle">View and track all inventory transactions</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex justify-content-md-end gap-2">
                    <button id="exportExcel" class="btn btn-success btn-sm">
                        <i class="fas fa-file-excel me-1"></i> Export
                    </button>
                    <button id="printTable" class="btn btn-info btn-sm">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                    <a href="/choims/modules/inventory/list.php" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="d-flex w-100 justify-content-between align-items-center">
            <div><?php echo $error; ?></div>
            <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Filter Form -->
    <div class="card animate__animated animate__fadeInUp animate__faster">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-filter"></i>
                Filter Transactions
                <?php if(!empty($filter_transaction_type) || !empty($filter_sku) || !empty($filter_location) || !empty($filter_start_date) || !empty($filter_end_date)): ?>
                    <span class="filter-badge">Active</span>
                <?php endif; ?>
            </div>
            <?php if(!empty($filter_transaction_type) || !empty($filter_sku) || !empty($filter_location) || !empty($filter_start_date) || !empty($filter_end_date)): ?>
                <a href="/choims/modules/inventory/stock_logs.php" class="btn btn-sm btn-outline-secondary btn-rounded">
                    <i class="fas fa-times me-1"></i> Clear Filters
                </a>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <div class="filter-card">
                <form method="get" action="" class="row g-3">
                    <div class="col-md-3">
                        <label for="transaction_type" class="form-label small fw-medium">Transaction Type</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-exchange-alt text-primary"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="transaction_type" name="transaction_type">
                                <option value="">All Transaction Types</option>
                                <option value="Stock In" <?php echo $filter_transaction_type === 'Stock In' ? 'selected' : ''; ?>>Stock In</option>
                                <option value="Stock Out" <?php echo $filter_transaction_type === 'Stock Out' ? 'selected' : ''; ?>>Consume</option>
                                <option value="Transfer" <?php echo $filter_transaction_type === 'Transfer' ? 'selected' : ''; ?>>Transfer</option>
                                <option value="Adjustment" <?php echo $filter_transaction_type === 'Adjustment' ? 'selected' : ''; ?>>Adjustment</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="sku" class="form-label small fw-medium">Product</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-box text-primary"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="sku" name="sku">
                                <option value="">All Products</option>
                                <?php
                                $current_category = '';
                                mysqli_data_seek($skuResult, 0);
                                while ($sku = mysqli_fetch_assoc($skuResult)):
                                    if ($current_category != $sku['category_name']) {
                                        if ($current_category != '') echo '</optgroup>';
                                        echo '<optgroup label="' . $sku['category_name'] . '">';
                                        $current_category = $sku['category_name'];
                                    }
                                ?>
                                    <option value="<?php echo $sku['sku_id']; ?>" <?php echo $filter_sku == $sku['sku_id'] ? 'selected' : ''; ?>>
                                        <?php echo $sku['sku_code'] . ' - ' . $sku['sku_name']; ?>
                                    </option>
                                <?php
                                endwhile;
                                if ($current_category != '') echo '</optgroup>';
                                ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="location" class="form-label small fw-medium">Location</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-map-marker-alt text-primary"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="location" name="location">
                                <option value="">All Locations</option>
                                <?php
                                mysqli_data_seek($locationResult, 0);
                                while ($location = mysqli_fetch_assoc($locationResult)):
                                ?>
                                    <option value="<?php echo $location['location_id']; ?>" <?php echo $filter_location == $location['location_id'] ? 'selected' : ''; ?>>
                                        <?php echo $location['location_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label class="form-label small fw-medium">Date Range</label>
                        <div class="row g-2">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-calendar-alt text-primary"></i></span>
                                    <input type="date" class="form-control rounded-end-3 border-start-0" id="start_date" name="start_date" value="<?php echo $filter_start_date; ?>" placeholder="From">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-calendar-alt text-primary"></i></span>
                                    <input type="date" class="form-control rounded-end-3 border-start-0" id="end_date" name="end_date" value="<?php echo $filter_end_date; ?>" placeholder="To">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-12 text-center mt-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-1"></i> Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Transaction Table -->
    <div class="card animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.1s; width: 100%;">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-history"></i>
                Transaction History
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive w-100" id="tableContainer">
                <div class="table-loading-state" id="tableLoadingState">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading transactions...</p>
                </div>
                <table class="table table-hover align-middle w-100" id="transactionTable" style="display: none; width: 100% !important;">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Date & Time</th>
                            <th>Product</th>
                            <th class="text-center">Transaction Type</th>
                            <th>Quantity</th>
                            <th>Source</th>
                            <th>Destination</th>
                            <th>Reference</th>
                            <th>Performed By</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($stmt && mysqli_num_rows($result) > 0): ?>
                            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <tr class="clickable-row" style="cursor: pointer;"
                                    data-transaction-id="<?php echo $row['transaction_id']; ?>"
                                    data-transaction-date="<?php echo date('Y-m-d h:i A', strtotime($row['transaction_date'])); ?>"
                                    data-product="<?php echo htmlspecialchars($row['sku_name']); ?>"
                                    data-sku-code="<?php echo htmlspecialchars($row['sku_code']); ?>"
                                    data-transaction-type="<?php echo ($row['transaction_type'] === 'Stock Out') ? 'Consume' : $row['transaction_type']; ?>"
                                    data-quantity="<?php echo $row['quantity']; ?>"
                                    data-source="<?php echo $row['transaction_type'] == 'Stock In' ?
                                        (isset($row['supplier_name']) && !empty($row['supplier_name']) ? htmlspecialchars($row['supplier_name']) : 'N/A') :
                                        (isset($row['source_location']) && !empty($row['source_location']) ? htmlspecialchars($row['source_location']) : 'N/A'); ?>"
                                    data-destination="<?php echo isset($row['destination_location']) && !empty($row['destination_location']) ? htmlspecialchars($row['destination_location']) : 'N/A'; ?>"
                                    data-reference="<?php echo isset($row['reference_document']) && !empty($row['reference_document']) ? htmlspecialchars($row['reference_document']) : 'N/A'; ?>"
                                    data-performed-by="<?php echo isset($row['performed_by']) && !empty($row['performed_by']) ? htmlspecialchars($row['performed_by']) : 'System'; ?>"
                                    data-notes="<?php echo isset($row['remarks']) && !empty($row['remarks']) ? htmlspecialchars($row['remarks']) : 'No notes available'; ?>"
                                    data-badge-class="<?php echo $badgeClass; ?>"
                                    data-badge-icon="<?php echo $badgeIcon; ?>">
                                    <td><?php echo $row['transaction_id']; ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($row['transaction_date'])); ?></td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2" style="width: 30px; height: 30px; background-color: var(--primary-bg); color: var(--primary); border-radius: 6px; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-box-open"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium"><?php echo $row['sku_name']; ?></div>
                                                <div class="small text-muted"><?php echo $row['sku_code']; ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <?php
                                        $badgeClass = '';
                                        $badgeIcon = '';
                                        switch ($row['transaction_type']) {
                                            case 'Stock In':
                                                $badgeClass = 'bg-success bg-opacity-10 text-success';
                                                $badgeIcon = 'fa-plus-circle';
                                                break;
                                            case 'Stock Out':
                                                $badgeClass = 'bg-danger bg-opacity-10 text-danger';
                                                $badgeIcon = 'fa-minus-circle';
                                                break;
                                            case 'Transfer':
                                                $badgeClass = 'bg-info bg-opacity-10 text-info';
                                                $badgeIcon = 'fa-exchange-alt';
                                                break;
                                            case 'Adjustment':
                                                $badgeClass = 'bg-warning bg-opacity-10 text-warning';
                                                $badgeIcon = 'fa-sync-alt';
                                                break;
                                            default:
                                                $badgeClass = 'bg-secondary bg-opacity-10 text-secondary';
                                                $badgeIcon = 'fa-question-circle';
                                        }
                                        ?>
                                        <span class="badge rounded-pill <?php echo $badgeClass; ?> px-3 py-2">
                                            <i class="fas <?php echo $badgeIcon; ?> me-1"></i>
                                            <?php
                                                // Display "Consume" instead of "Stock Out" for usage/consumption
                                                echo ($row['transaction_type'] === 'Stock Out') ? 'Consume' : $row['transaction_type'];
                                            ?>
                                        </span>
                                    </td>
                                    <td class="fw-medium"><?php echo $row['quantity']; ?></td>
                                    <td>
                                        <?php
                                        if ($row['transaction_type'] == 'Stock In') {
                                            echo isset($row['supplier_name']) && !empty($row['supplier_name']) ? $row['supplier_name'] : '<span class="text-muted">N/A</span>';
                                        } else {
                                            echo isset($row['source_location']) && !empty($row['source_location']) ? $row['source_location'] : '<span class="text-muted">N/A</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo isset($row['destination_location']) && !empty($row['destination_location']) ? $row['destination_location'] : '<span class="text-muted">N/A</span>'; ?></td>
                                    <td><?php echo isset($row['reference_document']) && !empty($row['reference_document']) ? $row['reference_document'] : '<span class="text-muted">N/A</span>'; ?></td>
                                    <td><?php echo isset($row['performed_by']) && !empty($row['performed_by']) ? $row['performed_by'] : '<span class="text-muted">System</span>'; ?></td>
                                    <td><?php echo isset($row['remarks']) && !empty($row['remarks']) ? $row['remarks'] : ''; ?></td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No transactions found</h5>
                                        <p class="text-muted">Try adjusting your filter criteria</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show loading animation
    document.querySelectorAll('.animate__animated').forEach(function(element, index) {
        element.style.opacity = '0';
        setTimeout(function() {
            element.style.opacity = '1';
        }, 100 * index);
    });

    // Hide the table initially and show loading state
    document.getElementById('transactionTable').style.display = 'none';
    document.getElementById('tableLoadingState').style.display = 'flex';

    // Always hide loading state after a short timeout to prevent infinite loading
    // Use a shorter timeout (1 second) to improve user experience
    setTimeout(function() {
        if(document.getElementById('tableLoadingState')) {
            document.getElementById('tableLoadingState').style.display = 'none';
        }
        if(document.getElementById('transactionTable')) {
            document.getElementById('transactionTable').style.display = 'table';
        }
    }, 1000);

    // Check if there are any rows in the table (excluding the 'no data' row)
    var hasData = $('#transactionTable tbody tr').length > 0 &&
                  $('#transactionTable tbody tr td[colspan="10"]').length === 0;

    // Initialize DataTable with modern styling
    var table = $('#transactionTable').DataTable({
        "order": [[1, "desc"]], // Sort by date descending
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        dom: 'Bfrtip',
        "drawCallback": function(settings) {
            // After table is drawn, make sure all rows have the proper styling
            $('#transactionTable tbody tr.clickable-row').css('cursor', 'pointer');
        },
        buttons: [
            {
                extend: 'copy',
                className: 'd-none'
            },
            {
                extend: 'excel',
                className: 'd-none',
                title: 'Stock Transaction Logs',
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    stripHtml: true,
                    columns: ':visible'
                },
                customize: function(xlsx) {
                    // Add styling to Excel export
                    var sheet = xlsx.xl.worksheets['sheet1.xml'];
                    $('row:first c', sheet).attr('s', '32'); // Add header style

                    // Replace "Stock Out" with "Consume" in Excel export
                    $('row c[t="Stock Out"]', sheet).each(function() {
                        $(this).attr('t', 'Consume');
                    });
                }
            },
            {
                extend: 'print',
                className: 'd-none',
                title: 'Stock Transaction Logs',
                messageTop: null,
                messageBottom: null,
                autoPrint: true,
                orientation: 'landscape',
                exportOptions: {
                    stripHtml: false,
                    columns: ':visible'
                },
                customize: function (win) {
                    // Remove any default headers/footers
                    $(win.document.body).find('h1').remove();
                    $(win.document.body).find('div.dt-print-heading').remove();

                    // Set document title to avoid about:blank
                    $(win.document).attr('title', 'Stock Transaction Logs');

                    // Add padding to the page
                    $(win.document.body).css({
                        'padding-left': '30px',
                        'padding-right': '30px',
                        'padding-top': '20px',
                        'padding-bottom': '20px'
                    });

                    // Get current user and formatted date for print header
                    var currentUser = '<?php echo isset($_SESSION["username"]) ? $_SESSION["username"] : "User"; ?>';
                    var currentDate = new Date().toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'});

                    // Add logo, title, and user info with date
                    $(win.document.body).prepend(
                        '<div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:20px;">' +
                        '<div style="display:flex; align-items:center;">' +
                        '<img src="/choims/assets/img/prqlogo2.png" style="height:80px; margin-right:20px;" />' +
                        '<h1 style="margin:0; color:#2e7d32; font-weight:bold;">Stock Transaction Logs</h1>' +
                        '</div>' +
                        '<div style="text-align:right; color:#2e7d32; font-size:14px; font-weight:500;">' +
                        '<div style="margin-bottom:5px;"><span style="display:inline-block; width:80px;">Printed by:</span>&nbsp;&nbsp;' + currentUser + '</div>' +
                        '<div><span style="display:inline-block; width:80px;">Date:</span>&nbsp;&nbsp;' + currentDate + '</div>' +
                        '</div>' +
                        '</div>'
                    );

                    // Clean up print view and remove date/about:blank
                    $(win.document.head).append(`
                        <style>
                            @page {
                                size: auto;
                                margin: 0mm;
                            }
                            body {
                                margin: 0;
                                padding: 1.6cm;
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                            }
                            table { margin-top: 15px; }
                            /* Add spacing after page breaks */
                            @media print {
                                @page :footer {
                                    display: none
                                }
                                @page :header {
                                    display: none
                                }
                                /* First page has no margin, other pages have 20px margin */
                                @page :first {
                                    margin-top: 0mm;
                                }
                                @page {
                                    margin-top: 20px;
                                }
                                tr {
                                    page-break-inside: avoid;
                                }
                                thead {
                                    display: table-header-group;
                                }
                                tfoot {
                                    display: table-footer-group;
                                }
                                /* Add margin to content after page break */
                                .pagebreak {
                                    page-break-before: always;
                                    margin-top: 20px;
                                }
                                tr.pagebreak {
                                    margin-top: 20px;
                                }
                            }
                        </style>
                    `);

                    // Keep the table styling intact
                    $(win.document.body).find('table')
                        .addClass('table table-bordered')
                        .css('font-size', '12px');

                    // Ensure transaction type badges retain their colors in print
                    $(win.document.body).find('.badge').each(function() {
                        var badgeClass = '';
                        if ($(this).hasClass('bg-success')) badgeClass = 'bg-success';
                        if ($(this).hasClass('bg-danger')) badgeClass = 'bg-danger';
                        if ($(this).hasClass('bg-primary')) badgeClass = 'bg-primary';
                        if ($(this).hasClass('bg-warning')) badgeClass = 'bg-warning';
                        if ($(this).hasClass('bg-info')) badgeClass = 'bg-info';

                        var color = '#777'; // default color
                        if (badgeClass === 'bg-success') color = '#43a047';
                        if (badgeClass === 'bg-danger') color = '#e53935';
                        if (badgeClass === 'bg-primary') color = '#2196f3';
                        if (badgeClass === 'bg-warning') color = '#ff9800';
                        if (badgeClass === 'bg-info') color = '#3b82f6';

                        $(this).css({
                            'background-color': color,
                            'color': '#fff',
                            'padding': '3px 8px',
                            'border-radius': '12px',
                            'font-size': '11px',
                            'font-weight': 'bold'
                        });

                        // Make sure "Stock Out" is displayed as "Consume" in print view
                        if ($(this).text().trim() === 'Stock Out') {
                            $(this).text('Consume');
                        }
                    });
                }
            }
        ],
        "initComplete": function(settings, json) {
            // Add custom styling to the DataTables elements
            $('.dataTables_wrapper .dataTables_filter input').addClass('form-control form-control-sm ms-2').css('width', '200px');
            $('.dataTables_wrapper .dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_info').addClass('text-muted small pt-3');

            // Hide loading state and show the table
            document.getElementById('tableLoadingState').style.display = 'none';
            document.getElementById('transactionTable').style.display = 'table';
        },
        "language": {
            "emptyTable": "No transactions found",
            "zeroRecords": "No matching transactions found - please adjust your filters"
        }
    });

    // Always ensure the loading state is hidden after initialization
    if(document.getElementById('tableLoadingState')) {
        document.getElementById('tableLoadingState').style.display = 'none';
    }
    if(document.getElementById('transactionTable')) {
        document.getElementById('transactionTable').style.display = 'table';
    }

    // If there's no data, make sure the table is visible and properly styled
    if (!hasData) {
        // Make sure the empty message is visible
        document.getElementById('transactionTable').style.display = 'table';

        // Disable export buttons for empty tables
        $('#exportExcel').prop('disabled', true).addClass('disabled');
        $('#printTable').prop('disabled', true).addClass('disabled');
    }

    // Export to Excel
    $('#exportExcel').on('click', function() {
        table.button('.buttons-excel').trigger();
    });

    // Print report
    $('#printTable').on('click', function() {
        table.button('.buttons-print').trigger();
    });

    // Add dismiss functionality to alerts
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        const closeButton = alert.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                alert.classList.add('animate__fadeOutUp');
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }
    });

    // Initialize Select2 for dropdowns
    if (typeof jQuery !== 'undefined' && jQuery.fn.select2) {
        jQuery('#sku').select2({
            placeholder: 'Select a product',
            theme: 'classic'
        });

        jQuery('#location').select2({
            placeholder: 'Select a location',
            theme: 'classic'
        });

        jQuery('#transaction_type').select2({
            placeholder: 'Select transaction type',
            theme: 'classic'
        });
    }

    // Use jQuery's delegated event handling for DataTables compatibility
    // This works with pagination, sorting, and filtering
    $('#transactionTable').on('click', 'tbody tr.clickable-row', function(e) {
        // Get the row that was clicked
        const row = this;

        // Get data from row attributes using dataset property
        const transactionId = row.dataset.transactionId;
        const transactionDate = row.dataset.transactionDate;
        const product = row.dataset.product;
        const skuCode = row.dataset.skuCode;
        const transactionType = row.dataset.transactionType;
        const quantity = row.dataset.quantity;
        const source = row.dataset.source;
        const destination = row.dataset.destination;
        const reference = row.dataset.reference;
        const performedBy = row.dataset.performedBy;
        const notes = row.dataset.notes;
        const badgeClass = row.dataset.badgeClass;
        const badgeIcon = row.dataset.badgeIcon;

        console.log("Row clicked:", transactionId); // Debug log

        // Populate modal with data
        document.getElementById('modal-transaction-id').textContent = transactionId;
        document.getElementById('modal-transaction-date').textContent = transactionDate;
        document.getElementById('modal-product').textContent = product;
        document.getElementById('modal-sku-code').textContent = skuCode;

        // Create badge for transaction type
        const badgeHtml = `<span class="badge rounded-pill ${badgeClass} px-3 py-2">
            <i class="fas ${badgeIcon} me-1"></i> ${transactionType}
        </span>`;
        document.getElementById('modal-transaction-type').innerHTML = badgeHtml;

        document.getElementById('modal-quantity').textContent = quantity;
        document.getElementById('modal-source').textContent = source;
        document.getElementById('modal-destination').textContent = destination;
        document.getElementById('modal-reference').textContent = reference;
        document.getElementById('modal-performed-by').textContent = performedBy;
        document.getElementById('modal-notes').textContent = notes;

        // Show the modal using Bootstrap's JavaScript API
        const transactionModal = new bootstrap.Modal(document.getElementById('transactionDetailsModal'));
        transactionModal.show();
    });

    // Add a class to highlight that rows are clickable
    document.querySelectorAll('#transactionTable tbody tr.clickable-row').forEach(row => {
        row.style.cursor = 'pointer';
    });
});
</script>

<!-- Transaction Details Modal -->
<div class="modal fade" id="transactionDetailsModal" tabindex="-1" aria-labelledby="transactionDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="transactionDetailsModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Transaction Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="fw-bold">Transaction ID:</label>
                            <div id="modal-transaction-id"></div>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold">Date & Time:</label>
                            <div id="modal-transaction-date"></div>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold">Transaction Type:</label>
                            <div id="modal-transaction-type"></div>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold">Quantity:</label>
                            <div id="modal-quantity"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="fw-bold">Product:</label>
                            <div id="modal-product"></div>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold">SKU Code:</label>
                            <div id="modal-sku-code"></div>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold">Source:</label>
                            <div id="modal-source"></div>
                        </div>
                        <div class="mb-3">
                            <label class="fw-bold">Destination:</label>
                            <div id="modal-destination"></div>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">Reference Document:</label>
                    <div id="modal-reference"></div>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">Performed By:</label>
                    <div id="modal-performed-by"></div>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">Notes:</label>
                    <div id="modal-notes"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>