<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
// Only GodMode, Superadmin, and Logistics can add maintenance records for office and medical equipment
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to add maintenance records for office and medical equipment.');

    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else if (hasRole('HIMU')) {
        header('Location: /choims/dashboards/himu.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Get form data
        $asset_id = sanitizeInput($_POST['asset_id']);
        $maintenance_date = sanitizeInput($_POST['maintenance_date']);
        $maintenance_type = sanitizeInput($_POST['maintenance_type']);
        $cost = !empty($_POST['cost']) ? sanitizeInput($_POST['cost']) : null;
        $technician_id = !empty($_POST['technician_id']) ? sanitizeInput($_POST['technician_id']) : null;
        $status = sanitizeInput($_POST['status']);
        $description = sanitizeInput($_POST['description']);
        $update_asset_status = isset($_POST['update_asset_status']) ? true : false;
        $created_by = sanitizeInput($_POST['created_by']);

        // Validate asset belongs to office or medical equipment category
        $assetQuery = "
            SELECT fa.asset_id, fa.asset_name, c.category_id, c.category_name
            FROM fixed_assets fa
            JOIN sku_master sm ON fa.sku_id = sm.sku_id
            JOIN categories c ON sm.category_id = c.category_id
            WHERE fa.asset_id = ? AND c.category_id IN (2, 3)
        ";
        $assetStmt = mysqli_prepare($conn, $assetQuery);
        mysqli_stmt_bind_param($assetStmt, 'i', $asset_id);
        mysqli_stmt_execute($assetStmt);
        $assetResult = mysqli_stmt_get_result($assetStmt);

        if (mysqli_num_rows($assetResult) === 0) {
            throw new Exception("Invalid asset selected or asset is not an office/medical equipment.");
        }

        $assetData = mysqli_fetch_assoc($assetResult);

        // Insert maintenance record
        $insertQuery = "
            INSERT INTO maintenance_records
            (asset_id, maintenance_date, maintenance_type, cost, technician_id, status, description, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ";

        $insertStmt = mysqli_prepare($conn, $insertQuery);
        mysqli_stmt_bind_param(
            $insertStmt,
            'issdssis',
            $asset_id,
            $maintenance_date,
            $maintenance_type,
            $cost,
            $technician_id,
            $status,
            $description,
            $created_by
        );

        $insertResult = mysqli_stmt_execute($insertStmt);

        if (!$insertResult) {
            throw new Exception("Failed to add maintenance record: " . mysqli_error($conn));
        }

        $record_id = mysqli_insert_id($conn);

        // Update asset status if checkbox is checked
        if ($update_asset_status) {
            $assetStatus = 'Available'; // Default

            if ($status === 'In Progress') {
                $assetStatus = 'Under Repair';
            } else if ($status === 'Scheduled') {
                $assetStatus = 'Under Repair';
            }

            $updateAssetQuery = "
                UPDATE fixed_assets
                SET status = ?, updated_at = NOW()
                WHERE asset_id = ?
            ";

            $updateAssetStmt = mysqli_prepare($conn, $updateAssetQuery);
            mysqli_stmt_bind_param($updateAssetStmt, 'si', $assetStatus, $asset_id);
            $updateAssetResult = mysqli_stmt_execute($updateAssetStmt);

            if (!$updateAssetResult) {
                throw new Exception("Failed to update asset status: " . mysqli_error($conn));
            }
        }

        // Prepare log data
        $log_data = [
            'asset_id' => $asset_id,
            'asset_name' => $assetData['asset_name'],
            'category' => $assetData['category_name'],
            'maintenance_date' => $maintenance_date,
            'maintenance_type' => $maintenance_type,
            'cost' => $cost,
            'technician_id' => $technician_id,
            'status' => $status,
            'description' => $description,
            'update_asset_status' => $update_asset_status
        ];

        // Log to regular audit log
        logActivity($conn, 'Create Maintenance Record', 'maintenance_records', $record_id, null, json_encode($log_data));

        // Log to detailed audit system if available
        if (function_exists('logDetailedAction')) {
            $changes_summary = "Added maintenance record for asset ID: $asset_id";
            
            logDetailedAction($conn, $_SESSION['user_id'], 'create', 'other', $record_id, [
                'entity_name' => 'Maintenance Record',
                'changes_summary' => $changes_summary,
                'new_values' => json_encode($log_data)
            ]);
        }

        // Commit transaction
        mysqli_commit($conn);

        // Set success message
        setFlashMessage('success', 'Maintenance record added successfully.');

        // Redirect to maintenance page
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);

        // Set error message
        setFlashMessage('error', $e->getMessage());

        // Redirect to maintenance page
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;
    }
}

// If not a POST request, redirect to maintenance page
header('Location: /choims/modules/logistics/maintenance.php');
exit;
?>
