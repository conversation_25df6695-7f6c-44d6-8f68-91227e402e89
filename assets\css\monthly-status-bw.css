/* Monthly Status Page - Black & White Theme with Rounded Edges and Shadow Depth */

:root {
  /* Black & White Color Palette */
  --primary: #000000;
  --primary-light: #333333;
  --primary-dark: #000000;
  --secondary: #555555;
  --text-on-primary: #ffffff;
  --text-primary: #000000;
  --text-secondary: #555555;
  --text-muted: #777777;
  --bg-primary: #ffffff;
  --bg-secondary: #f8f8f8;
  --bg-accent: #f0f0f0;
  --border-color: #e0e0e0;
  --success: #2d2d2d;
  --warning: #5a5a5a;
  --danger: #333333;
  --info: #4d4d4d;
  --light: #f8f8f8;
  --dark: #212121;
  --white: #ffffff;
  --gray-100: #f5f5f5;
  --gray-200: #eeeeee;
  --gray-300: #e0e0e0;
  --gray-400: #bdbdbd;
  --gray-500: #9e9e9e;
  --gray-600: #757575;
  --gray-700: #616161;
  --gray-800: #424242;
  --gray-900: #212121;

  /* Shadows & Effects */
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 12px 22px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 15px 30px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;

  /* Border Radius */
  --radius-sm: 10px;
  --radius: 15px;
  --radius-md: 20px;
  --radius-lg: 25px;
  --radius-full: 9999px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Base Styles */
body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: var(--space-5);
}

/* Page Title */
.page-title {
  margin-bottom: var(--space-5);
  animation: fadeIn 0.5s ease-out;
}

.page-title h1 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.page-title .lead {
  color: var(--text-secondary);
  font-weight: 400;
}

.page-title i {
  color: var(--primary);
}

/* Filter Section */
.filter-buttons {
  animation: fadeIn 0.5s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.filter-buttons .input-group {
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.filter-buttons .input-group-text,
.filter-buttons .form-select,
.filter-buttons .btn {
  border: none;
  padding: var(--space-3) var(--space-4);
}

.filter-buttons .input-group-text {
  background-color: var(--white);
  color: var(--text-primary);
}

.filter-buttons .form-select {
  background-color: var(--white);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.filter-buttons .form-select:focus {
  box-shadow: none;
  background-color: var(--gray-100);
}

.filter-buttons .btn {
  font-weight: 600;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

/* Stat Cards */
.stat-card {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  border: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: var(--white);
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.stat-card .card-body {
  padding: var(--space-5);
  position: relative;
  z-index: 1;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  line-height: 1;
}

.stat-link {
  color: var(--primary);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: var(--transition);
}

.stat-link:hover {
  color: var(--primary-light);
}

.stat-icon-wrapper {
  position: absolute;
  top: var(--space-5);
  right: var(--space-5);
  font-size: 2.5rem;
  opacity: 0.15;
  color: var(--primary);
}

/* Progress Bar */
.progress {
  height: 8px;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  overflow: hidden;
  margin-bottom: var(--space-3);
}

.progress-bar {
  background-color: var(--primary);
  border-radius: var(--radius-full);
}

/* Content Card */
.content-card {
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  background-color: var(--white);
  overflow: hidden;
  animation: scaleIn 0.5s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.content-card .card-header {
  background-color: #ffffff !important;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

.content-card .card-header h5 {
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.content-card .card-header .text-primary {
  color: var(--primary) !important;
}

.content-card .card-body {
  padding: 0;
}

/* Table Styling */
.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: #ffffff !important;
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: var(--space-3) var(--space-4);
  border-top: none;
  border-bottom: 1px solid var(--gray-200);
}

.table tbody td {
  padding: var(--space-4);
  vertical-align: middle;
  border-top: 1px solid var(--gray-200);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--gray-100);
}

/* Status Indicators */
.status-row.completed {
  background-color: var(--gray-100);
}

.status-row.pending {
  background-color: var(--white);
}

.text-success {
  color: var(--success) !important;
}

.text-warning {
  color: var(--warning) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

/* Buttons */
.btn {
  border-radius: var(--radius-sm);
  font-weight: 600;
  padding: var(--space-2) var(--space-4);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
  box-shadow: var(--shadow-sm);
}

.btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-info {
  background-color: var(--info);
  border-color: var(--info);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--gray-300);
}

.btn-outline-secondary:hover {
  background-color: var(--gray-200);
  color: var(--text-primary);
  border-color: var(--gray-300);
}

.btn-sm {
  font-size: 0.75rem;
  padding: var(--space-1) var(--space-2);
}

.btn-group {
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  margin: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-sm);
  border-bottom-left-radius: var(--radius-sm);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-sm);
  border-bottom-right-radius: var(--radius-sm);
}

/* Filter Buttons */
.filter-buttons .btn {
  background-color: var(--primary);
  color: var(--white);
  border: none;
}

.filter-buttons .btn:hover {
  background-color: var(--primary-light);
  box-shadow: var(--shadow-sm);
}

/* Ripple Effect */
.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
  width: 100px;
  height: 100px;
  transform: translate(-50%, -50%);
}

@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(4);
    opacity: 0;
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .stat-value {
    font-size: 1.75rem;
  }

  .stat-icon-wrapper {
    font-size: 2rem;
  }

  .table thead th,
  .table tbody td {
    padding: var(--space-2) var(--space-3);
  }

  .btn-group .btn {
    padding: var(--space-1) var(--space-2);
    font-size: 0.75rem;
  }

  .filter-buttons .input-group {
    flex-direction: column;
  }

  .filter-buttons .input-group > * {
    width: 100%;
    border-radius: 0;
  }

  .filter-buttons .input-group > *:first-child {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }

  .filter-buttons .input-group > *:last-child {
    border-bottom-left-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
  }
}

/* Print Styles */
@media print {
  .btn, .filter-buttons, .stat-link, .stat-icon-wrapper {
    display: none !important;
  }

  .content-card, .stat-card {
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
  }

  .table thead th {
    background-color: var(--gray-200) !important;
    color: black !important;
  }

  body {
    background-color: white !important;
  }
}
