$(document).ready(function() {
    // Check for new notifications every 30 seconds
    let lastNotificationId = 0;
    
    // Initialize with the current highest notification ID
    $('.notification-item').each(function() {
        const notificationId = parseInt($(this).data('notification-id'));
        if (notificationId > lastNotificationId) {
            lastNotificationId = notificationId;
        }
    });
    
    // Function to check for new notifications
    function checkForNewNotifications() {
        $.ajax({
            url: '/choims/includes/check_notifications.php',
            type: 'GET',
            data: { last_id: lastNotificationId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // If there are new notifications
                    if (response.new_notifications > 0) {
                        // Play sound for new notifications
                        playNotificationSound();
                        
                        // Update the notification count
                        const currentCount = parseInt($('.badge.bg-notification').text()) || 0;
                        const newCount = currentCount + response.new_notifications;
                        
                        // Update the badge
                        if ($('.badge.bg-notification').length) {
                            $('.badge.bg-notification').text(newCount);
                        } else {
                            $('.notification-bell').after('<span class="badge rounded-pill bg-notification">' + newCount + '</span>');
                        }
                        
                        // Flash the notification bell
                        $('.notification-bell').addClass('animate__animated animate__tada');
                        setTimeout(function() {
                            $('.notification-bell').removeClass('animate__animated animate__tada');
                        }, 1000);
                        
                        // Update the last notification ID
                        if (response.last_id > lastNotificationId) {
                            lastNotificationId = response.last_id;
                        }
                        
                        // Special handling for deletion notifications
                        if (response.has_deletion_notifications) {
                            // Show a more prominent alert for superadmins
                            if ($('body').hasClass('superadmin-role')) {
                                const alertHtml = `
                                    <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInDown" role="alert">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Alert!</strong> Items have been deleted by users. Please review in the audit logs.
                                        <a href="/choims/modules/reports/detailed_audit_logs.php?action_type=delete" class="alert-link ms-2">
                                            <i class="fas fa-external-link-alt me-1"></i>View Deletions
                                        </a>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                `;
                                
                                // Add the alert at the top of the content area
                                $('.content-wrapper').prepend(alertHtml);
                            }
                        }
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error checking for notifications:', error);
            }
        });
    }
    
    // Check for new notifications immediately and then every 30 seconds
    checkForNewNotifications();
    setInterval(checkForNewNotifications, 30000);
    
    // Mark notifications as read when clicked
    $(document).on('click', '.notification-item', function(e) {
        const notificationId = $(this).data('notification-id');
        
        $.ajax({
            url: '/choims/includes/mark_notification_read.php',
            type: 'POST',
            data: { notification_id: notificationId },
            dataType: 'json'
        });
        
        $(this).removeClass('unread');
    });
    
    // Mark all notifications as read
    $('#markAllReadBtn').on('click', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '/choims/includes/mark_all_notifications_read.php',
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Remove unread class from all notifications
                    $('.notification-item').removeClass('unread');
                    
                    // Update the badge count
                    $('.badge.bg-notification').remove();
                }
            }
        });
    });
});
