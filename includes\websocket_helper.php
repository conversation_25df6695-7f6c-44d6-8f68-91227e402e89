<?php
/**
 * WebSocket Helper Functions
 * Provides functions to send data to the WebSocket server
 */

/**
 * Send notification to WebSocket server
 *
 * @param array $notification Notification data
 * @param array $targetUsers Array of user IDs to send to
 * @param array $targetRoles Array of roles to send to
 * @return bool Success status
 */
function sendWebSocketNotification($notification, $targetUsers = [], $targetRoles = []) {
    $data = [
        'action' => 'notification',
        'notification' => $notification,
        'targetUsers' => $targetUsers,
        'targetRoles' => $targetRoles,
        'timestamp' => time()
    ];

    return sendToWebSocketServer($data);
}

/**
 * Send inventory update to WebSocket server
 *
 * @param array $inventoryData Inventory update data
 * @param array $targetRoles Array of roles to send to
 * @return bool Success status
 */
function sendWebSocketInventoryUpdate($inventoryData, $targetRoles = []) {
    $data = [
        'action' => 'inventory_update',
        'data' => $inventoryData,
        'targetRoles' => $targetRoles,
        'timestamp' => time()
    ];

    return sendToWebSocketServer($data);
}

/**
 * Send transfer update to WebSocket server
 *
 * @param array $transferData Transfer update data
 * @param array $targetUsers Array of user IDs to send to
 * @param array $targetRoles Array of roles to send to
 * @return bool Success status
 */
function sendWebSocketTransferUpdate($transferData, $targetUsers = [], $targetRoles = []) {
    $data = [
        'action' => 'transfer_update',
        'data' => $transferData,
        'targetUsers' => $targetUsers,
        'targetRoles' => $targetRoles,
        'timestamp' => time()
    ];

    return sendToWebSocketServer($data);
}

/**
 * Send data to WebSocket server
 *
 * @param array $data Data to send
 * @return bool Success status
 */
function sendToWebSocketServer($data) {
    // Check if socket extension is available
    if (!function_exists('socket_create')) {
        logError('WebSocket', 'PHP socket extension is not enabled. Real-time updates will not work.');
        return false;
    }

    // WebSocket server address
    $host = 'localhost';
    $port = 8081; // Control port, different from WebSocket port

    try {
        // Create socket
        $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        if ($socket === false) {
            logError('WebSocket', 'Failed to create socket: ' . socket_strerror(socket_last_error()));
            return false;
        }

        // Connect to server
        $result = socket_connect($socket, $host, $port);
        if ($result === false) {
            logError('WebSocket', 'Failed to connect to server: ' . socket_strerror(socket_last_error($socket)));
            socket_close($socket);
            return false;
        }

        // Send data
        $jsonData = json_encode($data);
        socket_write($socket, $jsonData, strlen($jsonData));

        // Close socket
        socket_close($socket);

        return true;
    } catch (Exception $e) {
        logError('WebSocket', 'Exception: ' . $e->getMessage());
        return false;
    }
}

/**
 * Log error to system log
 *
 * @param string $source Error source
 * @param string $message Error message
 */
function logError($source, $message) {
    // Log error to file
    $logFile = __DIR__ . '/../logs/websocket_errors.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$source}] {$message}" . PHP_EOL;

    // Create directory if it doesn't exist
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }

    // Append to log file
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}
