<?php
// Start output buffering to prevent 'headers already sent' errors
ob_start();

// Use relative paths
$base_path = dirname(dirname(__FILE__));
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');

// Get unread notifications count if user is logged in
$notificationCount = 0;
$recentNotifications = [];
if (isLoggedIn()) {
    $notificationCount = getUnreadNotificationsCount($conn, $_SESSION['user_id']);

    // Get recent notifications (limit to 5)
    $userId = $_SESSION['user_id'];
    $notificationsQuery = "
        SELECT
            notification_id, notification_type, title, message,
            related_entity, related_id, is_read, created_at
        FROM
            notifications
        WHERE
            user_id = ?
        ORDER BY
            created_at DESC
        LIMIT 5
    ";
    $stmt = mysqli_prepare($conn, $notificationsQuery);
    mysqli_stmt_bind_param($stmt, 'i', $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    while ($row = mysqli_fetch_assoc($result)) {
        // Determine icon based on type
        $icon = 'fa-bell';
        switch ($row['notification_type']) {
            case 'transfer_approval':
                $icon = 'fa-exchange-alt';
                break;
            case 'transfer_awaiting_approval':
                $icon = 'fa-clipboard-check';
                break;
            case 'transfer_approved':
                $icon = 'fa-check-circle';
                break;
            case 'transfer_ready':
                $icon = 'fa-dolly';
                break;
            case 'transfer_completed':
                $icon = 'fa-check-double';
                break;
            case 'low_stock':
                $icon = 'fa-exclamation-triangle';
                break;
            case 'system':
                $icon = 'fa-cog';
                break;
            case 'maintenance':
                $icon = 'fa-tools';
                break;
            case 'asset_deleted':
                $icon = 'fa-trash-alt text-danger';
                break;
            case 'inventory_deleted':
                $icon = 'fa-trash-alt text-danger';
                break;
            case 'asset_defective':
                $icon = 'fa-exclamation-triangle text-danger';
                break;
        }

        // Determine link based on related entity and ID
        $link = '#';
        switch ($row['related_entity']) {
            case 'transfer':
                // Check if this is a batch transfer by querying the database
                $checkBatchQuery = "SELECT COUNT(*) as count FROM batch_transfers WHERE batch_id = ?";
                $checkBatchStmt = mysqli_prepare($conn, $checkBatchQuery);
                mysqli_stmt_bind_param($checkBatchStmt, 'i', $row['related_id']);
                mysqli_stmt_execute($checkBatchStmt);
                $checkBatchResult = mysqli_stmt_get_result($checkBatchStmt);
                $isBatch = false;

                if ($checkBatchRow = mysqli_fetch_assoc($checkBatchResult)) {
                    $isBatch = ($checkBatchRow['count'] > 0);
                }

                if ($isBatch) {
                    $link = "/choims/modules/transfers/batch/view.php?id={$row['related_id']}";
                } else {
                    $link = "/choims/modules/transfers/view.php?id={$row['related_id']}";
                }
                break;
            case 'batch_transfer':
                $link = "/choims/modules/transfers/batch/view.php?id={$row['related_id']}";
                break;
            case 'asset':
                $link = "/choims/modules/assets/view.php?id={$row['related_id']}";
                break;
            case 'inventory':
                $link = "/choims/modules/inventory/view.php?id={$row['related_id']}";
                break;
            case 'maintenance':
                $link = "/choims/modules/assets/maintenance.php?id={$row['related_id']}";
                break;
        }

        $recentNotifications[] = [
            'id' => $row['notification_id'],
            'title' => $row['title'],
            'message' => $row['message'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at'],
            'icon' => $icon,
            'link' => $link
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCHIMS</title>

    <!-- Google Fonts - Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">

    <!-- Animate.css for animations -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/choims/assets/css/style.css">
    <!-- Sidebar Override CSS - Must be loaded after other CSS -->
    <link rel="stylesheet" href="/choims/assets/css/sidebar-override.css">
    <!-- Hide Footer CSS - Must be loaded after other CSS -->
    <link rel="stylesheet" href="/choims/assets/css/hide-footer.css">
    <!-- Mobile Enhancements CSS - Must be loaded after other CSS -->
    <link rel="stylesheet" href="/choims/assets/css/mobile-enhancements.css">
    <!-- Mobile Header CSS - For mobile toolbar and panels -->
    <link rel="stylesheet" href="/choims/assets/css/mobile-header.css">
    <!-- Mobile Sidebar Fix CSS - Must be loaded last -->
    <link rel="stylesheet" href="/choims/assets/css/mobile-sidebar-fix.css">

    <!-- jQuery (load before any scripts) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Immediate Sidebar Toggle Script - loads before other scripts -->
    <script src="/choims/assets/js/sidebar-toggle.js"></script>

    <!-- Notification fix script -->
    <script src="/choims/assets/js/notification-fix.js"></script>

    <!-- Global Search Script -->
    <script src="/choims/assets/js/global-search.js"></script>

    <!-- Mobile Enhancements Scripts -->
    <script src="/choims/assets/js/mobile-enhancements.js"></script>
    <script src="/choims/assets/js/mobile-forms.js"></script>
    <script src="/choims/assets/js/mobile-header.js"></script>

    <!-- Defective Asset Notification Script (for HIMU role) -->
    <?php if (isLoggedIn() && strtolower($_SESSION['role']) === 'himu'): ?>
    <script src="/choims/assets/js/defective-notification.js"></script>
    <?php endif; ?>

    <!-- Defective Asset Notification Script (for Logistics role) -->
    <?php if (isLoggedIn() && strtolower($_SESSION['role']) === 'logistics'): ?>
    <script src="/choims/assets/js/logistics-defective-notification.js"></script>
    <?php endif; ?>

    <!-- Notification Sounds -->
    <audio id="notificationSound" preload="auto">
        <source src="/choims/assets/sounds/notification.mp3" type="audio/mpeg">
    </audio>

    <!-- Defective Asset Notification Sound (for HIMU and Logistics roles) -->
    <?php if (isLoggedIn() && (strtolower($_SESSION['role']) === 'himu' || strtolower($_SESSION['role']) === 'logistics')): ?>
    <audio id="defectiveNotificationSound" preload="auto">
        <source src="/choims/assets/sounds/defective-alert.mp3" type="audio/mpeg">
    </audio>
    <?php endif; ?>

    <?php if (isLoggedIn()): ?>
    <!-- User data for authentication -->
    <script>
        const userId = '<?php echo $_SESSION["user_id"]; ?>';
        const userRole = '<?php echo $_SESSION["role"]; ?>';
    </script>
    <?php endif; ?>

    <script>
    // Function to play notification sound
    function playNotificationSound() {
        const sound = document.getElementById('notificationSound');
        if (sound) {
            sound.currentTime = 0;
            sound.play().catch(e => console.log('Error playing sound:', e));
        }
    }
    </script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>

    <style>
        :root {
            --primary-green: #2E7D32;
            --light-green: #4CAF50;
            --lighter-green: #8BC34A;
            --dark-green: #1B5E20;
            --accent-green: #00C853;
            --green-hover: #388E3C;
            --green-gradient: linear-gradient(135deg, var(--primary-green), var(--light-green));
            --green-text: #1B5E20;
            --green-border: #81C784;
            --green-bg-light: #E8F5E9;
        }

        /* Top navigation bar modern styles */
        @keyframes pulse-light {
            0% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4); }
            70% { box-shadow: 0 0 0 6px rgba(255, 255, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 255, 255, 0); }
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .brand-logo-container {
            background: var(--green-bg-light);
            border-radius: 8px;
            width: 38px;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .brand-icon {
            font-size: 1.3rem;
            color: var(--primary-green);
            transition: all 0.3s ease;
        }

        .navbar-brand:hover .brand-logo-container {
            background: rgba(76, 175, 80, 0.15);
            transform: scale(1.05);
        }

        .navbar-brand:hover .brand-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .brand-text {
            font-weight: 700;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            font-size: 1.5rem;
            color: var(--primary-green);
        }

        .navbar.navbar-dark.bg-green {
            background: white;
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
            z-index: 100;
            padding: 0.6rem 1rem;
            position: sticky;
            top: 0;
            border-bottom: 1px solid var(--green-bg-light);
        }

        .navbar .nav-link {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 30px;
            transition: all 0.3s;
            position: relative;
            margin: 0 0.25rem;
        }

        .navbar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .custom-toggler {
            border: none;
            padding: 0.4rem;
            border-radius: 8px;
            transition: all 0.3s;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .custom-toggler:focus {
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25);
        }

        .navbar .dropdown-menu {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            z-index: 1050;
            position: absolute;
        }

        .notification-dropdown {
            width: 400px;
            max-height: 450px;
            overflow-y: auto;
            padding: 0;
            border-radius: 16px;
            margin-top: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            animation: fadeInDown 0.3s ease;
            z-index: 1050;
            position: absolute;
            background: white;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .notification-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
            transition: all 0.25s ease;
            position: relative;
            overflow: hidden;
        }

        .notification-item:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background: var(--light-green);
            transition: width 0.3s ease;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item:hover {
            background-color: var(--green-bg-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .notification-item:hover:after {
            width: 100%;
        }

        .notification-item.unread {
            background-color: rgba(139, 195, 74, 0.1);
            border-left: 4px solid var(--light-green);
        }

        .notification-icon {
            background: linear-gradient(135deg, var(--light-green), var(--dark-green));
            color: white;
            padding: 0.5rem;
            border-radius: 14px;
            margin-right: 1rem;
            width: 46px;
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .notification-icon::after {
            content: '';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
        }

        .notification-icon::before {
            content: '';
            position: absolute;
            bottom: -5px;
            left: -5px;
            width: 15px;
            height: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .notification-item:hover .notification-icon {
            transform: scale(1.08) rotate(5deg);
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 0.3rem;
            color: var(--dark-green);
            font-size: 0.95rem;
            letter-spacing: 0.01em;
        }

        .notification-message {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 0.4rem;
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
            white-space: normal;
            hyphens: auto;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #858796;
            display: inline-block;
            background: rgba(0, 0, 0, 0.03);
            padding: 0.2rem 0.5rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .dropdown-header {
            background: linear-gradient(135deg, var(--primary-green), var(--light-green));
            color: white;
            font-weight: 600;
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .dropdown-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            right: -20px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .dropdown-header::before {
            content: '';
            position: absolute;
            top: -15px;
            left: -15px;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
        }

        .mark-all-read {
            font-size: 0.8rem;
            padding: 0.3rem 0.8rem;
            border-radius: 30px;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        .mark-all-read:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item:active {
            background-color: var(--light-green);
        }

        .badge.bg-notification {
            background-color: #f44336;
            color: white;
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 0.65rem;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 18px;
            height: 18px;
            padding: 0 0.35rem;
            border-radius: 6px;
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            animation: pulse-light 2s infinite;
            transform: translate(50%, -50%);
            font-weight: 600;
        }

        /* Page layout */
        body {
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .page-container {
            display: flex;
            flex: 1;
            margin-top: 56px; /* Add margin to account for fixed navbar */
        }

        .sidebar {
            position: fixed;
            top: 56px; /* Height of the navbar */
            left: 0;
            width: 250px; /* Set a fixed width for the sidebar */
            height: calc(100vh - 56px); /* Viewport height minus navbar height */
            padding-top: 0;
            overflow-y: auto;
            z-index: 1060;
        }

        .main-content {
            flex: 1;
            padding: 1.5rem;
            overflow-x: hidden;
            margin-left: 250px; /* Match the sidebar width */
        }

        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 56px;
                width: 100%;
                height: calc(100vh - 56px);
                z-index: 1060;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0; /* Reset margin for mobile */
                width: 100%;
            }
        }

        .notification-item .d-flex {
            width: 100%;
        }

        .notification-item .d-flex > div:last-child {
            flex: 1;
            min-width: 0;
        }

        /* User avatar and dropdown styles */
        .user-avatar-mini {
            width: 30px;
            height: 30px;
            border-radius: 10px;
            background: linear-gradient(135deg, var(--primary-green), var(--light-green));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.85rem;
            font-weight: 600;
            margin-right: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .user-dropdown-toggle {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 12px;
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 40px;
        }

        .user-dropdown-toggle:hover {
            background: var(--green-bg-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .user-dropdown-toggle:hover .user-avatar-mini {
            transform: scale(1.1);
        }

        .user-name-display {
            font-weight: 500;
            font-size: 0.95rem;
            margin-left: 4px;
            max-width: 140px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-dropdown {
            width: 280px;
            padding: 0;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            animation: fadeInDown 0.3s ease;
            z-index: 1050;
            position: absolute;
            background: white;
            margin-top: 10px;
        }

        .dropdown-user-info {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
            color: white;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .dropdown-user-info::after {
            content: '';
            position: absolute;
            bottom: -30px;
            right: -30px;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            z-index: 0;
        }

        .dropdown-user-info::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            z-index: 0;
        }

        .dropdown-user-avatar {
            width: 52px;
            height: 52px;
            border-radius: 14px;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 600;
            margin-right: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .dropdown-user-details {
            flex: 1;
            position: relative;
            z-index: 1;
        }

        .dropdown-user-name {
            margin-bottom: 0.3rem;
            font-weight: 700;
            font-size: 1.1rem;
            letter-spacing: 0.01em;
            color: white;
        }

        .dropdown-user-role {
            margin-bottom: 0;
            font-size: 0.85rem;
            opacity: 0.9;
            background: rgba(255, 255, 255, 0.2);
            display: inline-block;
            padding: 0.2rem 0.6rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .user-dropdown .dropdown-item {
            padding: 1rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: var(--green-bg-light);
            border-left: 3px solid var(--primary-green);
            padding-left: 1.75rem;
        }

        .user-dropdown .dropdown-item i {
            color: var(--primary-green);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--green-bg-light);
            border-radius: 8px;
            margin-right: 12px;
            font-size: 0.9rem;
        }

        /* Notification bell animation */
        .notification-bell {
            transition: all 0.3s ease;
        }

        @keyframes bellRing {
            0% { transform: rotate(0); }
            10% { transform: rotate(15deg); }
            20% { transform: rotate(-15deg); }
            30% { transform: rotate(10deg); }
            40% { transform: rotate(-10deg); }
            50% { transform: rotate(5deg); }
            60% { transform: rotate(-5deg); }
            70% { transform: rotate(0); }
            100% { transform: rotate(0); }
        }

        .nav-link.dropdown-toggle {
            background: white;
            border-radius: 12px;
            padding: 0.5rem 1rem;
            margin-right: 0.5rem;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .nav-link.dropdown-toggle:hover {
            background: var(--green-bg-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .notification-bell {
            font-size: 1rem;
            color: var(--primary-green);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .nav-item:hover .notification-bell {
            animation: bellRing 1s ease;
        }

        /* Date Time Display Styles */
        .datetime-display {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background: white;
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            height: 40px;
            color: var(--primary-green);
            font-weight: 500;
            font-size: 0.95rem;
        }

        .datetime-display:hover {
            background: var(--green-bg-light);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        /* Mobile Date Time Display Styles */
        .mobile-datetime-display {
            font-size: 0.8rem;
            color: var(--primary-green);
            font-weight: 500;
            background-color: rgba(46, 125, 50, 0.05);
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Ensure all dropdown items have nice transition effects */
        .dropdown-item {
            transition: all 0.2s ease;
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        .dropdown-item:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background-color: var(--light-green);
            z-index: -1;
            transform: translateX(-100%);
            transition: all 0.3s ease;
        }

        .dropdown-item:hover:before {
            transform: translateX(0);
        }

        /* Global Search Styles */
        .global-search-container {
            position: relative;
            margin-right: auto;
            margin-left: 1rem;
            width: 350px;
            max-width: 100%;
        }

        .global-search-box {
            position: relative;
            width: 100%;
        }

        .global-search-box i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-green);
            z-index: 10;
            font-size: 0.9rem;
        }

        .global-search-box input {
            width: 100%;
            padding: 0.65rem 1rem 0.65rem 2.5rem;
            border-radius: 30px;
            border: 1px solid rgba(0, 0, 0, 0.08);
            background-color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .global-search-box input:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
            padding-left: 2.75rem;
        }

        .global-search-box input:focus + i {
            color: var(--primary-green);
        }

        .global-search-results {
            position: absolute;
            top: calc(100% + 10px);
            left: 0;
            width: 100%;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 1050;
            max-height: 400px;
            overflow-y: auto;
            display: none;
            border: 1px solid rgba(0, 0, 0, 0.05);
            animation: fadeInDown 0.3s ease;
        }

        .global-search-results.show {
            display: block;
        }

        .search-results-header {
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-green), var(--light-green));
            color: white;
            font-weight: 600;
            border-radius: 16px 16px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .search-results-header::after {
            content: '';
            position: absolute;
            bottom: -20px;
            right: -20px;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .search-results-header::before {
            content: '';
            position: absolute;
            top: -15px;
            left: -15px;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
        }

        .search-results-section {
            padding: 0.5rem 0;
        }

        .search-results-section-title {
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            font-size: 0.85rem;
            color: var(--gray-600);
            background-color: rgba(0, 0, 0, 0.02);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .search-result-item {
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-item:hover {
            background-color: var(--green-bg-light);
        }

        .search-result-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            background: linear-gradient(135deg, var(--primary-green), var(--light-green));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .search-result-content {
            flex: 1;
        }

        .search-result-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
            font-size: 0.95rem;
        }

        .search-result-subtitle {
            font-size: 0.8rem;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-result-badge {
            font-size: 0.7rem;
            padding: 0.15rem 0.5rem;
            border-radius: 20px;
            font-weight: 500;
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--gray-700);
        }

        .search-result-badge.badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .search-result-badge.badge-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .search-result-badge.badge-danger {
            background-color: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .search-result-badge.badge-info {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .search-no-results {
            padding: 2rem 1.5rem;
            text-align: center;
            color: var(--gray-500);
        }

        .search-no-results i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--gray-400);
        }

        .search-view-all {
            padding: 0.75rem 1.5rem;
            text-align: center;
            background-color: var(--green-bg-light);
            color: var(--primary-green);
            font-weight: 500;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: 0 0 16px 16px;
            transition: all 0.2s ease;
        }

        .search-view-all:hover {
            background-color: rgba(76, 175, 80, 0.15);
        }

        @media (max-width: 991.98px) {
            .global-search-container {
                width: 100%;
                margin: 0.5rem 0 1rem 0;
            }
        }

        /* Mobile sidebar toggle button */
        #sidebarMobileToggle {
            color: var(--primary-green);
            font-size: 1.2rem;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        #sidebarMobileToggle:hover {
            background-color: rgba(66, 158, 70, 0.1);
            transform: scale(1.05);
        }

        #sidebarMobileToggle:active {
            transform: scale(0.95);
        }
    </style>
</head>
<body class="<?php echo strtolower($_SESSION['role']); ?>-role">
    <!-- Top Navbar with modern design -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white">
        <div class="container-fluid">
            <button type="button" class="btn btn-link d-lg-none me-2" id="sidebarMobileToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a class="navbar-brand" href="/choims/index.php">
                <span class="brand-text"></span>
            </a>

            <!-- Mobile toolbar with search, notifications and profile -->
            <?php if (isLoggedIn()): ?>
            <div class="mobile-header-toolbar d-flex d-lg-none align-items-center">
                <!-- Mobile Date Time Display -->
                <div class="mobile-datetime-display me-2" id="mobileDatetimeDisplay">
                    <span id="mobileCurrentDateTime"></span>
                </div>

                <!-- Mobile Search Button -->
                <button type="button" class="btn btn-link mobile-search-toggle" id="mobileSearchToggle">
                    <i class="fas fa-search"></i>
                </button>

                <!-- Mobile Notifications Button -->
                <div class="mobile-notification-btn position-relative">
                    <button type="button" class="btn btn-link" id="mobileNotificationToggle">
                        <i class="fas fa-bell"></i>
                        <?php if ($notificationCount > 0): ?>
                            <span class="badge rounded-pill bg-notification mobile-notification-badge"><?php echo $notificationCount; ?></span>
                        <?php endif; ?>
                    </button>
                </div>

                <!-- Mobile Profile Button -->
                <button type="button" class="btn btn-link mobile-profile-toggle" id="mobileProfileToggle">
                    <div class="user-avatar-mini mobile-avatar">
                        <?php
                          $initials = '';
                          if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                              $name_parts = explode(' ', $_SESSION['full_name']);
                              foreach ($name_parts as $part) {
                                  if (!empty($part)) $initials .= $part[0];
                              }
                              $initials = substr($initials, 0, 2);
                          } else if (isset($_SESSION['username'])) {
                              $initials = substr($_SESSION['username'], 0, 2);
                          }
                          echo strtoupper($initials);
                        ?>
                    </div>
                </button>
            </div>
            <?php endif; ?>

            <div class="collapse navbar-collapse" id="navbarNav">
                <?php if (isLoggedIn()): ?>
                <!-- Global Search Box -->
                <div class="global-search-container">
                    <div class="global-search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="globalSearch" placeholder="Search assets, users, suppliers, maintenance, categories..." autocomplete="off">
                    </div>
                    <div class="global-search-results" id="globalSearchResults">
                        <!-- Search results will appear here -->
                    </div>
                </div>
                <?php endif; ?>
                <ul class="navbar-nav ms-auto">
                    <?php if (isLoggedIn()): ?>
                    <!-- Date Time Display -->
                    <li class="nav-item me-3">
                        <div class="datetime-display" id="datetimeDisplay">
                            <i class="fas fa-clock me-1"></i>
                            <span id="currentDateTime"></span>
                        </div>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell notification-bell"></i>
                            <?php if ($notificationCount > 0): ?>
                                <span class="badge rounded-pill bg-notification"><?php echo $notificationCount; ?></span>
                            <?php endif; ?>
                        </a>
                        <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const notificationBell = document.getElementById('notificationsDropdown');
                            if (notificationBell) {
                                notificationBell.addEventListener('click', function() {
                                    // Only mark as read if there are unread notifications
                                    const badge = document.querySelector('.badge.bg-notification');
                                    if (badge) {
                                        // Mark all notifications as read via AJAX
                                        fetch('/choims/modules/notifications/mark_read.php', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/x-www-form-urlencoded',
                                            },
                                            body: 'mark_all=1'
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.success) {
                                                // Remove all unread styling
                                                const unreadItems = document.querySelectorAll('.notification-item.unread');
                                                unreadItems.forEach(item => {
                                                    item.classList.remove('unread');
                                                });

                                                // Remove notification badge
                                                badge.remove();

                                                // Play notification sound
                                                playNotificationSound();
                                            }
                                        })
                                        .catch(error => {
                                            console.error('Error marking notifications as read:', error);
                                        });
                                    }
                                });
                            }
                        });
                        </script>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-bell me-2"></i> Notifications</span>
                                <?php if (count($recentNotifications) > 0 && $notificationCount > 0): ?>
                                <a href="#" class="mark-all-read text-success small ms-2" id="markAllReadBtn">
                                    <i class="fas fa-check-double me-1"></i>Mark All Read
                                </a>
                                <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    const markAllReadBtn = document.getElementById('markAllReadBtn');
                                    if (markAllReadBtn) {
                                        markAllReadBtn.addEventListener('click', function(e) {
                                            e.preventDefault();
                                            // Mark all notifications as read via AJAX
                                            fetch('/choims/modules/notifications/mark_read.php', {
                                                method: 'POST',
                                                headers: {
                                                    'Content-Type': 'application/x-www-form-urlencoded',
                                                },
                                                body: 'mark_all=1'
                                            })
                                            .then(response => response.json())
                                            .then(data => {
                                                if (data.success) {
                                                    // Remove all unread styling
                                                    const unreadItems = document.querySelectorAll('.notification-item.unread');
                                                    unreadItems.forEach(item => {
                                                        item.classList.remove('unread');
                                                    });

                                                    // Remove notification badge
                                                    const badge = document.querySelector('.badge.bg-notification');
                                                    if (badge) {
                                                        badge.remove();
                                                    }

                                                    // Play notification sound
                                                    playNotificationSound();
                                                }
                                            })
                                            .catch(error => {
                                                console.error('Error marking notifications as read:', error);
                                            });
                                        });
                                    }
                                });
                                </script>
                                <?php endif; ?>
                            </h6></li>

                            <?php if (count($recentNotifications) > 0): ?>
                                <?php foreach ($recentNotifications as $notification): ?>
                                    <li>
                                        <a class="dropdown-item notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>"
                                           href="<?php echo $notification['link']; ?>"
                                           data-notification-id="<?php echo $notification['id']; ?>">
                                            <div class="d-flex align-items-start">
                                                <div class="notification-icon">
                                                    <i class="fas <?php echo $notification['icon']; ?> fa-fw"></i>
                                                </div>
                                                <div>
                                                    <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                                    <div class="notification-message"><?php
                                                        // Only show the first line of the message
                                                        $messageLines = explode("\n", $notification['message']);
                                                        echo htmlspecialchars($messageLines[0]);
                                                        if (count($messageLines) > 1) {
                                                            echo ' <span class="text-muted">[more details]</span>';
                                                        }
                                                    ?></div>
                                                    <div class="notification-time"><?php
                                                        // Format the timestamp nicely
                                                        $timestamp = strtotime($notification['created_at']);
                                                        $now = time();
                                                        $diff = $now - $timestamp;

                                                        if (!$timestamp) {
                                                            echo $notification['created_at'];
                                                        } elseif ($diff < 0) {
                                                            // Future date - show actual time
                                                            echo date('M j, Y g:i A', $timestamp);
                                                        } elseif ($diff < 60) {
                                                            echo "Just now";
                                                        } elseif ($diff < 3600) {
                                                            $minutes = floor($diff / 60);
                                                            echo $minutes . " minute" . ($minutes > 1 ? "s" : "") . " ago";
                                                        } elseif ($diff < 86400) {
                                                            $hours = floor($diff / 3600);
                                                            echo $hours . " hour" . ($hours > 1 ? "s" : "") . " ago";
                                                        } elseif ($diff < 604800) { // 7 days
                                                            $days = floor($diff / 86400);
                                                            echo $days . " day" . ($days > 1 ? "s" : "") . " ago";
                                                        } else {
                                                            echo date('M j, Y g:i A', $timestamp);
                                                        }
                                                    ?></div>
                                                </div>
                                            </div>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <li><span class="dropdown-item">No notifications</span></li>
                            <?php endif; ?>

                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="/choims/modules/notifications/list.php">View All Notifications</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown ms-2">
                        <a class="nav-link dropdown-toggle user-dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar-mini">
                                <?php
                                  $initials = '';
                                  if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                                      $name_parts = explode(' ', $_SESSION['full_name']);
                                      foreach ($name_parts as $part) {
                                          if (!empty($part)) $initials .= $part[0];
                                      }
                                      $initials = substr($initials, 0, 2);
                                  } else if (isset($_SESSION['username'])) {
                                      $initials = substr($_SESSION['username'], 0, 2);
                                  }
                                  echo strtoupper($initials);
                                ?>
                            </div>
                            <span class="user-name-display"><?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : $_SESSION['username']; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end user-dropdown">
                            <li class="dropdown-user-info">
                                <div class="dropdown-user-avatar">
                                    <?php echo strtoupper($initials); ?>
                                </div>
                                <div class="dropdown-user-details">
                                    <h6 class="dropdown-user-name"><?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : $_SESSION['username']; ?></h6>
                                    <p class="dropdown-user-role"><?php echo $_SESSION['role']; ?></p>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/choims/modules/users/profile.php"><i class="fas fa-id-card me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="/choims/modules/auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/choims/modules/auth/login.php"><i class="fas fa-sign-in-alt me-2"></i> Login</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Mobile-specific panels -->
    <?php if (isLoggedIn()): ?>
    <!-- Mobile Search Panel -->
    <div class="mobile-search-panel" id="mobileSearchPanel">
        <div class="mobile-panel-header">
            <h5><i class="fas fa-search me-2"></i> Search</h5>
            <button type="button" class="btn-close" id="closeSearchPanel"></button>
        </div>
        <div class="mobile-panel-body">
            <div class="mobile-search-container">
                <div class="mobile-search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="mobileGlobalSearch" placeholder="Search for assets, consumables, transaction codes..." autocomplete="off">
                </div>
                <div class="mobile-search-results" id="mobileGlobalSearchResults">
                    <!-- Search results will appear here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Notifications Panel -->
    <div class="mobile-notifications-panel" id="mobileNotificationsPanel">
        <div class="mobile-panel-header">
            <h5><i class="fas fa-bell me-2"></i> Notifications</h5>
            <button type="button" class="btn-close" id="closeNotificationsPanel"></button>
        </div>
        <div class="mobile-panel-body">
            <div class="mobile-notifications-container">
                <?php if (count($recentNotifications) > 0): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-muted small">Recent Notifications</span>
                        <?php if ($notificationCount > 0): ?>
                        <a href="#" class="mark-all-read text-success small" id="mobileMmarkAllReadBtn">
                            <i class="fas fa-check-double me-1"></i>Mark All Read
                        </a>
                        <?php endif; ?>
                    </div>

                    <div class="mobile-notifications-list">
                        <?php foreach ($recentNotifications as $notification): ?>
                            <a class="mobile-notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>"
                               href="<?php echo $notification['link']; ?>"
                               data-notification-id="<?php echo $notification['id']; ?>">
                                <div class="d-flex align-items-start">
                                    <div class="notification-icon">
                                        <i class="fas <?php echo $notification['icon']; ?> fa-fw"></i>
                                    </div>
                                    <div>
                                        <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                        <div class="notification-message"><?php
                                            // Only show the first line of the message
                                            $messageLines = explode("\n", $notification['message']);
                                            echo htmlspecialchars($messageLines[0]);
                                            if (count($messageLines) > 1) {
                                                echo ' <span class="text-muted">[more details]</span>';
                                            }
                                        ?></div>
                                        <div class="notification-time"><?php
                                            // Format the timestamp nicely
                                            $timestamp = strtotime($notification['created_at']);
                                            $now = time();
                                            $diff = $now - $timestamp;

                                            if (!$timestamp) {
                                                echo $notification['created_at'];
                                            } elseif ($diff < 0) {
                                                // Future date - show actual time
                                                echo date('M j, Y g:i A', $timestamp);
                                            } elseif ($diff < 60) {
                                                echo "Just now";
                                            } elseif ($diff < 3600) {
                                                $minutes = floor($diff / 60);
                                                echo $minutes . " minute" . ($minutes > 1 ? "s" : "") . " ago";
                                            } elseif ($diff < 86400) {
                                                $hours = floor($diff / 3600);
                                                echo $hours . " hour" . ($hours > 1 ? "s" : "") . " ago";
                                            } elseif ($diff < 604800) { // 7 days
                                                $days = floor($diff / 86400);
                                                echo $days . " day" . ($days > 1 ? "s" : "") . " ago";
                                            } else {
                                                echo date('M j, Y g:i A', $timestamp);
                                            }
                                        ?></div>
                                    </div>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-3">
                        <a href="/choims/modules/notifications/list.php" class="btn btn-outline-success btn-sm">View All Notifications</a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <p>No notifications</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Mobile Profile Panel -->
    <div class="mobile-profile-panel" id="mobileProfilePanel">
        <div class="mobile-panel-header">
            <h5><i class="fas fa-user me-2"></i> Profile</h5>
            <button type="button" class="btn-close" id="closeProfilePanel"></button>
        </div>
        <div class="mobile-panel-body">
            <div class="mobile-profile-container">
                <div class="mobile-profile-header">
                    <div class="mobile-profile-avatar">
                        <?php echo strtoupper($initials); ?>
                    </div>
                    <div class="mobile-profile-info">
                        <h5 class="mobile-profile-name"><?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : $_SESSION['username']; ?></h5>
                        <p class="mobile-profile-role"><?php echo $_SESSION['role']; ?></p>
                    </div>
                </div>

                <div class="mobile-profile-menu">
                    <a href="/choims/modules/users/profile.php" class="mobile-profile-menu-item">
                        <i class="fas fa-id-card me-2"></i> View Profile
                    </a>
                    <a href="/choims/modules/auth/logout.php" class="mobile-profile-menu-item">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Panel Overlay -->
    <div class="mobile-panel-overlay" id="mobilePanelOverlay"></div>
    <?php endif; ?>

    <div class="page-container">
        <?php if (isLoggedIn()): ?>
        <div class="sidebar">
            <?php include($base_path . '/includes/sidebar.php'); ?>
        </div>
        <div class="main-content">
        <?php else: ?>
        <div class="main-content w-100">
        <?php endif; ?>