<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get consumable ID from URL
$consumableId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$consumableId) {
    setFlashMessage('error', 'Invalid consumable ID');
    header("Location: index.php");
    exit;
}

// Verify the consumable exists and get its details
try {
    $stmt = $db->prepare("
        SELECT c.*, i.name as item_name
        FROM consumables c
        JOIN items i ON c.item_id = i.item_id
        WHERE c.consumable_id = ?
    ");
    $stmt->execute([$consumableId]);
    $consumable = $stmt->fetch();

    if (!$consumable) {
        setFlashMessage('error', 'Consumable not found');
        header("Location: index.php");
        exit;
    }

    // Check if the consumable has any transactions
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM transactions WHERE item_id = ?");
    $stmt->execute([$consumable['item_id']]);
    $transactionCount = $stmt->fetch()['count'];

    if ($transactionCount > 0) {
        setFlashMessage('error', 'Cannot delete consumable. It has transaction history records.');
        header("Location: view.php?id=" . $consumableId);
        exit;
    }

    // Begin transaction
    $db->beginTransaction();

    // Delete the consumable record
    $stmt = $db->prepare("DELETE FROM consumables WHERE consumable_id = ?");
    $stmt->execute([$consumableId]);

    // Delete the item record
    $stmt = $db->prepare("DELETE FROM items WHERE item_id = ?");
    $stmt->execute([$consumable['item_id']]);

    // Log the activity
    $auth->logActivity($auth->getCurrentUser()['user_id'], 'delete', 'consumables', $consumableId);

    $db->commit();
    setFlashMessage('success', 'Consumable deleted successfully');
    header("Location: index.php");
    exit;

} catch (PDOException $e) {
    $db->rollBack();
    error_log("Error deleting consumable: " . $e->getMessage());
    setFlashMessage('error', 'Error deleting consumable. Please try again.');
    header("Location: view.php?id=" . $consumableId);
    exit;
}
