/**
 * Mobile Sidebar Fix
 * This file contains specific fixes for the sidebar on mobile devices
 */

@media (max-width: 767.98px) {
    /* Fix for sidebar menu items */
    .sidebar .nav-item {
        width: 100% !important;
    }
    
    .sidebar .nav-link {
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
        width: 100% !important;
        padding: 0.75rem 1rem !important;
        font-size: 0.9rem !important;
    }
    
    .sidebar .nav-link i {
        min-width: 1.75rem !important;
        font-size: 1rem !important;
        text-align: center !important;
    }
    
    .sidebar .nav-link span {
        display: inline-block !important;
        width: auto !important;
        max-width: calc(100% - 2.5rem) !important;
    }
    
    /* Fix for submenu items */
    .sidebar .collapse {
        position: static !important;
        width: 100% !important;
        box-shadow: none !important;
        background: rgba(0, 0, 0, 0.1) !important;
        border-radius: 0.5rem !important;
        margin: 0.25rem 0 0.25rem 1rem !important;
    }
    
    .sidebar .collapse .nav-link {
        padding-left: 1.5rem !important;
    }
    
    /* Fix for sidebar header */
    .sidebar .app-brand {
        padding: 1rem !important;
    }
    
    .sidebar .app-brand-text,
    .sidebar .app-brand-subtext {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Fix for sidebar footer */
    .sidebar .sidebar-footer {
        padding: 1rem !important;
    }
    
    /* Fix for sidebar toggle */
    .sidebar .sidebar-toggle {
        display: none !important;
    }
    
    /* Fix for collapsed state */
    .sidebar.collapsed {
        width: 85% !important;
        max-width: 300px !important;
    }
    
    .sidebar.collapsed .nav-link span,
    .sidebar.collapsed .app-brand-text,
    .sidebar.collapsed .app-brand-subtext,
    .sidebar.collapsed .user-info {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Fix for transition */
    .sidebar {
        transition: transform 0.3s ease !important;
    }
    
    /* Fix for sidebar position */
    .sidebar {
        position: fixed !important;
        top: 56px !important;
        left: 0 !important;
        bottom: 0 !important;
        height: calc(100vh - 56px) !important;
        z-index: 1070 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
    }
}
