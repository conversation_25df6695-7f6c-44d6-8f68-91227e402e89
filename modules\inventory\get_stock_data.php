<?php
// This is a new file to provide stock movement data for the chart

// Include required database and functions without the header
$base_path = $_SERVER['DOCUMENT_ROOT'] . '/choims';
require_once($base_path . '/config/database.php'); 
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/auth.php');

// Ensure user is logged in
requireLogin();

// Get parameters
$inventory_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$months = isset($_GET['months']) ? intval($_GET['months']) : 6;

// Default response
$response = [
    'labels' => [],
    'stockData' => [],
    'stockInData' => [],
    'stockOutData' => []
];

// Validate inventory ID
if ($inventory_id <= 0) {
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Set date range
$end_date = date('Y-m-d');
$start_date = date('Y-m-d', strtotime("-$months months"));

// Get inventory location information
$locationQuery = "SELECT location_id FROM consumable_inventory WHERE inventory_id = ?";
$locationStmt = mysqli_prepare($conn, $locationQuery);
mysqli_stmt_bind_param($locationStmt, 'i', $inventory_id);
mysqli_stmt_execute($locationStmt);
$locationResult = mysqli_stmt_get_result($locationStmt);
$inventoryLocation = mysqli_fetch_assoc($locationResult)['location_id'];

// Check if user has permission to view this inventory's data
$hasFullAccess = hasRole('GodMode', 'Superadmin', 'Logistics');
if (!$hasFullAccess) {
    $userLocationId = getUserLocationId();
    if ($userLocationId != $inventoryLocation) {
        // Return empty response if user doesn't have permission to view this inventory
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
}

// Get initial stock level at start date
$initial_query = "
    SELECT current_quantity 
    FROM consumable_inventory 
    WHERE inventory_id = ?
";
$initial_stmt = mysqli_prepare($conn, $initial_query);
mysqli_stmt_bind_param($initial_stmt, 'i', $inventory_id);
mysqli_stmt_execute($initial_stmt);
$initial_result = mysqli_stmt_get_result($initial_stmt);
$initial_stock = 0;

if ($row = mysqli_fetch_assoc($initial_result)) {
    $initial_stock = $row['current_quantity'];
}

// Adjust initial stock by subtracting subsequent transactions
$adjust_query = "
    SELECT 
        COALESCE(SUM(CASE WHEN transaction_type = 'Stock In' OR 
                        (transaction_type = 'Transfer' AND destination_location_id = 
                            (SELECT location_id FROM consumable_inventory WHERE inventory_id = ?)) 
                    THEN quantity ELSE 0 END), 0) as stock_in,
        COALESCE(SUM(CASE WHEN transaction_type = 'Stock Out' OR 
                        (transaction_type = 'Transfer' AND source_location_id = 
                            (SELECT location_id FROM consumable_inventory WHERE inventory_id = ?)) 
                    THEN quantity ELSE 0 END), 0) as stock_out
    FROM consumable_transactions
    WHERE inventory_id = ? AND transaction_date >= ?
";

// Add location filtering for non-privileged roles
if (!$hasFullAccess) {
    $adjust_query .= " AND (source_location_id = ? OR destination_location_id = ?)";
}

$adjust_stmt = mysqli_prepare($conn, $adjust_query);

if (!$hasFullAccess) {
    mysqli_stmt_bind_param($adjust_stmt, 'iiisii', $inventory_id, $inventory_id, $inventory_id, $end_date, $userLocationId, $userLocationId);
} else {
    mysqli_stmt_bind_param($adjust_stmt, 'iiis', $inventory_id, $inventory_id, $inventory_id, $end_date);
}

mysqli_stmt_execute($adjust_stmt);
$adjust_result = mysqli_stmt_get_result($adjust_stmt);

if ($row = mysqli_fetch_assoc($adjust_result)) {
    $initial_stock = $initial_stock - $row['stock_in'] + $row['stock_out'];
}

// Generate monthly data
for ($i = 0; $i < $months; $i++) {
    $month_start = date('Y-m-01', strtotime("-$i months"));
    $month_end = date('Y-m-t', strtotime("-$i months"));
    
    // Get month label
    $month_label = date('M Y', strtotime($month_start));
    array_unshift($response['labels'], $month_label);
    
    // Get transactions for this month
    $monthly_query = "
        SELECT 
            COALESCE(SUM(CASE WHEN transaction_type = 'Stock In' OR 
                            (transaction_type = 'Transfer' AND destination_location_id = 
                                (SELECT location_id FROM consumable_inventory WHERE inventory_id = ?)) 
                        THEN quantity ELSE 0 END), 0) as stock_in,
            COALESCE(SUM(CASE WHEN transaction_type = 'Stock Out' OR 
                            (transaction_type = 'Transfer' AND source_location_id = 
                                (SELECT location_id FROM consumable_inventory WHERE inventory_id = ?)) 
                        THEN quantity ELSE 0 END), 0) as stock_out
        FROM consumable_transactions
        WHERE inventory_id = ? AND transaction_date BETWEEN ? AND ?
    ";

    // Add location filtering for non-privileged roles
    if (!$hasFullAccess) {
        $monthly_query .= " AND (source_location_id = ? OR destination_location_id = ?)";
    }

    $monthly_stmt = mysqli_prepare($conn, $monthly_query);

    if (!$hasFullAccess) {
        mysqli_stmt_bind_param($monthly_stmt, 'iiissii', $inventory_id, $inventory_id, $inventory_id, $month_start, $month_end, $userLocationId, $userLocationId);
    } else {
        mysqli_stmt_bind_param($monthly_stmt, 'iiiss', $inventory_id, $inventory_id, $inventory_id, $month_start, $month_end);
    }

    mysqli_stmt_execute($monthly_stmt);
    $monthly_result = mysqli_stmt_get_result($monthly_stmt);
    
    $stock_in = 0;
    $stock_out = 0;
    
    if ($row = mysqli_fetch_assoc($monthly_result)) {
        $stock_in = intval($row['stock_in']);
        $stock_out = intval($row['stock_out']);
    }
    
    // Calculate end-of-month stock level
    $initial_stock = $initial_stock + $stock_in - $stock_out;
    
    // Add to response arrays
    array_unshift($response['stockData'], $initial_stock);
    array_unshift($response['stockInData'], $stock_in);
    array_unshift($response['stockOutData'], $stock_out);
}

// Return response as JSON
header('Content-Type: application/json');
echo json_encode($response); 