/* Modern Notifications Styles */
.notifications-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: none;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.notifications-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.notifications-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
}

.notifications-title i {
  color: #f59e0b;
  font-size: 1.1rem;
}

.notifications-view-all {
  color: #3b82f6;
  font-size: 0.85rem;
  font-weight: 500;
  text-decoration: none;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  transition: all 0.2s ease;
  background: rgba(59, 130, 246, 0.08);
}

.notifications-view-all:hover {
  background: rgba(59, 130, 246, 0.15);
  color: #2563eb;
  text-decoration: none;
}

.notifications-body {
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.notifications-body::-webkit-scrollbar {
  width: 6px;
}

.notifications-body::-webkit-scrollbar-track {
  background: transparent;
}

.notifications-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 20px;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  gap: 0.75rem;
}

.notification-item {
  display: flex;
  padding: 1rem;
  border-radius: 12px;
  background: #f9fafb;
  transition: all 0.2s ease;
  position: relative;
  border-left: 4px solid #e5e7eb;
}

.notification-item:hover {
  background: #f3f4f6;
  transform: translateX(4px);
}

.notification-item.unread {
  background: #f0f7ff;
  border-left-color: #3b82f6;
}

.notification-item.unread:hover {
  background: #e6f0fd;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.notification-icon.transfer {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.notification-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.notification-icon.warning {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
  font-size: 0.95rem;
}

.notification-message {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.75rem;
}

.notification-meta i {
  font-size: 0.8rem;
}

.notification-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.notification-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #3b82f6;
}

.empty-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-notifications i {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-notifications-text {
  color: #6b7280;
  font-size: 0.95rem;
  max-width: 250px;
  line-height: 1.5;
}

/* Animation for new notifications */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.notification-item.unread .notification-badge {
  animation: pulse 2s infinite;
}
