<?php
/**
 * Cache Management Tool
 * 
 * This tool allows administrators to view and manage the cache system.
 */

// Use relative paths
$base_path = dirname(dirname(__FILE__));
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/cache.php');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure only administrators can run this script
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || 
    (strtolower($_SESSION['role']) !== 'godmode' && strtolower($_SESSION['role']) !== 'superadmin')) {
    die("Access denied. This script can only be run by administrators.");
}

// Initialize variables
$message = '';
$messageType = '';
$cacheStats = [];

// Get cache instance
$cache = Cache::getInstance();

// Process actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'clear_all':
                // Clear all cache
                $cache->clear();
                $message = 'Cache cleared successfully.';
                $messageType = 'success';
                break;
                
            case 'toggle_cache':
                // Toggle cache enabled/disabled
                $enabled = isset($_POST['enabled']) ? (bool)$_POST['enabled'] : false;
                $cache->setEnabled($enabled);
                $message = 'Cache ' . ($enabled ? 'enabled' : 'disabled') . ' successfully.';
                $messageType = 'success';
                break;
                
            case 'toggle_file_cache':
                // Toggle file cache enabled/disabled
                $enabled = isset($_POST['enabled']) ? (bool)$_POST['enabled'] : false;
                $cache->setFileCacheEnabled($enabled);
                $message = 'File cache ' . ($enabled ? 'enabled' : 'disabled') . ' successfully.';
                $messageType = 'success';
                break;
        }
    }
}

// Get cache statistics
$cacheStats = $cache->getStats();

// Include header
include_once($base_path . '/includes/header.php');
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Cache Management</h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($message)): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold">Cache Statistics</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Cache Type</th>
                                                    <th>Items</th>
                                                    <th>Size</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Memory Cache</td>
                                                    <td><?php echo $cacheStats['memory']['count']; ?> items</td>
                                                    <td><?php echo formatBytes($cacheStats['memory']['size']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td>File Cache</td>
                                                    <td><?php echo $cacheStats['file']['count']; ?> items</td>
                                                    <td><?php echo formatBytes($cacheStats['file']['size']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Total</strong></td>
                                                    <td><strong><?php echo $cacheStats['memory']['count'] + $cacheStats['file']['count']; ?> items</strong></td>
                                                    <td><strong><?php echo formatBytes($cacheStats['memory']['size'] + $cacheStats['file']['size']); ?></strong></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold">Cache Controls</h6>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="" class="mb-3">
                                        <input type="hidden" name="action" value="clear_all">
                                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to clear all cache?');">
                                            <i class="fas fa-trash"></i> Clear All Cache
                                        </button>
                                    </form>
                                    
                                    <hr>
                                    
                                    <form method="post" action="" class="mb-3">
                                        <input type="hidden" name="action" value="toggle_cache">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="cacheEnabled" name="enabled" value="1" checked>
                                            <label class="form-check-label" for="cacheEnabled">Enable Memory Cache</label>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save Setting
                                        </button>
                                    </form>
                                    
                                    <hr>
                                    
                                    <form method="post" action="">
                                        <input type="hidden" name="action" value="toggle_file_cache">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="fileCacheEnabled" name="enabled" value="1" checked>
                                            <label class="form-check-label" for="fileCacheEnabled">Enable File Cache</label>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Save Setting
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold">Cache Performance Tips</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h5>Optimizing Cache Performance</h5>
                                <ul>
                                    <li><strong>Memory Cache:</strong> Fast but not persistent between requests. Good for frequently accessed data.</li>
                                    <li><strong>File Cache:</strong> Slower but persistent between requests. Good for data that doesn't change often.</li>
                                    <li><strong>Cache TTL:</strong> Set appropriate time-to-live values based on how often data changes.</li>
                                    <li><strong>Cache Size:</strong> Monitor cache size to prevent excessive memory or disk usage.</li>
                                </ul>
                                <p>For best performance under heavy loads, use both memory and file caching with appropriate TTL values.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Helper function to format bytes
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Include footer
include_once($base_path . '/includes/footer.php');
?>
