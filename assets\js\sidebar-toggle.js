/**
 * Immediate Sidebar Toggle Handler
 * This script runs immediately to ensure the sidebar toggle works properly
 */

(function() {
    // Function to initialize the sidebar toggle
    function initSidebarToggle() {
        const sidebarMobileToggle = document.getElementById('sidebarMobileToggle');
        const sidebar = document.querySelector('.sidebar');

        if (sidebarMobileToggle && sidebar) {
            console.log('Sidebar toggle initialized (immediate)');

            // Ensure sidebar is in the correct initial state
            if (window.innerWidth <= 767.98) {
                // On mobile, ensure sidebar starts hidden and not collapsed
                sidebar.classList.remove('show', 'collapsed');
                sidebar.classList.add('mobile-sidebar');
                document.body.classList.remove('sidebar-open');
                document.body.style.overflow = '';
            }

            // Add click event listener
            sidebarMobileToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Sidebar toggle clicked (immediate handler)');

                // Toggle sidebar visibility
                sidebar.classList.toggle('show');

                // Always ensure collapsed class is removed on mobile
                if (window.innerWidth <= 767.98) {
                    sidebar.classList.remove('collapsed');
                }

                document.body.classList.toggle('sidebar-open');

                // Prevent scrolling of the body when sidebar is open
                if (sidebar.classList.contains('show')) {
                    document.body.style.overflow = 'hidden';
                    console.log('Sidebar is now visible');
                } else {
                    document.body.style.overflow = '';
                    console.log('Sidebar is now hidden');

                    // Add a small delay before fully hiding to allow animation to complete
                    setTimeout(function() {
                        if (!sidebar.classList.contains('show')) {
                            sidebar.style.visibility = 'hidden';
                            setTimeout(function() {
                                sidebar.style.visibility = '';
                            }, 300);
                        }
                    }, 10);
                }
            });

            // Add click event listener to close sidebar when clicking outside
            document.addEventListener('click', function(event) {
                if (sidebar.classList.contains('show') &&
                    !sidebar.contains(event.target) &&
                    !sidebarMobileToggle.contains(event.target)) {

                    console.log('Clicking outside sidebar - closing it');
                    sidebar.classList.remove('show');
                    document.body.classList.remove('sidebar-open');
                    document.body.style.overflow = '';

                    // Ensure collapsed class is removed on mobile
                    if (window.innerWidth <= 767.98) {
                        sidebar.classList.remove('collapsed');
                    }
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth <= 767.98) {
                    // On mobile, ensure proper classes
                    sidebar.classList.add('mobile-sidebar');

                    // If sidebar is not showing, ensure collapsed is removed
                    if (!sidebar.classList.contains('show')) {
                        sidebar.classList.remove('collapsed');
                    }
                } else {
                    // On desktop, remove mobile-specific class
                    sidebar.classList.remove('mobile-sidebar');
                }
            });

            // Add mobile-specific CSS
            const style = document.createElement('style');
            style.textContent = `
                @media (max-width: 767.98px) {
                    .sidebar.mobile-sidebar {
                        width: 85% !important;
                        max-width: 300px !important;
                    }

                    .sidebar.mobile-sidebar .nav-link span,
                    .sidebar.mobile-sidebar .app-brand-text,
                    .sidebar.mobile-sidebar .app-brand-subtext,
                    .sidebar.mobile-sidebar .user-info {
                        display: block !important;
                    }

                    .sidebar.mobile-sidebar.collapsed {
                        transform: translateX(-100%) !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Try to initialize immediately
    if (document.readyState === 'interactive' || document.readyState === 'complete') {
        initSidebarToggle();
    } else {
        // If document not ready, wait for it
        document.addEventListener('DOMContentLoaded', initSidebarToggle);
    }

    // Also try after a short delay to ensure DOM is fully loaded
    setTimeout(initSidebarToggle, 500);
})();
