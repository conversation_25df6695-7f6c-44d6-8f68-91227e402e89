/* Modern MR Report Page Styling */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Page Header */
.page-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  color: var(--primary);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-bottom: 0;
}

.breadcrumb {
  margin-bottom: 0;
  justify-content: flex-end;
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--gray-500);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: var(--space-5);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.25rem 1rem;
}

.form-floating > label {
  padding: 1rem;
  color: var(--gray-500);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
  color: var(--primary);
}

.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
}

textarea.form-control {
  min-height: 120px;
}

.form-floating > textarea.form-control {
  height: 120px;
}

.input-group-text {
  background-color: var(--primary-bg);
  border: 1px solid var(--gray-300);
  color: var(--primary-dark);
  border-radius: var(--radius-md);
}

.invalid-feedback {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.btn-sm {
  padding: 0.4rem 1rem;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: #0ca678;
  border-color: #0ca678;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-info {
  background-color: var(--info);
  border-color: var(--info);
  color: var(--white);
}

.btn-info:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: #4b636e;
  border-color: #4b636e;
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-lg {
  padding: 0.75rem 1.75rem;
  font-size: 1rem;
}

/* Alerts */
.alert {
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  margin-right: var(--space-4);
}

.alert-success .alert-icon {
  background-color: rgba(16, 185, 129, 0.2);
}

.alert-danger .alert-icon {
  background-color: rgba(239, 68, 68, 0.2);
}

.alert-warning .alert-icon {
  background-color: rgba(245, 158, 11, 0.2);
}

.alert-info .alert-icon {
  background-color: rgba(59, 130, 246, 0.2);
}

/* Tables */
.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table th {
  font-weight: 600;
  color: var(--dark);
  border-bottom-width: 1px;
  padding: var(--space-3) var(--space-4);
  background-color: var(--gray-100);
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.03em;
}

.table th:first-child {
  border-top-left-radius: var(--radius-md);
}

.table th:last-child {
  border-top-right-radius: var(--radius-md);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-bottom-color: var(--gray-200);
  font-size: 0.85rem;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--gray-100);
}

.table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-md);
}

.table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-md);
}

.table-responsive {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background-color: var(--white);
}

/* Status Badges */
.badge {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
}

.badge i {
  margin-right: 0.25rem;
}

.badge.bg-primary.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-success.bg-opacity-10 {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: #10b981 !important;
}

.badge.bg-warning.bg-opacity-10 {
  background-color: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
}

.badge.bg-danger.bg-opacity-10 {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.badge.bg-info.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-secondary.bg-opacity-10 {
  background-color: rgba(107, 114, 128, 0.1) !important;
  color: #6b7280 !important;
}

/* Filter Section */
.filter-section {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.filter-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.filter-title i {
  color: var(--primary);
  font-size: 1rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: var(--space-4);
}

.page-item {
  margin: 0 var(--space-1);
}

.page-link {
  border: none;
  border-radius: var(--radius-md);
  color: var(--gray-500);
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.page-item.active .page-link {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.page-link:hover {
  background-color: var(--gray-100);
  color: var(--primary);
  transform: translateY(-2px);
}

.page-item.disabled .page-link {
  color: var(--gray-400);
  background-color: transparent;
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* MR Report Specific Styles */
.mr-report-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  margin-bottom: var(--space-4);
  overflow: hidden;
  position: relative;
}

.mr-report-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
}

.mr-report-card:hover {
  box-shadow: var(--shadow-md);
}

.mr-report-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mr-report-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.mr-report-title i {
  color: var(--primary);
  font-size: 1.1rem;
}

.mr-report-body {
  padding: 0;
}

.mr-report-footer {
  background-color: var(--gray-100);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mr-report-info {
  font-size: 0.8rem;
  color: var(--gray-500);
}

/* Form Label */
.form-label {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--gray-500);
}

/* Export Button */
.export-btn {
  background: linear-gradient(to right, var(--success), #20c997);
  color: var(--white);
  border: none;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
  border-radius: var(--radius-md);
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(16, 185, 129, 0.3);
  color: var(--white);
}

.export-btn:active {
  transform: translateY(-1px);
}

/* Loading Spinner */
.spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  color: var(--primary);
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-header {
    padding: var(--space-4);
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .card-header, .card-body {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem;
  }
  
  .btn-lg {
    padding: 0.6rem 1.5rem;
  }
  
  .filter-section {
    padding: var(--space-3);
  }
  
  .table td, .table th {
    padding: var(--space-2);
  }
}
