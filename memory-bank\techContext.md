# Technical Context

## Technologies Used

### Backend
- **PHP 8.4.4**: Core server-side language
- **MySQL**: Relational database management system
- **PDO**: PHP Data Objects for database connectivity
- **PHP Sessions**: For authentication and state management

### Frontend
- **HTML5/CSS3**: Core markup and styling
- **JavaScript**: Client-side interactivity
- **Bootstrap**: Responsive UI framework
- **jQuery**: JavaScript library for DOM manipulation
- **AJAX**: Asynchronous JavaScript and XML for dynamic content loading

### Development Environment
- **XAMPP**: Development server package (Apache, MySQL, PHP)
- **Development Path**: C:\xampp\htdocs\choims
- **Git**: Version control (if applicable)

## Technical Constraints

### Network
- Local Area Network (LAN) connectivity between health centers
- Limited or inconsistent internet connectivity at some satellite locations
- Need for offline functionality in case of network outages

### Security
- Sensitive health department data requires proper security measures
- Role-based access control essential for data protection
- Audit logging required for all system activities

### Performance
- System must handle multiple concurrent users
- Database optimization needed for larger inventory datasets
- Efficient queries required for reporting functionality

### Browser Compatibility
- Support for modern browsers (Chrome, Firefox, Edge)
- Potential need for compatibility with older browser versions

## Dependencies

### External Libraries
- Bootstrap for responsive UI
- jQuery for enhanced JavaScript functionality
- Chart.js or similar for dashboard visualizations (if applicable)
- PHPSpreadsheet for report exports (if applicable)

### Internal Dependencies
- Configuration files for database connection
- Centralized authentication system
- Shared utility functions
- Common templates and layouts

## Development Setup

### Local Environment Setup
1. Install XAMPP with PHP 8.4.4
2. Clone/copy project to C:\xampp\htdocs\choims
3. Import database schema (from sql files)
4. Configure database connection in config files

### Database Configuration
- Host: localhost
- Database: choims (or as configured)
- User: root (development only)
- Password: (as configured in XAMPP)

### File Structure
```
choims/
├── assets/
│   ├── css/
│   ├── js/
│   └── images/
├── config/
├── includes/
├── layouts/
├── modules/
└── templates/
```

## Deployment Considerations
- Production environment security hardening
- Database migration and seeding
- User account setup for different roles
- Network configuration for health center access
- Backup and recovery procedures 