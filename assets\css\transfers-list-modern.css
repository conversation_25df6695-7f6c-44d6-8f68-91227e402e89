/* Modern Transfers List Page Styling */
:root {
  /* Colors - <PERSON> Green Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --secondary: #81C784;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Page Header */
.page-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  color: var(--primary);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-bottom: 0;
}

.breadcrumb {
  margin-bottom: 0;
  justify-content: flex-end;
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--gray-500);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: var(--space-5);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.25rem 1rem;
}

.form-floating > label {
  padding: 1rem;
  color: var(--gray-500);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
  color: var(--primary);
}

.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
}

textarea.form-control {
  min-height: 120px;
}

.form-floating > textarea.form-control {
  height: 120px;
}

.input-group-text {
  background-color: var(--primary-bg);
  border: 1px solid var(--gray-300);
  color: var(--primary-dark);
  border-radius: var(--radius-md);
}

.invalid-feedback {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.btn-sm {
  padding: 0.4rem 1rem;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: #0ca678;
  border-color: #0ca678;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-info {
  background-color: var(--info);
  border-color: var(--info);
  color: var(--white);
}

.btn-info:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: #4b636e;
  border-color: #4b636e;
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-lg {
  padding: 0.75rem 1.75rem;
  font-size: 1rem;
}

/* Alerts */
.alert {
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  margin-right: var(--space-4);
}

.alert-success .alert-icon {
  background-color: rgba(16, 185, 129, 0.2);
}

.alert-danger .alert-icon {
  background-color: rgba(239, 68, 68, 0.2);
}

.alert-warning .alert-icon {
  background-color: rgba(245, 158, 11, 0.2);
}

.alert-info .alert-icon {
  background-color: rgba(59, 130, 246, 0.2);
}

/* Tables */
.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table th {
  font-weight: 600;
  color: var(--dark);
  border-bottom-width: 1px;
  padding: var(--space-3) var(--space-4);
  background-color: var(--gray-100);
}

.table th:first-child {
  border-top-left-radius: var(--radius-md);
}

.table th:last-child {
  border-top-right-radius: var(--radius-md);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-bottom-color: var(--gray-200);
}

/* Date column styling */
.table td.date-column {
  min-width: 180px; /* Ensure enough space for date and time */
  white-space: nowrap; /* Prevent line breaks */
}

.date-time {
  display: flex;
  flex-direction: column;
}

.date-part {
  font-weight: 500;
  color: var(--dark);
}

.time-part {
  font-size: 0.85em;
  color: var(--gray-500);
  margin-top: 2px;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--gray-100);
}

.table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-md);
}

.table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-md);
}

.table-responsive {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background-color: var(--white);
}

/* Status Badges */
.badge {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
}

.badge i {
  margin-right: 0.25rem;
}

.badge.bg-primary.bg-opacity-10 {
  background-color: rgba(46, 125, 50, 0.1) !important;
  color: var(--primary) !important;
}

.badge.bg-success.bg-opacity-10 {
  background-color: rgba(0, 200, 83, 0.1) !important;
  color: var(--success) !important;
}

.badge.bg-warning.bg-opacity-10 {
  background-color: rgba(255, 213, 79, 0.1) !important;
  color: var(--warning) !important;
}

.badge.bg-danger.bg-opacity-10 {
  background-color: rgba(255, 82, 82, 0.1) !important;
  color: var(--danger) !important;
}

.badge.bg-info.bg-opacity-10 {
  background-color: rgba(77, 208, 225, 0.1) !important;
  color: var(--info) !important;
}

.badge.bg-secondary.bg-opacity-10 {
  background-color: rgba(129, 199, 132, 0.1) !important;
  color: var(--secondary) !important;
}

/* Filter Section */
.filter-section {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.filter-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-3);
}

/* Stats Cards */
.stats-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  margin-bottom: var(--space-4);
  overflow: hidden;
  position: relative;
  height: 100%;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary), var(--primary-light));
}

.stats-card-body {
  padding: var(--space-4);
}

.stats-card-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--gray-500);
  margin-bottom: var(--space-2);
}

.stats-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--space-2);
}

.stats-card-icon {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background-color: var(--primary-bg);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
}

.stats-card-icon i {
  font-size: 1.5rem;
}

.stats-card-footer {
  font-size: 0.85rem;
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.stats-card-footer i {
  color: var(--success);
}

/* Status Timeline */
.timeline-container {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-top: var(--space-3);
  padding: 0 var(--space-2);
}

.timeline-connector {
  position: absolute;
  top: 15px;
  left: 10%;
  width: 80%;
  height: 2px;
  background-color: var(--gray-300);
  z-index: 0;
}

.timeline-node {
  position: relative;
  z-index: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: var(--radius-full);
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
}

.timeline-node-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: var(--radius-full);
  background-color: var(--gray-300);
  color: var(--white);
  font-size: 0.75rem;
}

.timeline-label {
  font-size: 0.7rem;
  color: var(--gray-500);
  margin-top: var(--space-1);
  white-space: nowrap;
}

/* Progress Bar */
.progress {
  height: 8px;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  overflow: hidden;
  margin-top: var(--space-2);
}

.progress-bar {
  border-radius: var(--radius-full);
  transition: width 0.8s ease;
}

/* DataTables Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin-bottom: var(--space-3);
}

.dataTables_wrapper .dataTables_length select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.25rem 2rem 0.25rem 0.5rem;
  background-color: var(--white);
}

.dataTables_wrapper .dataTables_filter input {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: var(--radius-full);
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: var(--primary);
  color: var(--white) !important;
  border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gray-100);
  color: var(--primary) !important;
  border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  color: var(--gray-400) !important;
}

/* Fix DataTables styling to respect our rounded corners */
div.dataTables_wrapper div.dataTables_filter {
  margin-top: var(--space-3);
}

.dataTables_wrapper .row:first-child {
  margin-bottom: var(--space-3);
}

.dataTables_wrapper .row:last-child {
  margin-top: var(--space-3);
}

.dataTables_wrapper table.dataTable {
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.dataTables_wrapper table.dataTable thead th {
  border-bottom: 1px solid var(--gray-200);
}

.dataTables_wrapper table.dataTable thead th:first-child {
  border-top-left-radius: var(--radius-md);
}

.dataTables_wrapper table.dataTable thead th:last-child {
  border-top-right-radius: var(--radius-md);
}

.dataTables_wrapper table.dataTable tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-md);
}

.dataTables_wrapper table.dataTable tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-md);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: var(--space-4);
}

.page-item {
  margin: 0 var(--space-1);
}

.page-link {
  border: none;
  border-radius: var(--radius-md);
  color: var(--gray-500);
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.page-item.active .page-link {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.page-link:hover {
  background-color: var(--gray-100);
  color: var(--primary);
  transform: translateY(-2px);
}

.page-item.disabled .page-link {
  color: var(--gray-400);
  background-color: transparent;
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-header {
    padding: var(--space-4);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .card-header, .card-body {
    padding: var(--space-4);
  }

  .stats-card-value {
    font-size: 1.5rem;
  }

  .stats-card-icon {
    width: 40px;
    height: 40px;
  }

  .stats-card-icon i {
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem;
  }

  .btn-lg {
    padding: 0.6rem 1.5rem;
  }

  .timeline-container {
    flex-wrap: wrap;
    gap: var(--space-3);
  }

  .timeline-connector {
    display: none;
  }
}
