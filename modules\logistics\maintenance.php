<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add custom CSS for modern maintenance page
echo '<link rel="stylesheet" href="/choims/assets/css/maintenance-modern.css">';
echo '<link rel="stylesheet" href="/choims/assets/css/custom-modal.css">';
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
// Only GodMode, Superadmin, and Logistics can access this maintenance page
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to view office and medical equipment maintenance records.');

    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else if (hasRole('HIMU')) {
        header('Location: /choims/dashboards/himu.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Get filter parameters
$location_id = isset($_GET['location']) ? sanitizeInput($_GET['location']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$maintenance_type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : '';
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$date_from = isset($_GET['date_from']) ? sanitizeInput($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitizeInput($_GET['date_to']) : '';
$category_id = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';

// Get locations for dropdown
$locationsQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);

// Build the query for maintenance records with filters
$query = "
    SELECT mr.*, fa.asset_name, fa.serial_number, sm.sku_name,
           l.location_name, u.username as technician_name, c.category_name
    FROM maintenance_records mr
    JOIN fixed_assets fa ON mr.asset_id = fa.asset_id
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN locations l ON fa.current_location_id = l.location_id
    JOIN categories c ON sm.category_id = c.category_id
    LEFT JOIN users u ON mr.technician_id = u.user_id
    WHERE c.category_id IN (2, 3) -- Only Office Equipment (2) and Medical Equipment (3)
";

$params = [];
$types = '';

if (!empty($location_id)) {
    $query .= " AND fa.current_location_id = ?";
    $params[] = $location_id;
    $types .= 'i';
}

if (!empty($status)) {
    $query .= " AND mr.status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($maintenance_type)) {
    $query .= " AND mr.maintenance_type = ?";
    $params[] = $maintenance_type;
    $types .= 's';
}

if (!empty($search)) {
    $query .= " AND (fa.asset_name LIKE ? OR fa.serial_number LIKE ? OR sm.sku_name LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= 'sss';
}

if (!empty($date_from)) {
    $query .= " AND mr.maintenance_date >= ?";
    $params[] = $date_from;
    $types .= 's';
}

if (!empty($date_to)) {
    $query .= " AND mr.maintenance_date <= ?";
    $params[] = $date_to;
    $types .= 's';
}

if (!empty($category_id)) {
    $query .= " AND c.category_id = ?";
    $params[] = $category_id;
    $types .= 'i';
}

// Add order by clause
$query .= " ORDER BY mr.maintenance_date DESC";

// Prepare and execute the query
$stmt = mysqli_prepare($conn, $query);

if (!empty($params)) {
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}

mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get defective office and medical equipment for the defective assets card
$defectiveAssetsQuery = "
    SELECT fa.asset_id, fa.asset_name, fa.serial_number, fa.model, fa.status,
           sm.sku_name, c.category_name, l.location_name
    FROM fixed_assets fa
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    WHERE fa.status = 'Defective'
    AND c.category_id IN (2, 3) -- Only Office Equipment (2) and Medical Equipment (3)
";

$defectiveAssetsQuery .= " ORDER BY fa.asset_id DESC";
$defectiveAssetsResult = mysqli_query($conn, $defectiveAssetsQuery);

// Get current user ID from session
$currentUserId = $_SESSION['user_id'];

// Get technicians (users) for the add maintenance modal
$techniciansQuery = "
    SELECT user_id, username, full_name
    FROM users
    WHERE role IN ('admin', 'Logistics')
    ORDER BY username
";
$techniciansResult = mysqli_query($conn, $techniciansQuery);

// Get common maintenance types
$maintenanceTypes = [
    'Preventive Maintenance',
    'Corrective Maintenance',
    'Repair',
    'Upgrade',
    'Inspection',
    'Calibration',
    'Parts Replacement',
    'Cleaning',
    'Other'
];

// Get categories for filter dropdown
$categoryQuery = "SELECT category_id, category_name FROM categories WHERE category_id IN (2, 3) ORDER BY category_name";
$categoryResult = mysqli_query($conn, $categoryQuery);
?>

<div class="container-fluid">
    <!-- Modern Header Section -->
    <div class="maintenance-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <div class="maintenance-title-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div>
                    <h1 class="maintenance-title">Office & Medical Equipment Maintenance</h1>
                    <p class="maintenance-subtitle mt-1">
                        <i class="fas fa-calendar-check me-2"></i> Manage maintenance records and track equipment repairs
                    </p>
                </div>
            </div>
            <div class="header-actions">
                <a href="/choims/dashboards/logistics.php" class="action-btn action-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>

            </div>
        </div>
        <div class="mt-2">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/choims/dashboards/logistics.php">Dashboard</a></li>
                    <li class="breadcrumb-item active">Equipment Maintenance</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="filter-card animate__animated animate__fadeInUp animate__delay-1">
        <div class="filter-card-header">
            <div class="filter-title">
                <i class="fas fa-filter"></i> Filter Maintenance Records
            </div>
            <button type="button" id="toggleFilters" class="filter-toggle-btn">
                <i class="fas fa-sliders-h"></i> Adjust Filters
            </button>
        </div>
        <div class="filter-card-body" id="filterBody">
            <form method="get" action="" class="row g-3">
                <div class="col-md-3">
                    <label for="location" class="form-label">Location</label>
                    <select class="form-select" id="location" name="location">
                        <option value="">All Locations</option>
                        <?php while ($location = mysqli_fetch_assoc($locationsResult)): ?>
                            <option value="<?php echo $location['location_id']; ?>"
                                <?php echo ($location_id == $location['location_id']) ? 'selected' : ''; ?>>
                                <?php echo $location['location_name']; ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        <?php mysqli_data_seek($categoryResult, 0); ?>
                        <?php while ($category = mysqli_fetch_assoc($categoryResult)): ?>
                            <option value="<?php echo $category['category_id']; ?>"
                                <?php echo ($category_id == $category['category_id']) ? 'selected' : ''; ?>>
                                <?php echo $category['category_name']; ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="Scheduled" <?php echo ($status == 'Scheduled') ? 'selected' : ''; ?>>Scheduled</option>
                        <option value="In Progress" <?php echo ($status == 'In Progress') ? 'selected' : ''; ?>>In Progress</option>
                        <option value="Completed" <?php echo ($status == 'Completed') ? 'selected' : ''; ?>>Completed</option>
                        <option value="Cancelled" <?php echo ($status == 'Cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">Maintenance Type</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">All Types</option>
                        <?php foreach ($maintenanceTypes as $type): ?>
                            <option value="<?php echo $type; ?>" <?php echo ($maintenance_type == $type) ? 'selected' : ''; ?>>
                                <?php echo $type; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Search assets..." value="<?php echo $search; ?>">
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <div class="d-grid gap-2 d-md-flex w-100">
                        <button type="submit" class="btn btn-primary me-md-2 flex-grow-1">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <a href="/choims/modules/logistics/maintenance.php" class="btn btn-outline-secondary flex-grow-1">
                            <i class="fas fa-undo"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Defective Assets Card -->
    <div class="defective-card animate__animated animate__fadeInUp animate__delay-2">
        <div class="defective-card-header">
            <div class="defective-title">
                <i class="fas fa-exclamation-triangle"></i> Defective Equipment
            </div>
            <div class="defective-subtitle">
                These assets need maintenance or repair
            </div>
            <div class="defective-actions">
                <button type="button" class="action-btn action-secondary" id="refreshDefectiveList">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
        <div class="defective-card-body">
            <div class="defective-search">
                <i class="fas fa-search"></i>
                <input type="text" id="defectiveSearch" placeholder="Search defective assets...">
            </div>
            <div class="table-responsive">
                <table class="modern-table" id="defectiveAssetsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Asset</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($defectiveAssetsResult) > 0): ?>
                            <?php while ($asset = mysqli_fetch_assoc($defectiveAssetsResult)): ?>
                                <tr>
                                    <td><?php echo $asset['asset_id']; ?></td>
                                    <td>
                                        <div class="asset-info">
                                            <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" class="asset-name">
                                                <?php echo $asset['asset_name']; ?>
                                            </a>
                                            <div class="asset-details">
                                                <?php if (!empty($asset['serial_number'])): ?>
                                                    <span class="me-2"><i class="fas fa-barcode me-1"></i><?php echo $asset['serial_number']; ?></span>
                                                <?php endif; ?>
                                                <?php if (!empty($asset['model'])): ?>
                                                    <span><i class="fas fa-tag me-1"></i><?php echo $asset['model']; ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo $asset['category_name']; ?></td>
                                    <td><?php echo $asset['location_name']; ?></td>
                                    <td>
                                        <span class="status-badge status-defective">
                                            <?php echo $asset['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="table-actions">
                                            <button type="button" class="table-action-btn repair-btn" data-bs-toggle="modal" data-bs-target="#markForRepairModal<?php echo $asset['asset_id']; ?>" title="Mark for Repair">
                                                <i class="fas fa-tools"></i>
                                            </button>
                                            <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" class="table-action-btn view-btn" title="View Asset">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-check-circle"></i>
                                        <p>No defective equipment found</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Maintenance Records Card -->
    <div class="records-card animate__animated animate__fadeInUp animate__delay-3">
        <div class="records-card-header">
            <div class="records-title">
                <i class="fas fa-clipboard-list"></i> Maintenance Records
            </div>
            <div class="records-actions">
                <button type="button" class="action-btn action-secondary" id="refreshMaintenanceList">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>
        <div class="records-card-body">
            <div class="records-search">
                <i class="fas fa-search"></i>
                <input type="text" id="maintenanceSearch" placeholder="Search maintenance records...">
            </div>
            <div class="table-responsive">
                <table class="modern-table" id="maintenanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Date</th>
                            <th>Asset</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Type</th>
                            <th>Cost</th>
                            <th>Technician</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($record = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td><?php echo $record['record_id']; ?></td>
                                    <td><?php echo formatDate($record['maintenance_date']); ?></td>
                                    <td>
                                        <div class="asset-info">
                                            <a href="/choims/modules/assets/view.php?id=<?php echo $record['asset_id']; ?>" class="asset-name">
                                                <?php echo $record['asset_name']; ?>
                                            </a>
                                            <div class="asset-details">
                                                <?php if (!empty($record['serial_number'])): ?>
                                                    <span><i class="fas fa-barcode me-1"></i><?php echo $record['serial_number']; ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo $record['category_name']; ?></td>
                                    <td><?php echo $record['location_name']; ?></td>
                                    <td>
                                        <div class="maintenance-type">
                                            <?php
                                                $typeIcon = 'fas fa-tools';
                                                switch ($record['maintenance_type']) {
                                                    case 'Preventive Maintenance':
                                                        $typeIcon = 'fas fa-shield-alt';
                                                        break;
                                                    case 'Corrective Maintenance':
                                                        $typeIcon = 'fas fa-wrench';
                                                        break;
                                                    case 'Repair':
                                                        $typeIcon = 'fas fa-tools';
                                                        break;
                                                    case 'Upgrade':
                                                        $typeIcon = 'fas fa-arrow-up';
                                                        break;
                                                    case 'Inspection':
                                                        $typeIcon = 'fas fa-search';
                                                        break;
                                                    case 'Calibration':
                                                        $typeIcon = 'fas fa-balance-scale';
                                                        break;
                                                    case 'Parts Replacement':
                                                        $typeIcon = 'fas fa-exchange-alt';
                                                        break;
                                                    case 'Cleaning':
                                                        $typeIcon = 'fas fa-broom';
                                                        break;
                                                }
                                            ?>
                                            <i class="<?php echo $typeIcon; ?> me-1"></i>
                                            <?php echo $record['maintenance_type']; ?>
                                        </div>
                                    </td>
                                    <td><?php echo formatCurrency($record['cost']); ?></td>
                                    <td>
                                        <?php if (!empty($record['technician_name'])): ?>
                                            <div class="technician-info">
                                                <i class="fas fa-user-md me-1"></i>
                                                <?php echo $record['technician_name']; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Not assigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                            $statusClass = '';
                                            $statusIcon = 'fas fa-clock';
                                            switch ($record['status']) {
                                                case 'Scheduled':
                                                    $statusClass = 'status-scheduled';
                                                    $statusIcon = 'fas fa-calendar-alt';
                                                    break;
                                                case 'In Progress':
                                                    $statusClass = 'status-in-progress';
                                                    $statusIcon = 'fas fa-spinner fa-spin';
                                                    break;
                                                case 'Completed':
                                                    $statusClass = 'status-completed';
                                                    $statusIcon = 'fas fa-check-circle';
                                                    break;
                                                case 'Cancelled':
                                                    $statusClass = 'status-cancelled';
                                                    $statusIcon = 'fas fa-times-circle';
                                                    break;
                                            }
                                        ?>
                                        <span class="status-badge <?php echo $statusClass; ?>">
                                            <i class="<?php echo $statusIcon; ?> me-1"></i>
                                            <?php echo $record['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="/choims/modules/logistics/edit_maintenance.php?id=<?php echo $record['record_id']; ?>" class="table-action-btn edit-btn" title="Edit Record">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="table-action-btn delete-btn" data-bs-toggle="modal" data-bs-target="#deleteMaintenanceModal<?php echo $record['record_id']; ?>" title="Delete Record">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Delete Maintenance Record Modal -->
                                <div class="modal fade" id="deleteMaintenanceModal<?php echo $record['record_id']; ?>" tabindex="-1" aria-labelledby="deleteMaintenanceModalLabel<?php echo $record['record_id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteMaintenanceModalLabel<?php echo $record['record_id']; ?>">Confirm Deletion</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this maintenance record?</p>
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    This action cannot be undone.
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="/choims/modules/logistics/delete.php" method="post">
                                                    <input type="hidden" name="record_id" value="<?php echo $record['record_id']; ?>">
                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-clipboard-list"></i>
                                        <p>No maintenance records found</p>
                                        <!-- Add Maintenance Record button removed -->
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

<!-- Add Maintenance Record Modal -->
<div class="modal fade" id="addMaintenanceModal" tabindex="-1" aria-labelledby="addMaintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMaintenanceModalLabel">Add Maintenance Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/choims/modules/logistics/add_maintenance.php" method="post" id="addMaintenanceForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="asset_id" class="form-label">Asset *</label>
                            <select class="form-select" id="asset_id" name="asset_id" required>
                                <option value="">Select Asset</option>
                                <?php
                                // Get office and medical equipment assets
                                $assetsQuery = "
                                    SELECT fa.asset_id, fa.asset_name, fa.serial_number, sm.sku_name, c.category_name
                                    FROM fixed_assets fa
                                    JOIN sku_master sm ON fa.sku_id = sm.sku_id
                                    JOIN categories c ON sm.category_id = c.category_id
                                    WHERE c.category_id IN (2, 3) -- Only Office Equipment (2) and Medical Equipment (3)
                                    ORDER BY fa.asset_name
                                ";
                                $assetsResult = mysqli_query($conn, $assetsQuery);
                                while ($asset = mysqli_fetch_assoc($assetsResult)) {
                                    echo '<option value="' . $asset['asset_id'] . '">' . $asset['asset_name'] . ' (' . $asset['category_name'] . ')';
                                    if (!empty($asset['serial_number'])) {
                                        echo ' - SN: ' . $asset['serial_number'];
                                    }
                                    echo '</option>';
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="maintenance_date" class="form-label">Maintenance Date *</label>
                            <input type="date" class="form-control" id="maintenance_date" name="maintenance_date" required value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="maintenance_type" class="form-label">Maintenance Type *</label>
                            <select class="form-select" id="maintenance_type" name="maintenance_type" required>
                                <option value="">Select Type</option>
                                <?php foreach ($maintenanceTypes as $type): ?>
                                    <option value="<?php echo $type; ?>"><?php echo $type; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Scheduled">Scheduled</option>
                                <option value="In Progress">In Progress</option>
                                <option value="Completed">Completed</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="technician_id" class="form-label">Technician</label>
                            <select class="form-select" id="technician_id" name="technician_id">
                                <option value="">Select Technician</option>
                                <?php mysqli_data_seek($techniciansResult, 0); ?>
                                <?php while ($technician = mysqli_fetch_assoc($techniciansResult)): ?>
                                    <option value="<?php echo $technician['user_id']; ?>"
                                        <?php echo ($currentUserId == $technician['user_id']) ? 'selected' : ''; ?>>
                                        <?php echo $technician['username']; ?>
                                        <?php if (!empty($technician['full_name'])): ?>
                                            (<?php echo $technician['full_name']; ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cost" class="form-label">Cost</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="cost" name="cost" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="update_asset_status" name="update_asset_status" checked>
                        <label class="form-check-label" for="update_asset_status">
                            Update asset status based on maintenance status
                        </label>
                    </div>
                    <input type="hidden" name="created_by" value="<?php echo $currentUserId; ?>">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addMaintenanceForm" class="btn btn-primary">Save Record</button>
            </div>
        </div>
    </div>
</div>

<!-- Mark for Repair Modals -->
<?php mysqli_data_seek($defectiveAssetsResult, 0); ?>
<?php while ($asset = mysqli_fetch_assoc($defectiveAssetsResult)): ?>
    <div class="modal fade" id="markForRepairModal<?php echo $asset['asset_id']; ?>" tabindex="-1" aria-labelledby="markForRepairModalLabel<?php echo $asset['asset_id']; ?>" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="markForRepairModalLabel<?php echo $asset['asset_id']; ?>">Mark for Repair: <?php echo $asset['asset_name']; ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="/choims/modules/logistics/add_maintenance.php" method="post" id="repairForm<?php echo $asset['asset_id']; ?>">
                        <input type="hidden" name="asset_id" value="<?php echo $asset['asset_id']; ?>">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="maintenance_date<?php echo $asset['asset_id']; ?>" class="form-label">Maintenance Date *</label>
                                <input type="date" class="form-control" id="maintenance_date<?php echo $asset['asset_id']; ?>" name="maintenance_date" required value="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="maintenance_type<?php echo $asset['asset_id']; ?>" class="form-label">Maintenance Type *</label>
                                <select class="form-select" id="maintenance_type<?php echo $asset['asset_id']; ?>" name="maintenance_type" required onchange="handleMaintenanceTypeChange(this, <?php echo $asset['asset_id']; ?>)">
                                    <option value="">Select Type</option>
                                    <?php foreach ($maintenanceTypes as $type): ?>
                                        <option value="<?php echo $type; ?>"><?php echo $type; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="technician_id<?php echo $asset['asset_id']; ?>" class="form-label">Technician</label>
                                <select class="form-select" id="technician_id<?php echo $asset['asset_id']; ?>" name="technician_id">
                                    <option value="">Select Technician</option>
                                    <?php mysqli_data_seek($techniciansResult, 0); ?>
                                    <?php while ($technician = mysqli_fetch_assoc($techniciansResult)): ?>
                                        <option value="<?php echo $technician['user_id']; ?>"
                                            <?php echo ($currentUserId == $technician['user_id']) ? 'selected' : ''; ?>>
                                            <?php echo $technician['username']; ?>
                                            <?php if (!empty($technician['full_name'])): ?>
                                                (<?php echo $technician['full_name']; ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status<?php echo $asset['asset_id']; ?>" class="form-label">Status *</label>
                                <select class="form-select" id="status<?php echo $asset['asset_id']; ?>" name="status" required>
                                    <option value="In Progress" selected>In Progress</option>
                                    <option value="Scheduled">Scheduled</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description<?php echo $asset['asset_id']; ?>" class="form-label">Description *</label>
                            <textarea class="form-control" id="description<?php echo $asset['asset_id']; ?>" name="description" rows="3" required></textarea>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="update_asset_status<?php echo $asset['asset_id']; ?>" name="update_asset_status" checked>
                            <label class="form-check-label" for="update_asset_status<?php echo $asset['asset_id']; ?>">
                                Update asset status to "Under Repair"
                            </label>
                        </div>
                        <input type="hidden" name="created_by" value="<?php echo $currentUserId; ?>">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="repairForm<?php echo $asset['asset_id']; ?>" class="btn btn-primary">Save Record</button>
                </div>
            </div>
        </div>
    </div>
<?php endwhile; ?>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>

<script>
// Function to handle maintenance type change in the mark for repair modal
function handleMaintenanceTypeChange(selectElement, assetId) {
    // This function can be expanded if needed for specific maintenance types
    console.log('Maintenance type changed to: ' + selectElement.value + ' for asset ID: ' + assetId);
}

// Function to view maintenance record details
function viewMaintenanceRecord(recordId) {
    // Create a modal dynamically
    const modalId = 'viewMaintenanceModal' + recordId;

    // Check if modal already exists
    if (document.getElementById(modalId)) {
        // Just show the existing modal
        new bootstrap.Modal(document.getElementById(modalId)).show();
        return;
    }

    // Create modal container if it doesn't exist
    const modalContainer = document.createElement('div');
    modalContainer.className = 'modal fade';
    modalContainer.id = modalId;
    modalContainer.setAttribute('tabindex', '-1');
    modalContainer.setAttribute('aria-hidden', 'true');

    // Set loading content
    modalContainer.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Loading Maintenance Record...</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading record details...</p>
                </div>
            </div>
        </div>
    `;

    // Add to document
    document.body.appendChild(modalContainer);

    // Show modal
    const modal = new bootstrap.Modal(modalContainer);
    modal.show();

    // Fetch record details
    fetch('/choims/modules/logistics/get_maintenance_modal.php?id=' + recordId)
        .then(response => response.text())
        .then(html => {
            modalContainer.querySelector('.modal-dialog').outerHTML = html;
        })
        .catch(error => {
            console.error('Error fetching maintenance record:', error);
            modalContainer.querySelector('.modal-body').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error loading record details. Please try again.
                </div>
            `;
        });
}

$(document).ready(function() {
    // Toggle filter section
    $('#toggleFilters').on('click', function() {
        $('#filterBody').slideToggle(300);
        $(this).find('i').toggleClass('fa-sliders-h fa-times');

        if ($(this).find('i').hasClass('fa-times')) {
            $(this).html('<i class="fas fa-times"></i> Close Filters');
        } else {
            $(this).html('<i class="fas fa-sliders-h"></i> Adjust Filters');
        }
    });

    // Check if filters are applied and show/hide filter body accordingly
    const hasFilters = <?php echo (!empty($location_id) || !empty($status) || !empty($maintenance_type) || !empty($search) || !empty($date_from) || !empty($date_to) || !empty($category_id)) ? 'true' : 'false'; ?>;
    if (!hasFilters) {
        $('#filterBody').hide();
    }

    // Add basic search functionality for the maintenance table
    $('#maintenanceSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#maintenanceTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add basic search functionality for the defective assets table
    $('#defectiveSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#defectiveAssetsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add hover effect to table rows
    $('.modern-table tbody tr').hover(
        function() { $(this).css('background-color', 'var(--bg-secondary)'); },
        function() { $(this).css('background-color', ''); }
    );

    // Update asset status checkbox logic
    $('#status').on('change', function() {
        var maintenanceStatus = $(this).val();
        var checkbox = $('#update_asset_status');

        if (maintenanceStatus === 'Completed') {
            checkbox.prop('checked', true);
        } else if (maintenanceStatus === 'In Progress') {
            checkbox.prop('checked', true);
        } else {
            checkbox.prop('checked', false);
        }
    });

    // Refresh maintenance list
    $('#refreshMaintenanceList').on('click', function() {
        const button = $(this);
        const originalHtml = button.html();

        // Show loading spinner
        button.html('<i class="fas fa-spinner fa-spin"></i> Refreshing...');
        button.prop('disabled', true);

        // Reload the page
        setTimeout(function() {
            window.location.reload();
        }, 500);
    });

    // Refresh defective assets list
    $('#refreshDefectiveList').on('click', function() {
        const button = $(this);
        const originalHtml = button.html();

        // Show loading spinner
        button.html('<i class="fas fa-spinner fa-spin"></i> Refreshing...');
        button.prop('disabled', true);

        // Reload the page
        setTimeout(function() {
            window.location.reload();
        }, 500);
    });

    // Add animation to tables
    $('.modern-table').addClass('animate__animated animate__fadeIn');
});
</script>