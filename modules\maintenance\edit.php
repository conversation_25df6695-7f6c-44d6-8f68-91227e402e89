<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
// Only GodMode, Superadmin, and HIMU can edit maintenance records
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('HIMU')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to edit maintenance records.');

    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Add custom CSS for modern maintenance edit page
echo '<link rel="stylesheet" href="/choims/assets/css/maintenance-edit-modern.css">';
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';

// Add custom CSS for status buttons
echo '<style>
    /* Status buttons styling */
    .status-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .status-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-color);
        background-color: var(--bg-white);
        color: var(--text-primary);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: var(--shadow-sm);
    }

    .status-btn i {
        margin-right: 0.5rem;
    }

    .status-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .status-btn.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    /* Status button colors */
    .status-btn[data-status="Scheduled"] {
        border-color: #3b82f6;
        color: #3b82f6;
    }

    .status-btn[data-status="Scheduled"].active {
        background-color: #3b82f6;
        color: white;
    }

    .status-btn[data-status="In Progress"] {
        border-color: #f59e0b;
        color: #f59e0b;
    }

    .status-btn[data-status="In Progress"].active {
        background-color: #f59e0b;
        color: white;
    }

    .status-btn[data-status="Completed"] {
        border-color: #10b981;
        color: #10b981;
    }

    .status-btn[data-status="Completed"].active {
        background-color: #10b981;
        color: white;
    }

    .status-btn[data-status="Cancelled"] {
        border-color: #ef4444;
        color: #ef4444;
    }

    .status-btn[data-status="Cancelled"].active {
        background-color: #ef4444;
        color: white;
    }

    @media (max-width: 768px) {
        .status-buttons {
            flex-direction: column;
        }

        .status-btn {
            width: 100%;
        }
    }
</style>';

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('error', 'No maintenance record specified.');
    header('Location: /choims/modules/assets/maintenance.php');
    exit;
}

$record_id = sanitizeInput($_GET['id']);

// Get maintenance record details
$recordQuery = "
    SELECT mr.*, fa.asset_name, fa.serial_number, sm.sku_name, l.location_name
    FROM maintenance_records mr
    JOIN fixed_assets fa ON mr.asset_id = fa.asset_id
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN locations l ON fa.current_location_id = l.location_id
    WHERE mr.record_id = ?
";

$recordStmt = mysqli_prepare($conn, $recordQuery);
mysqli_stmt_bind_param($recordStmt, 'i', $record_id);
mysqli_stmt_execute($recordStmt);
$recordResult = mysqli_stmt_get_result($recordStmt);

// Check if record exists
if (mysqli_num_rows($recordResult) === 0) {
    setFlashMessage('error', 'Maintenance record not found.');
    header('Location: /choims/modules/assets/maintenance.php');
    exit;
}

$record = mysqli_fetch_assoc($recordResult);

// Get current user ID from session
$currentUserId = $_SESSION['user_id'];
$currentUserRole = $_SESSION['role'];

// Get all technicians for dropdown (only for admin users)
$techniciansQuery = "";
if (in_array($currentUserRole, ['GodMode', 'Superadmin', 'admin'])) {
    // Admin users can select any technician
    $techniciansQuery = "
        SELECT user_id, username, full_name
        FROM users
        WHERE role IN ('admin', 'HIMU', 'Logistics')
        ORDER BY username
    ";
} else {
    // Non-admin users can only select themselves
    $techniciansQuery = "
        SELECT user_id, username, full_name
        FROM users
        WHERE user_id = ?
    ";
}

$techniciansStmt = mysqli_prepare($conn, $techniciansQuery);
if (!in_array($currentUserRole, ['GodMode', 'Superadmin', 'admin'])) {
    mysqli_stmt_bind_param($techniciansStmt, 'i', $currentUserId);
}
mysqli_stmt_execute($techniciansStmt);
$techniciansResult = mysqli_stmt_get_result($techniciansStmt);

// Get common maintenance types
$maintenanceTypes = [
    'Preventive Maintenance',
    'Corrective Maintenance',
    'Repair',
    'Upgrade',
    'Inspection',
    'Calibration',
    'Software Update',
    'Cleaning',
    'Parts Replacement',
    'Other'
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate required fields
    $required_fields = ['maintenance_date', 'maintenance_type', 'status'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            setFlashMessage('error', 'Please fill in all required fields.');
            // Redirect back to this page
            header('Location: /choims/modules/maintenance/edit.php?id=' . $record_id);
            exit;
        }
    }

    // Sanitize input
    $maintenance_date = sanitizeInput($_POST['maintenance_date']);
    $maintenance_type = sanitizeInput($_POST['maintenance_type']);
    $cost = !empty($_POST['cost']) ? sanitizeInput($_POST['cost']) : 0;

    // Handle technician ID based on user role
    if (!in_array($currentUserRole, ['GodMode', 'Superadmin', 'admin'])) {
        // For non-admin users, force the technician to be the current user
        $technician_id = $currentUserId;
    } else {
        // For admin users, use the selected technician
        $technician_id = !empty($_POST['technician_id']) ? sanitizeInput($_POST['technician_id']) : null;
    }

    $status = sanitizeInput($_POST['status']);
    $description = !empty($_POST['description']) ? sanitizeInput($_POST['description']) : null;
    $update_asset_status = isset($_POST['update_asset_status']) ? 1 : 0;

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // Update maintenance record
        $updateQuery = "
            UPDATE maintenance_records
            SET maintenance_date = ?,
                maintenance_type = ?,
                cost = ?,
                technician_id = ?,
                status = ?,
                description = ?,
                updated_at = NOW()
            WHERE record_id = ?
        ";

        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param(
            $updateStmt,
            'ssdsssi',
            $maintenance_date,
            $maintenance_type,
            $cost,
            $technician_id,
            $status,
            $description,
            $record_id
        );

        $updateResult = mysqli_stmt_execute($updateStmt);

        if (!$updateResult) {
            throw new Exception("Failed to update maintenance record: " . mysqli_error($conn));
        }

        // Update asset status if checkbox is checked
        if ($update_asset_status) {
            $assetStatus = 'Available'; // Default

            if ($status === 'In Progress') {
                $assetStatus = 'Under Repair';
            } else if ($status === 'Scheduled') {
                $assetStatus = 'Under Repair';
            }

            $updateAssetQuery = "
                UPDATE fixed_assets
                SET status = ?, updated_at = NOW()
                WHERE asset_id = ?
            ";

            $updateAssetStmt = mysqli_prepare($conn, $updateAssetQuery);
            mysqli_stmt_bind_param($updateAssetStmt, 'si', $assetStatus, $record['asset_id']);
            $updateAssetResult = mysqli_stmt_execute($updateAssetStmt);

            if (!$updateAssetResult) {
                throw new Exception("Failed to update asset status: " . mysqli_error($conn));
            }
        }

        // Create audit log data
        $log_data = [
            'asset_id' => $record['asset_id'],
            'maintenance_date' => $maintenance_date,
            'maintenance_type' => $maintenance_type,
            'cost' => $cost,
            'technician_id' => $technician_id,
            'status' => $status,
            'description' => $description,
            'update_asset_status' => $update_asset_status
        ];

        // Get old values for audit log
        $old_data = [
            'asset_id' => $record['asset_id'],
            'maintenance_date' => $record['maintenance_date'],
            'maintenance_type' => $record['maintenance_type'],
            'cost' => $record['cost'],
            'technician_id' => $record['technician_id'],
            'status' => $record['status'],
            'description' => $record['description']
        ];

        // Log to regular audit log
        logActivity($conn, 'Update Maintenance Record', 'maintenance_records', $record_id, json_encode($old_data), json_encode($log_data));

        // Log to detailed audit system if available
        if (function_exists('logDetailedAction')) {
            logDetailedAction($conn, $_SESSION['user_id'], 'update', 'other', $record_id, [
                'entity_name' => 'Maintenance Record',
                'changes_summary' => "Updated maintenance record for asset ID: {$record['asset_id']}",
                'old_values' => json_encode($old_data),
                'new_values' => json_encode($log_data)
            ]);
        }

        // Commit transaction
        mysqli_commit($conn);

        // Set success message
        setFlashMessage('success', 'Maintenance record updated successfully.');

        // Redirect to maintenance page
        header('Location: /choims/modules/assets/maintenance.php');
        exit;

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);

        // Set error message
        setFlashMessage('error', $e->getMessage());

        // Redirect back to this page
        header('Location: /choims/modules/maintenance/edit.php?id=' . $record_id);
        exit;
    }
}
?>

<div class="container-fluid">
    <!-- Modern Header Section -->
    <div class="edit-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <div class="edit-title-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div>
                    <h1 class="edit-title">Edit Maintenance Record</h1>
                    <p class="edit-subtitle mt-1">
                        <i class="fas fa-clipboard-check me-2"></i> Update maintenance details for record #<?php echo $record_id; ?>
                    </p>
                </div>
            </div>
            <div class="header-actions">
                <a href="/choims/modules/assets/maintenance.php" class="action-btn action-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
        </div>
        <div class="mt-2">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/choims/dashboards/index.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/choims/modules/assets/maintenance.php">Maintenance</a></li>
                    <li class="breadcrumb-item active">Edit Record</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Asset Information Card -->
    <div class="edit-card animate__animated animate__fadeInUp animate__delay-1">
        <div class="edit-card-header">
            <h2 class="edit-card-title">
                <i class="fas fa-laptop-medical"></i> Asset Information
            </h2>
            <a href="/choims/modules/assets/view.php?id=<?php echo $record['asset_id']; ?>" class="action-btn action-secondary">
                <i class="fas fa-eye"></i> View Asset Details
            </a>
        </div>
        <div class="edit-card-body">
            <div class="asset-info-grid">
                <div class="info-item">
                    <div class="info-label">Asset Name</div>
                    <div class="info-value"><?php echo $record['asset_name']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Asset Type</div>
                    <div class="info-value"><?php echo $record['sku_name']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Serial Number</div>
                    <div class="info-value"><?php echo $record['serial_number'] ?: 'N/A'; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Current Location</div>
                    <div class="info-value"><?php echo $record['location_name']; ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Form Card -->
    <div class="edit-card animate__animated animate__fadeInUp animate__delay-2">
        <div class="edit-card-header">
            <h2 class="edit-card-title">
                <i class="fas fa-edit"></i> Maintenance Details
            </h2>
        </div>
        <div class="edit-card-body">
            <form action="/choims/modules/maintenance/edit.php?id=<?php echo $record_id; ?>" method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label for="maintenance_date" class="form-label">Maintenance Date <span class="required">*</span></label>
                        <input type="date" class="form-control" id="maintenance_date" name="maintenance_date" required value="<?php echo $record['maintenance_date']; ?>">
                    </div>
                    <div class="form-group">
                        <label for="maintenance_type" class="form-label">Maintenance Type <span class="required">*</span></label>
                        <select class="form-select" id="maintenance_type" name="maintenance_type" required>
                            <?php foreach ($maintenanceTypes as $type): ?>
                                <option value="<?php echo $type; ?>" <?php if ($record['maintenance_type'] == $type) echo 'selected'; ?>>
                                    <?php echo $type; ?>
                                </option>
                            <?php endforeach; ?>
                            <?php if (!in_array($record['maintenance_type'], $maintenanceTypes)): ?>
                                <option value="<?php echo $record['maintenance_type']; ?>" selected>
                                    <?php echo $record['maintenance_type']; ?>
                                </option>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="cost" class="form-label">Cost (₱)</label>
                        <input type="number" class="form-control" id="cost" name="cost" step="0.01" min="0" value="<?php echo $record['cost']; ?>">
                    </div>
                    <div class="form-group">
                        <label for="technician_id" class="form-label">Technician</label>
                        <select class="form-select" id="technician_id" name="technician_id" <?php if (!in_array($currentUserRole, ['GodMode', 'Superadmin', 'admin'])) echo 'disabled'; ?>>
                            <?php if (in_array($currentUserRole, ['GodMode', 'Superadmin', 'admin'])): ?>
                                <option value="">Select Technician</option>
                            <?php endif; ?>
                            <?php mysqli_data_seek($techniciansResult, 0); ?>
                            <?php while ($technician = mysqli_fetch_assoc($techniciansResult)): ?>
                                <option value="<?php echo $technician['user_id']; ?>" <?php if ($record['technician_id'] == $technician['user_id']) echo 'selected'; ?>>
                                    <?php echo $technician['full_name']; ?> (<?php echo $technician['username']; ?>)
                                </option>
                            <?php endwhile; ?>
                        </select>
                        <?php if (!in_array($currentUserRole, ['GodMode', 'Superadmin', 'admin'])): ?>
                            <input type="hidden" name="technician_id" value="<?php echo $currentUserId; ?>">
                            <div class="form-text text-muted">As a non-admin user, you are automatically assigned as the technician.</div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Status <span class="required">*</span></label>
                    <div class="status-buttons">
                        <input type="hidden" id="status" name="status" value="<?php echo $record['status']; ?>" required>
                        <button type="button" class="status-btn <?php echo ($record['status'] === 'Scheduled') ? 'active' : ''; ?>" data-status="Scheduled">
                            <i class="fas fa-calendar-alt"></i> Scheduled
                        </button>
                        <button type="button" class="status-btn <?php echo ($record['status'] === 'In Progress') ? 'active' : ''; ?>" data-status="In Progress">
                            <i class="fas fa-spinner"></i> In Progress
                        </button>
                        <button type="button" class="status-btn <?php echo ($record['status'] === 'Completed') ? 'active' : ''; ?>" data-status="Completed">
                            <i class="fas fa-check-circle"></i> Completed
                        </button>
                        <button type="button" class="status-btn <?php echo ($record['status'] === 'Cancelled') ? 'active' : ''; ?>" data-status="Cancelled">
                            <i class="fas fa-times-circle"></i> Cancelled
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea class="form-control" id="notes" name="description" rows="4"><?php echo $record['description']; ?></textarea>
                </div>

                <div class="form-check mb-4">
                    <input class="form-check-input" type="checkbox" id="update_asset_status" name="update_asset_status" value="1">
                    <label class="form-check-label" for="update_asset_status">
                        Update asset status to match maintenance status
                    </label>
                </div>

                <div class="form-actions">
                    <a href="/choims/modules/assets/maintenance.php" class="action-btn action-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Cancel
                    </a>
                    <button type="submit" class="action-btn action-primary" id="submitButton">
                        <i class="fas fa-save me-1"></i> Update Record
                    </button>
                </div>

                <!-- Debug form data (hidden) -->
                <div class="debug-info" style="display: none;">
                    <pre id="formDataDebug"></pre>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Status buttons functionality
    $('.status-btn').on('click', function() {
        // Remove active class from all buttons
        $('.status-btn').removeClass('active');

        // Add active class to clicked button
        $(this).addClass('active');

        // Update hidden input value
        var selectedStatus = $(this).data('status');
        $('#status').val(selectedStatus);

        // Update asset status checkbox based on selected status
        updateAssetStatusCheckbox(selectedStatus);
    });

    // Update asset status checkbox logic
    function updateAssetStatusCheckbox(status) {
        var checkbox = $('#update_asset_status');

        if (status === 'Completed' || status === 'In Progress') {
            checkbox.prop('checked', true);
        } else {
            checkbox.prop('checked', false);
        }
    }

    // Initialize asset status checkbox based on current status
    updateAssetStatusCheckbox($('#status').val());

    // Add animation to form submission
    $('form').on('submit', function(e) {
        // Show loading animation on the submit button
        $('.action-primary').html('<i class="fas fa-spinner fa-spin"></i> Updating...');
        $('.action-primary').prop('disabled', true);

        // Add animation to cards
        $('.edit-card').removeClass('animate__fadeInUp').addClass('animate__fadeOutUp');

        // Continue with form submission
        return true;
    });

    // Add animation to status buttons
    $('.status-btn').hover(
        function() {
            if (!$(this).hasClass('active')) {
                $(this).addClass('animate__animated animate__pulse');
            }
        },
        function() {
            $(this).removeClass('animate__animated animate__pulse');
        }
    );

    // Add animation to the submit button
    $('.action-primary').hover(
        function() { $(this).addClass('animate__animated animate__pulse'); },
        function() { $(this).removeClass('animate__animated animate__pulse'); }
    );
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>