<?php
// <PERSON><PERSON><PERSON> to fix items with IS prefix that were incorrectly imported as fixed assets
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in with appropriate permissions
requireLogin();
if (!hasRole('godmode') && !hasRole('superadmin')) {
    header("Location: /choims/index.php");
    exit;
}

// Initialize variables
$success = [];
$errors = [];
$fixed = 0;
$skipped = 0;

// Process the form submission
if (isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
    try {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // 1. Get all SKUs with IS prefix (IT Supply)
        $skuQuery = "SELECT sku_id, sku_code, sku_name FROM sku_master WHERE sku_code LIKE 'IS-%'";
        $skuResult = mysqli_query($conn, $skuQuery);
        
        if (!$skuResult) {
            throw new Exception("Error fetching IS prefix SKUs: " . mysqli_error($conn));
        }
        
        $isSkus = [];
        while ($row = mysqli_fetch_assoc($skuResult)) {
            $isSkus[$row['sku_id']] = $row;
        }
        
        if (empty($isSkus)) {
            $errors[] = "No SKUs with IS prefix found in the database.";
        } else {
            $success[] = "Found " . count($isSkus) . " SKUs with IS prefix.";
            
            // 2. Update the item_type in sku_master to 'Consumable'
            $skuIds = implode(',', array_keys($isSkus));
            $updateSkuQuery = "UPDATE sku_master SET item_type = 'Consumable' WHERE sku_id IN ($skuIds)";
            $updateSkuResult = mysqli_query($conn, $updateSkuQuery);
            
            if (!$updateSkuResult) {
                throw new Exception("Error updating SKU item types: " . mysqli_error($conn));
            }
            
            $success[] = "Updated all IS prefix SKUs to type 'Consumable'.";
            
            // 3. Get all fixed assets with IS prefix SKUs
            $assetsQuery = "SELECT fa.*, l.location_name 
                           FROM fixed_assets fa 
                           JOIN sku_master sm ON fa.sku_id = sm.sku_id 
                           JOIN locations l ON fa.current_location_id = l.location_id
                           WHERE sm.sku_code LIKE 'IS-%'";
            $assetsResult = mysqli_query($conn, $assetsQuery);
            
            if (!$assetsResult) {
                throw new Exception("Error fetching IS prefix assets: " . mysqli_error($conn));
            }
            
            $isAssets = [];
            while ($row = mysqli_fetch_assoc($assetsResult)) {
                $isAssets[] = $row;
            }
            
            if (empty($isAssets)) {
                $success[] = "No assets with IS prefix SKUs found in fixed_assets table.";
            } else {
                $success[] = "Found " . count($isAssets) . " assets with IS prefix SKUs to convert.";
                
                // 4. Group assets by SKU and location for consolidation
                $consolidatedItems = [];
                foreach ($isAssets as $asset) {
                    $key = $asset['sku_id'] . '_' . $asset['current_location_id'];
                    if (!isset($consolidatedItems[$key])) {
                        $consolidatedItems[$key] = [
                            'sku_id' => $asset['sku_id'],
                            'location_id' => $asset['current_location_id'],
                            'location_name' => $asset['location_name'],
                            'quantity' => 0,
                            'assets' => []
                        ];
                    }
                    $consolidatedItems[$key]['quantity']++;
                    $consolidatedItems[$key]['assets'][] = $asset['asset_id'];
                }
                
                // 5. For each consolidated item, add to consumable_inventory
                foreach ($consolidatedItems as $item) {
                    // Check if inventory record already exists
                    $checkQuery = "SELECT inventory_id FROM consumable_inventory 
                                  WHERE sku_id = ? AND location_id = ?";
                    $checkStmt = mysqli_prepare($conn, $checkQuery);
                    mysqli_stmt_bind_param($checkStmt, 'ii', $item['sku_id'], $item['location_id']);
                    mysqli_stmt_execute($checkStmt);
                    $checkResult = mysqli_stmt_get_result($checkStmt);
                    
                    if (mysqli_num_rows($checkResult) > 0) {
                        // Update existing inventory record
                        $inventoryRow = mysqli_fetch_assoc($checkResult);
                        $inventoryId = $inventoryRow['inventory_id'];
                        
                        $updateQuery = "UPDATE consumable_inventory 
                                       SET current_quantity = current_quantity + ?, 
                                           updated_at = NOW() 
                                       WHERE inventory_id = ?";
                        $updateStmt = mysqli_prepare($conn, $updateQuery);
                        mysqli_stmt_bind_param($updateStmt, 'ii', $item['quantity'], $inventoryId);
                        $updateResult = mysqli_stmt_execute($updateStmt);
                        
                        if (!$updateResult) {
                            throw new Exception("Error updating inventory: " . mysqli_error($conn));
                        }
                        
                        $success[] = "Updated existing inventory for " . $isSkus[$item['sku_id']]['sku_name'] . 
                                    " at " . $item['location_name'] . " with " . $item['quantity'] . " additional units.";
                    } else {
                        // Create new inventory record
                        $insertQuery = "INSERT INTO consumable_inventory 
                                       (sku_id, location_id, current_quantity, min_quantity, 
                                        status, low_stock_threshold, critical_threshold, 
                                        created_at, updated_at) 
                                       VALUES (?, ?, ?, 0, 'Available', 10, 3, NOW(), NOW())";
                        $insertStmt = mysqli_prepare($conn, $insertQuery);
                        mysqli_stmt_bind_param($insertStmt, 'iii', 
                                              $item['sku_id'], $item['location_id'], $item['quantity']);
                        $insertResult = mysqli_stmt_execute($insertStmt);
                        
                        if (!$insertResult) {
                            throw new Exception("Error creating inventory: " . mysqli_error($conn));
                        }
                        
                        $inventoryId = mysqli_insert_id($conn);
                        
                        $success[] = "Created new inventory for " . $isSkus[$item['sku_id']]['sku_name'] . 
                                    " at " . $item['location_name'] . " with " . $item['quantity'] . " units.";
                    }
                    
                    // Add transaction record
                    $transactionQuery = "INSERT INTO consumable_transactions 
                                        (inventory_id, transaction_type, quantity, 
                                         destination_location_id, reference_document, 
                                         remarks, performed_by, transaction_date) 
                                        VALUES (?, 'Stock In', ?, ?, 'Fixed Asset Conversion', 
                                                'Converted from fixed assets', ?, NOW())";
                    $transactionStmt = mysqli_prepare($conn, $transactionQuery);
                    $reference = "Converted from fixed assets: " . implode(", ", $item['assets']);
                    mysqli_stmt_bind_param($transactionStmt, 'iiis', 
                                          $inventoryId, $item['quantity'], $item['location_id'], 
                                          $_SESSION['user_id']);
                    $transactionResult = mysqli_stmt_execute($transactionStmt);
                    
                    if (!$transactionResult) {
                        throw new Exception("Error creating transaction: " . mysqli_error($conn));
                    }
                    
                    // 6. Delete the fixed assets
                    $assetIds = implode(',', $item['assets']);
                    $deleteQuery = "DELETE FROM fixed_assets WHERE asset_id IN ($assetIds)";
                    $deleteResult = mysqli_query($conn, $deleteQuery);
                    
                    if (!$deleteResult) {
                        throw new Exception("Error deleting fixed assets: " . mysqli_error($conn));
                    }
                    
                    $fixed += $item['quantity'];
                }
            }
        }
        
        // Commit the transaction
        mysqli_commit($conn);
        $success[] = "Successfully fixed $fixed items with IS prefix.";
        
    } catch (Exception $e) {
        // Rollback the transaction on error
        mysqli_rollback($conn);
        $errors[] = "Error: " . $e->getMessage();
    }
}
?>

<div class="container-fluid mt-4">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Fix Items with IS Prefix</h6>
        </div>
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5>Errors:</h5>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <h5>Success:</h5>
                    <ul>
                        <?php foreach ($success as $message): ?>
                            <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <p><strong>Summary:</strong></p>
                    <p>Fixed <?php echo $fixed; ?> items with IS prefix that were incorrectly imported as fixed assets.</p>
                    <p>These items will now appear in the Consumable Reports.</p>
                </div>
                
                <div class="mt-3">
                    <a href="/choims/modules/reports/inventory.php" class="btn btn-primary">
                        <i class="fas fa-list"></i> View Consumable Reports
                    </a>
                    <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Return to Dashboard
                    </a>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> Warning</h5>
                    <p>This tool will fix items with IS prefix that were incorrectly imported as fixed assets.</p>
                    <p>It will:</p>
                    <ol>
                        <li>Find all SKUs with IS prefix and update them to type 'Consumable'</li>
                        <li>Find all fixed assets with these SKUs</li>
                        <li>Convert them to consumable inventory items</li>
                        <li>Delete the original fixed asset records</li>
                    </ol>
                    <p><strong>This process cannot be undone.</strong> Make sure you have a backup of your database before proceeding.</p>
                </div>
                
                <form method="post" onsubmit="return confirm('Are you sure you want to convert items with IS prefix from fixed assets to consumables? This cannot be undone.');">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                        <label class="form-check-label" for="confirmCheck">
                            I understand that this will permanently convert items with IS prefix from fixed assets to consumables
                        </label>
                    </div>
                    
                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-danger">Fix IS Prefix Items</button>
                    <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">Cancel</a>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>
