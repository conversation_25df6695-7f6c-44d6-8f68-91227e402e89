<?php
// <PERSON>ript to remove specific users from the database
// Use relative paths
$base_path = dirname(dirname(dirname(__FILE__)));
require_once($base_path . '/includes/database.php');
require_once($base_path . '/includes/functions.php');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is authorized (must be superadmin)
if (!isset($_SESSION['role']) || (strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode')) {
    die("Unauthorized access. You must be a superadmin to run this script.");
}

// Connect to database
try {
    $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

    if (!$conn) {
        die("Database connection failed: " . mysqli_connect_error());
    }

    // User IDs to remove
    $user_ids = [3, 28, 41, 42, 43, 44, 45];

    // HTML header with some basic styling
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>User Removal <PERSON>ript</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #2c3e50; }
            .user-list { margin: 20px 0; }
            .user-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            .success { color: green; }
            .error { color: red; }
            .warning { color: #e67e22; }
            .button {
                display: inline-block;
                padding: 10px 15px;
                background-color: #3498db;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin-right: 10px;
            }
            .button.delete { background-color: #e74c3c; }
            .button:hover { opacity: 0.9; }
            .info-box {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
            }
            .info-box h3 { margin-top: 0; color: #2c3e50; }
        </style>
    </head>
    <body>
        <h1>User Removal Script</h1>";

    // Check if confirmation is received
    if (isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
        // Track results
        $success = [];
        $failed = [];

        // Check for all tables that might have foreign key constraints to users
        $fk_tables = [];
        $fk_query = "
            SELECT TABLE_NAME, COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE REFERENCED_TABLE_NAME = 'users'
            AND REFERENCED_COLUMN_NAME = 'user_id'
            AND TABLE_SCHEMA = '" . DB_NAME . "'
        ";
        $fk_result = mysqli_query($conn, $fk_query);

        if ($fk_result) {
            while ($row = mysqli_fetch_assoc($fk_result)) {
                $fk_tables[] = [
                    'table' => $row['TABLE_NAME'],
                    'column' => $row['COLUMN_NAME']
                ];
            }
        }

        echo "<div class='info-box'>";
        echo "<h3>Foreign Key Information</h3>";

        if (empty($fk_tables)) {
            echo "<p>No foreign key constraints found referencing the users table.</p>";
        } else {
            echo "<p>The following tables have foreign key constraints to the users table:</p>";
            echo "<ul>";
            foreach ($fk_tables as $table) {
                echo "<li>{$table['table']} (column: {$table['column']})</li>";
            }
            echo "</ul>";
            echo "<p>The script will attempt to handle these constraints.</p>";
        }
        echo "</div>";

        // Process each user ID
        foreach ($user_ids as $user_id) {
            // Get user details for logging
            $query = "SELECT username, full_name FROM users WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'i', $user_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            if (!$user) {
                $failed[] = "User ID $user_id - User not found";
                continue;
            }

            // Handle all foreign key constraints
            $deleted_related = [];

            // Process each table with foreign key constraints
            foreach ($fk_tables as $fk_table) {
                $table_name = $fk_table['table'];
                $column_name = $fk_table['column'];

                // Delete related records
                $delete_query = "DELETE FROM `$table_name` WHERE `$column_name` = ?";
                $delete_stmt = mysqli_prepare($conn, $delete_query);

                if ($delete_stmt) {
                    mysqli_stmt_bind_param($delete_stmt, 'i', $user_id);

                    try {
                        if (mysqli_stmt_execute($delete_stmt)) {
                            $affected_rows = mysqli_stmt_affected_rows($delete_stmt);
                            if ($affected_rows > 0) {
                                $deleted_related[] = "$table_name ($affected_rows rows)";
                            }
                        }
                    } catch (Exception $e) {
                        error_log("Failed to delete from $table_name for user $user_id: " . $e->getMessage());
                    }
                }
            }

            // Now try to delete the user
            $delete_query = "DELETE FROM users WHERE user_id = ?";
            $delete_stmt = mysqli_prepare($conn, $delete_query);
            mysqli_stmt_bind_param($delete_stmt, 'i', $user_id);

            if (mysqli_stmt_execute($delete_stmt)) {
                $success_msg = "User ID $user_id - {$user['username']} ({$user['full_name']}) removed successfully";

                // Add information about related records that were deleted
                if (!empty($deleted_related)) {
                    $success_msg .= "<br><small>Related records deleted: " . implode(", ", $deleted_related) . "</small>";
                }

                $success[] = $success_msg;

                // We won't log this deletion since we're deleting the audit logs
            } else {
                // If normal deletion fails, try with foreign key checks disabled
                $error_message = mysqli_error($conn);

                if (strpos($error_message, 'foreign key constraint fails') !== false) {
                    // Disable foreign key checks
                    mysqli_query($conn, "SET FOREIGN_KEY_CHECKS=0");

                    // Try deletion again
                    if (mysqli_stmt_execute($delete_stmt)) {
                        $success_msg = "User ID $user_id - {$user['username']} ({$user['full_name']}) removed successfully (with foreign key checks disabled)";

                        // Add information about related records that were deleted
                        if (!empty($deleted_related)) {
                            $success_msg .= "<br><small>Related records deleted: " . implode(", ", $deleted_related) . "</small>";
                        }

                        $success_msg .= "<br><small class='warning'>Warning: Foreign key checks were disabled for this deletion. Database integrity may be affected.</small>";

                        $success[] = $success_msg;
                    } else {
                        $failed[] = "User ID $user_id - {$user['username']} - Error even with foreign key checks disabled: " . mysqli_error($conn);
                    }

                    // Re-enable foreign key checks
                    mysqli_query($conn, "SET FOREIGN_KEY_CHECKS=1");
                } else {
                    $failed[] = "User ID $user_id - {$user['username']} - Error: " . $error_message;
                }
            }
        }

        // Display results
        echo "<h2>Results:</h2>";

        if (!empty($success)) {
            echo "<h3>Successfully Removed:</h3>";
            echo "<ul>";
            foreach ($success as $message) {
                echo "<li class='success'>$message</li>";
            }
            echo "</ul>";
        }

        if (!empty($failed)) {
            echo "<h3>Failed to Remove:</h3>";
            echo "<ul>";
            foreach ($failed as $message) {
                echo "<li class='error'>$message</li>";
            }
            echo "</ul>";
        }

        echo "<a href='/choims/modules/admin/users.php' class='button'>Return to User Management</a>";
    } else {
        // Show confirmation page with user details
        echo "<p>You are about to remove the following users from the database:</p>";
        echo "<div class='user-list'>";

        foreach ($user_ids as $user_id) {
            $query = "SELECT user_id, username, full_name, role FROM users WHERE user_id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'i', $user_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $user = mysqli_fetch_assoc($result);

            echo "<div class='user-item'>";
            if ($user) {
                echo "<strong>User ID:</strong> {$user['user_id']}<br>";
                echo "<strong>Username:</strong> {$user['username']}<br>";
                echo "<strong>Full Name:</strong> {$user['full_name']}<br>";
                echo "<strong>Role:</strong> {$user['role']}";
            } else {
                echo "<strong>User ID:</strong> $user_id - <span class='error'>User not found</span>";
            }
            echo "</div>";
        }

        echo "</div>";

        // Confirmation form
        echo "<form method='post' action='' onsubmit=\"return confirm('Are you sure you want to delete these users? This action cannot be undone.')\">";
        echo "<input type='hidden' name='confirm' value='yes'>";
        echo "<button type='submit' class='button delete'>Confirm Deletion</button>";
        echo "<a href='/choims/modules/admin/users.php' class='button'>Cancel</a>";
        echo "</form>";
    }

    echo "</body></html>";

} catch (Exception $e) {
    die("Error: " . $e->getMessage());
} finally {
    if (isset($conn) && $conn) {
        mysqli_close($conn);
    }
}
?>
