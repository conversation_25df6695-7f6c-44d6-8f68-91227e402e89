/*
 * Enhanced Modal Styles for Admin Users Page
 * CHOIMS - City Health Office Inventory Management System
 */

/* Modal Base Styles */
.modal-enhanced {
  --modal-animation-duration: 0.3s;
}

.modal-enhanced .modal-dialog {
  max-width: 800px;
}

.modal-enhanced .modal-content {
  border: none;
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  animation: modalFadeIn var(--modal-animation-duration) ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modal Header */
.modal-enhanced .modal-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: 1.75rem 2rem;
  position: relative;
}

.modal-enhanced .modal-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, var(--primary) 0%, transparent 100%);
  opacity: 0.5;
}

.modal-enhanced .modal-title {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modal-enhanced .modal-title i {
  color: var(--white);
  background: var(--primary);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 1rem;
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
}

.modal-enhanced .btn-close {
  background-color: var(--gray-100);
  opacity: 1;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.modal-enhanced .btn-close:hover {
  background-color: var(--gray-200);
  transform: rotate(90deg);
}

/* Modal Body */
.modal-enhanced .modal-body {
  padding: 2rem;
}

/* User Avatar Container */
.user-avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.user-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 2rem;
  box-shadow: 0 8px 16px rgba(46, 125, 50, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 3px solid white;
}

.user-avatar-large::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.user-avatar-large:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 20px rgba(46, 125, 50, 0.25);
}

.user-avatar-large i {
  font-size: 2rem;
}

/* Form Sections */
.form-section {
  background-color: var(--gray-50);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
}

.form-section:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border-color: var(--gray-300);
}

.form-section h6 {
  color: var(--primary);
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-section h6::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, var(--gray-300), transparent);
  margin-left: 0.5rem;
}

/* Form Controls */
.modal-enhanced .form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.modal-enhanced .form-label i {
  color: var(--primary);
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.modal-enhanced .form-control,
.modal-enhanced .form-select {
  border-radius: 10px;
  border: 1px solid var(--gray-300);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s;
  background-color: var(--white);
}

.modal-enhanced .form-control:focus,
.modal-enhanced .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.modal-enhanced .form-text {
  color: var(--gray-500);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

/* Input Group */
.modal-enhanced .input-group {
  position: relative;
}

.modal-enhanced .input-group .form-control {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}

.modal-enhanced .input-group .btn {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border: 1px solid var(--gray-300);
  border-left: none;
  background-color: var(--white);
  color: var(--gray-500);
  padding: 0.75rem 1rem;
  transition: all 0.2s;
}

.modal-enhanced .input-group .btn:hover {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

/* Password Strength Indicator - Removed as requested */

/* Modal Footer */
.modal-enhanced .modal-footer {
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: 1.5rem 2rem;
  gap: 1rem;
}

.modal-enhanced .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 10px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-enhanced .btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
}

.modal-enhanced .btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(46, 125, 50, 0.25);
}

.modal-enhanced .btn-secondary {
  background-color: var(--gray-200);
  border-color: var(--gray-200);
  color: var(--gray-700);
}

.modal-enhanced .btn-secondary:hover {
  background-color: var(--gray-300);
  border-color: var(--gray-300);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.05);
}

/* Required Field Indicator */
.required-field .form-label::after {
  content: '*';
  color: var(--danger);
  margin-left: 0.25rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .modal-enhanced .modal-header,
  .modal-enhanced .modal-body,
  .modal-enhanced .modal-footer {
    padding: 1.25rem;
  }

  .form-section {
    padding: 1.25rem;
  }

  .user-avatar-large {
    width: 70px;
    height: 70px;
    font-size: 1.75rem;
  }

  .user-avatar-large i {
    font-size: 1.75rem;
  }

  .modal-enhanced .modal-title {
    font-size: 1.1rem;
  }

  .modal-enhanced .modal-title i {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
  }

  .modal-enhanced .btn {
    padding: 0.6rem 1.25rem;
  }
}
