<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in and has appropriate role
requireLogin();
requireRole('GodMode', 'Superadmin');

// Initialize variables
$location_name = '';
$description = '';
$location_type = '';
$error_message = '';
$success_message = '';

// Process form submission for adding/editing location
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_location'])) {
    // Get form data
    $location_id = isset($_POST['location_id']) ? (int)$_POST['location_id'] : null;
    $location_name = sanitizeInput($_POST['location_name']);
    $description = sanitizeInput($_POST['description']);
    $location_type = sanitizeInput($_POST['location_type']);
    
    // Validate form data
    $errors = [];
    
    if (empty($location_name)) {
        $errors[] = "Location name is required.";
    }
    
    if (empty($location_type)) {
        $errors[] = "Location type is required.";
    }
    
    // If there are no errors, save the location
    if (empty($errors)) {
        if ($location_id) {
            // Update existing location
            $query = "
                UPDATE locations 
                SET location_name = ?, description = ?, location_type = ? 
                WHERE location_id = ?
            ";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'sssi', $location_name, $description, $location_type, $location_id);
            
            if (mysqli_stmt_execute($stmt)) {
                // Create audit log
                createAuditLog(
                    $_SESSION['user_id'], 
                    "Location updated", 
                    'location', 
                    $location_id, 
                    null, 
                    json_encode([
                        'location_name' => $location_name,
                        'description' => $description,
                        'location_type' => $location_type
                    ])
                );
                
                $success_message = "Location updated successfully.";
                
                // Reset form values
                $location_name = '';
                $description = '';
                $location_type = '';
            } else {
                $error_message = "Error updating location: " . mysqli_error($conn);
            }
        } else {
            // Create new location
            $query = "
                INSERT INTO locations (location_name, description, location_type, created_at) 
                VALUES (?, ?, ?, NOW())
            ";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'sss', $location_name, $description, $location_type);
            
            if (mysqli_stmt_execute($stmt)) {
                $new_location_id = mysqli_insert_id($conn);
                
                // Create audit log
                createAuditLog(
                    $_SESSION['user_id'], 
                    "Location created", 
                    'location', 
                    $new_location_id, 
                    null, 
                    json_encode([
                        'location_name' => $location_name,
                        'description' => $description,
                        'location_type' => $location_type
                    ])
                );
                
                $success_message = "Location created successfully.";
                
                // Reset form values
                $location_name = '';
                $description = '';
                $location_type = '';
            } else {
                $error_message = "Error creating location: " . mysqli_error($conn);
            }
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
}

// Delete location
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_location'])) {
    $location_id = (int)$_POST['location_id'];
    
    // Check if location is in use (has assets or inventory)
    $checkQuery = "
        SELECT 
            (SELECT COUNT(*) FROM fixed_assets WHERE current_location_id = ?) +
            (SELECT COUNT(*) FROM consumable_inventory WHERE location_id = ?) as total_count
    ";
    $stmt = mysqli_prepare($conn, $checkQuery);
    mysqli_stmt_bind_param($stmt, 'ii', $location_id, $location_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $row = mysqli_fetch_assoc($result);
    
    if ($row['total_count'] > 0) {
        $error_message = "Cannot delete location because it has associated assets or inventory items.";
    } else {
        // Delete the location
        $query = "DELETE FROM locations WHERE location_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'i', $location_id);
        
        if (mysqli_stmt_execute($stmt)) {
            // Create audit log
            createAuditLog(
                $_SESSION['user_id'], 
                "Location deleted", 
                'location', 
                $location_id, 
                null, 
                null
            );
            
            $success_message = "Location deleted successfully.";
        } else {
            $error_message = "Error deleting location: " . mysqli_error($conn);
        }
    }
}

// Get all locations for display
$query = "SELECT * FROM locations ORDER BY location_name";
$result = mysqli_query($conn, $query);
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Location Management</h1>
        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#locationModal">
            <i class="fas fa-plus"></i> Add New Location
        </button>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">System Locations</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="locationsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Location Name</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($location = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td><?php echo $location['location_id']; ?></td>
                                    <td><?php echo htmlspecialchars($location['location_name']); ?></td>
                                    <td><?php echo htmlspecialchars($location['location_type']); ?></td>
                                    <td><?php echo htmlspecialchars($location['description'] ?: 'N/A'); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm editLocationBtn" 
                                                data-location='<?php echo json_encode($location); ?>'>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" action="" style="display:inline;" 
                                              onsubmit="return confirm('Are you sure you want to delete this location? This action cannot be undone.');">
                                            <input type="hidden" name="location_id" value="<?php echo $location['location_id']; ?>">
                                            <input type="hidden" name="delete_location" value="1">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center">No locations found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Location Modal -->
<div class="modal fade" id="locationModal" tabindex="-1" role="dialog" aria-labelledby="locationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="locationModalLabel">Add New Location</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="locationForm" method="POST" action="">
                    <input type="hidden" id="location_id" name="location_id" value="">
                    <div class="form-group">
                        <label for="location_name">Location Name *</label>
                        <input type="text" class="form-control" id="location_name" name="location_name" required>
                    </div>
                    <div class="form-group">
                        <label for="location_type">Location Type *</label>
                        <select class="form-control" id="location_type" name="location_type" required>
                            <option value="">Select Type</option>
                            <option value="Main Office">Main Office</option>
                            <option value="Department">Department</option>
                            <option value="Health Center">Health Center</option>
                            <option value="Satellite Office">Satellite Office</option>
                            <option value="Warehouse">Warehouse</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <input type="hidden" name="save_location" value="1">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" form="locationForm" class="btn btn-primary">Save Location</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#locationsTable').DataTable({
        pageLength: 25
    });
    
    // Handle edit location button
    const editButtons = document.querySelectorAll('.editLocationBtn');
    editButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const locationData = JSON.parse(this.getAttribute('data-location'));
            
            // Set modal title
            document.getElementById('locationModalLabel').textContent = 'Edit Location';
            
            // Populate form fields
            document.getElementById('location_id').value = locationData.location_id;
            document.getElementById('location_name').value = locationData.location_name;
            document.getElementById('location_type').value = locationData.location_type;
            document.getElementById('description').value = locationData.description || '';
            
            // Show the modal
            $('#locationModal').modal('show');
        });
    });
    
    // Reset form when adding new location
    const addButton = document.querySelector('[data-target="#locationModal"]');
    addButton.addEventListener('click', function() {
        document.getElementById('locationModalLabel').textContent = 'Add New Location';
        document.getElementById('locationForm').reset();
        document.getElementById('location_id').value = '';
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>