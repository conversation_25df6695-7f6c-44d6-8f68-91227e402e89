/* Modern Transfers Create Page Styling */
:root {
  /* Colors - <PERSON> Green Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --secondary: #81C784;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Page Header */
.page-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  color: var(--primary);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-bottom: 0;
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: var(--space-5);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.25rem 1rem;
}

.form-floating > label {
  padding: 1rem;
  color: var(--gray-500);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
  color: var(--primary);
}

.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
}

textarea.form-control {
  min-height: 120px;
}

.form-floating > textarea.form-control {
  height: 120px;
}

.input-group-text {
  background-color: var(--primary-bg);
  border: 1px solid var(--gray-300);
  color: var(--primary-dark);
  border-radius: var(--radius-md);
}

.invalid-feedback {
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.btn-success:hover {
  background-color: #0ca678;
  border-color: #0ca678;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-secondary {
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-lg {
  padding: 0.75rem 1.75rem;
  font-size: 1rem;
}

/* Alerts */
.alert {
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  margin-right: var(--space-4);
}

.alert-success .alert-icon {
  background-color: rgba(16, 185, 129, 0.2);
}

.alert-danger .alert-icon {
  background-color: rgba(239, 68, 68, 0.2);
}

.alert-warning .alert-icon {
  background-color: rgba(245, 158, 11, 0.2);
}

.alert-info .alert-icon {
  background-color: rgba(59, 130, 246, 0.2);
}

/* Select2 Styling */
.select2-container--default .select2-selection--single {
  height: calc(3.5rem + 2px);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--dark);
  line-height: 2rem;
  padding-left: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 3.5rem;
  right: 0.75rem;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--primary);
}

.select2-dropdown {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
}

.select2-container--open .select2-dropdown--below {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.select2-search--dropdown .select2-search__field {
  padding: 0.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
}

/* Select2 Validation Styling */
.is-invalid + .select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--single.is-invalid {
  border-color: var(--danger);
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.select2-container--default.select2-container--focus .select2-selection--single:not(.is-invalid):focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Custom Transfer Page Styling */
.transfer-page {
  max-width: 1200px;
  margin: 0 auto;
}

.transfer-section {
  margin-bottom: var(--space-5);
}

.transfer-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.transfer-section-title i {
  color: var(--primary);
  font-size: 1.2rem;
}

.transfer-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  margin-bottom: var(--space-4);
  overflow: hidden;
  position: relative;
}

.transfer-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.transfer-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary), var(--primary-light));
  transition: width 0.3s ease;
}

.transfer-card:hover::before {
  width: 6px;
}

.transfer-card-body {
  padding: var(--space-4);
}

.transfer-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.transfer-card-title i {
  color: var(--primary);
  font-size: 1.1rem;
}

/* Asset Selection Card */
.asset-selection-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  margin-bottom: var(--space-4);
  overflow: hidden;
  position: relative;
}

.asset-selection-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary), var(--primary-light));
}

.asset-selection-card:hover {
  box-shadow: var(--shadow-md);
}

.asset-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background-color: var(--primary-bg);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
}

/* Dashboard header section */
.dashboard-header {
  position: relative;
  background: var(--white);
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 1.5rem 2rem 1rem;
  border-radius: 0 0 20px 20px;
  color: var(--dark);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border-bottom: 1px solid var(--primary-soft);
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.dashboard-header::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
  pointer-events: none;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.dashboard-header .title-container {
  display: flex;
  align-items: center;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin: 0;
  letter-spacing: -0.025em;
}

.dashboard-title-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
}

.dashboard-title-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3), rgba(0, 0, 0, 0.05));
  pointer-events: none;
}

.dashboard-actions {
  display: flex;
  gap: 0.75rem;
}

.dashboard-date {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin: 0;
  position: relative;
  z-index: 1;
}

.dashboard-date i {
  margin-right: 0.5rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  color: var(--primary);
  background: var(--primary-ultra-soft);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid var(--primary-soft);
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 2px 4px rgba(46, 125, 50, 0.1);
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  background: var(--primary-bg);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(46, 125, 50, 0.15);
  color: var(--primary-dark);
}

.action-btn:hover::after {
  opacity: 1;
}

.action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.action-btn i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.action-primary {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* Filter Card Styling */
.filter-card {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.1), 0 6px 12px rgba(46, 125, 50, 0.08);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
  position: relative;
}

.filter-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.filter-card-header {
  padding: 1.5rem 1.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--primary-soft);
  background-color: rgba(46, 125, 50, 0.03);
  background-image: linear-gradient(135deg, rgba(46, 125, 50, 0.05), rgba(46, 125, 50, 0.01));
  position: relative;
  overflow: hidden;
}

.filter-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 115, 223, 0.08) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: 0;
}

.filter-card-header::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(78, 115, 223, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
}

.filter-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2e384d;
  display: flex;
  align-items: center;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.filter-title i {
  margin-right: 0.85rem;
  color: var(--white);
  font-size: 1.2em;
  background: var(--primary-gradient);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(46, 125, 50, 0.25), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transform: rotate(-5deg);
}

.filter-title i::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3), rgba(0, 0, 0, 0.05));
  border-radius: 12px;
  z-index: -1;
}

.filter-card-body {
  padding: 1.75rem;
  background-color: rgba(255, 255, 255, 0.8);
}

/* Records Card Styling */
.records-card {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.1), 0 6px 12px rgba(46, 125, 50, 0.08);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
  position: relative;
}

.records-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.records-card-header {
  padding: 1.5rem 1.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--primary-soft);
  background-color: rgba(46, 125, 50, 0.03);
  background-image: linear-gradient(135deg, rgba(46, 125, 50, 0.05), rgba(46, 125, 50, 0.01));
  position: relative;
  overflow: hidden;
}

.records-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(78, 115, 223, 0.08) 0%, rgba(255, 255, 255, 0) 60%);
  z-index: 0;
}

.records-card-header::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(78, 115, 223, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  z-index: 0;
}

.records-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2e384d;
  display: flex;
  align-items: center;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.records-title i {
  margin-right: 0.85rem;
  color: var(--white);
  font-size: 1.2em;
  background: var(--primary-gradient);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(46, 125, 50, 0.25), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transform: rotate(-5deg);
}

.records-title i::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3), rgba(0, 0, 0, 0.05));
  border-radius: 12px;
  z-index: -1;
}

.records-card-body {
  padding: 1.75rem;
  background-color: rgba(255, 255, 255, 0.8);
}

/* Filter Actions */
.filter-actions {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.filter-buttons-container {
  display: flex;
  gap: 1rem;
  background-color: #f8f9fc;
  padding: 0.5rem;
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.filter-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  min-width: 160px;
}

.filter-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.filter-btn i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.filter-btn-primary {
  color: var(--white);
  background: var(--primary-gradient);
  box-shadow: 0 10px 20px rgba(46, 125, 50, 0.25), 0 6px 6px rgba(46, 125, 50, 0.22), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
}

.filter-btn-primary:hover {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  transform: translateY(-3px);
  box-shadow: 0 15px 25px rgba(46, 125, 50, 0.3), 0 10px 10px rgba(46, 125, 50, 0.2), inset 0 -2px 5px rgba(0, 0, 0, 0.2);
}

.filter-btn-primary:hover::after {
  opacity: 1;
}

.filter-btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(46, 125, 50, 0.3), inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.filter-btn-secondary {
  color: var(--primary-dark);
  background: var(--white);
  border: 1px solid var(--primary-soft);
  box-shadow: 0 4px 6px rgba(46, 125, 50, 0.05);
}

.filter-btn-secondary:hover {
  background-color: var(--primary-ultra-soft);
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(46, 125, 50, 0.1);
}

.filter-btn-secondary:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Alert Message Styling */
.alert-message {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  background-color: #fff;
  border: 1px solid transparent;
}

.alert-message.alert-success {
  background-color: var(--primary-ultra-soft);
  border-color: var(--primary-soft);
  color: var(--primary-dark);
}

.alert-message.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
}

.alert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  margin-right: 1rem;
  flex-shrink: 0;
}

.alert-message.alert-success .alert-icon {
  color: #2e7d32;
}

.alert-message.alert-danger .alert-icon {
  color: #842029;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.alert-description {
  font-size: 0.9rem;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.25rem;
  line-height: 1;
  padding: 0;
  margin-left: 1rem;
  cursor: pointer;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}

.alert-close:hover {
  opacity: 1;
}

/* Transfer Flow Visualization */
.transfer-flow {
  position: relative;
  padding: var(--space-4) 0;
}

.transfer-location-box {
  background-color: var(--white);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  padding: var(--space-4);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.transfer-arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  transition: all 0.3s ease;
  border-radius: 50%;
  background-color: rgba(46, 125, 50, 0.05);
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.transfer-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.transfer-arrow-container.active {
  background-color: rgba(46, 125, 50, 0.15);
  transform: scale(1.1);
}

.transfer-arrow-container.error {
  background-color: rgba(239, 68, 68, 0.15);
}

.transfer-arrow i {
  transition: all 0.3s ease;
}

.transfer-location-box:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.transfer-location-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gray-500);
  margin-bottom: var(--space-2);
}

.transfer-location-icon {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background-color: var(--primary-bg);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
}

.transfer-arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 0;
}

.transfer-arrow {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background-color: var(--white);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.transfer-arrow i {
  color: var(--primary);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.transfer-arrow.active {
  background-color: var(--primary-bg);
  transform: scale(1.1);
}

.transfer-arrow.active i {
  color: var(--primary);
  animation: pulse 1.5s infinite;
}

.transfer-arrow.error {
  background-color: rgba(239, 68, 68, 0.1);
}

.transfer-arrow.error i {
  color: var(--danger);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Notes Section */
.notes-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  margin-bottom: var(--space-4);
  overflow: hidden;
  position: relative;
}

.notes-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--info), var(--primary));
}

.notes-card:hover {
  box-shadow: var(--shadow-md);
}

.notes-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: var(--space-5);
}

.btn-cancel {
  background-color: var(--white);
  color: var(--gray-500);
  border: 1px solid var(--gray-300);
  transition: all 0.3s ease;
}

.btn-cancel:hover {
  background-color: var(--gray-100);
  color: var(--gray-700);
  transform: translateY(-2px);
}

.btn-submit-transfer {
  background: linear-gradient(to right, var(--primary), var(--primary-light));
  color: var(--white);
  border: none;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(46, 125, 50, 0.2);
}

.btn-submit-transfer:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 8px rgba(46, 125, 50, 0.3);
}

.btn-submit-transfer:active {
  transform: translateY(-1px);
}

/* Form Help Text */
.form-help-text {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  margin-top: var(--space-2);
  color: var(--gray-500);
  font-size: 0.8rem;
}

.form-help-text i {
  color: var(--info);
  font-size: 0.9rem;
  margin-top: 2px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .transfer-flow {
    flex-direction: column;
  }

  .transfer-arrow-container {
    transform: rotate(90deg);
    margin: var(--space-3) 0;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--space-4);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .card-header, .card-body {
    padding: var(--space-4);
  }

  .action-buttons {
    flex-direction: column-reverse;
    gap: var(--space-3);
  }

  .btn-submit-transfer, .btn-cancel {
    width: 100%;
  }
}
