/* Modern UI Styles for Inventory Additions Report - Bold & Creative Version */
:root {
  /* Modern Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --accent: #FF5722;
  --accent-light: #FF8A65;
  --success: #00C853;
  --warning: #FFD600;
  --danger: #F44336;
  --info: #00B0FF;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Gradients */
  --green-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
  --accent-gradient: linear-gradient(135deg, var(--accent), var(--accent-light));
  --card-gradient: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
  --shadow: 0 4px 12px rgba(0,0,0,0.08);
  --shadow-md: 0 8px 24px rgba(0,0,0,0.12);
  --shadow-lg: 0 16px 32px rgba(0,0,0,0.15);
  --shadow-accent: 0 5px 15px rgba(255, 87, 34, 0.3);
  --shadow-primary: 0 5px 15px rgba(46, 125, 50, 0.3);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.5rem;
  --radius-xl: 2rem;
  --radius-full: 9999px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  --space-10: 4rem;

  /* Transitions */
  --transition-fast: all 0.05s cubic-bezier(0.4, 0, 0.2, 1);
  --transition: all 0.08s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.12s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Page Background with Pattern */
body {
  background-color: var(--light);
  background-image:
    radial-gradient(var(--gray-200) 1px, transparent 1px),
    radial-gradient(var(--gray-200) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Container with Glass Effect */
.container-fluid {
  padding: var(--space-6) var(--space-5);
  max-width: 1400px;
  margin: 0 auto;
}

/* Modern Header with Accent */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  position: relative;
  padding-bottom: var(--space-4);
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
}

.page-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: var(--primary-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  position: relative;
}

.page-title i {
  font-size: 2rem;
  background: var(--green-gradient);
  color: white;
  padding: var(--space-3);
  border-radius: var(--radius);
  box-shadow: var(--shadow-primary);
}

.page-actions {
  display: flex;
  gap: var(--space-3);
}

/* Modern Button Styles */
.btn {
  font-weight: 600;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius);
  transition: var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  border: none;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(100%);
  transition: var(--transition-fast);
  z-index: -1;
}

.btn:hover::before {
  transform: translateY(0);
}

.btn-primary {
  background: var(--green-gradient);
  color: white;
}

.btn-primary:hover {
  box-shadow: var(--shadow-primary);
  transform: translateY(-2px);
}

.btn-accent {
  background: var(--accent-gradient);
  color: white;
}

.btn-accent:hover {
  box-shadow: var(--shadow-accent);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  border: 2px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background: var(--primary-bg);
  transform: translateY(-2px);
}

/* Modern Tabs */
.nav-tabs {
  border: none;
  background: var(--white);
  border-radius: var(--radius-full);
  padding: var(--space-1);
  display: inline-flex;
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow);
}

.nav-tabs .nav-item {
  margin: 0;
}

.nav-tabs .nav-link {
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-2) var(--space-5);
  font-weight: 600;
  color: var(--gray-500);
  transition: var(--transition);
}

.nav-tabs .nav-link:hover {
  color: var(--primary);
}

.nav-tabs .nav-link.active {
  background: var(--green-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.nav-tabs .nav-link i {
  margin-right: var(--space-2);
}

/* Stats Cards with 3D Effect */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-5);
  margin-bottom: var(--space-6);
}

.stat-card {
  background: var(--white);
  border-radius: var(--radius-md);
  padding: var(--space-5);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--card-gradient);
  z-index: -1;
}

.stat-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--green-gradient);
  transform: scaleX(0);
  transform-origin: left;
  transition: var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-4px) rotate(0.5deg);
  box-shadow: var(--shadow-lg);
}

.stat-card:hover::after {
  transform: scaleX(1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
}

.stat-icon i {
  font-size: 1.8rem;
  color: var(--white);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: var(--space-1);
  background: var(--green-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Stat Card Colors */
.stat-primary .stat-icon {
  background: var(--green-gradient);
}

.stat-success .stat-icon {
  background: linear-gradient(135deg, var(--success), #69F0AE);
}

.stat-success .stat-value {
  background: linear-gradient(135deg, var(--success), #69F0AE);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-warning .stat-icon {
  background: linear-gradient(135deg, var(--warning), #FFFF00);
}

.stat-warning .stat-value {
  background: linear-gradient(135deg, var(--warning), #FFFF00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-info .stat-icon {
  background: linear-gradient(135deg, var(--info), #80D8FF);
}

.stat-info .stat-value {
  background: linear-gradient(135deg, var(--info), #80D8FF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Modern Cards with Glass Effect */
.card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: none;
  overflow: hidden;
  margin-bottom: var(--space-6);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background: transparent;
  padding: var(--space-5);
  border-bottom: 1px solid rgba(209, 213, 219, 0.5);
  position: relative;
}

.card-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 5px;
  background: var(--accent-gradient);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin: 0;
  display: flex;
  align-items: center;
}

.card-title i {
  margin-right: var(--space-3);
  color: var(--accent);
  font-size: 1.4rem;
}

.card-body {
  padding: var(--space-5);
}

/* Filter Toggle Button */
.filter-toggle {
  width: 100%;
  text-align: left;
  background: transparent;
  border: none;
  font-weight: 700;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition);
  padding: 0;
}

.filter-toggle:hover {
  color: var(--accent);
}

.filter-toggle i {
  transition: var(--transition);
}

.filter-toggle:not(.collapsed) i {
  transform: rotate(180deg);
}

/* Modern Form Elements */
.form-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--primary-dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
}

.form-label i {
  margin-right: var(--space-2);
  color: var(--primary);
}

.form-control, .form-select {
  border-radius: var(--radius);
  border: 2px solid var(--gray-200);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition);
  height: auto;
  font-size: 0.95rem;
  background-color: rgba(255, 255, 255, 0.8);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
  background-color: white;
}

.form-control::placeholder {
  color: var(--gray-400);
}

/* Modern Table Styling */
.table-container {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  background: white;
}

.table {
  width: 100%;
  margin-bottom: 0;
  color: var(--dark);
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  font-weight: 700;
  padding: var(--space-4);
  color: var(--primary-dark);
  white-space: nowrap;
  background-color: rgba(46, 125, 50, 0.08);
  border: none;
  position: relative;
}

.table th:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--primary-light);
  opacity: 0.5;
}

.table td {
  padding: var(--space-4);
  border: none;
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(46, 125, 50, 0.02);
}

.table-hover tbody tr {
  transition: var(--transition);
}

.table-hover tbody tr:hover {
  background-color: rgba(46, 125, 50, 0.05);
  transform: scale(1.01);
  box-shadow: var(--shadow-sm);
  z-index: 1;
  position: relative;
}

/* Data Badges */
.badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-success {
  background-color: rgba(0, 200, 83, 0.1);
  color: var(--success);
}

.badge-warning {
  background-color: rgba(255, 214, 0, 0.1);
  color: #FF8F00;
}

.badge-info {
  background-color: rgba(0, 176, 255, 0.1);
  color: var(--info);
}

/* Chart Styling */
.chart-container {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  box-shadow: var(--shadow);
  height: 350px;
  position: relative;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
}

.chart-title i {
  margin-right: var(--space-2);
  color: var(--accent);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-fade-in {
  animation: fadeIn 0.1s ease forwards;
}

.animate-slide-up {
  animation: slideInUp 0.1s ease forwards;
}

.animate-pulse {
  animation: pulse 0.5s infinite;
}

/* Animation Speed Classes */
.animate-fast {
  animation-duration: 0.08s !important;
}

.animate-faster {
  animation-duration: 0.05s !important;
}

/* Animation Delays */
.delay-1 { animation-delay: 0.01s; }
.delay-2 { animation-delay: 0.02s; }
.delay-3 { animation-delay: 0.03s; }
.delay-4 { animation-delay: 0.04s; }
.delay-5 { animation-delay: 0.05s; }

/* Responsive Adjustments */
@media (max-width: 992px) {
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .card-body {
    padding: var(--space-4);
  }

  .page-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .page-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .nav-tabs {
    width: 100%;
    justify-content: center;
  }

  .nav-tabs .nav-link {
    padding: var(--space-2) var(--space-3);
  }

  .card-header, .card-body {
    padding: var(--space-3);
  }

  .table th, .table td {
    padding: var(--space-2);
  }
}
