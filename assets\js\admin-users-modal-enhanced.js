/**
 * Enhanced User Modal Functionality
 * CHOIMS - City Health Office Inventory Management System
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize password toggle functionality
    initPasswordToggle();

    // Initialize password strength meter
    initPasswordStrengthMeter();

    // Initialize avatar preview
    initAvatarPreview();

    // Add form validation
    initFormValidation();

    // Add animation effects
    initAnimationEffects();
});

/**
 * Initialize password toggle visibility
 */
function initPasswordToggle() {
    // Handle both .password-toggle and .toggle-password classes for consistency
    const toggleButtons = document.querySelectorAll('.password-toggle, .toggle-password');

    toggleButtons.forEach(button => {
        // Remove any existing event listeners to prevent duplicates
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        newButton.addEventListener('click', function() {
            // Find the password field - handle both structures
            let passwordField;
            if (this.classList.contains('password-toggle')) {
                passwordField = this.closest('.input-group').querySelector('input');
            } else {
                passwordField = this.previousElementSibling;
            }

            const icon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                this.setAttribute('title', 'Hide password');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                this.setAttribute('title', 'Show password');
            }
        });
    });

    // Log for debugging
    console.log('Password toggle initialized for', toggleButtons.length, 'buttons');
}

/**
 * Initialize password strength meter - simplified version
 */
function initPasswordStrengthMeter() {
    // Password strength meter removed as requested
}

/**
 * Initialize avatar preview based on name
 */
function initAvatarPreview() {
    const firstNameField = document.getElementById('first_name');
    const lastNameField = document.getElementById('last_name');
    const avatarElement = document.querySelector('.user-avatar-large');

    if (!firstNameField || !lastNameField || !avatarElement) return;

    function updateAvatar() {
        const firstName = firstNameField.value.trim();
        const lastName = lastNameField.value.trim();

        if (firstName || lastName) {
            const firstInitial = firstName ? firstName[0].toUpperCase() : '';
            const lastInitial = lastName ? lastName[0].toUpperCase() : '';
            avatarElement.textContent = firstInitial + lastInitial;
        } else {
            avatarElement.innerHTML = '<i class="fas fa-user"></i>';
        }
    }

    firstNameField.addEventListener('input', updateAvatar);
    lastNameField.addEventListener('input', updateAvatar);

    // Initialize on page load
    updateAvatar();
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    const addUserForm = document.getElementById('addUserForm');
    if (!addUserForm) return;

    addUserForm.addEventListener('submit', function(e) {
        let isValid = true;

        // Get all required fields
        const requiredFields = this.querySelectorAll('.required-field input, .required-field select');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');

                // Create or update feedback message
                let feedback = field.nextElementSibling;
                if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                    feedback = document.createElement('div');
                    feedback.classList.add('invalid-feedback');
                    field.parentNode.insertBefore(feedback, field.nextSibling);
                }
                feedback.textContent = 'This field is required';
            } else {
                field.classList.remove('is-invalid');
                const feedback = field.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = '';
                }
            }
        });

        // Validate email format
        const emailField = document.getElementById('email');
        if (emailField && emailField.value.trim()) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailField.value.trim())) {
                isValid = false;
                emailField.classList.add('is-invalid');

                let feedback = emailField.nextElementSibling;
                if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                    feedback = document.createElement('div');
                    feedback.classList.add('invalid-feedback');
                    emailField.parentNode.insertBefore(feedback, emailField.nextSibling);
                }
                feedback.textContent = 'Please enter a valid email address';
            }
        }

        // Password strength validation removed as requested

        if (!isValid) {
            e.preventDefault();
            e.stopPropagation();
        }
    });

    // Clear validation on input
    const formInputs = addUserForm.querySelectorAll('input, select');
    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
            const feedback = this.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.textContent = '';
            }
        });
    });
}

/**
 * Initialize animation effects
 */
function initAnimationEffects() {
    // Add staggered animation to form sections
    const formSections = document.querySelectorAll('.form-section');
    formSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        section.style.transitionDelay = `${0.1 + (index * 0.1)}s`;

        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, 100);
    });

    // Add subtle hover effect to form controls
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            const parent = this.closest('.mb-3');
            if (parent) {
                parent.style.transform = 'translateY(-2px)';
                parent.style.transition = 'transform 0.2s ease';
            }
        });

        control.addEventListener('blur', function() {
            const parent = this.closest('.mb-3');
            if (parent) {
                parent.style.transform = '';
            }
        });
    });
}
