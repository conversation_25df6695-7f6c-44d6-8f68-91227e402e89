<?php
// Set Philippines timezone
date_default_timezone_set('Asia/Manila');

// Database connection settings
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'choims');

// Also define the constants used in includes/database.php for compatibility
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');

// Function to get database connection
function getDBConnection() {
    static $conn = null;
    
    if ($conn === null) {
        // Establish database connection
        $conn = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
        
        // Check if connection was successful
        if (!$conn) {
            die("Connection failed: " . mysqli_connect_error());
        }
        
        // Set character set
        mysqli_set_charset($conn, "utf8mb4");
        
        // Set SQL mode to prevent zero dates
        mysqli_query($conn, "SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION'");
    }
    
    return $conn;
}

// Establish database connection for backward compatibility
$conn = getDBConnection();
?>