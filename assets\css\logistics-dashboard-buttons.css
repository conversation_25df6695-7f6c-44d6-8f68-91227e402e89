/* Logistics Dashboard - Modern Button Styling */

/* Apply the action-btn styling to all buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  color: var(--white);
  background: var(--primary-gradient);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 20px rgba(46, 125, 50, 0.25), 0 6px 6px rgba(46, 125, 50, 0.22), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.btn:hover {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  transform: translateY(-3px);
  box-shadow: 0 15px 25px rgba(46, 125, 50, 0.3), 0 10px 10px rgba(46, 125, 50, 0.2), inset 0 -2px 5px rgba(0, 0, 0, 0.2);
  color: var(--white);
}

.btn:hover::after {
  opacity: 1;
}

.btn:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(78, 115, 223, 0.3), inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

/* Button size variations */
.btn-sm {
  padding: 0.6rem 1.25rem;
  font-size: 0.85rem;
  border-radius: 0.6rem;
}

.btn-sm i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* Button color variations */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--white);
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: var(--white);
}

.btn-info {
  background: linear-gradient(135deg, #03A9F4, #0288D1);
  color: var(--white);
}

.btn-warning {
  background: linear-gradient(135deg, #FFC107, #FFA000);
  color: var(--white);
}

.btn-danger {
  background: linear-gradient(135deg, #F44336, #D32F2F);
  color: var(--white);
}

.btn-secondary {
  background: linear-gradient(135deg, #78909C, #546E7A);
  color: var(--white);
}

.btn-outline-success {
  background: transparent;
  border: 2px solid var(--light-green);
  color: var(--primary-green);
  box-shadow: 0 4px 6px rgba(46, 125, 50, 0.1);
}

.btn-outline-success:hover {
  background: var(--primary-gradient);
  color: var(--white);
  border-color: transparent;
}

/* Chart action buttons */
.chart-actions .btn {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  border-radius: 0.5rem;
  background: rgba(46, 125, 50, 0.1);
  color: var(--primary-green);
  box-shadow: 0 2px 4px rgba(46, 125, 50, 0.1);
}

.chart-actions .btn.active {
  background: var(--primary-gradient);
  color: var(--white);
}

/* Dropdown styling */
.dropdown-menu {
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: none;
  padding: 0.5rem;
}

.dropdown-item {
  border-radius: 0.5rem;
  padding: 0.6rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--primary-green);
}

.dropdown-item i {
  margin-right: 0.5rem;
  color: var(--primary-green);
}
