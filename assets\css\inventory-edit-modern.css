/* Modern Inventory Edit Page Styling - Bold & Creative */
:root {
  /* Bold Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --accent: #FF5722;
  --accent-light: #FF8A65;
  --success: #00C853;
  --warning: #FFD600;
  --danger: #F44336;
  --info: #00B0FF;
  --white: #ffffff;
  --dark: #1e293b;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Gradients */
  --primary-gradient: linear-gradient(135deg, var(--primary), var(--primary-light));
  --success-gradient: linear-gradient(135deg, var(--success), #69F0AE);
  --danger-gradient: linear-gradient(135deg, var(--danger), #FF8A80);
  --warning-gradient: linear-gradient(135deg, var(--warning), #FFFF00);

  /* Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 6px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 15px 25px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15);

  /* 3D Effects */
  --shadow-3d: 0 8px 24px rgba(0, 0, 0, 0.12), 0 16px 32px rgba(0, 0, 0, 0.1);
  --shadow-3d-hover: 0 16px 32px rgba(0, 0, 0, 0.15), 0 24px 48px rgba(0, 0, 0, 0.12);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.5rem;
  --radius-xl: 2rem;
  --radius-full: 9999px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  --space-10: 4rem;

  /* Transitions */
  --transition-fast: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Page Background with Pattern */
body {
  background-color: var(--light);
  background-image:
    radial-gradient(var(--gray-200) 1px, transparent 1px),
    radial-gradient(var(--gray-200) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Page Container */
.container-fluid {
  padding: var(--space-5) var(--space-4);
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Header */
.edit-header {
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-3d);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
  animation: scaleIn 0.5s ease-out;
}

.edit-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05), rgba(76, 175, 80, 0.1));
  z-index: 0;
}

.edit-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--primary-gradient);
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
}

.title-container {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.edit-title-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.8rem;
  box-shadow: var(--shadow-md);
  animation: pulse 3s infinite;
}

.edit-title {
  font-size: 2rem;
  font-weight: 800;
  color: var(--primary-dark);
  margin-bottom: 0;
  line-height: 1.3;
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
}

.edit-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-top: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.header-actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: 0.7rem 1.4rem;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition);
  text-decoration: none;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: var(--transition);
}

.action-btn:hover::after {
  opacity: 1;
}

.action-primary {
  background-color: var(--primary);
  color: var(--white);
}

.action-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  color: var(--white);
}

.action-secondary {
  background-color: var(--white);
  color: var(--primary);
  border: 2px solid var(--primary);
}

.action-secondary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  color: var(--primary);
}

/* Breadcrumbs */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: var(--space-2) 0;
  margin-bottom: 0;
  list-style: none;
  background-color: transparent;
  border-radius: var(--radius);
  font-size: 0.85rem;
  position: relative;
  z-index: 1;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--space-2);
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: var(--space-2);
  color: var(--gray-400);
  content: "/";
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

.breadcrumb-item a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: var(--gray-500);
}

/* Card Styling */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow-3d);
  margin-bottom: var(--space-5);
  overflow: hidden;
  transition: var(--transition);
  animation: fadeIn 0.5s ease;
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-3d-hover);
  transform: translateY(-5px);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--primary), transparent);
}

.card-header h6 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header h6 i {
  color: var(--accent);
  font-size: 1.4rem;
}

.card-body {
  padding: var(--space-5);
}

/* Info Section */
.info-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.info-item {
  display: flex;
  flex-direction: column;
  transition: var(--transition);
  padding: var(--space-3);
  border-radius: var(--radius);
}

.info-item:hover {
  background-color: rgba(46, 125, 50, 0.05);
  transform: translateY(-2px);
}

.info-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--gray-500);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.info-label i {
  color: var(--primary);
}

.info-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--dark);
}

/* Form Styling */
.form-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--primary-dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-label i {
  color: var(--primary);
}

.form-control, .form-select {
  border-radius: var(--radius);
  border: 2px solid var(--gray-200);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-fast);
  height: auto;
  font-size: 0.95rem;
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.2);
  transform: translateY(-2px);
}

.form-control::placeholder {
  color: var(--gray-400);
}

.form-text {
  font-size: 0.8rem;
  color: var(--gray-500);
  margin-top: var(--space-1);
}

/* Button Styling */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: 0.7rem 1.4rem;
  border-radius: var(--radius);
  font-weight: 600;
  font-size: 0.9rem;
  transition: var(--transition);
  box-shadow: var(--shadow);
  border: none;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: var(--transition);
}

.btn:hover::after {
  opacity: 1;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--white);
}

.btn-danger {
  background: var(--danger-gradient);
  color: var(--white);
}

.btn-success {
  background: var(--success-gradient);
  color: var(--white);
}

.btn-warning {
  background: var(--warning-gradient);
  color: var(--dark);
}

.btn-block {
  width: 100%;
}

/* Stock Management Cards */
.stock-card {
  height: 100%;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-bounce);
  box-shadow: var(--shadow-md);
  border: none;
  position: relative;
}

.stock-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.stock-card .card-body {
  padding: var(--space-4);
  position: relative;
  z-index: 1;
}

.stock-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-position: center;
  background-size: cover;
  opacity: 0.03;
  z-index: 0;
}

.stock-card-success {
  box-shadow: 0 0 15px rgba(0, 200, 83, 0.15);
  border-top: 3px solid var(--success);
}

.stock-card-success::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%2300C853' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='12' y1='5' x2='12' y2='19'%3E%3C/line%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.stock-card-danger {
  box-shadow: 0 0 15px rgba(244, 67, 54, 0.15);
  border-top: 3px solid var(--danger);
}

.stock-card-danger::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23F44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.stock-card-warning {
  box-shadow: 0 0 15px rgba(255, 214, 0, 0.15);
  border-top: 3px solid var(--warning);
}

.stock-card-warning::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FFD600' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z'%3E%3C/path%3E%3Cpath d='M12 8v4'%3E%3C/path%3E%3Cpath d='M12 16h.01'%3E%3C/path%3E%3C/svg%3E");
}

.text-xs {
  font-size: 0.9rem;
}

.font-weight-bold {
  font-weight: 700;
}

.text-uppercase {
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.text-success {
  color: var(--success) !important;
}

.text-danger {
  color: var(--danger) !important;
}

.text-warning {
  color: var(--warning) !important;
}

/* Alerts */
.alert {
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-5);
  margin-bottom: var(--space-4);
  border: none;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.alert::before {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 1.2rem;
}

.alert::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.alert-success::before {
  content: "\f058"; /* fa-check-circle */
}

.alert-success::after {
  background-color: var(--success);
}

.alert-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.alert-danger::before {
  content: "\f057"; /* fa-times-circle */
}

.alert-danger::after {
  background-color: var(--danger);
}

/* Status Badges */
.badge {
  padding: 0.5em 1em;
  font-weight: 600;
  font-size: 0.85rem;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.bg-success {
  background: var(--success-gradient) !important;
  color: var(--white);
}

.bg-warning {
  background: var(--warning-gradient) !important;
  color: var(--dark);
}

.bg-danger {
  background: var(--danger-gradient) !important;
  color: var(--white);
}

/* Section Headings */
.section-heading {
  color: var(--primary-dark);
  font-weight: 700;
  font-size: 1.2rem;
  margin: var(--space-5) 0 var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  position: relative;
  padding-left: var(--space-4);
}

.section-heading::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .edit-header {
    padding: var(--space-4);
  }

  .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .card-header, .card-body {
    padding: var(--space-4);
  }

  .info-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .edit-title {
    font-size: 1.5rem;
  }

  .edit-title-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container-fluid {
    padding: var(--space-3);
  }

  .edit-title {
    font-size: 1.25rem;
  }

  .edit-title-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .card-header h6 {
    font-size: 1rem;
  }

  .info-row {
    padding: var(--space-3);
    grid-template-columns: 1fr;
  }

  .btn {
    padding: 0.6rem 1.2rem;
  }
}
