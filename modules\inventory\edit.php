<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in and has appropriate role
requireLogin();
requireRole('Logistics', 'Department', 'HealthCenter');

// Get inventory ID from URL
$inventory_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : 0;

// Start by only including the essential PHP files needed for permission checking
$base_path = $_SERVER['DOCUMENT_ROOT'] . '/choims';
require_once($base_path . '/config/database.php');
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/auth.php');

// Initialize session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure user is logged in and has appropriate role
requireLogin();
requireRole('Logistics', 'Department', 'HealthCenter');

// Get inventory ID from URL
$inventory_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : 0;

// Get inventory details
$query = "
    SELECT i.*, s.sku_code, s.sku_name, c.category_name, l.location_name
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON i.location_id = l.location_id
    WHERE i.inventory_id = ?
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $inventory_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Inventory not found
    $_SESSION['error'] = "Inventory item not found.";
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

$inventory = mysqli_fetch_assoc($result);

// Check if user has permission for this location
// Logistics users can only edit inventory in their assigned location
if ((hasRole('Logistics') && getUserLocationId() != $inventory['location_id']) ||
    (!hasRole('Logistics') && getUserLocationId() != $inventory['location_id'])) {
    // User doesn't have permission
    $_SESSION['error'] = "You do not have permission to edit this inventory item.";
    header("Location: /choims/modules/inventory/list.php");
    exit();
}

// Now include the header after all permission checks
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern inventory edit CSS
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-edit-modern.css">';
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';

$success_message = '';
$error_message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate inputs
    $min_quantity = sanitizeInput($_POST['min_quantity']);
    $low_stock_threshold = sanitizeInput($_POST['low_stock_threshold']);
    $critical_threshold = sanitizeInput($_POST['critical_threshold']);

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // Save old values for audit log
        $old_values = [
            'min_quantity' => $inventory['min_quantity'],
            'low_stock_threshold' => $inventory['low_stock_threshold'],
            'critical_threshold' => $inventory['critical_threshold']
        ];

        // Update inventory record
        $updateQuery = "
            UPDATE consumable_inventory
            SET min_quantity = ?,
                low_stock_threshold = ?,
                critical_threshold = ?,
                updated_at = NOW()
            WHERE inventory_id = ?
        ";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'iiii', $min_quantity, $low_stock_threshold, $critical_threshold, $inventory_id);
        mysqli_stmt_execute($updateStmt);

        // Create audit log
        createAuditLog(
            $_SESSION['user_id'],
            'update',
            'consumable_inventory',
            $inventory_id,
            json_encode($old_values),
            json_encode([
                'min_quantity' => $min_quantity,
                'low_stock_threshold' => $low_stock_threshold,
                'critical_threshold' => $critical_threshold
            ])
        );

        // Commit transaction
        mysqli_commit($conn);

        $success_message = "Inventory item updated successfully.";

        // Refresh inventory data
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $inventory = mysqli_fetch_assoc($result);

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error_message = "Error updating inventory: " . $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <!-- Modern Header Section -->
    <div class="edit-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <div class="edit-title-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div>
                    <h1 class="edit-title">Edit Inventory Item</h1>
                    <p class="edit-subtitle">
                        <i class="fas fa-info-circle me-1"></i> Update inventory details and manage stock levels
                    </p>
                </div>
            </div>
            <div class="header-actions">
                <a href="/choims/modules/inventory/view.php?id=<?php echo $inventory_id; ?>" class="action-btn action-secondary">
                    <i class="fas fa-eye"></i> View Item
                </a>
                <a href="/choims/modules/inventory/list.php" class="action-btn action-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
        </div>
        <div class="mt-2">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/choims/dashboard/">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/choims/modules/inventory/list.php">Inventory</a></li>
                    <li class="breadcrumb-item active">Edit Item</li>
                </ol>
            </nav>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success animate__animated animate__fadeIn"><?php echo $success_message; ?></div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger animate__animated animate__fadeIn"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <div class="card animate__animated animate__fadeInUp animate__delay-1">
        <div class="card-header">
            <h6><i class="fas fa-info-circle"></i> Inventory Information</h6>
        </div>
        <div class="card-body">
            <div class="info-row">
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-barcode me-1"></i> SKU Code</div>
                    <div class="info-value"><?php echo $inventory['sku_code']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-box me-1"></i> Item Name</div>
                    <div class="info-value"><?php echo $inventory['sku_name']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-tag me-1"></i> Category</div>
                    <div class="info-value"><?php echo $inventory['category_name']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-map-marker-alt me-1"></i> Location</div>
                    <div class="info-value"><?php echo $inventory['location_name']; ?></div>
                </div>
            </div>

            <div class="info-row mt-4">
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-cubes me-1"></i> Current Quantity</div>
                    <div class="info-value" style="font-size: 1.2rem; font-weight: 700;"><?php echo $inventory['current_quantity']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-chart-line me-1"></i> Status</div>
                    <div class="info-value">
                        <?php
                        $statusClass = '';
                        $statusIcon = '';
                        switch ($inventory['status']) {
                            case 'Available':
                                $statusClass = 'bg-success';
                                $statusIcon = 'fas fa-check-circle';
                                break;
                            case 'Low Stock':
                                $statusClass = 'bg-warning';
                                $statusIcon = 'fas fa-exclamation-circle';
                                break;
                            case 'Out of Stock':
                                $statusClass = 'bg-danger';
                                $statusIcon = 'fas fa-exclamation-triangle';
                                break;
                            default:
                                $statusClass = 'bg-secondary';
                                $statusIcon = 'fas fa-question-circle';
                        }
                        ?>
                        <span class="badge <?php echo $statusClass; ?>">
                            <i class="<?php echo $statusIcon; ?> me-1"></i> <?php echo $inventory['status']; ?>
                        </span>
                    </div>
                </div>
                <div class="info-item">
                    <div class="info-label"><i class="fas fa-calendar-check me-1"></i> Last Restock</div>
                    <div class="info-value"><?php echo !empty($inventory['last_restock_date']) ? formatDate($inventory['last_restock_date']) : 'N/A'; ?></div>
                </div>
            </div>

            <h5 class="section-heading mt-4 mb-3">Threshold Settings</h5>

            <form method="post" action="">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="min_quantity" class="form-label">
                                <i class="fas fa-sort-amount-down me-1"></i> Minimum Quantity *
                            </label>
                            <input type="number" class="form-control" id="min_quantity" name="min_quantity"
                                   min="0" value="<?php echo $inventory['min_quantity']; ?>" required>
                            <small class="form-text">Recommended minimum quantity for this item</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="low_stock_threshold" class="form-label">
                                <i class="fas fa-exclamation-circle me-1"></i> Low Stock Threshold *
                            </label>
                            <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold"
                                   min="0" value="<?php echo $inventory['low_stock_threshold']; ?>" required>
                            <small class="form-text">When quantity falls below this level, item will be marked as 'Low Stock'</small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="critical_threshold" class="form-label">
                                <i class="fas fa-exclamation-triangle me-1"></i> Critical Threshold *
                            </label>
                            <input type="number" class="form-control" id="critical_threshold" name="critical_threshold"
                                   min="0" value="<?php echo $inventory['critical_threshold']; ?>" required>
                            <small class="form-text">When quantity falls below this level, item will be marked as 'Out of Stock'</small>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                    <a href="/choims/modules/inventory/view.php?id=<?php echo $inventory_id; ?>" class="btn btn-danger">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Stock Management -->
    <div class="card animate__animated animate__fadeInUp animate__delay-2">
        <div class="card-header">
            <h6><i class="fas fa-warehouse"></i> Stock Management</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <form action="/choims/modules/inventory/add_stock.php" method="post">
                        <input type="hidden" name="inventory_id" value="<?php echo $inventory_id; ?>">
                        <input type="hidden" name="transaction_type" value="Stock In">

                        <div class="stock-card stock-card-success h-100">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-3">
                                    <i class="fas fa-plus-circle me-1"></i> Stock In
                                </div>

                                <div class="mb-3">
                                    <label for="quantity_in" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i> Quantity *
                                    </label>
                                    <input type="number" class="form-control" id="quantity_in" name="quantity" min="1" required>
                                </div>

                                <div class="mb-3">
                                    <label for="supplier_id" class="form-label">
                                        <i class="fas fa-truck me-1"></i> Supplier
                                    </label>
                                    <select class="form-select" id="supplier_id" name="supplier_id">
                                        <option value="">Select Supplier</option>
                                        <?php
                                        $supplierQuery = "SELECT supplier_id, supplier_name FROM suppliers ORDER BY supplier_name";
                                        $supplierResult = mysqli_query($conn, $supplierQuery);
                                        while ($supplier = mysqli_fetch_assoc($supplierResult)):
                                        ?>
                                            <option value="<?php echo $supplier['supplier_id']; ?>">
                                                <?php echo $supplier['supplier_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="unit_cost" class="form-label">
                                        <i class="fas fa-dollar-sign me-1"></i> Unit Cost
                                    </label>
                                    <input type="number" step="0.01" class="form-control" id="unit_cost" name="unit_cost">
                                </div>

                                <div class="mb-3">
                                    <label for="reference_in" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i> Reference
                                    </label>
                                    <input type="text" class="form-control" id="reference_in" name="reference_document">
                                </div>

                                <div class="mb-3">
                                    <label for="remarks_in" class="form-label">
                                        <i class="fas fa-comment-alt me-1"></i> Remarks
                                    </label>
                                    <textarea class="form-control" id="remarks_in" name="remarks" rows="2"></textarea>
                                </div>

                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-plus-circle"></i> Add Stock
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-md-4 mb-4">
                    <form action="/choims/modules/inventory/add_stock.php" method="post">
                        <input type="hidden" name="inventory_id" value="<?php echo $inventory_id; ?>">
                        <input type="hidden" name="transaction_type" value="Stock Out">

                        <div class="stock-card stock-card-danger h-100">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-3">
                                    <i class="fas fa-minus-circle me-1"></i> Stock Out
                                </div>

                                <div class="mb-3">
                                    <label for="quantity_out" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i> Quantity *
                                    </label>
                                    <input type="number" class="form-control" id="quantity_out" name="quantity"
                                           min="1" max="<?php echo $inventory['current_quantity']; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="reference_out" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i> Reference
                                    </label>
                                    <input type="text" class="form-control" id="reference_out" name="reference_document">
                                </div>

                                <div class="mb-3">
                                    <label for="remarks_out" class="form-label">
                                        <i class="fas fa-comment-alt me-1"></i> Remarks
                                    </label>
                                    <textarea class="form-control" id="remarks_out" name="remarks" rows="2"></textarea>
                                </div>

                                <button type="submit" class="btn btn-danger btn-block"
                                        <?php echo $inventory['current_quantity'] == 0 ? 'disabled' : ''; ?>>
                                    <i class="fas fa-minus-circle"></i> Remove Stock
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-md-4 mb-4">
                    <form action="/choims/modules/inventory/add_stock.php" method="post">
                        <input type="hidden" name="inventory_id" value="<?php echo $inventory_id; ?>">
                        <input type="hidden" name="transaction_type" value="Adjustment">

                        <div class="stock-card stock-card-warning h-100">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-3">
                                    <i class="fas fa-sync-alt me-1"></i> Stock Adjustment
                                </div>

                                <div class="mb-3">
                                    <label for="quantity_adjust" class="form-label">
                                        <i class="fas fa-hashtag me-1"></i> New Quantity *
                                    </label>
                                    <input type="number" class="form-control" id="quantity_adjust" name="quantity"
                                           min="0" value="<?php echo $inventory['current_quantity']; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="reference_adjust" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i> Reference
                                    </label>
                                    <input type="text" class="form-control" id="reference_adjust" name="reference_document">
                                </div>

                                <div class="mb-3">
                                    <label for="remarks_adjust" class="form-label">
                                        <i class="fas fa-comment-alt me-1"></i> Remarks *
                                    </label>
                                    <textarea class="form-control" id="remarks_adjust" name="remarks" rows="2" required
                                              placeholder="Please provide a reason for this adjustment"></textarea>
                                </div>

                                <button type="submit" class="btn btn-warning btn-block">
                                    <i class="fas fa-sync-alt"></i> Adjust Stock
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>