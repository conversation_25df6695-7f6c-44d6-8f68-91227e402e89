<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in and has superadmin role
requireLogin();
if (strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode') {
    // Redirect to unauthorized page
    $_SESSION['error'] = "You don't have permission to access this feature. Only superadmins can import assets.";
    header("Location: /choims/index.php");
    exit();
}

// Helper functions to cache lookup data
function getSkuCache($conn) {
    $cache = [];
    $sql = "SELECT sku_id, sku_code FROM sku_master";
    $result = mysqli_query($conn, $sql);
    while ($row = mysqli_fetch_assoc($result)) {
        $cache[$row['sku_code']] = $row['sku_id'];
    }
    return $cache;
}

function getLocationCache($conn) {
    $cache = [];
    $sql = "SELECT location_id, location_name FROM locations";
    $result = mysqli_query($conn, $sql);
    while ($row = mysqli_fetch_assoc($result)) {
        $cache[$row['location_name']] = $row['location_id'];
    }
    return $cache;
}

function getSourceCache($conn) {
    $cache = [];
    $sql = "SELECT source_id, source_name FROM sources";
    $result = mysqli_query($conn, $sql);
    while ($row = mysqli_fetch_assoc($result)) {
        $cache[$row['source_name']] = $row['source_id'];
    }
    return $cache;
}

function getSupplierCache($conn) {
    $cache = [];
    $sql = "SELECT supplier_id, supplier_name FROM suppliers";
    $result = mysqli_query($conn, $sql);
    while ($row = mysqli_fetch_assoc($result)) {
        $cache[$row['supplier_name']] = $row['supplier_id'];
    }
    return $cache;
}

// Include PhpSpreadsheet
require $_SERVER['DOCUMENT_ROOT'] . '/choims/vendor/autoload.php';
// Use PhpSpreadsheet
use PhpOffice\PhpSpreadsheet\IOFactory;

// Check if PhpSpreadsheet class exists
if (!class_exists('PhpOffice\PhpSpreadsheet\IOFactory')) {
    $error_message = 'The PhpSpreadsheet library is not available. Please run:<br>';
    $error_message .= '<code>composer require phpoffice/phpspreadsheet</code><br><br>';
    $error_message .= 'You may also need to install the following PHP extensions:<br>';
    $error_message .= '- ext-fileinfo<br>';
    $error_message .= '- ext-gd<br>';
    $error_message .= '- ext-zip<br><br>';
    $error_message .= 'To install these extensions in XAMPP:<br>';
    $error_message .= '1. Open php.ini file in your XAMPP installation<br>';
    $error_message .= '2. Uncomment the lines (remove semicolon) for:<br>';
    $error_message .= '<code>;extension=fileinfo</code><br>';
    $error_message .= '<code>;extension=gd</code><br>';
    $error_message .= '<code>;extension=zip</code><br>';
    $error_message .= '3. Restart Apache server';
} else {
    // Initialize variables
    $success = false;
    $error_message = '';
    $import_results = [];
    $valid_records = 0;
    $error_records = 0;

    // Process the uploaded file
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excelFile'])) {
        try {
            // Check for upload errors
            if ($_FILES['excelFile']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception("Upload error: " . $_FILES['excelFile']['error']);
            }

            // Validate file type
            $allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']; // xlsx

            // Check file extension
            $fileExt = pathinfo($_FILES['excelFile']['name'], PATHINFO_EXTENSION);
            if ($fileExt !== 'xlsx') {
                throw new Exception("Invalid file extension. Please upload an Excel file (.xlsx)");
            }

            // Load the spreadsheet
            $spreadsheet = IOFactory::load($_FILES['excelFile']['tmp_name']);
            $worksheet = $spreadsheet->getActiveSheet(); // Assets sheet

            // Get data as array (skip header row)
            $data = $worksheet->toArray();
            $header = array_shift($data); // Remove and store header row

            // Begin database transaction
            mysqli_begin_transaction($conn);

            // Cache lookup data
            $skuCache = getSkuCache($conn);
            $locationCache = getLocationCache($conn);
            $sourceCache = getSourceCache($conn);
            $supplierCache = getSupplierCache($conn);

            // Log the import action
            $importLogQuery = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
                           VALUES (?, ?, ?, ?, NULL, ?, ?)";
            $importLogStmt = mysqli_prepare($conn, $importLogQuery);

            $user_id = $_SESSION['user_id'];
            $action = 'Bulk Import';
            $entity_type = 'Fixed Assets';
            $entity_id = 0; // Generic for the entire import
            $new_values = json_encode(['file' => $_FILES['excelFile']['name'], 'initiated_at' => date('Y-m-d H:i:s')]);
            $ip_address = $_SERVER['REMOTE_ADDR'];

            mysqli_stmt_bind_param($importLogStmt, 'ississ', $user_id, $action, $entity_type, $entity_id, $new_values, $ip_address);
            mysqli_stmt_execute($importLogStmt);
            $importLogId = mysqli_insert_id($conn);

            foreach ($data as $rowIndex => $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Map columns to variables (based on template structure)
                $sku_code = trim($row[0] ?? '');
                $asset_name = trim($row[1] ?? '');
                $serial_number = trim($row[2] ?? '');
                $model = trim($row[3] ?? '');
                $specifications = trim($row[4] ?? '');
                $status = trim($row[5] ?? '');
                $assigned_to = trim($row[6] ?? '');
                $location_name = trim($row[7] ?? '');
                $unit_section = trim($row[8] ?? '');
                $local_mr = trim($row[9] ?? '');
                $purchase_date = !empty($row[10]) ? date('Y-m-d', strtotime($row[10])) : null;
                $unit_cost = !empty($row[11]) ? floatval($row[11]) : null;
                $receipt_type_raw = trim($row[12] ?? '');

                // Map receipt_type to valid ENUM values
                $receipt_type = '';
                if (!empty($receipt_type_raw)) {
                    // Normalize the receipt type to match the ENUM values
                    $receipt_type_map = [
                        'SALES INVOICE' => 'Local MR',
                        'SALES INV' => 'Local MR',
                        'SI' => 'Local MR',
                        'DR' => 'Local MR',
                        'D.R' => 'Local MR',
                        'D.R.' => 'Local MR',
                        'DELIVERY RECEIPT' => 'Local MR',
                        'LOCAL MR' => 'Local MR',
                        'PTR' => 'P.T.R',
                        'P.T.R' => 'P.T.R',
                        'P.T.R.' => 'P.T.R',
                        'PROPERTY TRANSFER RECEIPT' => 'P.T.R',
                        'OR' => 'O.R',
                        'O.R' => 'O.R',
                        'O.R.' => 'O.R',
                        'OFFICIAL RECEIPT' => 'O.R',
                        'PO' => 'Local MR',
                        'P.O' => 'Local MR',
                        'P.O.' => 'Local MR',
                        'PURCHASE ORDER' => 'Local MR'
                    ];

                    $receipt_type_upper = strtoupper($receipt_type_raw);
                    if (isset($receipt_type_map[$receipt_type_upper])) {
                        $receipt_type = $receipt_type_map[$receipt_type_upper];
                    } else {
                        // Try to match with one of the valid values
                        $valid_types = ['Sales Invoice', 'D.R', 'P.T.R', 'O.R', 'P.O'];
                        foreach ($valid_types as $valid_type) {
                            if (stripos($receipt_type_raw, str_replace('.', '', $valid_type)) !== false) {
                                $receipt_type = $valid_type;
                                break;
                            }
                        }
                    }
                }
                $series_number = trim($row[13] ?? '');
                $supplier_name = trim($row[14] ?? '');
                $warranty_expiry = !empty($row[15]) ? date('Y-m-d', strtotime($row[15])) : null;
                $source_name = trim($row[16] ?? '');
                $remarks = trim($row[17] ?? '');

                $rowNumber = $rowIndex + 2; // +2 to account for header row and 0-indexing
                $record = ['row' => $rowNumber, 'asset_name' => $asset_name];

                // Validate required fields
                if (empty($sku_code) || empty($asset_name) || empty($status) || empty($location_name)) {
                    $record['status'] = 'Error';
                    $record['message'] = 'Missing required fields';
                    $import_results[] = $record;
                    $error_records++;
                    continue;
                }

                // Validate and map SKU code to sku_id
                if (!isset($skuCache[$sku_code])) {
                    $record['status'] = 'Error';
                    $record['message'] = 'Invalid SKU code: ' . $sku_code;
                    $import_results[] = $record;
                    $error_records++;
                    continue;
                }
                $sku_id = $skuCache[$sku_code];

                // Validate and map location to location_id
                if (!isset($locationCache[$location_name])) {
                    $record['status'] = 'Error';
                    $record['message'] = 'Invalid location: ' . $location_name;
                    $import_results[] = $record;
                    $error_records++;
                    continue;
                }
                $location_id = $locationCache[$location_name];

                // Validate status
                $valid_statuses = ['In use', 'Available', 'Under Repair', 'Defective'];
                if (!in_array($status, $valid_statuses)) {
                    $record['status'] = 'Error';
                    $record['message'] = 'Invalid status: ' . $status;
                    $import_results[] = $record;
                    $error_records++;
                    continue;
                }

                // Map source name to source_id (if provided)
                $source_id = 'NULL';
                if (!empty($source_name)) {
                    if (isset($sourceCache[$source_name])) {
                        $source_id = $sourceCache[$source_name];
                    } else {
                        $record['status'] = 'Error';
                        $record['message'] = 'Invalid source: ' . $source_name;
                        $import_results[] = $record;
                        $error_records++;
                        continue;
                    }
                }

                // Map supplier name to supplier_id (if provided)
                $supplier_id = 'NULL';
                if (!empty($supplier_name)) {
                    if (isset($supplierCache[$supplier_name])) {
                        $supplier_id = $supplierCache[$supplier_name];
                    } else {
                        $record['status'] = 'Error';
                        $record['message'] = 'Invalid supplier: ' . $supplier_name;
                        $import_results[] = $record;
                        $error_records++;
                        continue;
                    }
                }

                // Fix NULL values for SQL
                $purchase_date_sql = ($purchase_date === null) ? 'NULL' : "'" . $purchase_date . "'";
                $unit_cost_sql = ($unit_cost === null) ? 'NULL' : $unit_cost;
                $warranty_expiry_sql = ($warranty_expiry === null) ? 'NULL' : "'" . $warranty_expiry . "'";
                // Validate receipt_type against allowed ENUM values
                $valid_receipt_types = ['Local MR', 'P.T.R', 'O.R'];
                if (!empty($receipt_type) && !in_array($receipt_type, $valid_receipt_types)) {
                    $record['status'] = 'Error';
                    $record['message'] = 'Invalid receipt type: ' . $receipt_type_raw . '. Must be one of: ' . implode(', ', $valid_receipt_types);
                    $import_results[] = $record;
                    $error_records++;
                    continue;
                }

                $receipt_type_sql = empty($receipt_type) ? 'NULL' : "'" . mysqli_real_escape_string($conn, $receipt_type) . "'";
                $supplier_id_sql = ($supplier_id === 'NULL') ? 'NULL' : $supplier_id;
                $source_id_sql = ($source_id === 'NULL') ? 'NULL' : $source_id;

                // Set up the direct SQL statement (for complex parameters handling)
                $insertSQL = "
                    INSERT INTO fixed_assets (
                        sku_id, asset_name, serial_number, model, specifications,
                        status, assigned_to, current_location_id, unit_section, local_mr,
                        purchase_date, unit_cost, receipt_type, series_number, supplier_id,
                        warranty_expiry, source_id, remarks, created_by, created_at
                    ) VALUES (
                        " . intval($sku_id) . ",
                        '" . mysqli_real_escape_string($conn, $asset_name) . "',
                        '" . mysqli_real_escape_string($conn, $serial_number) . "',
                        '" . mysqli_real_escape_string($conn, $model) . "',
                        '" . mysqli_real_escape_string($conn, $specifications) . "',
                        '" . mysqli_real_escape_string($conn, $status) . "',
                        '" . mysqli_real_escape_string($conn, $assigned_to) . "',
                        " . intval($location_id) . ",
                        '" . mysqli_real_escape_string($conn, $unit_section) . "',
                        '" . mysqli_real_escape_string($conn, $local_mr) . "',
                        " . $purchase_date_sql . ",
                        " . $unit_cost_sql . ",
                        " . $receipt_type_sql . ",
                        '" . mysqli_real_escape_string($conn, $series_number) . "',
                        " . $supplier_id_sql . ",
                        " . $warranty_expiry_sql . ",
                        " . $source_id_sql . ",
                        '" . mysqli_real_escape_string($conn, $remarks) . "',
                        " . intval($_SESSION['user_id']) . ",
                        NOW()
                    )
                ";

                // Execute query
                if (mysqli_query($conn, $insertSQL)) {
                    $record['status'] = 'Success';
                    $record['message'] = 'Asset imported successfully';
                    $valid_records++;

                    // Log successful import in audit log
                    $asset_id = mysqli_insert_id($conn);
                    $auditSql = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
                            VALUES (?, ?, ?, ?, NULL, ?, ?)";
                    $auditStmt = mysqli_prepare($conn, $auditSql);

                    $action = 'Import';
                    $entity_type = 'Fixed Asset';
                    $entity_id = $asset_id;
                    $new_values = json_encode([
                        'asset_name' => $asset_name,
                        'sku_id' => $sku_id,
                        'location_id' => $location_id,
                        'status' => $status,
                        'import_id' => $importLogId
                    ]);

                    mysqli_stmt_bind_param($auditStmt, 'ississ', $user_id, $action, $entity_type, $entity_id, $new_values, $ip_address);
                    mysqli_stmt_execute($auditStmt);
                    mysqli_stmt_close($auditStmt);
                } else {
                    $record['status'] = 'Error';
                    $record['message'] = 'Database error: ' . mysqli_error($conn);
                    $error_records++;
                }

                $import_results[] = $record;
            }

            // If all went well, commit the transaction
            if ($error_records === 0) {
                mysqli_commit($conn);
                $success = true;
            } else {
                // If there were errors, only commit if there were also successful imports
                if ($valid_records > 0) {
                    mysqli_commit($conn);
                    $success = true;
                } else {
                    mysqli_rollback($conn);
                    $success = false;
                }
            }

        } catch (Exception $e) {
            $error_message = $e->getMessage();
            // Roll back if exception occurs
            if (isset($conn) && $conn->connect_errno === 0) {
                mysqli_rollback($conn);
            }
        }
    }
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Import Results</h1>
        <a href="import_assets.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to Import
        </a>
    </div>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
        </div>
    <?php elseif (isset($success) && $success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i> Import completed! Successfully imported <?php echo $valid_records; ?> assets.
            <?php if ($error_records > 0): ?>
                There were <?php echo $error_records; ?> records with errors.
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Display results table -->
    <?php if (!empty($import_results)): ?>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Import Details</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="resultsTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Row</th>
                                <th>Asset Name</th>
                                <th>Status</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($import_results as $result): ?>
                                <tr class="<?php echo $result['status'] === 'Success' ? 'table-success' : 'table-danger'; ?>">
                                    <td><?php echo $result['row']; ?></td>
                                    <td><?php echo htmlspecialchars($result['asset_name']); ?></td>
                                    <td><?php echo $result['status']; ?></td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Summary</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-success text-white shadow">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Successful Imports</div>
                                <div class="h3 mb-0"><?php echo $valid_records; ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-danger text-white shadow">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Failed Imports</div>
                                <div class="h3 mb-0"><?php echo $error_records; ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-info text-white shadow">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-uppercase mb-1">Total Processed</div>
                                <div class="h3 mb-0"><?php echo count($import_results); ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <a href="/choims/modules/assets/list.php" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i> View Assets List
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Initialize DataTables for results table -->
<script>
$(document).ready(function() {
    $('#resultsTable').DataTable({
        "order": [],
        "pageLength": 25
    });
});
</script>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>