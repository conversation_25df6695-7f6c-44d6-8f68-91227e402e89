<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Initialize variables
$suppliers = [];
$errors = [];
$formData = [
    'supplier_id' => 0,
    'name' => '',
    'code' => '',
    'contact_person' => '',
    'contact_number' => '',
    'address' => '',
    'status' => 'active'
];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $formData = [
        'supplier_id' => isset($_POST['supplier_id']) ? (int)$_POST['supplier_id'] : 0,
        'name' => isset($_POST['name']) ? trim($_POST['name']) : '',
        'code' => isset($_POST['code']) ? trim(strtoupper($_POST['code'])) : '',
        'contact_person' => isset($_POST['contact_person']) ? trim($_POST['contact_person']) : '',
        'contact_number' => isset($_POST['contact_number']) ? trim($_POST['contact_number']) : '',
        'address' => isset($_POST['address']) ? trim($_POST['address']) : '',
        'status' => isset($_POST['status']) ? $_POST['status'] : 'active'
    ];

    // Validate form data
    if (empty($formData['name'])) {
        $errors[] = 'Name is required';
    }

    if (empty($formData['code'])) {
        $errors[] = 'Code is required';
    } elseif (strlen($formData['code']) > 20) {
        $errors[] = 'Code must be 20 characters or less';
    }

    // Check if code is unique
    if (!empty($formData['code'])) {
        try {
            $stmt = $db->prepare("SELECT supplier_id FROM suppliers WHERE code = ? AND supplier_id != ?");
            $stmt->execute([$formData['code'], $formData['supplier_id']]);
            if ($stmt->rowCount() > 0) {
                $errors[] = 'Code already exists';
            }
        } catch (PDOException $e) {
            error_log("Error checking supplier code: " . $e->getMessage());
            $errors[] = 'Database error occurred';
        }
    }

    // If no errors, process the operation
    if (empty($errors)) {
        try {
            // Check if it's an insert or update operation
            if ($formData['supplier_id'] > 0) {
                // Update existing supplier
                $stmt = $db->prepare("
                    UPDATE suppliers 
                    SET name = ?, code = ?, contact_person = ?, contact_number = ?, address = ?, status = ?
                    WHERE supplier_id = ?
                ");
                $result = $stmt->execute([
                    $formData['name'],
                    $formData['code'],
                    $formData['contact_person'],
                    $formData['contact_number'],
                    $formData['address'],
                    $formData['status'],
                    $formData['supplier_id']
                ]);

                if ($result) {
                    $auth->logActivity('Updated supplier', 'suppliers', $formData['supplier_id']);
                    setFlashMessage('success', 'Supplier updated successfully');
                } else {
                    $errors[] = 'Failed to update supplier';
                }
            } else {
                // Insert new supplier
                $stmt = $db->prepare("
                    INSERT INTO suppliers (name, code, contact_person, contact_number, address, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $result = $stmt->execute([
                    $formData['name'],
                    $formData['code'],
                    $formData['contact_person'],
                    $formData['contact_number'],
                    $formData['address'],
                    $formData['status']
                ]);

                if ($result) {
                    $supplierId = $db->lastInsertId();
                    $auth->logActivity('Added new supplier', 'suppliers', $supplierId);
                    setFlashMessage('success', 'Supplier added successfully');
                    // Reset form data for new entry
                    $formData = [
                        'supplier_id' => 0,
                        'name' => '',
                        'code' => '',
                        'contact_person' => '',
                        'contact_number' => '',
                        'address' => '',
                        'status' => 'active'
                    ];
                } else {
                    $errors[] = 'Failed to add supplier';
                }
            }
        } catch (PDOException $e) {
            error_log("Error processing supplier: " . $e->getMessage());
            $errors[] = 'Database error occurred';
        }
    }
}

// Handle delete operation
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $supplierId = (int)$_GET['id'];
    
    try {
        // Check if supplier is in use
        $stmt = $db->prepare("
            SELECT COUNT(*) as count 
            FROM fixed_assets 
            WHERE supplier_id = ?
        ");
        $stmt->execute([$supplierId]);
        $assetCount = $stmt->fetch()['count'];
        
        if ($assetCount > 0) {
            setFlashMessage('error', 'Cannot delete supplier as it is being used by ' . $assetCount . ' fixed asset(s)');
        } else {
            // Delete the supplier
            $stmt = $db->prepare("DELETE FROM suppliers WHERE supplier_id = ?");
            $result = $stmt->execute([$supplierId]);
            
            if ($result) {
                $auth->logActivity('Deleted supplier', 'suppliers', $supplierId);
                setFlashMessage('success', 'Supplier deleted successfully');
            } else {
                setFlashMessage('error', 'Failed to delete supplier');
            }
        }
    } catch (PDOException $e) {
        error_log("Error deleting supplier: " . $e->getMessage());
        setFlashMessage('error', 'Database error occurred while deleting supplier');
    }
    
    // Redirect to avoid form resubmission
    header("Location: " . BASE_URL . "/admin/inventory/suppliers.php");
    exit;
}

// Handle edit operation
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $supplierId = (int)$_GET['id'];
    
    try {
        $stmt = $db->prepare("SELECT * FROM suppliers WHERE supplier_id = ?");
        $stmt->execute([$supplierId]);
        
        if ($supplier = $stmt->fetch()) {
            $formData = [
                'supplier_id' => $supplier['supplier_id'],
                'name' => $supplier['name'],
                'code' => $supplier['code'],
                'contact_person' => $supplier['contact_person'],
                'contact_number' => $supplier['contact_number'],
                'address' => $supplier['address'],
                'status' => $supplier['status']
            ];
        } else {
            setFlashMessage('error', 'Supplier not found');
        }
    } catch (PDOException $e) {
        error_log("Error fetching supplier: " . $e->getMessage());
        setFlashMessage('error', 'Database error occurred while fetching supplier');
    }
}

// Fetch all suppliers
try {
    $stmt = $db->query("SELECT * FROM suppliers ORDER BY name ASC");
    $suppliers = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching suppliers: " . $e->getMessage());
    setFlashMessage('error', 'Database error occurred while fetching suppliers');
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Supplier Management</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Inventory Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <?php echo $formData['supplier_id'] > 0 ? 'Edit Supplier' : 'Add New Supplier'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <input type="hidden" name="supplier_id" value="<?php echo $formData['supplier_id']; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($formData['name']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="code" class="form-label">Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="code" name="code" 
                                   value="<?php echo htmlspecialchars($formData['code']); ?>" required
                                   maxlength="20" placeholder="e.g. SUP001">
                            <small class="text-muted">Unique code, max 20 characters</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="contact_person" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                   value="<?php echo htmlspecialchars($formData['contact_person']); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="contact_number" class="form-label">Contact Number</label>
                            <input type="text" class="form-control" id="contact_number" name="contact_number" 
                                   value="<?php echo htmlspecialchars($formData['contact_number']); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($formData['address']); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo $formData['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo $formData['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo $formData['supplier_id'] > 0 ? 'Update Supplier' : 'Add Supplier'; ?>
                            </button>
                            
                            <?php if ($formData['supplier_id'] > 0): ?>
                                <a href="suppliers.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Suppliers List</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($suppliers)): ?>
                        <p class="text-muted text-center">No suppliers found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Code</th>
                                        <th>Contact Person</th>
                                        <th>Contact Number</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($suppliers as $supplier): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($supplier['name']); ?></td>
                                            <td><code><?php echo htmlspecialchars($supplier['code']); ?></code></td>
                                            <td>
                                                <?php echo !empty($supplier['contact_person']) ? 
                                                    htmlspecialchars($supplier['contact_person']) : 
                                                    '<em class="text-muted">Not specified</em>'; ?>
                                            </td>
                                            <td>
                                                <?php echo !empty($supplier['contact_number']) ? 
                                                    htmlspecialchars($supplier['contact_number']) : 
                                                    '<em class="text-muted">Not specified</em>'; ?>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $supplier['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo ucfirst($supplier['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="suppliers.php?action=edit&id=<?php echo $supplier['supplier_id']; ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <a href="suppliers.php?action=delete&id=<?php echo $supplier['supplier_id']; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('Are you sure you want to delete this supplier? This action cannot be undone.')"
                                                       title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../templates/footer.php'; ?> 