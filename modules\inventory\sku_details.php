<?php
// Start by only including the essential PHP files needed for permission checking
$base_path = $_SERVER['DOCUMENT_ROOT'] . '/choims';
require_once($base_path . '/config/database.php'); 
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/auth.php');

// Initialize session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Ensure user is logged in
requireLogin();

// Get SKU ID from URL parameter
$sku_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($sku_id <= 0) {
    $_SESSION['error'] = "Invalid SKU ID";
    header("Location: /choims/modules/reports/inventory_summary.php");
    exit();
}

// Get SKU details
$skuQuery = "
    SELECT 
        s.*, c.category_name, c.requires_himu_approval
    FROM sku_master s
    JOIN categories c ON s.category_id = c.category_id
    WHERE s.sku_id = ?
";
$skuStmt = mysqli_prepare($conn, $skuQuery);
mysqli_stmt_bind_param($skuStmt, 'i', $sku_id);
mysqli_stmt_execute($skuStmt);
$skuResult = mysqli_stmt_get_result($skuStmt);

if (mysqli_num_rows($skuResult) == 0) {
    $_SESSION['error'] = "SKU not found";
    header("Location: /choims/modules/reports/inventory_summary.php");
    exit();
}

$sku = mysqli_fetch_assoc($skuResult);

// Now include the header after all checks
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Get fixed assets for this SKU
$assetsQuery = "
    SELECT 
        a.*, l.location_name
    FROM fixed_assets a
    JOIN locations l ON a.current_location_id = l.location_id
    WHERE a.sku_id = ? AND a.is_active = 1
    ORDER BY a.current_location_id, a.asset_id
";
$assetsStmt = mysqli_prepare($conn, $assetsQuery);
mysqli_stmt_bind_param($assetsStmt, 'i', $sku_id);
mysqli_stmt_execute($assetsStmt);
$assetsResult = mysqli_stmt_get_result($assetsStmt);

// Get consumable inventory for this SKU
$consumablesQuery = "
    SELECT 
        i.*, l.location_name
    FROM consumable_inventory i
    JOIN locations l ON i.location_id = l.location_id
    WHERE i.sku_id = ?
    ORDER BY l.location_name
";
$consumablesStmt = mysqli_prepare($conn, $consumablesQuery);
mysqli_stmt_bind_param($consumablesStmt, 'i', $sku_id);
mysqli_stmt_execute($consumablesStmt);
$consumablesResult = mysqli_stmt_get_result($consumablesStmt);

// Count totals
$totalAssets = mysqli_num_rows($assetsResult);
$totalConsumableQty = 0;
$locationStockData = [];

while ($row = mysqli_fetch_assoc($consumablesResult)) {
    $totalConsumableQty += $row['current_quantity'];
    $locationStockData[] = $row;
}

// Reset consumables result pointer for later use
mysqli_data_seek($consumablesResult, 0);
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-barcode me-2 text-primary"></i> SKU Details: <?php echo $sku['sku_code']; ?>
        </h1>
        <div>
            <a href="/choims/modules/reports/inventory_summary.php" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Inventory Summary
            </a>
        </div>
    </div>

    <div class="row">
        <!-- SKU Information -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">SKU Information</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>SKU Code:</strong> <?php echo $sku['sku_code']; ?>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Item Name:</strong> <?php echo $sku['sku_name']; ?>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Category:</strong> <?php echo $sku['category_name']; ?>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Type:</strong> 
                            <?php if ($sku['item_type'] == 'Fixed'): ?>
                                <span class="badge bg-primary">Fixed Asset</span>
                            <?php else: ?>
                                <span class="badge bg-success">Consumable</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Requires HIMU Approval:</strong> 
                            <?php echo $sku['requires_himu_approval'] ? 'Yes' : 'No'; ?>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <strong>Description:</strong> 
                            <?php echo !empty($sku['description']) ? $sku['description'] : 'No description available'; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Summary Box -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Summary</h6>
                </div>
                <div class="card-body">
                    <?php if ($sku['item_type'] == 'Fixed'): ?>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <h4 class="small font-weight-bold">Total Assets <span class="float-end"><?php echo $totalAssets; ?></span></h4>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <h4 class="small font-weight-bold">Total Quantity <span class="float-end"><?php echo $totalConsumableQty; ?></span></h4>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <h4 class="small font-weight-bold">Locations <span class="float-end"><?php echo count($locationStockData); ?></span></h4>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <?php if ($sku['item_type'] == 'Fixed'): ?>
                <!-- Fixed Assets Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Fixed Assets (<?php echo $totalAssets; ?>)</h6>
                    </div>
                    <div class="card-body">
                        <?php if ($totalAssets > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="assetsTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Asset ID</th>
                                            <th>Name</th>
                                            <th>Serial Number</th>
                                            <th>Status</th>
                                            <th>Location</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($asset = mysqli_fetch_assoc($assetsResult)): ?>
                                            <tr>
                                                <td><?php echo $asset['asset_id']; ?></td>
                                                <td><?php echo $asset['asset_name']; ?></td>
                                                <td><?php echo $asset['serial_number']; ?></td>
                                                <td>
                                                    <?php 
                                                    $statusClass = '';
                                                    switch ($asset['status']) {
                                                        case 'In use':
                                                            $statusClass = 'bg-primary';
                                                            break;
                                                        case 'Available':
                                                            $statusClass = 'bg-success';
                                                            break;
                                                        case 'Under Repair':
                                                            $statusClass = 'bg-warning text-dark';
                                                            break;
                                                        case 'Defective':
                                                            $statusClass = 'bg-danger';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $asset['status']; ?></span>
                                                </td>
                                                <td><?php echo $asset['location_name']; ?></td>
                                                <td>
                                                    <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-info-circle fa-3x text-warning mb-3"></i>
                                <h5>No fixed assets found</h5>
                                <p class="text-muted">There are no fixed assets for this SKU.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Consumable Inventory Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Inventory Stock by Location</h6>
                    </div>
                    <div class="card-body">
                        <?php if (count($locationStockData) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="consumablesTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Location</th>
                                            <th>Current Quantity</th>
                                            <th>Status</th>
                                            <th>Last Restock</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($locationStockData as $stock): ?>
                                            <tr>
                                                <td><?php echo $stock['location_name']; ?></td>
                                                <td><?php echo $stock['current_quantity']; ?></td>
                                                <td>
                                                    <?php 
                                                    $statusClass = '';
                                                    switch ($stock['status']) {
                                                        case 'Available':
                                                            $statusClass = 'bg-success';
                                                            break;
                                                        case 'Low Stock':
                                                            $statusClass = 'bg-warning text-dark';
                                                            break;
                                                        case 'Out of Stock':
                                                            $statusClass = 'bg-danger';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $stock['status']; ?></span>
                                                </td>
                                                <td>
                                                    <?php echo !empty($stock['last_restock_date']) ? date('M d, Y', strtotime($stock['last_restock_date'])) : 'N/A'; ?>
                                                </td>
                                                <td>
                                                    <a href="/choims/modules/inventory/view.php?id=<?php echo $stock['inventory_id']; ?>" class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-info-circle fa-3x text-warning mb-3"></i>
                                <h5>No inventory stock found</h5>
                                <p class="text-muted">There is no consumable inventory for this SKU.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#assetsTable').DataTable({
        responsive: true
    });
    $('#consumablesTable').DataTable({
        responsive: true
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?> 