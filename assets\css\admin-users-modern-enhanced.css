/* 
 * Enhanced Modern React-like UI Styles for Admin Users Page
 * CHOIMS - City Health Office Inventory Management System
 */

:root {
  /* Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  
  /* Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  
  /* Transitions */
  --transition-all: all 0.2s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__delay-1 {
  animation-delay: 0.1s;
}

.animate__delay-2 {
  animation-delay: 0.2s;
}

.animate__delay-3 {
  animation-delay: 0.3s;
}

.animate__faster {
  animation-duration: 0.3s;
}

/* Container */
.container-fluid {
  padding: var(--space-5);
}

/* Page Title */
.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: var(--space-5);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.page-title i {
  color: var(--primary);
}

/* Stats Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 6px 12px rgba(0, 0, 0, 0.08);
  padding: 1.75rem;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  opacity: 1;
}

.stat-primary::before {
  background-color: var(--primary);
}

.stat-warning::before {
  background-color: var(--warning);
}

.stat-info::before {
  background-color: var(--info);
}

.stat-danger::before {
  background-color: var(--danger);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
}

.stat-primary .stat-icon {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--primary);
}

.stat-warning .stat-icon {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.stat-info .stat-icon {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.stat-danger .stat-icon {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.stat-icon i {
  font-size: 1.5rem;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gray-800);
  line-height: 1.2;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-500);
  margin-top: 0.25rem;
}

/* Filter Card */
.filter-card {
  background-color: var(--white);
  border-radius: 16px;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.filter-card-header {
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--gray-200);
}

.filter-title {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-badge {
  background-color: var(--primary-bg);
  color: var(--primary);
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  margin-left: 0.75rem;
}

.filter-card-body {
  padding: 1.5rem;
}

.filter-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.filter-group {
  margin-bottom: 0;
}

.filter-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.filter-control {
  width: 100%;
  padding: 0.6rem 1rem;
  font-size: 0.95rem;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  transition: all 0.2s;
}

.filter-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
  outline: none;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

/* Records Card */
.records-card {
  background-color: var(--white);
  border-radius: 16px;
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.records-card-header {
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--gray-200);
}

.records-title {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.records-card-body {
  padding: 1.5rem;
}

.records-search {
  position: relative;
  margin-bottom: 1.5rem;
}

.records-search i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
}

.records-search input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  font-size: 0.95rem;
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  transition: all 0.2s;
}

.records-search input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
  outline: none;
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table thead th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 1rem 1.5rem;
  border-bottom: 2px solid var(--gray-200);
  text-align: left;
}

.modern-table tbody td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-700);
  font-size: 0.95rem;
  vertical-align: middle;
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.modern-table tbody tr {
  transition: background-color 0.2s;
}

.modern-table tbody tr:hover {
  background-color: var(--gray-50);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.status-active {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-inactive {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

/* Role Badges */
.role-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

.role-GodMode { 
  background-color: rgba(119, 66, 245, 0.1); 
  color: #7742f5; 
}

.role-Superadmin { 
  background-color: rgba(78, 115, 223, 0.1); 
  color: #4e73df; 
}

.role-Logistics { 
  background-color: rgba(54, 185, 204, 0.1); 
  color: #36b9cc; 
}

.role-HIMU { 
  background-color: rgba(28, 200, 138, 0.1); 
  color: #1cc88a; 
}

.role-Department { 
  background-color: rgba(246, 194, 62, 0.1); 
  color: #f6c23e; 
}

.role-HealthCenter { 
  background-color: rgba(253, 126, 20, 0.1); 
  color: #fd7e14; 
}

/* User Card */
.user-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background-color: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.85rem;
  color: var(--gray-500);
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
  gap: 0.5rem;
}

.action-btn-primary {
  background-color: var(--primary);
  color: white;
}

.action-btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
}

.action-btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

.action-btn-secondary:hover {
  background-color: var(--gray-200);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
  background-color: var(--gray-100);
  color: var(--gray-700);
  margin-right: 0.25rem;
}

.table-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.view-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

.edit-btn:hover {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.delete-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

/* Enhanced Modal Styles */
.modal-enhanced {
  padding-right: 0 !important;
}

.modal-enhanced .modal-content {
  border: none;
  border-radius: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.modal-enhanced .modal-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: 1.5rem;
}

.modal-enhanced .modal-title {
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-enhanced .modal-title i {
  color: var(--primary);
}

.modal-enhanced .modal-body {
  padding: 1.5rem;
}

.modal-enhanced .modal-footer {
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: 1.25rem 1.5rem;
  gap: 0.75rem;
}

.modal-enhanced .close {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--gray-100);
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.modal-enhanced .close:hover {
  background-color: var(--gray-200);
  transform: rotate(90deg);
}

.modal-enhanced .form-group {
  margin-bottom: 1.25rem;
}

.modal-enhanced .form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.modal-enhanced .form-control {
  border-radius: 8px;
  border: 1px solid var(--gray-300);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.modal-enhanced .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.modal-enhanced .form-select {
  border-radius: 8px;
  border: 1px solid var(--gray-300);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.modal-enhanced .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.modal-enhanced .btn {
  padding: 0.6rem 1.25rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s;
}

.modal-enhanced .btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.modal-enhanced .btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
}

.modal-enhanced .btn-secondary {
  background-color: var(--gray-200);
  border-color: var(--gray-200);
  color: var(--gray-700);
}

.modal-enhanced .btn-secondary:hover {
  background-color: var(--gray-300);
  border-color: var(--gray-300);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* DataTables Custom Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter {
  margin-bottom: 1.5rem;
}

.dataTables_wrapper .dataTables_length select {
  border-radius: 8px;
  border: 1px solid var(--gray-300);
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  font-size: 0.95rem;
  transition: all 0.2s;
}

.dataTables_wrapper .dataTables_length select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
  outline: none;
}

.dataTables_wrapper .dataTables_filter {
  display: none; /* Hide default search as we use custom search */
}

.dataTables_wrapper .dataTables_info {
  color: var(--gray-500);
  font-size: 0.9rem;
  padding-top: 1.5rem;
}

.dataTables_wrapper .dataTables_paginate {
  padding-top: 1.5rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border: none !important;
  background: none !important;
  border-radius: 8px;
  padding: 0.5rem 0.75rem !important;
  margin: 0 0.25rem;
  color: var(--gray-500) !important;
  transition: all 0.2s;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: var(--gray-100) !important;
  color: var(--primary) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: var(--primary) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(46, 125, 50, 0.15);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background-color: var(--primary-dark) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  color: var(--gray-400) !important;
  cursor: not-allowed;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
  background: none !important;
  transform: none;
  box-shadow: none;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .container-fluid {
    padding: var(--space-4);
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .stat-card {
    padding: 1.25rem;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
  
  .filter-card-header,
  .records-card-header {
    padding: 1rem 1.25rem;
  }
  
  .filter-card-body,
  .records-card-body {
    padding: 1.25rem;
  }
  
  .filter-form {
    grid-template-columns: 1fr;
  }
  
  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 768px) {
  .container-fluid {
    padding: var(--space-3);
  }
  
  .page-title {
    font-size: 1.25rem;
  }
  
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .filter-title,
  .records-title {
    font-size: 1rem;
  }
  
  .action-btn {
    padding: 0.4rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .table-responsive {
    margin-left: -1.25rem;
    margin-right: -1.25rem;
    width: calc(100% + 2.5rem);
  }
}
