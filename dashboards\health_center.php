<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireRole('HealthCenter');

// Get user's location
$userLocationId = $_SESSION['location_id'];
$locationQuery = "SELECT * FROM locations WHERE location_id = ?";
$locationStmt = mysqli_prepare($conn, $locationQuery);
mysqli_stmt_bind_param($locationStmt, 'i', $userLocationId);
mysqli_stmt_execute($locationStmt);
$locationResult = mysqli_stmt_get_result($locationStmt);
$location = mysqli_fetch_assoc($locationResult);

// Check if it's time for monthly inventory update
$showMonthlyUpdateButton = isTimeForMonthlyInventoryUpdate(); // Show only 2 days before month end
$hasCompletedMonthlyUpdate = false;

if ($showMonthlyUpdateButton) {
    // Check if this location has already completed their monthly update
    $hasCompletedMonthlyUpdate = hasCompletedMonthlyInventoryUpdate($conn, $userLocationId);
}

// Generate CSRF token for the monthly update form
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get asset counts by category for user's location
$assetsByCategoryQuery = "
    SELECT c.category_name, COUNT(a.asset_id) as asset_count
    FROM categories c
    LEFT JOIN sku_master s ON c.category_id = s.category_id
    LEFT JOIN fixed_assets a ON s.sku_id = a.sku_id AND a.current_location_id = ? AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    WHERE c.category_id IN (1, 2, 3)
    GROUP BY c.category_id
    ORDER BY c.category_id
";
$assetsByCategoryStmt = mysqli_prepare($conn, $assetsByCategoryQuery);
mysqli_stmt_bind_param($assetsByCategoryStmt, 'i', $userLocationId);
mysqli_stmt_execute($assetsByCategoryStmt);
$assetsByCategoryResult = mysqli_stmt_get_result($assetsByCategoryStmt);

// Get low stock items at user's location
$lowStockQuery = "
    SELECT i.inventory_id, s.sku_name, i.current_quantity, i.low_stock_threshold, i.critical_threshold, i.status
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    WHERE i.location_id = ? AND (i.status = 'Low Stock' OR i.status = 'Out of Stock') AND (i.is_deleted = 0 OR i.is_deleted IS NULL)
    ORDER BY
        CASE
            WHEN i.status = 'Out of Stock' THEN 1
            WHEN i.status = 'Low Stock' THEN 2
            ELSE 3
        END,
        s.sku_name ASC
    LIMIT 10
";
$lowStockStmt = mysqli_prepare($conn, $lowStockQuery);
mysqli_stmt_bind_param($lowStockStmt, 'i', $userLocationId);
mysqli_stmt_execute($lowStockStmt);
$lowStockResult = mysqli_stmt_get_result($lowStockStmt);

// Get pending transfers count (both individual and batch transfers)
$pendingTransfersCountQuery = "
    SELECT
        (SELECT COUNT(*) FROM transfers
         WHERE (source_location_id = ? OR destination_location_id = ?)
         AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) +
        (SELECT COUNT(*) FROM batch_transfers
         WHERE (source_location_id = ? OR destination_location_id = ?)
         AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) AS total
";
$pendingTransfersCountStmt = mysqli_prepare($conn, $pendingTransfersCountQuery);
mysqli_stmt_bind_param($pendingTransfersCountStmt, 'iiii', $userLocationId, $userLocationId, $userLocationId, $userLocationId);
mysqli_stmt_execute($pendingTransfersCountStmt);
$pendingTransfersCountResult = mysqli_stmt_get_result($pendingTransfersCountStmt);
$pendingTransfersCount = mysqli_fetch_assoc($pendingTransfersCountResult)['total'];

// Get transfers waiting for acceptance (where this location is the destination)
$pendingTransfersQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        l1.location_name AS source_location,
        u1.full_name AS initiated_by,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT c.requires_himu_approval FROM fixed_assets a JOIN sku_master s ON a.sku_id = s.sku_id JOIN categories c ON s.category_id = c.category_id WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT c.requires_himu_approval FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id JOIN categories c ON s.category_id = c.category_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 0
        END AS requires_himu_approval
    FROM transfers t
    JOIN locations l1 ON t.source_location_id = l1.location_id
    JOIN users u1 ON t.initiated_by = u1.user_id
    WHERE t.destination_location_id = ?
    AND (
        t.status = 'Approved by HIMU'
        OR
        (t.status = 'Approved by Logistics' AND
         NOT EXISTS (
             SELECT 1 FROM fixed_assets a
             JOIN sku_master s ON a.sku_id = s.sku_id
             JOIN categories c ON s.category_id = c.category_id
             WHERE a.asset_id = t.asset_id AND c.requires_himu_approval = 1
         )
         AND
         NOT EXISTS (
             SELECT 1 FROM consumable_inventory ci
             JOIN sku_master s ON ci.sku_id = s.sku_id
             JOIN categories c ON s.category_id = c.category_id
             WHERE ci.inventory_id = t.inventory_id AND c.requires_himu_approval = 1
         )
        )
    )
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        l1.location_name AS source_location,
        u1.full_name AS initiated_by,
        b.requires_himu_approval
    FROM batch_transfers b
    JOIN locations l1 ON b.source_location_id = l1.location_id
    JOIN users u1 ON b.initiated_by = u1.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.destination_location_id = ?
    AND (
        b.status = 'Approved by HIMU'
        OR
        (b.status = 'Approved by Logistics' AND b.requires_himu_approval = 0)
    )
    GROUP BY b.batch_id
    LIMIT 5)

    ORDER BY transfer_date DESC
";
$pendingTransfersStmt = mysqli_prepare($conn, $pendingTransfersQuery);
mysqli_stmt_bind_param($pendingTransfersStmt, 'ii', $userLocationId, $userLocationId);
mysqli_stmt_execute($pendingTransfersStmt);
$pendingTransfersResult = mysqli_stmt_get_result($pendingTransfersStmt);

// Recent transfers to/from this location
$transfersQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM transfers t
    JOIN locations l1 ON t.source_location_id = l1.location_id
    JOIN locations l2 ON t.destination_location_id = l2.location_id
    JOIN users u1 ON t.initiated_by = u1.user_id
    WHERE t.source_location_id = ? OR t.destination_location_id = ?
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM batch_transfers b
    JOIN locations l1 ON b.source_location_id = l1.location_id
    JOIN locations l2 ON b.destination_location_id = l2.location_id
    JOIN users u1 ON b.initiated_by = u1.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.source_location_id = ? OR b.destination_location_id = ?
    GROUP BY b.batch_id
    LIMIT 5)

    ORDER BY transfer_date DESC
    LIMIT 5
";
$transfersStmt = mysqli_prepare($conn, $transfersQuery);
mysqli_stmt_bind_param($transfersStmt, 'iiii', $userLocationId, $userLocationId, $userLocationId, $userLocationId);
mysqli_stmt_execute($transfersStmt);
$transfersResult = mysqli_stmt_get_result($transfersStmt);

// Get latest system notifications for this location
$notificationsQuery = "
    SELECT notification_id, notification_type, title, message, is_read, created_at
    FROM notifications
    WHERE user_id = ? OR (related_entity = 'location' AND related_id = ?)
    ORDER BY created_at DESC
    LIMIT 5
";
$notificationsStmt = mysqli_prepare($conn, $notificationsQuery);
mysqli_stmt_bind_param($notificationsStmt, 'ii', $_SESSION['user_id'], $userLocationId);
mysqli_stmt_execute($notificationsStmt);
$notificationsResult = mysqli_stmt_get_result($notificationsStmt);
?>

<!-- Include Animate.css for animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Include logistics dashboard CSS for consistent styling -->
<link rel="stylesheet" href="/choims/assets/css/logistics-dashboard-modern-white.css">
<link rel="stylesheet" href="/choims/assets/css/logistics-dashboard-buttons.css">

<!-- Include modern health center dashboard CSS -->
<link rel="stylesheet" href="/choims/assets/css/health-center-dashboard-modern.css">

<!-- Include modern notifications CSS -->
<link rel="stylesheet" href="/choims/dashboards/notifications-modern.css">

<!-- Include enhanced notifications CSS -->
<link rel="stylesheet" href="/choims/dashboards/enhanced-notifications.css">

<!-- Include monthly inventory update modern CSS -->
<link rel="stylesheet" href="/choims/assets/css/monthly-inventory-update-modern.css">

<!-- Include compact styling for health center dashboard -->
<link rel="stylesheet" href="/choims/assets/css/health-center-compact.css">

<!-- Modern dashboard header -->
<header class="dashboard-header animate__animated animate__fadeIn">
    <div class="header-row">
        <div class="title-container">
            <?php
            // Extract first name from full name
            $firstName = '';
            if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                $nameParts = explode(' ', $_SESSION['full_name']);
                $firstName = $nameParts[0];
            }
            ?>
            <h1 class="dashboard-title">Hi, <?php echo htmlspecialchars($firstName); ?></h1>
        </div>
        <div class="dashboard-actions">
            <a href="/choims/modules/transfers/create.php" class="action-btn">
                <i class="fas fa-exchange-alt"></i> Initiate Transfer
            </a>
            <a href="/choims/modules/inventory/list.php" class="action-btn">
                <i class="fas fa-boxes"></i> View Consumables
            </a>
            <a href="/choims/modules/assets/list.php" class="action-btn">
                <i class="fas fa-laptop-medical"></i> Manage Assets
            </a>
            <?php if ($showMonthlyUpdateButton): ?>
            <a href="#" class="action-btn <?php echo $hasCompletedMonthlyUpdate ? 'bg-success text-white' : 'bg-warning text-dark'; ?>" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                <i class="fas fa-calendar-check"></i> <?php echo $hasCompletedMonthlyUpdate ? 'Update Completed' : 'Monthly Update'; ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
    <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
</header>

<div class="container-fluid">

    <?php if ($showMonthlyUpdateButton && !$hasCompletedMonthlyUpdate): ?>
    <!-- Monthly Inventory Update Alert -->
    <div class="alert alert-warning animate__animated animate__pulse animate__infinite" style="border-left: 4px solid #f6c23e;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-calendar-check me-2"></i> Monthly Inventory Update Required</h5>
                <p class="mb-0">Please update your consumables inventory for the month of <?php echo date('F Y'); ?>.</p>
            </div>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                <i class="fas fa-clipboard-check me-1"></i> Update Now
            </button>
        </div>
    </div>
    <?php elseif ($showMonthlyUpdateButton && $hasCompletedMonthlyUpdate): ?>
    <!-- Monthly Inventory Update Completed Alert -->
    <div class="alert alert-success" style="border-left: 4px solid #1cc88a;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-check-circle me-2"></i> Monthly Inventory Update Completed</h5>
                <p class="mb-0">Thank you for completing your inventory update for <?php echo date('F Y'); ?>.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <?php
                        // Count assets
                        $assetsQuery = "SELECT COUNT(*) as total FROM fixed_assets WHERE current_location_id = ? AND is_active = 1 AND (is_deleted = 0 OR is_deleted IS NULL)";
                        $assetsStmt = mysqli_prepare($conn, $assetsQuery);
                        mysqli_stmt_bind_param($assetsStmt, 'i', $userLocationId);
                        mysqli_stmt_execute($assetsStmt);
                        $assetsResult = mysqli_stmt_get_result($assetsStmt);
                        $assetsCount = mysqli_fetch_assoc($assetsResult)['total'];
                        ?>
                        <div class="stat-label">FIXED ASSETS</div>
                        <div class="stat-value counter-value"><?php echo number_format($assetsCount); ?></div>
                        <div class="stat-description mb-2">Total equipment count</div>
                        <div class="stat-trend">
                            <span class="text-primary"><i class="fas fa-laptop-medical me-1"></i>At your health center</span>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-assets">
                        <i class="fas fa-laptop"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/assets/list.php" class="view-details text-success">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <?php
                        // Count consumables
                        $inventoryQuery = "SELECT COUNT(*) as total FROM consumable_inventory WHERE location_id = ?";
                        $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
                        mysqli_stmt_bind_param($inventoryStmt, 'i', $userLocationId);
                        mysqli_stmt_execute($inventoryStmt);
                        $inventoryResult = mysqli_stmt_get_result($inventoryStmt);
                        $inventoryCount = mysqli_fetch_assoc($inventoryResult)['total'];
                        ?>
                        <div class="stat-label">CONSUMABLE ITEMS</div>
                        <div class="stat-value counter-value"><?php echo number_format($inventoryCount); ?></div>
                        <div class="stat-description mb-2">Total consumables in system</div>
                        <div class="stat-trend">
                            <span class="text-success"><i class="fas fa-cubes me-1"></i>Available inventory</span>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-inventory">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php" class="view-details text-primary">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <?php
                        // Count low stock items
                        $lowStockCountQuery = "SELECT COUNT(*) as total FROM consumable_inventory WHERE location_id = ? AND (status = 'Low Stock' OR status = 'Out of Stock')";
                        $lowStockCountStmt = mysqli_prepare($conn, $lowStockCountQuery);
                        mysqli_stmt_bind_param($lowStockCountStmt, 'i', $userLocationId);
                        mysqli_stmt_execute($lowStockCountStmt);
                        $lowStockCountResult = mysqli_stmt_get_result($lowStockCountStmt);
                        $lowStockCount = mysqli_fetch_assoc($lowStockCountResult)['total'];
                        ?>
                        <div class="stat-label">LOW STOCK ITEMS</div>
                        <div class="stat-value counter-value"><?php echo number_format($lowStockCount); ?></div>
                        <div class="stat-description mb-2">Items needing restocking</div>
                        <div class="stat-trend">
                            <?php if($lowStockCount > 0): ?>
                            <span class="text-warning"><i class="fas fa-exclamation-circle me-1"></i>Items need attention</span>
                            <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>All items in stock</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php?filter=low_stock" class="view-details text-warning">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">PENDING TRANSFERS</div>
                        <div class="stat-value counter-value"><?php echo number_format($pendingTransfersCount); ?></div>
                        <div class="stat-description mb-2">Awaiting approval/acceptance</div>
                        <div class="stat-trend">
                            <?php if($pendingTransfersCount > 0): ?>
                            <span class="text-primary"><i class="fas fa-clock me-1"></i>Needs attention</span>
                            <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>All transfers processed</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-transfer">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/transfers/list.php?status=pending" class="view-details text-primary">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column (Main Content) -->
        <div class="col-lg-8">
            <!-- Transfers Waiting for Confirmation -->
            <div class="chart-card mb-4 animate__animated animate__fadeIn animate__delay-1s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-truck-loading"></i> Transfers Waiting for Confirmation
                    </div>
                    <a href="/choims/modules/transfers/list.php" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All Transfers
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (mysqli_num_rows($pendingTransfersResult) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Transfer</th>
                                        <th>Date</th>
                                        <th>From</th>
                                        <th>Items</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($transfer = mysqli_fetch_assoc($pendingTransfersResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="status-badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'status-approved' : 'status-completed'; ?> mb-1">
                                                        <i class="<?php echo ($transfer['transfer_type'] == 'Batch') ? 'fas fa-layer-group' : 'fas fa-box'; ?>"></i>
                                                        <?php echo $transfer['transfer_type']; ?>
                                                    </span>
                                                    <small class="text-muted">
                                                    <?php echo !empty($transfer['transaction_code']) ? $transfer['transaction_code'] : 'N/A'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td><?php echo formatDate($transfer['transfer_date']); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['source_location']); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo ($transfer['status'] == 'Approved by Logistics') ? 'status-pending' : 'status-approved'; ?>">
                                                    <i class="<?php echo ($transfer['status'] == 'Approved by Logistics') ? 'fas fa-clock' : 'fas fa-check-circle'; ?>"></i>
                                                    <?php echo $transfer['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($transfer['transfer_type'] == 'Batch'): ?>
                                                        <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-success">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>

                                                        <?php if ($transfer['status'] == 'Approved by HIMU' || ($transfer['status'] == 'Approved by Logistics' && !$transfer['requires_himu_approval'])): ?>
                                                            <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>&action=receive" class="btn btn-success">
                                                                <i class="fas fa-check"></i> Confirm
                                                            </a>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-success">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>

                                                        <?php if ($transfer['status'] == 'Approved by HIMU' || ($transfer['status'] == 'Approved by Logistics' && !$transfer['requires_himu_approval'])): ?>
                                                            <a href="/choims/modules/transfers/approve.php?id=<?php echo $transfer['id']; ?>" class="btn btn-success">
                                                                <i class="fas fa-check"></i> Confirm
                                                            </a>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p class="empty-state-text">No transfers are currently waiting for confirmation.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Low Stock Items -->
            <div class="chart-card mb-4 animate__animated animate__fadeIn animate__delay-2s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exclamation-triangle"></i> Low Stock Items
                    </div>
                    <a href="/choims/modules/inventory/list.php?filter=low_stock" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All Low Stock
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (mysqli_num_rows($lowStockResult) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Quantity</th>
                                        <th>Threshold</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($item = mysqli_fetch_assoc($lowStockResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-box me-2 text-muted"></i>
                                                    <span><?php echo $item['sku_name']; ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <strong><?php echo $item['current_quantity']; ?></strong>
                                                <?php
                                                // Calculate percentage of stock level
                                                $percentage = 0;
                                                if ($item['low_stock_threshold'] > 0) {
                                                    $percentage = min(100, round(($item['current_quantity'] / $item['low_stock_threshold']) * 100));
                                                }
                                                $barClass = 'stock-level-critical';
                                                if ($percentage > 50) {
                                                    $barClass = 'stock-level-low';
                                                }
                                                ?>
                                                <div class="stock-level">
                                                    <div class="stock-level-bar <?php echo $barClass; ?>" style="width: <?php echo $percentage; ?>%"></div>
                                                </div>
                                            </td>
                                            <td><?php echo $item['low_stock_threshold']; ?></td>
                                            <td>
                                                <?php
                                                if ($item['status'] == 'Out of Stock') {
                                                    echo '<span class="status-badge status-critical"><i class="fas fa-times-circle"></i> Out of Stock</span>';
                                                } else {
                                                    echo '<span class="status-badge status-low"><i class="fas fa-exclamation-circle"></i> Low Stock</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/choims/modules/inventory/view.php?id=<?php echo $item['inventory_id']; ?>" class="btn btn-outline-success">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p class="empty-state-text">All items are adequately stocked.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Fixed Assets Details -->
            <div class="chart-card mb-4 animate__animated animate__fadeIn animate__delay-3s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-laptop-medical"></i> Fixed Assets Details
                    </div>
                    <a href="/choims/modules/assets/list.php" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All Assets
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php
                    // Get list of fixed assets at this location
                    $assetsQuery = "
                        SELECT a.asset_id, a.asset_name, a.serial_number, a.status, s.sku_name, c.category_name
                        FROM fixed_assets a
                        JOIN sku_master s ON a.sku_id = s.sku_id
                        JOIN categories c ON s.category_id = c.category_id
                        WHERE a.current_location_id = ? AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
                        ORDER BY a.asset_id DESC
                        LIMIT 10
                    ";
                    $assetsStmt = mysqli_prepare($conn, $assetsQuery);
                    mysqli_stmt_bind_param($assetsStmt, 'i', $userLocationId);
                    mysqli_stmt_execute($assetsStmt);
                    $assetsResult = mysqli_stmt_get_result($assetsStmt);

                    if (mysqli_num_rows($assetsResult) > 0):
                    ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Asset</th>
                                        <th>Type</th>
                                        <th>Serial Number</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($asset = mysqli_fetch_assoc($assetsResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted small me-2">#<?php echo $asset['asset_id']; ?></span>
                                                    <span><?php echo $asset['asset_name']; ?></span>
                                                </div>
                                            </td>
                                            <td><?php echo $asset['category_name']; ?></td>
                                            <td><?php echo $asset['serial_number'] ?: 'N/A'; ?></td>
                                            <td>
                                                <?php
                                                $statusClass = 'status-normal';
                                                $statusIcon = 'fas fa-check-circle';

                                                if ($asset['status'] == 'Under Repair') {
                                                    $statusClass = 'status-pending';
                                                    $statusIcon = 'fas fa-tools';
                                                } elseif ($asset['status'] == 'Defective') {
                                                    $statusClass = 'status-critical';
                                                    $statusIcon = 'fas fa-exclamation-circle';
                                                }
                                                ?>
                                                <span class="status-badge <?php echo $statusClass; ?>">
                                                    <i class="<?php echo $statusIcon; ?>"></i>
                                                    <?php echo $asset['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" class="btn btn-outline-success">
                                                        <i class="fas fa-eye"></i> View Details
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-info-circle"></i>
                            <p class="empty-state-text">No fixed assets available at this location.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Column (Sidebar) -->
        <div class="col-lg-4">
            <!-- Enhanced System Notifications Widget -->
            <div class="notifications-widget animate__animated animate__fadeIn animate__delay-1s">
                <div class="card-header">
                    <div>
                        <i class="fas fa-bell"></i>
                        <span>System Notifications</span>
                        <?php
                        // Count unread notifications
                        mysqli_data_seek($notificationsResult, 0);
                        $unreadCount = 0;
                        while ($row = mysqli_fetch_assoc($notificationsResult)) {
                            if (!$row['is_read']) {
                                $unreadCount++;
                            }
                        }
                        mysqli_data_seek($notificationsResult, 0);

                        if ($unreadCount > 0):
                        ?>
                        <span class="notification-count"><?php echo $unreadCount; ?></span>
                        <?php endif; ?>
                    </div>
                    <a href="/choims/modules/notifications/list.php" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All
                    </a>
                </div>

                <div class="card-body">
                    <?php if (mysqli_num_rows($notificationsResult) > 0): ?>
                        <div class="notifications-list">
                            <?php while ($notification = mysqli_fetch_assoc($notificationsResult)): ?>
                                <?php
                                    // Determine notification icon based on type
                                    $iconClass = 'fas fa-info-circle';
                                    $iconType = 'info';

                                    if (stripos($notification['title'], 'transfer') !== false) {
                                        $iconClass = 'fas fa-exchange-alt';
                                        $iconType = 'transfer';
                                    } elseif (stripos($notification['title'], 'approved') !== false ||
                                              stripos($notification['title'], 'completed') !== false ||
                                              stripos($notification['title'], 'inventory') !== false ||
                                              stripos($notification['title'], 'stock') !== false) {
                                        $iconClass = 'fas fa-check-circle';
                                        $iconType = 'success';
                                    } elseif (stripos($notification['title'], 'rejected') !== false ||
                                              stripos($notification['title'], 'low stock') !== false ||
                                              stripos($notification['title'], 'alert') !== false ||
                                              stripos($notification['title'], 'warning') !== false) {
                                        $iconClass = 'fas fa-exclamation-triangle';
                                        $iconType = 'warning';
                                    } elseif (stripos($notification['title'], 'error') !== false ||
                                             stripos($notification['title'], 'fail') !== false) {
                                        $iconClass = 'fas fa-times-circle';
                                        $iconType = 'danger';
                                    }

                                    // Format the time
                                    $createdAt = strtotime($notification['created_at']);
                                    $now = time();
                                    $diff = $now - $createdAt;

                                    if ($diff < 60) {
                                        $timeAgo = 'Just now';
                                    } elseif ($diff < 3600) {
                                        $mins = floor($diff / 60);
                                        $timeAgo = $mins . ' min' . ($mins > 1 ? 's' : '') . ' ago';
                                    } elseif ($diff < 86400) {
                                        $hours = floor($diff / 3600);
                                        $timeAgo = $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
                                    } elseif ($diff < 172800) {
                                        $timeAgo = 'Yesterday';
                                    } else {
                                        $timeAgo = date('M d', $createdAt);
                                    }
                                ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>">
                                    <?php if (!$notification['is_read']): ?>
                                    <div class="notification-badge"></div>
                                    <?php endif; ?>

                                    <div class="notification-icon <?php echo $iconType; ?>">
                                        <i class="<?php echo $iconClass; ?>"></i>
                                    </div>

                                    <div class="notification-content">
                                        <div class="notification-header">
                                            <h6 class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                            <span class="notification-time"><?php echo $timeAgo; ?></span>
                                        </div>

                                        <div class="notification-message">
                                            <?php echo htmlspecialchars($notification['message']); ?>
                                        </div>

                                        <div class="notification-actions">
                                            <button type="button" class="notification-action-btn" onclick="markAsRead(<?php echo $notification['notification_id']; ?>)">
                                                <i class="fas fa-check"></i> Mark as read
                                            </button>
                                            <button type="button" class="notification-action-btn" onclick="viewDetails(<?php echo $notification['notification_id']; ?>)">
                                                <i class="fas fa-eye"></i> View details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>

                        <div class="notifications-footer">
                            <span class="text-muted">Showing <?php echo mysqli_num_rows($notificationsResult); ?> notifications</span>
                            <div class="notifications-footer-actions">
                                <button type="button" class="notifications-footer-btn" onclick="markAllAsRead()">
                                    <i class="fas fa-check-double"></i> Mark all as read
                                </button>
                                <a href="/choims/modules/notifications/list.php" class="notifications-footer-btn primary">
                                    <i class="fas fa-bell"></i> Manage all
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="empty-notifications">
                            <div class="empty-notifications-icon">
                                <i class="fas fa-bell-slash"></i>
                            </div>
                            <h5 class="empty-notifications-title">All Caught Up!</h5>
                            <p class="empty-notifications-text">You don't have any notifications at this time.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Add JavaScript for notification interactions -->
            <script>
                // These functions would be connected to your backend in a real implementation
                function markAsRead(notificationId) {
                    console.log('Marking notification as read:', notificationId);
                    // In a real implementation, you would make an AJAX call to your backend
                    // For now, we'll just update the UI
                    const notification = document.querySelector(`.notification-item:has(button[onclick*="${notificationId}"])`);
                    if (notification) {
                        notification.classList.remove('unread');
                        const badge = notification.querySelector('.notification-badge');
                        if (badge) badge.remove();

                        // Update the unread count
                        const countBadge = document.querySelector('.notification-count');
                        if (countBadge) {
                            const currentCount = parseInt(countBadge.textContent);
                            if (currentCount > 1) {
                                countBadge.textContent = currentCount - 1;
                            } else {
                                countBadge.remove();
                            }
                        }
                    }
                }

                function viewDetails(notificationId) {
                    console.log('Viewing notification details:', notificationId);
                    // In a real implementation, this would navigate to a details page or open a modal
                    window.location.href = `/choims/modules/notifications/view.php?id=${notificationId}`;
                }

                function markAllAsRead() {
                    console.log('Marking all notifications as read');
                    // In a real implementation, you would make an AJAX call to your backend
                    // For now, we'll just update the UI
                    document.querySelectorAll('.notification-item.unread').forEach(item => {
                        item.classList.remove('unread');
                        const badge = item.querySelector('.notification-badge');
                        if (badge) badge.remove();
                    });

                    // Remove the count badge
                    const countBadge = document.querySelector('.notification-count');
                    if (countBadge) countBadge.remove();
                }
            </script>

            <!-- Recent Transfers -->
            <div class="chart-card mb-4 animate__animated animate__fadeIn animate__delay-2s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exchange-alt"></i> Recent Transfers
                    </div>
                    <a href="/choims/modules/transfers/list.php" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (mysqli_num_rows($transfersResult) > 0): ?>
                        <div class="list-group list-group-flush">
                            <?php while($transfer = mysqli_fetch_assoc($transfersResult)): ?>
                                <div class="list-group-item border-0 py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <?php if ($transfer['source_location'] == $location['location_name']): ?>
                                                <div class="avatar-sm rounded-circle bg-danger text-white d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-arrow-right"></i>
                                                </div>
                                            <?php else: ?>
                                                <div class="avatar-sm rounded-circle bg-success text-white d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-arrow-left"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <span class="status-badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'status-approved' : 'status-completed'; ?> me-2">
                                                    <i class="<?php echo ($transfer['transfer_type'] == 'Batch') ? 'fas fa-layer-group' : 'fas fa-box'; ?>"></i>
                                                    <?php echo $transfer['transfer_type']; ?>
                                                </span>
                                                <small class="text-muted"><?php echo formatDate($transfer['transfer_date']); ?></small>
                                            </div>
                                            <p class="mb-1 text-truncate">
                                                <?php echo htmlspecialchars($transfer['item_name']); ?>
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <?php
                                                    if ($transfer['source_location'] == $location['location_name']) {
                                                        echo '<span class="responsive-status outgoing"><i class="fas fa-arrow-right"></i> To: ' . htmlspecialchars($transfer['destination_location']) . '</span>';
                                                    } else {
                                                        echo '<span class="responsive-status incoming"><i class="fas fa-arrow-left"></i> From: ' . htmlspecialchars($transfer['source_location']) . '</span>';
                                                    }
                                                    ?>
                                                </small>
                                                <?php
                                                $statusClass = 'status-pending';
                                                $statusIcon = 'fas fa-clock';

                                                switch ($transfer['status']) {
                                                    case 'Pending':
                                                        $statusClass = 'status-pending';
                                                        $statusIcon = 'fas fa-clock';
                                                        break;
                                                    case 'Approved by Logistics':
                                                    case 'Approved by HIMU':
                                                        $statusClass = 'status-approved';
                                                        $statusIcon = 'fas fa-check-circle';
                                                        break;
                                                    case 'Completed':
                                                        $statusClass = 'status-completed';
                                                        $statusIcon = 'fas fa-check-double';
                                                        break;
                                                    case 'Rejected':
                                                        $statusClass = 'status-rejected';
                                                        $statusIcon = 'fas fa-times-circle';
                                                        break;
                                                    default:
                                                        $statusClass = 'status-pending';
                                                        $statusIcon = 'fas fa-clock';
                                                }
                                                ?>
                                                <span class="status-badge <?php echo $statusClass; ?>">
                                                    <i class="<?php echo $statusIcon; ?>"></i>
                                                    <?php echo $transfer['status']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-exchange-alt"></i>
                            <p class="empty-state-text">No recent transfers.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>


        </div>
    </div>
</div>

<!-- Add JavaScript for counter animation -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Counter animation for stat values
        const counterElements = document.querySelectorAll('.counter-value');

        counterElements.forEach(function(element) {
            const target = parseInt(element.textContent.replace(/,/g, ''));
            const duration = 1500; // Animation duration in milliseconds
            const startTime = Date.now();
            const startValue = 0;

            function updateCounter() {
                const currentTime = Date.now();
                const elapsedTime = currentTime - startTime;

                if (elapsedTime < duration) {
                    const value = Math.floor(easeOutQuad(elapsedTime, startValue, target, duration));
                    element.textContent = value.toLocaleString();
                    requestAnimationFrame(updateCounter);
                } else {
                    element.textContent = target.toLocaleString();
                }
            }

            function easeOutQuad(t, b, c, d) {
                t /= d;
                return -c * t * (t - 2) + b;
            }

            updateCounter();
        });
    });
</script>

<?php if ($showMonthlyUpdateButton): ?>
<!-- Monthly Inventory Update Modal -->
<div class="modal fade" id="monthlyUpdateModal" tabindex="-1" aria-labelledby="monthlyUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content inventory-modal">
            <div class="modal-header inventory-modal-header">
                <div class="modal-title-wrapper">
                    <div class="modal-icon-circle">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h5 class="modal-title" id="monthlyUpdateModalLabel">
                        Monthly Inventory Update - <?php echo date('F Y'); ?>
                    </h5>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body inventory-modal-body">
                <?php if ($hasCompletedMonthlyUpdate): ?>
                    <div class="alert alert-success inventory-alert">
                        <div class="alert-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="alert-heading">Update Complete!</h6>
                            <p class="mb-0">You have already completed your monthly inventory update for <?php echo date('F Y'); ?>.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="inventory-intro">
                        <h5 class="inventory-intro-title">Time for your monthly inventory check</h5>
                        <p class="inventory-intro-text">
                            Please confirm that you have reviewed and updated your consumables inventory for the month of <?php echo date('F Y'); ?>.
                            This will help ensure accurate inventory tracking and planning.
                        </p>
                    </div>

                    <form action="/choims/modules/inventory/monthly_update.php" method="post" class="inventory-update-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="update_quantities" value="1">

                    <!-- Inventory Items to Update -->
                    <div class="inventory-card">
                        <div class="inventory-card-header">
                            <div class="inventory-card-title">
                                <i class="fas fa-edit"></i>
                                <span>Update Inventory Quantities</span>
                            </div>
                        </div>
                        <div class="inventory-card-body">
                            <div class="inventory-table-container">
                                <table class="inventory-table" id="inventoryItemsTable">
                                    <thead>
                                        <tr>
                                            <th>SKU Code</th>
                                            <th>Item Name</th>
                                            <th>Category</th>
                                            <th>Current Qty</th>
                                            <th>New Qty</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Get inventory items for this location
                                        $itemsQuery = "
                                            SELECT
                                                i.inventory_id,
                                                i.current_quantity,
                                                i.status,
                                                s.sku_id,
                                                s.sku_code,
                                                s.sku_name,
                                                c.category_name
                                            FROM
                                                consumable_inventory i
                                            JOIN
                                                sku_master s ON i.sku_id = s.sku_id
                                            JOIN
                                                categories c ON s.category_id = c.category_id
                                            WHERE
                                                i.location_id = ?
                                            ORDER BY
                                                c.category_name, s.sku_name
                                        ";
                                        $itemsStmt = mysqli_prepare($conn, $itemsQuery);
                                        mysqli_stmt_bind_param($itemsStmt, 'i', $userLocationId);
                                        mysqli_stmt_execute($itemsStmt);
                                        $itemsResult = mysqli_stmt_get_result($itemsStmt);

                                        $rowCount = 0;

                                        while ($item = mysqli_fetch_assoc($itemsResult)):
                                            $rowCount++;
                                        ?>
                                        <tr class="inventory-item-row">
                                            <td><span class="sku-code"><?php echo $item['sku_code']; ?></span></td>
                                            <td><?php echo $item['sku_name']; ?></td>
                                            <td><span class="category-name"><?php echo $item['category_name']; ?></span></td>
                                            <td>
                                                <span class="current-qty"><?php echo $item['current_quantity']; ?></span>
                                            </td>
                                            <td>
                                                <input type="hidden" name="inventory_ids[]" value="<?php echo $item['inventory_id']; ?>">
                                                <input type="hidden" name="old_quantities[]" value="<?php echo $item['current_quantity']; ?>">
                                                <div class="quantity-input-wrapper">
                                                    <input type="number" name="new_quantities[]" class="quantity-input new-qty"
                                                           value="<?php echo $item['current_quantity']; ?>" min="0">
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($item['status'] == 'Available'): ?>
                                                    <span class="status-badge status-available">Available</span>
                                                <?php elseif ($item['status'] == 'Low Stock'): ?>
                                                    <span class="status-badge status-low">Low Stock</span>
                                                <?php else: ?>
                                                    <span class="status-badge status-out">Out of Stock</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="inventory-tip">
                        <div class="tip-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="tip-content">
                            <h6>Tip</h6>
                            <p>Update the quantities above to reflect your current inventory. Any changes will be recorded in the audit log.</p>
                        </div>
                    </div>

                    <div class="form-group notes-group">
                        <label for="notes" class="form-label">Notes or Comments (Optional)</label>
                        <textarea class="form-control notes-input" id="notes" name="notes" rows="3" placeholder="Enter any notes about your inventory update..."></textarea>
                    </div>

                    <div class="confirm-checkbox">
                        <input class="confirm-input" type="checkbox" id="confirmUpdate" required>
                        <label class="confirm-label" for="confirmUpdate">
                            I confirm that I have reviewed and updated the inventory for <?php echo $location['location_name']; ?> for the month of <?php echo date('F Y'); ?>.
                        </label>
                    </div>

                    <div class="form-actions">
                        <a href="/choims/modules/inventory/list.php" class="btn-review">
                            <i class="fas fa-list"></i> Review Inventory First
                        </a>
                        <button type="submit" class="btn-complete" id="submitUpdate" disabled>
                            <i class="fas fa-check-circle"></i> Complete Monthly Update
                        </button>
                    </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Enhanced inventory update modal interactions
    document.addEventListener('DOMContentLoaded', function() {
        const confirmCheckbox = document.getElementById('confirmUpdate');
        const submitButton = document.getElementById('submitUpdate');
        const inventoryItemRows = document.querySelectorAll('.inventory-item-row');

        // Enable/disable submit button based on checkbox with animation
        if (confirmCheckbox && submitButton) {
            confirmCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    submitButton.disabled = false;
                    submitButton.classList.add('btn-complete-active');
                } else {
                    submitButton.disabled = true;
                    submitButton.classList.remove('btn-complete-active');
                }
            });
        }

        // Enhanced quantity input interactions
        const newQtyInputs = document.querySelectorAll('.new-qty');
        if (newQtyInputs) {
            newQtyInputs.forEach(input => {
                // Add focus effects
                input.addEventListener('focus', function() {
                    this.closest('.quantity-input-wrapper').classList.add('input-focused');
                });

                input.addEventListener('blur', function() {
                    this.closest('.quantity-input-wrapper').classList.remove('input-focused');
                });

                // Highlight changed quantities with animation
                input.addEventListener('change', function() {
                    const row = this.closest('tr');
                    const currentQty = parseInt(row.querySelector('.current-qty').textContent);
                    const newQty = parseInt(this.value);

                    if (newQty !== currentQty) {
                        // Add changed state
                        this.classList.add('qty-changed');
                        row.classList.add('row-changed');

                        // Add visual indicator for increase/decrease
                        if (newQty > currentQty) {
                            this.classList.add('qty-increased');
                            this.classList.remove('qty-decreased');
                        } else if (newQty < currentQty) {
                            this.classList.add('qty-decreased');
                            this.classList.remove('qty-increased');
                        }
                    } else {
                        // Remove all change indicators
                        this.classList.remove('qty-changed', 'qty-increased', 'qty-decreased');
                        row.classList.remove('row-changed');
                    }
                });
            });
        }

        // Add animation to the modal when it opens
        const monthlyUpdateModal = document.getElementById('monthlyUpdateModal');
        if (monthlyUpdateModal) {
            monthlyUpdateModal.addEventListener('shown.bs.modal', function() {
                const modalContent = this.querySelector('.modal-content');
                modalContent.classList.add('modal-animate-in');
            });
        }
    });
</script>
<?php endif; ?>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>