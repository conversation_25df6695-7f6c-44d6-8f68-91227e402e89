<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start a session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Required files
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/detailed_audit_log.php';

// Set header for JSON response
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'You must be logged in to perform this action']);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['error' => 'Invalid request method']);
    exit;
}

// Check if action is specified
if (!isset($_POST['action']) || $_POST['action'] !== 'add_sku') {
    echo json_encode(['error' => 'Invalid action']);
    exit;
}

// Get form data
$category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
$sku_code = isset($_POST['sku_code']) ? trim($_POST['sku_code']) : '';
$sku_name = isset($_POST['sku_name']) ? trim($_POST['sku_name']) : '';
$unit_of_measure = isset($_POST['unit_of_measure']) ? trim($_POST['unit_of_measure']) : '';
$description = isset($_POST['description']) ? trim($_POST['description']) : '';
$item_type = isset($_POST['item_type']) ? trim($_POST['item_type']) : 'Fixed';

// Validate required fields
if (empty($category_id) || empty($sku_code) || empty($sku_name) || empty($unit_of_measure)) {
    echo json_encode(['error' => 'All required fields must be filled']);
    exit;
}

// Check if SKU code already exists
$check_sql = "SELECT sku_id FROM sku_master WHERE sku_code = ?";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->bind_param("s", $sku_code);
$check_stmt->execute();
$result = $check_stmt->get_result();

if ($result->num_rows > 0) {
    echo json_encode(['error' => 'SKU with this code already exists']);
    exit;
}
$check_stmt->close();

// Insert new SKU
$sql = "INSERT INTO sku_master (category_id, sku_code, sku_name, description, unit_of_measure, item_type, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
$stmt = $conn->prepare($sql);
$stmt->bind_param("isssss", $category_id, $sku_code, $sku_name, $description, $unit_of_measure, $item_type);

if ($stmt->execute()) {
    $sku_id = $stmt->insert_id;
    
    // Log the activity
    logActivity($conn, 'Added new SKU: ' . $sku_code . ' - ' . $sku_name, 'sku', $sku_id);
    
    // Return success response with the new SKU ID
    echo json_encode([
        'success' => true,
        'message' => 'SKU added successfully',
        'sku_id' => $sku_id,
        'sku_code' => $sku_code,
        'sku_name' => $sku_name
    ]);
} else {
    echo json_encode(['error' => 'Error adding SKU: ' . $conn->error]);
}

$stmt->close();
