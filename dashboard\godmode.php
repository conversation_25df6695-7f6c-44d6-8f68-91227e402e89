<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has GODMODE permission
if (!$auth->hasRole(ROLE_GODMODE)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$currentUser = $auth->getCurrentUser();

try {
    // Fetch inventory statistics
    $stmt = $db->query("
        SELECT 
            (SELECT COUNT(*) FROM fixed_assets) as total_assets,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'in_use') as assets_in_use,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'under_repair') as assets_under_repair,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'defective') as defective_assets,
            (SELECT COUNT(DISTINCT item_id) FROM inventory WHERE quantity > 0) as consumables_in_stock,
            (SELECT COUNT(DISTINCT item_id) FROM inventory WHERE quantity <= minimum_stock) as low_stock_items
    ");
    $inventoryStats = $stmt->fetch();

    // Fetch transfer statistics
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_transfers,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_transfers,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_transfers,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_transfers,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_transfers
        FROM transfer_requests
    ");
    $transferStats = $stmt->fetch();

    // Fetch recent transfers
    $stmt = $db->query("
        SELECT tr.*, 
               i.name as item_name,
               CONCAT(u.first_name, ' ', u.last_name) as requester_name,
               sd.name as source_dept,
               shc.name as source_hc,
               dd.name as dest_dept,
               dhc.name as dest_hc
        FROM transfer_requests tr
        JOIN items i ON tr.item_id = i.item_id
        JOIN users u ON tr.requested_by = u.user_id
        LEFT JOIN departments sd ON tr.source_department_id = sd.department_id
        LEFT JOIN health_centers shc ON tr.source_health_center_id = shc.health_center_id
        LEFT JOIN departments dd ON tr.destination_department_id = dd.department_id
        LEFT JOIN health_centers dhc ON tr.destination_health_center_id = dhc.health_center_id
        ORDER BY tr.created_at DESC
        LIMIT 5
    ");
    $recentTransfers = $stmt->fetchAll();

    // Fetch low stock alerts
    $stmt = $db->query("
        SELECT i.name, inv.quantity, inv.minimum_stock,
               COALESCE(d.name, hc.name) as location
        FROM inventory inv
        JOIN items i ON inv.item_id = i.item_id
        LEFT JOIN departments d ON inv.department_id = d.department_id
        LEFT JOIN health_centers hc ON inv.health_center_id = hc.health_center_id
        WHERE inv.quantity <= inv.minimum_stock
        ORDER BY inv.quantity ASC
        LIMIT 5
    ");
    $lowStockAlerts = $stmt->fetchAll();

    // Fetch recent activities
    $stmt = $db->query("
        SELECT al.*, 
               CONCAT(u.first_name, ' ', u.last_name) as user_name
        FROM audit_logs al
        JOIN users u ON al.user_id = u.user_id
        ORDER BY al.created_at DESC
        LIMIT 5
    ");
    $recentActivities = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error in GODMODE dashboard: " . $e->getMessage());
    setFlashMessage('error', 'Error loading dashboard data');
}

require_once '../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">GODMODE Dashboard</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Export Reports
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="../reports/inventory.php">Inventory Report</a></li>
                <li><a class="dropdown-item" href="../reports/transfers.php">Transfer Report</a></li>
                <li><a class="dropdown-item" href="../reports/assets.php">Asset Report</a></li>
                <li><a class="dropdown-item" href="../reports/stock-movement.php">Stock Movement Report</a></li>
            </ul>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <!-- Fixed Assets Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Fixed Assets
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['total_assets']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-laptop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consumables Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Consumables In Stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['consumables_in_stock']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Transfers -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Transfers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($transferStats['pending_transfers']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['low_stock_items']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Recent Transfers -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt mr-1"></i>
                        Recent Transfers
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Item</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentTransfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>">
                                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                    <td>
                                        <?php 
                                        if (isset($transfer['source_dept']) && !empty($transfer['source_dept'])) {
                                            echo htmlspecialchars($transfer['source_dept']);
                                        } elseif (isset($transfer['source_hc']) && !empty($transfer['source_hc'])) {
                                            echo htmlspecialchars($transfer['source_hc']);
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if (isset($transfer['dest_dept']) && !empty($transfer['dest_dept'])) {
                                            echo htmlspecialchars($transfer['dest_dept']);
                                        } elseif (isset($transfer['dest_hc']) && !empty($transfer['dest_hc'])) {
                                            echo htmlspecialchars($transfer['dest_hc']);
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            $status_class = 'secondary';
                                            if ($transfer['status'] == 'pending') {
                                                $status_class = 'warning';
                                            } elseif ($transfer['status'] == 'approved') {
                                                $status_class = 'info';
                                            } elseif ($transfer['status'] == 'completed') {
                                                $status_class = 'success';
                                            } elseif ($transfer['status'] == 'rejected') {
                                                $status_class = 'danger';
                                            }
                                            echo $status_class;
                                        ?>">
                                            <?php echo ucfirst($transfer['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/transfers/" class="btn btn-primary btn-sm">
                            View All Transfers
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Alerts -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        Low Stock Alerts
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Location</th>
                                    <th>Current Stock</th>
                                    <th>Min. Stock</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockAlerts as $alert): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($alert['name']); ?></td>
                                    <td><?php echo htmlspecialchars($alert['location']); ?></td>
                                    <td class="text-danger"><?php echo number_format($alert['quantity']); ?></td>
                                    <td><?php echo number_format($alert['minimum_stock']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/consumables/" class="btn btn-danger btn-sm">
                            View All Stock Levels
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history mr-1"></i>
                        Recent Activities
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <?php foreach ($recentActivities as $activity): ?>
                        <div class="timeline-item">
                            <div class="timeline-item-marker">
                                <div class="timeline-item-marker-text">
                                    <?php echo formatDate($activity['created_at'], 'M d, H:i'); ?>
                                </div>
                                <div class="timeline-item-marker-indicator bg-primary"></div>
                            </div>
                            <div class="timeline-item-content">
                                <?php echo htmlspecialchars($activity['user_name']); ?> 
                                <?php echo htmlspecialchars($activity['action']); ?> 
                                <?php echo htmlspecialchars($activity['entity_type']); ?> 
                                #<?php echo htmlspecialchars($activity['entity_id']); ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../admin/audit-logs.php" class="btn btn-primary btn-sm">
                            View All Activities
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../templates/footer.php'; ?>
