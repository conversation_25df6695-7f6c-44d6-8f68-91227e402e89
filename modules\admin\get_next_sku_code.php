<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start a session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Required files
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Set header for JSON response
header('Content-Type: application/json');

// Check if request has category_id parameter
if (!isset($_GET['category_id']) || empty($_GET['category_id'])) {
    echo json_encode(['error' => 'Category ID is required']);
    exit;
}

// Get category ID from request
$category_id = (int)$_GET['category_id'];

// Define category code mapping
$category_codes = [
    1 => 'IE', // IT Equipment
    2 => 'OE', // Office Equipment
    3 => 'ME', // Medical Equipment
    4 => 'IS', // IT Supply
    5 => 'OS', // Office Supply
    6 => 'MS'  // Medical Supply
];

// Check if category ID exists in our mapping
if (!isset($category_codes[$category_id])) {
    echo json_encode(['error' => 'Invalid category ID']);
    exit;
}

// Get the 2-letter code for this category
$category_code = $category_codes[$category_id];

// Find the next available SKU number for this category
$pattern = $category_code . '-%';

$sql = "SELECT MAX(CAST(SUBSTRING_INDEX(sku_code, '-', -1) AS UNSIGNED)) as max_num 
        FROM sku_master 
        WHERE sku_code LIKE ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $pattern);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

if ($row['max_num'] === null) {
    // No SKUs yet for this category
    $next_sku = $category_code . '-0001';
} else {
    // Increment the last number
    $next_num = intval($row['max_num']) + 1;
    $next_sku = $category_code . '-' . str_pad($next_num, 4, '0', STR_PAD_LEFT);
}

// Return the next SKU code
echo json_encode(['sku_code' => $next_sku]); 