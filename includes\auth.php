<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';

class Auth {
    private static $instance = null;
    private $db;
    private $user = null;

    private function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function login($username, $password) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM users WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Store user data in session
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['department_id'] = $user['department_id'];
                $_SESSION['health_center_id'] = $user['health_center_id'];

                // Log login activity
                $this->logActivity($user['user_id'], 'login', 'users', $user['user_id']);

                return true;
            }
            return false;
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }

    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'users', $_SESSION['user_id']);
        }

        // Destroy session
        session_destroy();

        // Clear session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
    }

    public function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }

    public function getCurrentUser() {
        if ($this->user === null && isset($_SESSION['user_id'])) {
            try {
                $stmt = $this->db->prepare("SELECT * FROM users WHERE user_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $this->user = $stmt->fetch();
            } catch (PDOException $e) {
                error_log("Error fetching user: " . $e->getMessage());
                return null;
            }
        }
        return $this->user;
    }

    public function hasRole($role) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        if ($role === $_SESSION['role']) {
            return true;
        }

        // GodMode has access to everything
        if ($_SESSION['role'] === ROLE_GODMODE) {
            return true;
        }

        return false;
    }

    public function requireRole($role) {
        if (!$this->hasRole($role)) {
            header("Location: " . BASE_URL . "/access-denied.php");
            exit;
        }
    }

    public function canAccessDepartment($departmentId) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        // GodMode and Superadmin can access all departments
        if (in_array($_SESSION['role'], [ROLE_GODMODE, ROLE_SUPERADMIN])) {
            return true;
        }

        // Department users can only access their own department
        if ($_SESSION['role'] === ROLE_DEPARTMENT) {
            return $_SESSION['department_id'] == $departmentId;
        }

        return false;
    }

    public function canAccessHealthCenter($healthCenterId) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        // GodMode and Superadmin can access all health centers
        if (in_array($_SESSION['role'], [ROLE_GODMODE, ROLE_SUPERADMIN])) {
            return true;
        }

        // Health center users can only access their own health center
        if ($_SESSION['role'] === ROLE_HEALTH_CENTER) {
            return $_SESSION['health_center_id'] == $healthCenterId;
        }

        return false;
    }

    private function logActivity($userId, $action, $entityType, $entityId) {
        try {
            // Log to regular audit logs
            $stmt = $this->db->prepare(
                "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, ip_address)
                 VALUES (?, ?, ?, ?, ?)"
            );
            $stmt->execute([
                $userId,
                $action,
                $entityType,
                $entityId,
                isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0'
            ]);

            // Also log to detailed audit logs
            if ($action == 'login' || $action == 'logout') {
                require_once __DIR__ . '/detailed_audit_log.php';
                $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

                if ($conn) {
                    $new_values = [
                        'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0',
                        'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown'
                    ];

                    logUserAction($conn, $userId, $action, $userId, null, $new_values);
                    mysqli_close($conn);
                }
            }
        } catch (PDOException $e) {
            error_log("Error logging activity: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error in detailed logging: " . $e->getMessage());
        }
    }

    // Prevent cloning of the instance
    private function __clone() {}

    // Prevent unserializing of the instance
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}
