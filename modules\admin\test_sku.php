<?php
// Enable detailed error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Start session if needed
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include needed files
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Don't include the header yet - check everything first
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SKU Access Diagnostic</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3>SKU Access Diagnostic</h3>
            </div>
            <div class="card-body">
                <h4>Session Information</h4>
                <ul class="list-group mb-4">
                    <li class="list-group-item">
                        <strong>Session ID:</strong> <?php echo session_id(); ?>
                    </li>
                    <li class="list-group-item">
                        <strong>User ID:</strong> <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Not set'; ?>
                    </li>
                    <li class="list-group-item">
                        <strong>Role:</strong> <?php echo isset($_SESSION['role']) ? $_SESSION['role'] : 'Not set'; ?>
                    </li>
                    <li class="list-group-item">
                        <strong>Location ID:</strong> <?php echo isset($_SESSION['location_id']) ? $_SESSION['location_id'] : 'Not set'; ?>
                    </li>
                </ul>

                <h4>Role Checks</h4>
                <ul class="list-group mb-4">
                    <li class="list-group-item">
                        <strong>isLoggedIn():</strong> <?php echo isLoggedIn() ? 'Yes' : 'No'; ?>
                    </li>
                    <li class="list-group-item">
                        <strong>hasRole('superadmin'):</strong> <?php echo hasRole('superadmin') ? 'Yes' : 'No'; ?>
                    </li>
                    <li class="list-group-item">
                        <strong>hasRole('godmode'):</strong> <?php echo hasRole('godmode') ? 'Yes' : 'No'; ?>
                    </li>
                    <li class="list-group-item">
                        <strong>SKU role check pass:</strong> 
                        <?php 
                        $roleCheck = isset($_SESSION['user_id']) && isset($_SESSION['role']) && 
                                    ($_SESSION['role'] === 'superadmin' || $_SESSION['role'] === 'godmode');
                        echo $roleCheck ? 'Yes' : 'No'; 
                        ?>
                    </li>
                </ul>

                <h4>Full Session Data</h4>
                <pre><?php print_r($_SESSION); ?></pre>

                <div class="mt-4">
                    <a href="../../dashboards/superadmin.php" class="btn btn-secondary">Back to Dashboard</a>
                    <a href="sku.php" class="btn btn-primary">Try SKU Management</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 