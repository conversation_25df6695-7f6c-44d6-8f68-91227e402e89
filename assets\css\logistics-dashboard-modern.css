/* Logistics Dashboard - Modern Rounded White Design */

:root {
  /* Modern Color Palette - Based on reference image */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --secondary: #1B5E20;
  --text-on-primary: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-accent: #f5f5f5;
  --border-color: #f0f0f0;
  --success: #4CAF50;
  --warning: #ff9800;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #E8F5E9;
  --dark: #1B5E20;

  /* Background Colors */
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --bg-pattern: #f9f9f9;

  /* Shadows & Effects */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 1rem;
  --space-4: 1.5rem;
  --space-5: 2rem;
  --space-6: 3rem;
}

/* Base styles and typography */
body {
  background-color: var(--bg-light);
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.8) 2px, transparent 2px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.8) 2px, transparent 2px),
    linear-gradient(rgba(255, 255, 255, 0.6) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.6) 1px, transparent 1px);
  background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
  background-position: -2px -2px, -2px -2px, -1px -1px, -1px -1px;
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: 0 1.5rem;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--dark);
}

.h2 {
  font-size: 1.75rem;
  letter-spacing: -0.025em;
}

.text-muted {
  color: #64748b !important;
}

/* Dashboard header section */
.dashboard-header {
  position: relative;
  background: #ffffff;
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 2rem 2rem 1.75rem;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  color: var(--text-primary);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.dashboard-header::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
}

.dashboard-header .title-container {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0;
  letter-spacing: -0.025em;
}

.dashboard-title-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.75rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  margin-right: 1.25rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-title-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);
  z-index: 1;
}

.dashboard-date {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.dashboard-date i {
  margin-right: 0.5rem;
  opacity: 0.8;
}

.dashboard-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.action-btn {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius);
  padding: 0.625rem 1.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: var(--transition);
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  color: white;
}

.action-btn i {
  font-size: 0.9rem;
}

.action-btn.action-primary {
  background-color: var(--success);
  border-color: var(--success);
}

.action-btn.action-primary:hover {
  background-color: var(--primary);
}

/* Quick stats overview */
.quick-stats {
  margin-top: 1rem;
  display: flex;
  gap: 2rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: var(--text-primary);
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Buttons */
.btn {
  border-radius: var(--radius);
  font-weight: 600;
  padding: 0.625rem 1.25rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  text-decoration: none !important;
}

.btn::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0));
  z-index: 1;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn:active {
  transform: translateY(0);
}

.btn-success {
  background-color: var(--success);
  border: none;
}

.btn-success:hover {
  background-color: #0d9668;
}

.btn-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius);
  margin-right: 0.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.btn-with-icon:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-with-icon:last-child {
  margin-right: 0;
}

/* Dashboard cards */
.dashboard-card {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  overflow: hidden;
  height: 100%;
  background: var(--bg-white);
  position: relative;
  border-top: 6px solid transparent;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 0;
  width: 100%;
  height: 6px;
  z-index: 5;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  opacity: 0;
  transition: var(--transition);
}

.dashboard-card:nth-child(1) { border-top-color: var(--success); }
.dashboard-card:nth-child(2) { border-top-color: var(--info); }
.dashboard-card:nth-child(3) { border-top-color: var(--warning); }
.dashboard-card:nth-child(4) { border-top-color: var(--primary); }

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.dashboard-card:hover::before {
  opacity: 1;
}

.dashboard-card .card-body {
  padding: 1.5rem;
  flex: 1;
}

.dashboard-card .card-footer {
  background-color: var(--bg-white);
  border-top: 1px solid var(--bg-accent);
  padding: var(--space-3) var(--space-4);
  transition: var(--transition);
}

.dashboard-card:hover .card-footer {
  background-color: var(--bg-light);
}

/* Card stats styling */
.stat-label {
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-description {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.stat-trend {
  font-size: 0.875rem;
  font-weight: 600;
}

.stat-trend .text-success { color: var(--success) !important; }
.stat-trend .text-primary { color: var(--info) !important; }
.stat-trend .text-warning { color: var(--warning) !important; }

/* Dashboard icons */
.dashboard-icon {
  width: 64px;
  height: 64px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.75rem;
  position: relative;
  overflow: hidden;
}

.dashboard-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);
  z-index: 1;
}

.icon-assets {
  background-color: var(--success);
  box-shadow: 0 10px 15px rgba(76, 175, 80, 0.2);
}

.icon-inventory {
  background-color: var(--info);
  box-shadow: 0 10px 15px rgba(139, 195, 74, 0.2);
}

.icon-warning {
  background-color: var(--warning);
  box-shadow: 0 10px 15px rgba(255, 193, 7, 0.2);
}

.icon-transfer {
  background-color: var(--primary);
  box-shadow: 0 10px 15px rgba(46, 125, 50, 0.2);
}

/* View Details */
.view-details {
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition);
  text-decoration: none !important;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius);
}

.view-details:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.view-details i {
  transition: var(--transition);
}

.view-details:hover i {
  transform: translateX(5px);
}

.text-success.view-details:hover { color: var(--success) !important; }
.text-primary.view-details:hover { color: var(--info) !important; }
.text-warning.view-details:hover { color: var(--warning) !important; }

/* Chart cards */
.chart-card {
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  background-color: var(--bg-white);
  transition: var(--transition);
  margin-bottom: var(--space-4);
  position: relative;
}

.chart-card:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.chart-card .card-header {
  padding: var(--space-4);
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--bg-accent);
  font-weight: 700;
  display: flex;
  align-items: center;
  color: var(--dark);
  font-size: 1rem;
}

.chart-card .card-header i {
  color: var(--primary);
  margin-right: var(--space-2);
  font-size: 1.125rem;
  width: 30px;
  height: 30px;
  background-color: rgba(46, 125, 50, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-card .card-body {
  padding: var(--space-3);
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-actions .btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-light);
  color: var(--secondary);
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-actions .btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.chart-actions .btn:hover::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.5;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

.chart-actions .btn:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
}

.chart-actions .btn.active {
  background-color: var(--success);
  color: white;
}

/* Chart legend */
.chart-legend {
  margin-top: var(--space-3);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: var(--space-3);
  margin-bottom: var(--space-2);
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: var(--space-2);
}

.legend-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark);
}

/* Table styling */
.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.table thead th {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
  padding: 1rem;
}

.table tbody td {
  padding: var(--space-3);
  vertical-align: middle;
  border-color: var(--bg-accent);
  font-size: 0.875rem;
  transition: var(--transition);
}

.table tbody tr {
  transition: var(--transition);
  position: relative;
  border-bottom: 1px solid rgba(46, 125, 50, 0.1);
}

.table tbody tr:hover {
  background-color: rgba(76, 175, 80, 0.05);
}

.table tbody tr::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--bg-accent), transparent);
}

.text-truncate {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

/* Status badges */
.status-badge {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  transition: var(--transition);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.status-badge::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
  z-index: -1;
  transition: var(--transition);
}

.status-badge:hover::before {
  width: 100%;
}

.badge-pending {
  background-color: rgba(255, 193, 7, 0.15);
  color: #FFC107;
}

.badge-pending::before {
  background-color: rgba(255, 193, 7, 0.05);
}

.badge-approved {
  background-color: rgba(139, 195, 74, 0.15);
  color: var(--info);
}

.badge-approved::before {
  background-color: rgba(139, 195, 74, 0.05);
}

.badge-completed {
  background-color: rgba(76, 175, 80, 0.15);
  color: var(--success);
}

.badge-completed::before {
  background-color: rgba(76, 175, 80, 0.05);
}

.badge-rejected {
  background-color: rgba(220, 53, 69, 0.15);
  color: var(--danger);
}

.badge-rejected::before {
  background-color: rgba(220, 53, 69, 0.05);
}

/* Low stock container */
.low-stock-container {
  background-color: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  transition: var(--transition);
}

.low-stock-container:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.low-stock-header {
  background: var(--primary);
  color: white;
  padding: var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.low-stock-header-title {
  font-weight: 700;
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.low-stock-header-title i {
  color: var(--warning);
}

.btn-view-all {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
  display: flex;
  align-items: center;
}

.btn-view-all:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.low-stock-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.low-stock-table th, .low-stock-table td {
  padding: var(--space-3);
  font-size: 0.875rem;
}

.low-stock-table th {
  background-color: var(--bg-light);
  font-weight: 600;
  color: var(--secondary);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.low-stock-table td {
  border-bottom: 1px solid var(--bg-accent);
}

.low-stock-table tr:last-child td {
  border-bottom: none;
}

.low-stock-table tr:hover td {
  background-color: var(--bg-light);
}

.low-stock-empty {
  padding: var(--space-6);
  text-align: center;
}

.low-stock-empty i {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: var(--space-3);
}

.low-stock-empty p {
  color: #64748b;
  font-size: 1rem;
  margin-bottom: var(--space-3);
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: var(--space-6) var(--space-4);
}

.empty-state i {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: var(--space-3);
}

.empty-state p {
  color: #64748b;
  margin-bottom: var(--space-3);
}

/* Animations */
.animate__animated.animate__fadeIn {
  animation-duration: 0.8s;
}

.animate__animated.animate__fadeInUp {
  animation-duration: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .stat-value {
    font-size: 1.75rem;
  }

  .dashboard-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .dashboard-card,
  .chart-card,
  .low-stock-container {
    margin-bottom: var(--space-4);
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .low-stock-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .btn-view-all {
    align-self: flex-start;
  }
}

/* Responsive adjustments for header */
@media (max-width: 992px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 1.5rem;
  }

  .dashboard-actions {
    margin-top: 1rem;
    flex-wrap: wrap;
  }

  .quick-stats {
    margin-top: 1rem;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-title-icon {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .action-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Chart container */
.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: 1rem;
}

/* Dashboard Header */
.dashboard-header {
    background-color: var(--primary);
    color: #ffffff;
    padding: 1.5rem;
    border-radius: var(--radius);
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
}

.dashboard-title {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: var(--text-secondary);
    font-weight: 400;
}