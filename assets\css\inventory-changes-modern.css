/* Modern Inventory Changes Report Styling */
:root {
  /* Colors - <PERSON> Green Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --secondary: #81C784;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
  --space-10: 4rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-3d: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Page Background with Pattern */
body {
  background-color: var(--light);
  background-image:
    radial-gradient(var(--gray-200) 1px, transparent 1px),
    radial-gradient(var(--gray-200) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

/* Container with Glass Effect */
.container-fluid {
  padding: var(--space-6) var(--space-5);
  max-width: 1400px;
  margin: 0 auto;
}

/* Page Title */
.page-title {
  font-size: 1.85rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 1.75rem;
  display: flex;
  align-items: center;
  position: relative;
}

.page-title i {
  margin-right: 0.75rem;
  color: var(--white);
  font-size: 1.25rem;
  background-color: var(--primary);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 6px 12px rgba(78, 115, 223, 0.25);
  position: relative;
  z-index: 1;
}

.page-title i::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.2), rgba(0, 0, 0, 0.1));
  border-radius: 50%;
  z-index: -1;
}

/* Info Alert */
.info-alert {
  background-color: var(--primary-ultra-soft);
  border-left: 4px solid var(--primary);
  color: var(--gray-700);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius);
  margin-bottom: var(--space-5);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.info-alert i {
  color: var(--primary);
  font-size: 1.25rem;
  margin-right: var(--space-3);
}

/* Stats Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 6px 12px rgba(0, 0, 0, 0.08);
  padding: 1.75rem;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  opacity: 1;
}

.stat-primary::before {
  background-color: var(--primary);
}

.stat-warning::before {
  background-color: var(--warning);
}

.stat-success::before {
  background-color: var(--success);
}

.stat-danger::before {
  background-color: var(--danger);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.stat-primary .stat-icon {
  background-color: var(--primary-bg);
  color: var(--primary);
}

.stat-warning .stat-icon {
  background-color: rgba(255, 213, 79, 0.2);
  color: #FF8F00;
}

.stat-success .stat-icon {
  background-color: rgba(0, 200, 83, 0.2);
  color: var(--success);
}

.stat-danger .stat-icon {
  background-color: rgba(255, 82, 82, 0.2);
  color: var(--danger);
}

.stat-icon i {
  font-size: 1.5rem;
}

.stat-content {
  flex-grow: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Category Card */
.category-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  overflow: hidden;
  border: 1px solid var(--gray-200);
  transition: var(--transition);
}

.category-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.category-header {
  background-color: var(--primary-ultra-soft);
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid var(--primary-soft);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-title {
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-dark);
}

.category-title i {
  color: var(--primary);
}

.category-body {
  padding: 0;
}

/* Table Styles */
.table {
  margin-bottom: 0;
}

.table th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.03em;
  padding: var(--space-3) var(--space-4);
  border-top: none;
  border-bottom: 2px solid var(--gray-200);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-top: none;
  border-bottom: 1px solid var(--gray-200);
  font-size: 0.9rem;
}

.table tr:hover td {
  background-color: var(--primary-ultra-soft);
}

.table code {
  background-color: var(--gray-100);
  color: var(--primary-dark);
  padding: 0.2rem 0.4rem;
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  letter-spacing: 0.03em;
}

.status-success {
  background-color: rgba(0, 200, 83, 0.15);
  color: var(--success);
}

.status-danger {
  background-color: rgba(255, 82, 82, 0.15);
  color: var(--danger);
}

.status-warning {
  background-color: rgba(255, 213, 79, 0.15);
  color: #FF8F00;
}

.status-info {
  background-color: rgba(77, 208, 225, 0.15);
  color: var(--info);
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.25rem;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: var(--radius);
  transition: var(--transition);
  border: none;
  cursor: pointer;
  gap: 0.5rem;
  box-shadow: var(--shadow-sm);
}

.action-btn-primary {
  background-color: var(--primary);
  color: white;
}

.action-btn-primary:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.action-btn-success {
  background-color: var(--success);
  color: white;
}

.action-btn-success:hover {
  background-color: #00B34A;
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.action-btn-outline {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.action-btn-outline:hover {
  background-color: var(--primary-ultra-soft);
  box-shadow: var(--shadow-sm);
}

.action-buttons {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-5);
  justify-content: center;
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__faster {
  animation-duration: 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Print Styles */
@media print {
  .action-btn, .action-buttons, .sidebar, .topbar {
    display: none !important;
  }
  
  .container-fluid {
    padding: 0 !important;
    max-width: 100% !important;
  }
  
  .category-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    margin-bottom: 1.5rem !important;
    page-break-inside: avoid;
  }
  
  .category-header {
    background-color: #f8f9fa !important;
    color: #000 !important;
    border-bottom: 1px solid #ddd !important;
  }
  
  .table th {
    background-color: #f8f9fa !important;
    color: #000 !important;
    border-bottom: 1px solid #ddd !important;
  }
  
  .table td {
    border-bottom: 1px solid #eee !important;
  }
  
  .status-badge {
    border: 1px solid currentColor !important;
    background-color: transparent !important;
  }
  
  body {
    background: none !important;
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .container-fluid {
    padding: var(--space-4);
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .category-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .table th, .table td {
    padding: var(--space-2) var(--space-3);
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.25rem;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .action-btn {
    width: 100%;
  }
  
  .table-responsive {
    margin: 0 -1rem;
  }
}
