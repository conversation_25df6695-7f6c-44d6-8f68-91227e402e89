# CHOIMS Project Brief

## Project Overview
CHOIMS (City Health Office Inventory Management System) is a comprehensive inventory management system designed for the city health department. It manages inventory across the main city health office and satellite health centers connected through a Local Area Network (LAN).

## Core Requirements

### System Purpose
- Provide a centralized inventory management system for city health offices and satellite centers
- Track fixed assets and consumables across multiple locations
- Enable transfer operations with appropriate approval workflows
- Maintain detailed audit trails and logs
- Support role-specific access and functionality

### Primary Goals
1. Improve inventory tracking accuracy across all health centers
2. Streamline asset transfers between locations
3. Provide role-appropriate access to inventory data
4. Generate comprehensive reports for decision-making
5. Maintain complete audit trails for all inventory operations

### Key User Roles
1. **GodMode** - Complete system access and control
2. **Superadmin** - Comprehensive system oversight without configuration abilities
3. **Logistics Personnel** - Inventory input and management, transfer initiation
4. **HIMU Personnel** - IT equipment/supply transfer approval and monitoring
5. **Department Staff** - Department-specific inventory management
6. **Health Center Staff** - Center-specific inventory management and transfer acceptance

### Asset Categories
- IT Equipment
- Office Equipment 
- IT Supply
- Office Supply
- Medical Equipment

## Project Scope
- Asset management with detailed specifications
- Transfer operations with appropriate approval workflows
- Role-based access control
- Real-time inventory tracking
- Comprehensive logging and audit trails
- Role-specific dashboards
- Satellite health center inventory management 