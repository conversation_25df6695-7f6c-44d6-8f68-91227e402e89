<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Initialize variables
$items = [];
$errors = [];
$formData = [
    'item_id' => 0,
    'category_id' => '',
    'name' => '',
    'sku' => '',
    'description' => '',
    'is_fixed_asset' => 0,
    'specifications' => '',
    'unit_cost' => ''
];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $formData = [
        'item_id' => isset($_POST['item_id']) ? (int)$_POST['item_id'] : 0,
        'category_id' => isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0,
        'name' => isset($_POST['name']) ? trim($_POST['name']) : '',
        'sku' => isset($_POST['sku']) ? trim(strtoupper($_POST['sku'])) : '',
        'description' => isset($_POST['description']) ? trim($_POST['description']) : '',
        'is_fixed_asset' => isset($_POST['is_fixed_asset']) ? 1 : 0,
        'specifications' => isset($_POST['specifications']) ? trim($_POST['specifications']) : '',
        'unit_cost' => isset($_POST['unit_cost']) ? (float)$_POST['unit_cost'] : 0
    ];

    // Validate form data
    if (empty($formData['name'])) {
        $errors[] = 'Name is required';
    }

    if (empty($formData['sku'])) {
        $errors[] = 'SKU is required';
    } elseif (strlen($formData['sku']) > 20) {
        $errors[] = 'SKU must be 20 characters or less';
    }

    if (empty($formData['category_id'])) {
        $errors[] = 'Category is required';
    }

    // Check if SKU is unique
    if (!empty($formData['sku'])) {
        try {
            $stmt = $db->prepare("SELECT item_id FROM items WHERE sku = ? AND item_id != ?");
            $stmt->execute([$formData['sku'], $formData['item_id']]);
            if ($stmt->rowCount() > 0) {
                $errors[] = 'SKU already exists';
            }
        } catch (PDOException $e) {
            error_log("Error checking item SKU: " . $e->getMessage());
            $errors[] = 'Database error occurred';
        }
    }

    // If no errors, process the operation
    if (empty($errors)) {
        try {
            // Check if it's an insert or update operation
            if ($formData['item_id'] > 0) {
                // Update existing item
                $stmt = $db->prepare("
                    UPDATE items 
                    SET category_id = ?, sku = ?, name = ?, description = ?,
                        is_fixed_asset = ?, specifications = ?, unit_cost = ?
                    WHERE item_id = ?
                ");
                $result = $stmt->execute([
                    $formData['category_id'],
                    $formData['sku'],
                    $formData['name'],
                    $formData['description'],
                    $formData['is_fixed_asset'],
                    $formData['specifications'],
                    $formData['unit_cost'],
                    $formData['item_id']
                ]);

                if ($result) {
                    $auth->logActivity('Updated item', 'items', $formData['item_id']);
                    setFlashMessage('success', 'Item updated successfully');
                } else {
                    $errors[] = 'Failed to update item';
                }
            } else {
                // Insert new item
                $stmt = $db->prepare("
                    INSERT INTO items (category_id, sku, name, description, is_fixed_asset, specifications, unit_cost)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $result = $stmt->execute([
                    $formData['category_id'],
                    $formData['sku'],
                    $formData['name'],
                    $formData['description'],
                    $formData['is_fixed_asset'],
                    $formData['specifications'],
                    $formData['unit_cost']
                ]);

                if ($result) {
                    $itemId = $db->lastInsertId();
                    $auth->logActivity('Added new item', 'items', $itemId);
                    setFlashMessage('success', 'Item added successfully');
                    // Reset form data for new entry
                    $formData = [
                        'item_id' => 0,
                        'category_id' => '',
                        'name' => '',
                        'sku' => '',
                        'description' => '',
                        'is_fixed_asset' => 0,
                        'specifications' => '',
                        'unit_cost' => ''
                    ];
                } else {
                    $errors[] = 'Failed to add item';
                }
            }
        } catch (PDOException $e) {
            error_log("Error processing item: " . $e->getMessage());
            $errors[] = 'Database error occurred: ' . $e->getMessage();
        }
    }
}

// Handle delete operation
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $itemId = (int)$_GET['id'];
    
    try {
        // Check if item is in use in fixed_assets
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM fixed_assets WHERE item_id = ?");
        $stmt->execute([$itemId]);
        $fixedAssetCount = $stmt->fetch()['count'];
        
        // Check if item is in use in inventory
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM inventory WHERE item_id = ?");
        $stmt->execute([$itemId]);
        $inventoryCount = $stmt->fetch()['count'];
        
        if ($fixedAssetCount > 0 || $inventoryCount > 0) {
            setFlashMessage('error', 'Cannot delete item as it is being used by ' . 
                           ($fixedAssetCount > 0 ? $fixedAssetCount . ' fixed asset(s)' : '') . 
                           ($fixedAssetCount > 0 && $inventoryCount > 0 ? ' and ' : '') .
                           ($inventoryCount > 0 ? $inventoryCount . ' inventory record(s)' : ''));
        } else {
            // Delete the item
            $stmt = $db->prepare("DELETE FROM items WHERE item_id = ?");
            $result = $stmt->execute([$itemId]);
            
            if ($result) {
                $auth->logActivity('Deleted item', 'items', $itemId);
                setFlashMessage('success', 'Item deleted successfully');
            } else {
                setFlashMessage('error', 'Failed to delete item');
            }
        }
    } catch (PDOException $e) {
        error_log("Error deleting item: " . $e->getMessage());
        setFlashMessage('error', 'Database error occurred while deleting item');
    }
    
    // Redirect to avoid form resubmission
    header("Location: " . BASE_URL . "/admin/inventory/items.php");
    exit;
}

// Handle edit operation
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $itemId = (int)$_GET['id'];
    
    try {
        $stmt = $db->prepare("SELECT * FROM items WHERE item_id = ?");
        $stmt->execute([$itemId]);
        
        if ($item = $stmt->fetch()) {
            $formData = [
                'item_id' => $item['item_id'],
                'category_id' => $item['category_id'],
                'name' => $item['name'],
                'sku' => $item['sku'],
                'description' => $item['description'],
                'is_fixed_asset' => $item['is_fixed_asset'],
                'specifications' => $item['specifications'],
                'unit_cost' => $item['unit_cost']
            ];
        } else {
            setFlashMessage('error', 'Item not found');
        }
    } catch (PDOException $e) {
        error_log("Error fetching item: " . $e->getMessage());
        setFlashMessage('error', 'Database error occurred while fetching item');
    }
}

// Initialize filters
$categoryFilter = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$typeFilter = isset($_GET['type']) ? $_GET['type'] : '';
$searchQuery = isset($_GET['search']) ? trim($_GET['search']) : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

// Build query
$where = [];
$params = [];

if ($categoryFilter) {
    $where[] = "i.category_id = ?";
    $params[] = $categoryFilter;
}

if ($typeFilter) {
    $where[] = "i.is_fixed_asset = ?";
    $params[] = ($typeFilter === 'fixed_asset') ? 1 : 0;
}

if ($searchQuery) {
    $where[] = "(i.name LIKE ? OR i.sku LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// Fetch categories for filter
try {
    $stmt = $db->query("SELECT category_id, name FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

// Get total items count
try {
    $countQuery = "SELECT COUNT(*) as total FROM items i $whereClause";
    $stmt = $db->prepare($countQuery);
    $stmt->execute($params);
    $totalRecords = $stmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $limit);
} catch (PDOException $e) {
    error_log("Error counting items: " . $e->getMessage());
    $totalRecords = 0;
    $totalPages = 1;
}

// Fetch items
try {
    $query = "
        SELECT i.*, c.name as category_name
        FROM items i
        JOIN categories c ON i.category_id = c.category_id
        $whereClause
        ORDER BY i.name ASC
        LIMIT ? OFFSET ?
    ";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $items = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching items: " . $e->getMessage());
    setFlashMessage('error', 'Error loading items');
    $items = [];
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Item Management</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Inventory Dashboard
        </a>
    </div>

    <div class="row">
        <!-- Add/Edit Item Form -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <?php echo $formData['item_id'] > 0 ? 'Edit Item' : 'Add New Item'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <input type="hidden" name="item_id" value="<?php echo $formData['item_id']; ?>">
                        
                        <div class="mb-3">
                            <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>" 
                                            <?php echo (int)$formData['category_id'] === (int)$category['category_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($formData['name']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sku" class="form-label">SKU <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="sku" name="sku" 
                                   value="<?php echo htmlspecialchars($formData['sku']); ?>" required
                                   maxlength="20" placeholder="e.g. IT-001">
                            <small class="text-muted">Unique Stock Keeping Unit, max 20 characters</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($formData['description']); ?></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_fixed_asset" name="is_fixed_asset" 
                                   <?php echo $formData['is_fixed_asset'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_fixed_asset">This is a Fixed Asset</label>
                            <small class="d-block text-muted">Check if this item is a fixed asset rather than a consumable</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="specifications" class="form-label">Technical Specifications</label>
                            <textarea class="form-control" id="specifications" name="specifications" rows="3"><?php echo htmlspecialchars($formData['specifications']); ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="unit_cost" class="form-label">Unit Cost</label>
                            <div class="input-group">
                                <span class="input-group-text">₱</span>
                                <input type="number" class="form-control" id="unit_cost" name="unit_cost" 
                                       value="<?php echo htmlspecialchars($formData['unit_cost']); ?>"
                                       step="0.01" min="0">
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo $formData['item_id'] > 0 ? 'Update Item' : 'Add Item'; ?>
                            </button>
                            
                            <?php if ($formData['item_id'] > 0): ?>
                                <a href="items.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Items List -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Items List</h5>
                </div>
                <div class="card-body">
                    <!-- Filters -->
                    <form method="GET" class="row g-3 mb-4">
                        <div class="col-md-4">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>"
                                            <?php echo $categoryFilter == $category['category_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="fixed_asset" <?php echo $typeFilter === 'fixed_asset' ? 'selected' : ''; ?>>Fixed Asset</option>
                                <option value="consumable" <?php echo $typeFilter === 'consumable' ? 'selected' : ''; ?>>Consumable</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="Search by name or SKU">
                        </div>
                        
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </form>

                    <?php if (empty($items)): ?>
                        <p class="text-muted text-center">No items found.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>SKU</th>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Type</th>
                                        <th>Unit Cost</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($items as $item): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($item['sku']); ?></code></td>
                                            <td><?php echo htmlspecialchars($item['name']); ?></td>
                                            <td><?php echo htmlspecialchars($item['category_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo $item['is_fixed_asset'] ? 'bg-info' : 'bg-success'; ?>">
                                                    <?php echo $item['is_fixed_asset'] ? 'Fixed Asset' : 'Consumable'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatCurrency($item['unit_cost']); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="items.php?action=edit&id=<?php echo $item['item_id']; ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <?php if ($auth->hasRole(ROLE_GODMODE) || $auth->hasRole(ROLE_SUPERADMIN)): ?>
                                                        <a href="items.php?action=delete&id=<?php echo $item['item_id']; ?>" 
                                                           class="btn btn-sm btn-danger" 
                                                           onclick="return confirm('Are you sure you want to delete this item? This action cannot be undone.')"
                                                           title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Page navigation" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&category=<?php echo $categoryFilter; ?>&type=<?php echo $typeFilter; ?>&search=<?php echo urlencode($searchQuery); ?>">
                                                Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo $categoryFilter; ?>&type=<?php echo $typeFilter; ?>&search=<?php echo urlencode($searchQuery); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&category=<?php echo $categoryFilter; ?>&type=<?php echo $typeFilter; ?>&search=<?php echo urlencode($searchQuery); ?>">
                                                Next
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../templates/footer.php'; ?> 