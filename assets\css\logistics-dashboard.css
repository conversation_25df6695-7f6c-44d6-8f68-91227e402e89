/* Logistics Dashboard Modern Enhancements */

/* Dashboard header enhancements */
.dashboard-title-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 10px rgba(39, 174, 96, 0.25);
}

/* Search box styling */
.search-container {
  position: relative;
  max-width: 320px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  z-index: 10;
}

.search-input {
  padding-left: 38px;
  border-radius: 50px;
  border: 1px solid #e2e8f0;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  box-shadow: none;
}

.search-input:focus {
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.15);
  border-color: #27ae60;
  background-color: white;
}

/* Enhanced buttons */
.btn-with-icon {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  margin-right: 0.5rem;
  box-shadow: 0 4px 6px rgba(39, 174, 96, 0.15);
  transition: all 0.3s ease;
}

.btn-with-icon:last-child {
  margin-right: 0;
}

.btn-with-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(39, 174, 96, 0.2);
}

.btn-with-icon i {
  margin-right: 8px;
  font-size: 0.9em;
}

.btn-success {
  background-color: #27ae60;
  border-color: #27ae60;
}

.btn-success:hover {
  background-color: #2ecc71;
  border-color: #2ecc71;
}

/* Card entrance animations */
.dashboard-card,
.chart-card,
.low-stock-container {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.dashboard-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-card:nth-child(4) { animation-delay: 0.4s; }

.row:nth-child(3) .chart-card { animation-delay: 0.5s; }
.row:nth-child(3) .low-stock-container { animation-delay: 0.5s; }
.row:nth-child(4) .chart-card { animation-delay: 0.6s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced card styling */
.dashboard-card {
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%; /* Ensure consistent height */
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
}

.chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
}

.chart-card:hover {
  box-shadow: 0 10px 24px rgba(0, 0, 0, 0.1);
}

.chart-card .card-header {
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 16px 20px;
  font-weight: 600;
  color: #2c3e50;
}

.chart-card .card-header i {
  color: #27ae60;
  margin-right: 8px;
}

/* Enhanced stat cards */
.stat-label {
  font-size: 0.85rem;
  font-weight: 700;
  letter-spacing: 0.03em;
  color: #7f8c8d;
  margin-bottom: 6px;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 6px;
  line-height: 1;
}

.stat-description {
  font-size: 0.9rem;
  color: #95a5a6;
  margin-bottom: 0.5rem !important;
  min-height: 20px; /* Ensure consistent height */
}

.stat-trend {
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: 0 !important;
  min-height: 24px; /* Ensure consistent height */
}

.stat-trend .text-success {
  color: #27ae60 !important;
}

.stat-trend .text-danger {
  color: #e74c3c !important;
}

.stat-trend .text-primary {
  color: #3498db !important;
}

.stat-trend .text-muted {
  font-weight: 400;
  font-size: 0.75rem;
}

.dashboard-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
  margin-left: 1rem;
  align-self: center;
}

.icon-assets {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  box-shadow: 0 4px 10px rgba(39, 174, 96, 0.25);
}

.icon-inventory {
  background: linear-gradient(135deg, #3498db, #2980b9);
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.25);
}

.icon-warning {
  background: linear-gradient(135deg, #f39c12, #f1c40f);
  box-shadow: 0 4px 10px rgba(243, 156, 18, 0.25);
}

.icon-transfer {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  box-shadow: 0 4px 10px rgba(155, 89, 182, 0.25);
}

/* Table enhancements */
.dashboard-table {
  border-collapse: separate;
  border-spacing: 0;
}

.dashboard-table thead th {
  background-color: #f8f9fa;
  color: #7f8c8d;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 12px 16px;
}

.dashboard-table tbody td {
  padding: 12px 16px;
  border-top: 1px solid #ecf0f1;
  vertical-align: middle;
}

.dashboard-table tbody tr:hover {
  background-color: #f9f9f9;
}

/* Status badges */
.status-badge {
  padding: 4px 10px;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
}

.status-badge::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.badge-pending {
  background-color: rgba(243, 156, 18, 0.15);
  color: #f39c12;
}

.badge-pending::before {
  background-color: #f39c12;
}

.badge-approved {
  background-color: rgba(52, 152, 219, 0.15);
  color: #3498db;
}

.badge-approved::before {
  background-color: #3498db;
}

.badge-completed {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}

.badge-completed::before {
  background-color: #27ae60;
}

.badge-rejected {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

.badge-rejected::before {
  background-color: #e74c3c;
}

/* Low stock section improvements */
.low-stock-container {
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05);
  background-color: white;
  overflow: hidden;
  height: 100%;
}

.low-stock-header {
  padding: 16px 20px;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.low-stock-header-title {
  font-weight: 600;
  color: #2c3e50;
}

.low-stock-header-title i {
  color: #f39c12;
  margin-right: 8px;
}

.btn-view-all {
  font-size: 0.8rem;
  font-weight: 600;
  color: #ffffff !important; /* Added !important to override any other styles */
  background-color: #27ae60;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  padding: 6px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
}

.btn-view-all:hover {
  background-color: #2ecc71;
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.btn-view-all::after {
  content: '\f061';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-left: 6px;
  font-size: 0.7rem;
}

.low-stock-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.low-stock-table thead th {
  background-color: #f8f9fa;
  color: #7f8c8d;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: 12px 16px;
}

.low-stock-table tbody td {
  padding: 12px 16px;
  border-top: 1px solid #ecf0f1;
  vertical-align: middle;
}

.low-stock-table tbody tr:hover {
  background-color: #f9f9f9;
}

.low-stock-empty {
  padding: 60px 20px;
  text-align: center;
  color: #95a5a6;
}

.low-stock-empty i {
  font-size: 2.5rem;
  color: #27ae60;
  margin-bottom: 1rem;
}

/* Quick actions enhancements */
.quick-action {
  padding: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.quick-action:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.quick-action h5 {
  margin-top: 1rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Responsive improvements */
@media (max-width: 992px) {
  .stat-value {
    font-size: 1.8rem;
  }

  .dashboard-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .chart-card,
  .dashboard-card,
  .low-stock-container {
    margin-bottom: 1.5rem;
  }

  .stat-label {
    font-size: 0.8rem;
  }
}

/* Empty state styling improvements */
.empty-state {
  padding: 40px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-state i {
  font-size: 2.5rem;
  color: #bdc3c7;
  margin-bottom: 1rem;
}

.empty-state p {
  color: #95a5a6;
  margin-bottom: 1.5rem;
}

/* Chart enhancements */
.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-actions .btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.625rem;
  font-weight: 600;
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
  color: #718096;
  transition: all 0.2s ease;
}

.chart-actions .btn:hover,
.chart-actions .btn.active {
  background-color: #27ae60;
  color: white;
  border-color: #27ae60;
}

/* Transfers Awaiting Acceptance Widget - Now using standard card styling */

/* Highlight effect for widget when clicked from toast */
.highlight-widget {
  animation: highlight-pulse 0.5s ease-in-out 3;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 6px 16px rgba(155, 89, 182, 0.1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 6px 30px rgba(155, 89, 182, 0.4);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 6px 16px rgba(155, 89, 182, 0.1);
    transform: scale(1);
  }
}

.chart-container {
  margin: 0 auto;
  position: relative;
}

.chart-legend {
  padding: 10px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  font-size: 0.8rem;
  color: #718096;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
}

.btn-action {
  border-radius: 50px;
  font-weight: 600;
  padding: 0.375rem 1rem;
  box-shadow: 0 4px 6px rgba(39, 174, 96, 0.15);
  transition: all 0.2s ease;
}

.btn-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(39, 174, 96, 0.2);
}

/* Card footer improvements */
.card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 12px 20px;
}

.view-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.85rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  color: #ffffff !important;
  background-color: #27ae60;
  padding: 8px 16px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
}

.view-details:hover {
  background-color: #2ecc71;
  box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
  transform: translateY(-2px);
}

.view-details i {
  font-size: 0.75rem;
  transition: transform 0.2s ease;
  margin-left: 8px;
}

.view-details:hover i {
  transform: translateX(3px);
}

/* Specific colors for different card types */
.text-success.view-details {
  background-color: #27ae60;
}

.text-success.view-details:hover {
  background-color: #2ecc71;
}

.text-primary.view-details {
  background-color: #3498db;
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}

.text-primary.view-details:hover {
  background-color: #2980b9;
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.text-warning.view-details {
  background-color: #f39c12;
  box-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);
}

.text-warning.view-details:hover {
  background-color: #f1c40f;
  box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
}

.text-danger.view-details {
  background-color: #e74c3c;
  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.2);
}

.text-danger.view-details:hover {
  background-color: #c0392b;
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}
