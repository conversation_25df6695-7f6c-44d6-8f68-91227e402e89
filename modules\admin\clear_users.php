<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in and has godmode role only
requireLogin();
if (strtolower($_SESSION['role']) !== 'godmode') {
    // Redirect to unauthorized page
    $_SESSION['error'] = "You don't have permission to access this feature. Only godmode can clear users.";
    header("Location: /choims/index.php");
    exit();
}

$success = false;
$error_message = '';
$deleted_count = 0;

// Process the request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_clear'])) {
    try {
        // Begin database transaction
        mysqli_begin_transaction($conn);

        // Get a GodMode user ID to reassign foreign key references
        $godModeUserQuery = "SELECT user_id FROM users WHERE LOWER(role) = 'godmode' LIMIT 1";
        $godModeResult = mysqli_query($conn, $godModeUserQuery);

        if (!$godModeResult || mysqli_num_rows($godModeResult) == 0) {
            throw new Exception("No GodMode user found to reassign references");
        }

        $godModeUser = mysqli_fetch_assoc($godModeResult);
        $godModeUserId = $godModeUser['user_id'];

        // Update fixed_assets table to reassign created_by to a GodMode user
        $updateAssetsQuery = "UPDATE fixed_assets
                             SET created_by = $godModeUserId
                             WHERE created_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        $updateAssetsResult = mysqli_query($conn, $updateAssetsQuery);

        if (!$updateAssetsResult) {
            throw new Exception("Failed to update fixed assets references: " . mysqli_error($conn));
        }

        // Update transfers table for initiated_by, logistics_approval_by, himu_approval_by
        $updateTransfersQuery = "UPDATE transfers
                               SET initiated_by = $godModeUserId
                               WHERE initiated_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        $updateTransfersResult = mysqli_query($conn, $updateTransfersQuery);

        if (!$updateTransfersResult) {
            throw new Exception("Failed to update transfers initiated_by references: " . mysqli_error($conn));
        }

        $updateLogisticsApprovalQuery = "UPDATE transfers
                                      SET logistics_approval_by = $godModeUserId
                                      WHERE logistics_approval_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        mysqli_query($conn, $updateLogisticsApprovalQuery);

        $updateHimuApprovalQuery = "UPDATE transfers
                                 SET himu_approval_by = $godModeUserId
                                 WHERE himu_approval_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        mysqli_query($conn, $updateHimuApprovalQuery);

        // Update consumable_transactions table
        $updateTransactionsQuery = "UPDATE consumable_transactions
                                  SET performed_by = $godModeUserId
                                  WHERE performed_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        mysqli_query($conn, $updateTransactionsQuery);

        // Update maintenance_records table
        $updateMaintenanceQuery = "UPDATE maintenance_records
                                 SET created_by = $godModeUserId
                                 WHERE created_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        mysqli_query($conn, $updateMaintenanceQuery);

        // Update reports table
        $updateReportsQuery = "UPDATE reports
                             SET generated_by = $godModeUserId
                             WHERE generated_by IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        mysqli_query($conn, $updateReportsQuery);

        // First, delete audit logs for non-godmode users to avoid foreign key constraint
        $deleteLogsQuery = "DELETE FROM audit_logs
                           WHERE user_id IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        $logsResult = mysqli_query($conn, $deleteLogsQuery);

        if (!$logsResult) {
            throw new Exception("Failed to clear audit logs: " . mysqli_error($conn));
        }

        // Also delete detailed audit logs for non-godmode users
        $deleteDetailedLogsQuery = "DELETE FROM detailed_audit_logs
                                  WHERE user_id IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        $detailedLogsResult = mysqli_query($conn, $deleteDetailedLogsQuery);

        if (!$detailedLogsResult) {
            throw new Exception("Failed to clear detailed audit logs: " . mysqli_error($conn));
        }

        // Delete notifications for non-godmode users
        $deleteNotificationsQuery = "DELETE FROM notifications
                                   WHERE user_id IN (SELECT user_id FROM users WHERE LOWER(role) != 'godmode')";
        mysqli_query($conn, $deleteNotificationsQuery);

        // Now delete all users except those with 'godmode' role
        $deleteQuery = "DELETE FROM users WHERE LOWER(role) != 'godmode'";
        $result = mysqli_query($conn, $deleteQuery);

        if ($result) {
            $deleted_count = mysqli_affected_rows($conn);

            // Log the action to the old audit log system
            $logQuery = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
                        VALUES (?, ?, ?, ?, NULL, ?, ?)";
            $logStmt = mysqli_prepare($conn, $logQuery);

            $user_id = $_SESSION['user_id'];
            $action = 'Clear Users';
            $entity_type = 'Users';
            $entity_id = 0; // Generic for bulk action
            $new_values = json_encode(['cleared_users' => $deleted_count, 'timestamp' => date('Y-m-d H:i:s')]);
            $ip_address = $_SERVER['REMOTE_ADDR'];

            mysqli_stmt_bind_param($logStmt, 'ississ', $user_id, $action, $entity_type, $entity_id, $new_values, $ip_address);
            mysqli_stmt_execute($logStmt);

            // Log the action to the detailed audit log system
            $log_data = [
                'entity_name' => 'Users',
                'changes_summary' => "Cleared {$deleted_count} non-godmode users from the system",
                'new_values' => json_encode(['cleared_users' => $deleted_count, 'timestamp' => date('Y-m-d H:i:s')]),
                'ip_address' => $_SERVER['REMOTE_ADDR']
            ];

            logDetailedAction($conn, $user_id, 'delete', 'user', 0, $log_data);
            mysqli_stmt_close($logStmt);

            // Commit the transaction
            mysqli_commit($conn);
            $success = true;
        } else {
            throw new Exception("Failed to clear users: " . mysqli_error($conn));
        }
    } catch (Exception $e) {
        // Roll back if exception occurs
        if (isset($conn) && $conn->connect_errno === 0) {
            mysqli_rollback($conn);
        }
        $error_message = $e->getMessage();
    }
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Clear User Database</h1>
        <a href="/choims/modules/admin/index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to Admin
        </a>
    </div>

    <?php if ($success): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i> Successfully cleared user database. <?php echo $deleted_count; ?> user(s) deleted. Godmode accounts were preserved.
            <p class="mt-2 mb-0"><small>All references to deleted users in fixed assets, transfers, and other tables have been reassigned to a GodMode user.</small></p>
        </div>
    <?php elseif (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Clear Users</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i> <strong>Warning:</strong> This action will delete ALL users from the database except accounts with the 'godmode' role. This cannot be undone.
                <p class="mt-2 mb-0">All references to deleted users in fixed assets, transfers, and other tables will be reassigned to a GodMode user to maintain data integrity.</p>
            </div>

            <?php if (!$success): ?>
            <form method="POST" action="">
                <div class="form-group mb-4">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="confirmCheck" required>
                        <label class="custom-control-label" for="confirmCheck">I understand that this action will permanently delete all regular users</label>
                    </div>
                </div>

                <button type="submit" name="confirm_clear" id="clearBtn" class="btn btn-danger" disabled>
                    <i class="fas fa-trash me-2"></i> Clear User Database
                </button>
            </form>
            <?php else: ?>
            <div class="text-center mt-4">
                <a href="/choims/modules/admin/users.php" class="btn btn-primary">
                    <i class="fas fa-users me-2"></i> View Remaining Users
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Enable the clear button only when confirmation checkbox is checked
    $('#confirmCheck').change(function() {
        if($(this).is(':checked')) {
            $('#clearBtn').prop('disabled', false);
        } else {
            $('#clearBtn').prop('disabled', true);
        }
    });
});
</script>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>