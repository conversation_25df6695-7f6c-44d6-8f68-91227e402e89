/* Color Palette - Green Theme */
:root {
    --primary-green: #2E7D32;
    --light-green: #4CAF50;
    --lighter-green: #8BC34A;
    --dark-green: #1B5E20;
    --accent-green: #00C853;
    --green-hover: #388E3C;
    --green-gradient: linear-gradient(135deg, var(--primary-green), var(--light-green));
    --green-text: #1B5E20;
    --green-border: #81C784;
    --green-bg-light: #E8F5E9;
}

/* General Styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
    color: #333;
}

.footer {
    margin-top: auto;
    background-color: var(--dark-green);
    color: white;
    padding: 1rem 0;
    display: none !important; /* Hide the footer system-wide */
}

/* Hide all footer implementations */
footer.fixed-bottom,
footer,
.footer,
div.footer,
.login-footer,
.footer.fade-in {
    display: none !important;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    background: white !important;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1040;
    height: 56px;
    padding-left: 270px; /* Sidebar width + some padding */
}

.navbar-brand {
    font-weight: bold;
    color: var(--primary-green) !important;
    letter-spacing: 0.5px;
}

.navbar-light .navbar-nav .nav-link {
    color: #333 !important;
    transition: all 0.3s ease;
}

.navbar-light .navbar-nav .nav-link:hover {
    color: var(--primary-green) !important;
    background-color: rgba(46, 125, 50, 0.05);
    border-radius: 4px;
}

/* Cards */
.card {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    border: none;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-header.bg-primary {
    background-color: var(--primary-green) !important;
    color: white;
}

.card-body {
    padding: 1.5rem;
}

.card.bg-light {
    background-color: var(--green-bg-light) !important;
}

/* Forms */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.6rem 0.75rem;
    transition: all 0.2s;
}

.form-control:focus {
    border-color: var(--light-green);
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.6rem 2.25rem 0.6rem 0.75rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%232E7D32' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    transition: all 0.2s;
}

.form-select:focus {
    border-color: var(--light-green);
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.input-group-text {
    background-color: var(--green-bg-light);
    border: 1px solid #ced4da;
    color: var(--green-text);
}

.form-text {
    color: #6c757d;
}

.invalid-feedback {
    font-size: 0.85rem;
}

/* Select2 Customization */
.select2-container--bootstrap-5 .select2-selection {
    border-radius: 6px !important;
    border: 1px solid #ced4da !important;
    padding: 0.25rem 0.5rem !important;
    min-height: calc(1.5em + 0.75rem + 2px) !important;
}

.select2-container--bootstrap-5.select2-container--focus .select2-selection,
.select2-container--bootstrap-5.select2-container--open .select2-selection {
    border-color: var(--light-green) !important;
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25) !important;
}

.select2-container--bootstrap-5 .select2-dropdown {
    border-color: var(--light-green) !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) !important;
}

.select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected=true] {
    background-color: var(--primary-green) !important;
    color: white !important;
}

.select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
    background-color: var(--green-bg-light) !important;
    color: var(--dark-green) !important;
}

/* Tables */
.table thead th {
    background-color: var(--green-bg-light);
    border-bottom: 2px solid var(--green-border);
    color: var(--dark-green);
    font-weight: 600;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(76, 175, 80, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(76, 175, 80, 0.03);
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--dark-green);
    border-color: var(--dark-green);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-success {
    background-color: var(--light-green);
    border-color: var(--light-green);
}

.btn-success:hover, .btn-success:focus {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-outline-primary {
    color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-outline-primary:hover {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
    color: white;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon i {
    margin-right: 0.5rem;
}

/* Progress Bar */
.progress {
    height: 0.5rem;
    border-radius: 1rem;
    background-color: #e9ecef;
    margin: 0.5rem 0;
}

.progress-bar {
    border-radius: 1rem;
    background-color: var(--primary-green);
}

.progress-bar.bg-warning {
    background-color: #FFC107 !important;
}

.progress-bar.bg-danger {
    background-color: #DC3545 !important;
}

/* Alerts */
.alert {
    margin-bottom: 1.5rem;
    border-radius: 8px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert-success {
    color: var(--dark-green);
    background-color: var(--green-bg-light);
    border-left: 4px solid var(--primary-green);
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: var(--primary-green);
}

.page-item.active .page-link {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

/* Dashboard Widgets */
.dashboard-widget {
    text-align: center;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    background-color: white;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
    border-top: 4px solid var(--light-green);
}

.dashboard-widget:hover {
    transform: translateY(-5px);
}

.dashboard-widget i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-green);
}

.dashboard-widget .widget-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.dashboard-widget .widget-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--dark-green);
}

/* Status Badges */
.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
    border-radius: 4px;
}

.badge-pending {
    background-color: #ffc107;
    color: #000;
}

.badge-approved {
    background-color: var(--light-green);
    color: white;
}

.badge-rejected {
    background-color: #dc3545;
    color: white;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-green);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-green);
}

/* Pills, Tabs, etc. */
.nav-pills .nav-link.active {
    background-color: var(--primary-green);
}

.nav-pills .nav-link {
    color: var(--primary-green);
    border-radius: 6px;
    transition: all 0.2s;
}

.nav-pills .nav-link:hover:not(.active) {
    background-color: var(--green-bg-light);
}

/* Card Group Nested Styles */
.card .card {
    margin-bottom: 1rem;
}

.card .card-body {
    padding: 1.25rem;
}

.transfer-arrow {
    background-color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    margin: 0 auto;
}

/* Custom Form Validation Styles */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: var(--light-green);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%234CAF50' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-select:valid,
.form-select.is-valid {
    border-color: var(--light-green);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Loading Spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--primary-green);
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .container {
        width: 100% !important;
        max-width: none !important;
    }

    /* Hide all footer implementations when printing */
    .footer,
    footer,
    footer.fixed-bottom,
    div.footer,
    .login-footer,
    .footer.fade-in {
        display: none !important;
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .navbar {
        padding-left: 1rem; /* Reset padding on mobile */
    }

    .navbar-brand {
        font-size: 1.1rem;
    }

    .dashboard-widget {
        padding: 1rem;
    }

    .dashboard-widget i {
        font-size: 1.5rem;
    }

    .dashboard-widget .widget-value {
        font-size: 1.5rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }
}

/* Ensure notification dropdown scrolls properly */
.dropdown-menu.notification-dropdown {
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* Add styles for notification items - transition for read/unread status */
.notification-item {
    transition: all 0.3s ease;
}

.notification-item.unread {
    background-color: #f1f8e9;
    border-left: 3px solid var(--light-green);
    font-weight: bold;
}

.notification-item:not(.unread) {
    background-color: #ffffff;
    border-left: 3px solid transparent;
    font-weight: normal;
}