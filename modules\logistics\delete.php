<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
// Only GodMode, Superadmin, and Logistics can delete maintenance records
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to delete maintenance records.');

    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else if (hasRole('HIMU')) {
        header('Location: /choims/modules/assets/maintenance.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Check if form is submitted with a record ID
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['record_id']) && !empty($_POST['record_id'])) {
    $record_id = sanitizeInput($_POST['record_id']);

    // Verify record exists and get record details for audit log
    $recordQuery = "SELECT * FROM maintenance_records WHERE record_id = ?";
    $recordStmt = mysqli_prepare($conn, $recordQuery);
    mysqli_stmt_bind_param($recordStmt, 'i', $record_id);
    mysqli_stmt_execute($recordStmt);
    $recordResult = mysqli_stmt_get_result($recordStmt);

    if (mysqli_num_rows($recordResult) === 0) {
        setFlashMessage('error', 'Maintenance record not found.');
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;
    }

    $recordDetails = mysqli_fetch_assoc($recordResult);
    $asset_id = $recordDetails['asset_id'];

    // Begin transaction
    mysqli_begin_transaction($conn);

    try {
        // Delete the maintenance record
        $deleteQuery = "DELETE FROM maintenance_records WHERE record_id = ?";
        $deleteStmt = mysqli_prepare($conn, $deleteQuery);
        mysqli_stmt_bind_param($deleteStmt, 'i', $record_id);
        $deleteResult = mysqli_stmt_execute($deleteStmt);

        if (!$deleteResult) {
            throw new Exception("Failed to delete maintenance record: " . mysqli_error($conn));
        }

        // Create audit log using the record details we already retrieved
        $log_data = json_encode($recordDetails);

        // Log to regular audit log
        logActivity($conn, 'Delete Maintenance Record', 'maintenance_records', $record_id, $log_data, null);

        // Log to detailed audit system if available
        if (function_exists('logDetailedAction')) {
            logDetailedAction($conn, $_SESSION['user_id'], 'delete', 'other', $record_id, [
                'entity_name' => 'Maintenance Record',
                'changes_summary' => "Deleted maintenance record for asset ID: $asset_id",
                'old_values' => $log_data
            ]);
        }

        // Commit transaction
        mysqli_commit($conn);

        // Set success message
        setFlashMessage('success', 'Maintenance record deleted successfully.');

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);

        // Set error message
        setFlashMessage('error', $e->getMessage());
    }
} else {
    // Invalid request
    setFlashMessage('error', 'Invalid request to delete maintenance record.');
}

// Redirect back to logistics maintenance page
header('Location: /choims/modules/logistics/maintenance.php');
exit;
?>
