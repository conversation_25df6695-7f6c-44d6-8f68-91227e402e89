<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/audit_log.php');

// Ensure user is logged in and has appropriate permissions
requireLogin();
if (!hasRole('Superadmin')) {
    header('Location: /choims/access-denied.php');
    exit;
}

// Validate input
if (!isset($_POST['asset_id']) || empty($_POST['asset_id'])) {
    $_SESSION['error'] = "Invalid asset ID.";
    header('Location: /choims/modules/assets/list.php?show_deleted=1');
    exit;
}

$asset_id = sanitizeInput($_POST['asset_id']);

// Check if asset exists and is deleted
$checkQuery = "SELECT * FROM fixed_assets WHERE asset_id = ? AND is_deleted = 1";
$stmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    $_SESSION['error'] = "Asset not found or not marked as deleted.";
    header('Location: /choims/modules/assets/list.php?show_deleted=1');
    exit;
}

$asset = mysqli_fetch_assoc($result);

// Get old values for audit log
$oldValues = json_encode([
    'is_deleted' => 1
]);

// Perform restoration
$restoreQuery = "UPDATE fixed_assets SET is_deleted = 0 WHERE asset_id = ?";
$stmt = mysqli_prepare($conn, $restoreQuery);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);

if (mysqli_stmt_execute($stmt)) {
    // Log the action
    logAction(
        $conn,
        $_SESSION['user_id'],
        'Restored deleted asset',
        'asset',
        $asset_id,
        $oldValues,
        json_encode(['is_deleted' => 0])
    );
    
    $_SESSION['success'] = "Asset has been restored successfully.";
} else {
    $_SESSION['error'] = "Failed to restore asset: " . mysqli_error($conn);
}

// Redirect back to deleted assets list
header('Location: /choims/modules/assets/list.php?show_deleted=1');
exit;
?> 