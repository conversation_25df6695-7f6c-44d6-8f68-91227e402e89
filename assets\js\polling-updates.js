/**
 * Polling Updates for CHOIMS
 *
 * This script provides real-time updates through polling as a fallback
 * when WebSockets are not available.
 */

// Initialize polling for updates
function initPollingUpdates() {
    console.log('Initializing polling updates');

    // Determine what page we're on
    const currentPath = window.location.pathname;
    const isTransferPage = currentPath.includes('/transfers/');
    const isHimuDashboard = currentPath.includes('/dashboards/himu') || currentPath.includes('/dashboard/himu');
    const isLogisticsDashboard = currentPath.includes('/dashboards/logistics');
    const isDepartmentDashboard = currentPath.includes('/dashboards/department');
    const isHealthCenterDashboard = currentPath.includes('/dashboards/health_center');

    // Set up polling intervals based on page type
    if (isHimuDashboard) {
        console.log('Setting up polling for HIMU dashboard');
        // Poll for HIMU dashboard updates every 10 seconds
        setInterval(refreshHimuDashboard, 10000);
    }

    if (isLogisticsDashboard) {
        console.log('Setting up polling for Logistics dashboard');
        // Poll for Logistics dashboard updates every 10 seconds
        setInterval(refreshLogisticsDashboard, 10000);
    }

    if (isDepartmentDashboard || isHealthCenterDashboard) {
        console.log('Setting up polling for Department/Health Center dashboard');
        // Poll for Department/Health Center dashboard updates every 15 seconds
        setInterval(refreshLocationDashboard, 15000);
    }

    if (isTransferPage) {
        console.log('Setting up polling for Transfers page');
        // Poll for Transfer page updates every 10 seconds
        setInterval(refreshTransferPage, 10000);
    }

    // Always check for notifications
    setInterval(checkForNotifications, 30000);
}

// Refresh HIMU dashboard
function refreshHimuDashboard() {
    console.log('Refreshing HIMU dashboard');

    // Refresh pending transfers table
    const transfersTable = document.querySelector('.card-body .table-responsive');
    if (transfersTable) {
        fetch('/choims/modules/transfers/himu_pending_widget.php')
            .then(response => response.text())
            .then(html => {
                transfersTable.innerHTML = html;
                highlightElement(transfersTable);
            })
            .catch(error => {
                console.error('Error refreshing HIMU dashboard transfers:', error);
            });
    }

    // Refresh pending approvals count
    const pendingApprovalsCount = document.querySelector('.stat-card.approvals .stat-value');
    if (pendingApprovalsCount) {
        fetch('/choims/modules/transfers/himu_approval_count.php')
            .then(response => response.json())
            .then(data => {
                const oldCount = parseInt(pendingApprovalsCount.textContent);
                const newCount = data.count;
                pendingApprovalsCount.textContent = newCount;

                if (newCount !== oldCount) {
                    highlightElement(pendingApprovalsCount);
                }
            })
            .catch(error => {
                console.error('Error refreshing HIMU approval count:', error);
            });
    }
}

// Refresh Logistics dashboard
function refreshLogisticsDashboard() {
    console.log('Refreshing Logistics dashboard');

    // Refresh awaiting transfers widget
    const awaitingWidget = document.querySelector('.awaiting-confirmation-widget');
    if (awaitingWidget) {
        fetch('/choims/modules/transfers/awaiting_widget.php')
            .then(response => response.text())
            .then(html => {
                awaitingWidget.innerHTML = html;
                highlightElement(awaitingWidget);
            })
            .catch(error => {
                console.error('Error refreshing awaiting transfers widget:', error);
            });
    }

    // Refresh transfer statistics
    fetch('/choims/modules/transfers/logistics_stats.php')
        .then(response => response.json())
        .then(data => {
            // Update pending transfers count
            const pendingElement = document.querySelector('.stat-card.pending-transfers .stat-value');
            if (pendingElement) {
                pendingElement.textContent = data.pending;
                highlightElement(pendingElement);
            }

            // Update completed transfers count
            const completedElement = document.querySelector('.stat-card.completed-transfers .stat-value');
            if (completedElement) {
                completedElement.textContent = data.completed;
                highlightElement(completedElement);
            }
        })
        .catch(error => {
            console.error('Error refreshing logistics transfer stats:', error);
        });
}

// Refresh Department/Health Center dashboard
function refreshLocationDashboard() {
    console.log('Refreshing Location dashboard');

    // Refresh recent transfers widget
    const recentTransfersWidget = document.querySelector('.recent-transfers-widget');
    if (recentTransfersWidget) {
        fetch('/choims/modules/transfers/recent_widget.php')
            .then(response => response.text())
            .then(html => {
                recentTransfersWidget.innerHTML = html;
                highlightElement(recentTransfersWidget);
            })
            .catch(error => {
                console.error('Error refreshing recent transfers widget:', error);
            });
    }
}

// Refresh Transfer page
function refreshTransferPage() {
    console.log('Refreshing Transfer page');

    // Refresh transfer table if present
    const transferTable = document.querySelector('#transferTable');
    if (transferTable) {
        // Get current URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const queryString = urlParams.toString();

        // Reload the table content
        fetch(`/choims/modules/transfers/table.php?${queryString}`)
            .then(response => response.text())
            .then(html => {
                transferTable.innerHTML = html;
                highlightElement(transferTable);
            })
            .catch(error => {
                console.error('Error refreshing transfer table:', error);
            });
    }
}

// Check for new notifications
function checkForNotifications() {
    console.log('Checking for notifications');

    fetch('/choims/modules/notifications/check.php')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                // Update notification counter
                const notificationCounter = document.querySelector('#notificationCount');
                if (notificationCounter) {
                    notificationCounter.textContent = data.count;
                    notificationCounter.style.display = 'inline-block';
                }
            }
        })
        .catch(error => {
            console.error('Error checking for notifications:', error);
        });
}

// Highlight an element to indicate it's been updated
function highlightElement(element) {
    // Add highlight class
    element.classList.add('bg-highlight-pulse');

    // Remove it after animation completes
    setTimeout(() => {
        element.classList.remove('bg-highlight-pulse');
    }, 2000);
}

// Add CSS for highlight animation
function addHighlightStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .bg-highlight-pulse {
            animation: highlightPulse 2s ease-in-out;
        }

        @keyframes highlightPulse {
            0% { background-color: transparent; }
            20% { background-color: rgba(0, 123, 255, 0.2); }
            100% { background-color: transparent; }
        }
    `;
    document.head.appendChild(style);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing polling updates');
    initPollingUpdates();
    addHighlightStyles();
});
