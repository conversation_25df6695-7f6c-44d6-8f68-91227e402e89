/* Main Styling for CHOIMS */

/* Dashboard Statistics Cards */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

/* Sidebar Styling */
.sidebar {
    position: fixed;
    top: 0 !important;
    bottom: 0;
    left: 0;
    z-index: 1050;
    padding: 0 0 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    height: 100vh !important;
    overflow-y: auto;
    background-color: #429e46;
    border-right: none;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
}

.sidebar .nav-link.active {
    color: #4e73df;
}

.sidebar .nav-link:hover {
    color: #0d6efd;
}

/* Main Content Area */
.main-content {
    margin-left: 250px;
    padding: 1.5rem;
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        top: 0 !important;
        height: 100vh !important;
    }

    .sidebar.show {
        transform: translateX(0);
    }
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.08);
}

/* Card Styling */
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Form Styling */
.form-control:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* Status Badges */
.badge {
    padding: 0.4em 0.65em;
}

/* Stock Status Indicators */
.stock-normal {
    color: #1cc88a;
}

.stock-warning {
    color: #f6c23e;
}

.stock-critical {
    color: #e74a3b;
}

/* Login Page Styling */
.login-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    width: 100%;
    max-width: 400px;
}

/* Navigation Tabs */
.nav-tabs .nav-link.active {
    font-weight: bold;
    border-bottom: 3px solid #4e73df;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 25px;
    right: 25px;
    display: none;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    background: #4e73df;
    color: #fff;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
}

.back-to-top:hover {
    background: #2e59d9;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}