/* Modern Asset View Page Styling */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --transfer-color: #6366f1;
  --maintenance-color: #0ea5e9;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Asset Header */
.asset-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.asset-header:hover {
  box-shadow: var(--shadow-md);
}

.asset-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.asset-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: var(--space-1);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.asset-subtitle {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin-bottom: var(--space-2);
}

.asset-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-full);
  background-color: var(--primary);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
}

.asset-icon i {
  font-size: 1.5rem;
}

/* Asset Cards */
.asset-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  margin-bottom: var(--space-4);
  overflow: hidden;
  transition: all 0.3s ease;
}

.asset-card:hover {
  box-shadow: var(--shadow-md);
}

.asset-card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.asset-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary), var(--primary-light));
}

.asset-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.asset-card-title i {
  color: var(--primary);
  font-size: 1.1rem;
}

.asset-card-body {
  padding: var(--space-4);
}

/* Asset Fields */
.asset-field {
  padding: var(--space-2) 0;
  display: flex;
  border-bottom: 1px solid var(--gray-100);
}

.asset-field:last-child {
  border-bottom: none;
}

.field-label {
  font-weight: 600;
  width: 40%;
  color: var(--gray-500);
  font-size: 0.85rem;
}

.field-value {
  width: 60%;
  font-size: 0.9rem;
}

/* Status Badges */
.badge {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
}

.badge i {
  margin-right: 0.25rem;
}

.badge.bg-primary.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-success.bg-opacity-10 {
  background-color: rgba(16, 185, 129, 0.1) !important;
  color: #10b981 !important;
}

.badge.bg-warning.bg-opacity-10 {
  background-color: rgba(245, 158, 11, 0.1) !important;
  color: #f59e0b !important;
}

.badge.bg-danger.bg-opacity-10 {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: #ef4444 !important;
}

.badge.bg-info.bg-opacity-10 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
}

.badge.bg-secondary.bg-opacity-10 {
  background-color: rgba(107, 114, 128, 0.1) !important;
  color: #6b7280 !important;
}

/* Action Buttons */
.action-btn {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 0.85rem;
  position: relative;
  overflow: hidden;
}

.action-btn i {
  margin-right: 0.25rem;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  border-radius: inherit;
  transition: all 0.3s ease;
  z-index: 1;
}

.action-btn:hover::after {
  opacity: 1;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  70% {
    transform: scale(1.05);
    opacity: 0.2;
  }
  100% {
    transform: scale(0.95);
    opacity: 0;
  }
}

/* Soft Buttons */
.btn-soft-primary {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--primary);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 0.85rem;
}

.btn-soft-primary:hover {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
  transform: translateY(-2px);
}

.btn-soft-secondary {
  background-color: rgba(107, 114, 128, 0.1);
  color: var(--gray-500);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 0.85rem;
}

.btn-soft-secondary:hover {
  background-color: var(--gray-500);
  color: white;
  box-shadow: 0 4px 10px rgba(107, 114, 128, 0.2);
  transform: translateY(-2px);
}

.btn-soft-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 0.85rem;
}

.btn-soft-success:hover {
  background-color: var(--success);
  color: white;
  box-shadow: 0 4px 10px rgba(16, 185, 129, 0.2);
  transform: translateY(-2px);
}

.btn-soft-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 0.85rem;
}

.btn-soft-info:hover {
  background-color: var(--info);
  color: white;
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

.btn-sm.btn-soft-primary,
.btn-sm.btn-soft-secondary,
.btn-sm.btn-soft-success,
.btn-sm.btn-soft-info {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

/* Timeline */
.timeline-container {
  position: relative;
  margin: 0;
  padding: 0;
}

.timeline-item {
  position: relative;
  padding: 0.75rem 0 0.75rem 1.5rem;
  border-left: 2px solid var(--transfer-color);
  margin-left: 0.5rem;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: -0.4rem;
  top: 1rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: var(--transfer-color);
}

.timeline-item:last-child {
  border-left-color: transparent;
}

.timeline-date {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--transfer-color);
  margin-bottom: 0.25rem;
}

.timeline-content {
  background: var(--gray-100);
  border-radius: var(--radius-md);
  padding: 0.75rem;
  font-size: 0.85rem;
}

/* Modern Horizontal Timeline */
.timeline-compact {
  padding: 0.5rem;
}

.timeline-item-compact {
  background-color: var(--white);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-left: 3px solid var(--transfer-color);
  box-shadow: var(--shadow-sm);
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
}

.timeline-item-compact:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.timeline-item-compact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(99, 102, 241, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.timeline-item-compact:hover::before {
  opacity: 1;
}

.timeline-date-compact {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--gray-500);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.timeline-content-compact {
  font-size: 0.85rem;
}

.text-transfer {
  color: var(--transfer-color);
}

.text-maintenance {
  color: var(--maintenance-color);
}

/* Tables */
.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  font-weight: 600;
  color: var(--dark);
  border-bottom-width: 1px;
  padding: var(--space-2) var(--space-3);
  background-color: var(--gray-100);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.table td {
  padding: var(--space-2) var(--space-3);
  vertical-align: middle;
  border-bottom-color: var(--gray-200);
  font-size: 0.85rem;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--gray-100);
}

/* Modern Maintenance Cards */
.maintenance-cards {
  padding: 0.5rem;
}

.maintenance-card-item {
  background-color: var(--white);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-left: 3px solid var(--maintenance-color);
  box-shadow: var(--shadow-sm);
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.maintenance-card-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.maintenance-card-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(14, 165, 233, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.maintenance-card-item:hover::before {
  opacity: 1;
}

.maintenance-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.maintenance-card-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--dark);
}

.maintenance-card-date {
  font-size: 0.8rem;
  color: var(--gray-500);
  display: flex;
  align-items: center;
}

.maintenance-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.maintenance-card-details {
  flex: 1;
}

.maintenance-card-actions {
  flex-shrink: 0;
}

.maintenance-card-type {
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
  color: var(--maintenance-color);
  font-weight: 500;
}

.maintenance-card-performer {
  font-size: 0.8rem;
  color: var(--gray-500);
}

/* Modern Maintenance Modal */
.maintenance-modal {
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.maintenance-modal .modal-header {
  border-bottom: none;
  padding: 1.5rem 1.5rem 0.5rem;
  background-color: white;
}

.maintenance-modal .modal-body {
  padding: 1rem 1.5rem;
}

.maintenance-modal .modal-footer {
  border-top: none;
  padding: 0.5rem 1.5rem 1.5rem;
}

.maintenance-detail-box {
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  padding: 1rem;
  margin-bottom: 1rem;
}

.maintenance-detail-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--gray-500);
  margin-bottom: 0.25rem;
}

.maintenance-detail-value {
  font-size: 0.9rem;
}

/* Specifications Box */
.specs-box {
  background-color: var(--gray-100);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  margin-bottom: var(--space-3);
}

.specs-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: var(--space-2);
}

.specs-content {
  font-size: 0.85rem;
  color: var(--gray-700);
  white-space: pre-line;
}

/* Maintenance Records */
.maintenance-card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.maintenance-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--maintenance-color), #38bdf8);
}

.maintenance-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--maintenance-color);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.maintenance-card-title i {
  color: var(--maintenance-color);
  font-size: 1.1rem;
}

/* Transfer History */
.transfer-card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-3) var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.transfer-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--transfer-color), #818cf8);
}

.transfer-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--transfer-color);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.transfer-card-title i {
  color: var(--transfer-color);
  font-size: 1.1rem;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .field-label, .field-value {
    width: 100%;
  }

  .asset-field {
    flex-direction: column;
  }

  .asset-header {
    padding: var(--space-3);
  }

  .asset-title {
    font-size: 1.25rem;
  }
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Transfer History Timeline */
.transfer-timeline {
    padding: 1rem;
}

.transfer-item {
    border-left: 2px solid #eaeaea;
    padding-left: 1rem;
    padding-bottom: 1.5rem;
    position: relative;
}

.transfer-item:last-child {
    padding-bottom: 0.5rem;
}

.transfer-item:before {
    content: '';
    width: 12px;
    height: 12px;
    background-color: #eaeaea;
    border-radius: 50%;
    position: absolute;
    left: -7px;
    top: 8px;
}

.transfer-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.transfer-date {
    font-size: 0.9rem;
    color: #777;
}

.transfer-id {
    font-size: 0.9rem;
    font-weight: 600;
    color: #444;
}

.transfer-content {
    background-color: #f9f9f9;
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.transfer-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.transfer-status.warning {
    background-color: #fff8e1;
    color: #f9a825;
}

.transfer-status.info {
    background-color: #e3f2fd;
    color: #1976d2;
}

.transfer-status.success {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.transfer-status.danger {
    background-color: #ffebee;
    color: #c62828;
}

.transfer-status.secondary {
    background-color: #eceff1;
    color: #455a64;
}

.transfer-locations {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.location-item {
    flex: 1;
}

.location-arrow {
    color: #aaa;
    margin: 0 0.5rem;
}

.location-label {
    font-size: 0.75rem;
    color: #777;
}

.location-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.location-name.source {
    color: #555;
}

.location-name.destination {
    color: #333;
}

.transfer-user {
    font-size: 0.85rem;
    color: #777;
    margin-bottom: 0.5rem;
}

.transfer-actions {
    text-align: right;
}

/* MR History Timeline */
.mr-timeline {
    padding: 1rem;
}

.mr-item {
    border-left: 2px solid #e0f7fa;
    padding-left: 1rem;
    padding-bottom: 1.5rem;
    position: relative;
}

.mr-item:last-child {
    padding-bottom: 0.5rem;
}

.mr-item:before {
    content: '';
    width: 12px;
    height: 12px;
    background-color: #4fc3f7;
    border-radius: 50%;
    position: absolute;
    left: -7px;
    top: 8px;
}

.mr-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.mr-date {
    font-size: 0.9rem;
    color: #777;
}

.mr-content {
    background-color: #f9fdff;
    border-radius: 0.5rem;
    padding: 0.75rem;
    border: 1px solid #e1f5fe;
}

.mr-details {
    margin-bottom: 0.5rem;
}

.detail-label {
    font-size: 0.75rem;
    color: #777;
}

.detail-value {
    font-size: 0.9rem;
}

.mr-footer {
    font-size: 0.85rem;
    color: #777;
    padding-top: 0.5rem;
    border-top: 1px dashed #e1f5fe;
}

.card-icon-bg.mr {
    background-color: #e1f5fe;
    color: #0288d1;
}

.empty-state-icon.mr {
    background-color: #e1f5fe;
    color: #0288d1;
}

.btn-soft-primary.btn-sm {
    padding: 0.35rem 0.75rem;
    font-size: 0.85rem;
}
