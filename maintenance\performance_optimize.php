<?php
// Performance Optimization Script
// Purpose: Optimize database performance and configuration
// Usage: Run before deployment or during maintenance windows

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';

// Security check - only system admins should access this
if (!isset($_SESSION['user_id']) || !hasRole('godmode')) {
    header("Location: /choims/index.php");
    exit;
}

// Operations success/error tracking
$success = [];
$errors = [];
$recommendations = [];

// Only proceed if confirmation is provided
$confirmed = isset($_POST['confirm']) && $_POST['confirm'] === 'yes';

// Display the confirmation form if not confirmed
if (!$confirmed) {
    include_once '../includes/header.php';
    ?>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-tachometer-alt me-2"></i>Performance Optimization</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p><strong>This tool will perform the following operations:</strong></p>
                    <ul>
                        <li>Analyze database tables for optimal performance</li>
                        <li>Add missing indexes if needed</li>
                        <li>Optimize MySQL configuration for better performance</li>
                        <li>Provide recommendations for server optimization</li>
                    </ul>
                    <p><strong>Note:</strong> This operation may take several minutes to complete and could affect system performance temporarily.</p>
                </div>
                
                <form method="post">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                        <label class="form-check-label" for="confirmCheck">
                            I understand this will temporarily affect system performance
                        </label>
                    </div>
                    
                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-primary">Run Performance Optimization</button>
                    <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    exit;
}

// If confirmed, proceed with optimization tasks
try {
    // 1. Check and add indexes for frequently queried columns
    $indexesToCheck = [
        ['table' => 'audit_logs', 'column' => 'log_time', 'name' => 'idx_audit_logs_log_time'],
        ['table' => 'audit_logs', 'column' => 'entity_type', 'name' => 'idx_audit_logs_entity_type'],
        ['table' => 'notifications', 'column' => 'created_at', 'name' => 'idx_notifications_created_at'],
        ['table' => 'notifications', 'column' => 'is_read', 'name' => 'idx_notifications_is_read'],
        ['table' => 'consumable_inventory', 'column' => 'status', 'name' => 'idx_consumable_inventory_status'],
        ['table' => 'fixed_assets', 'column' => 'status', 'name' => 'idx_fixed_assets_status'],
        ['table' => 'transfers', 'column' => 'status', 'name' => 'idx_transfers_status'],
        ['table' => 'transfers', 'column' => 'transfer_date', 'name' => 'idx_transfers_transfer_date']
    ];
    
    foreach ($indexesToCheck as $index) {
        // Check if index exists
        $checkIndexSql = "SHOW INDEX FROM {$index['table']} WHERE Column_name = ? AND Key_name = ?";
        $checkStmt = mysqli_prepare($conn, $checkIndexSql);
        mysqli_stmt_bind_param($checkStmt, 'ss', $index['column'], $index['name']);
        mysqli_stmt_execute($checkStmt);
        $indexResult = mysqli_stmt_get_result($checkStmt);
        
        if (mysqli_num_rows($indexResult) == 0) {
            // Index doesn't exist, create it
            $createIndexSql = "CREATE INDEX {$index['name']} ON {$index['table']} ({$index['column']})";
            if (mysqli_query($conn, $createIndexSql)) {
                $success[] = "Added missing index {$index['name']} on {$index['table']}.{$index['column']}";
            } else {
                $errors[] = "Failed to add index {$index['name']}: " . mysqli_error($conn);
            }
        } else {
            $success[] = "Index {$index['name']} already exists";
        }
    }
    
    // 2. Analyze tables for optimization
    $tablesToAnalyze = [
        'audit_logs',
        'batch_transfers',
        'batch_transfer_assets',
        'batch_transfer_inventory',
        'categories',
        'consumable_inventory',
        'consumable_transactions',
        'fixed_assets',
        'locations',
        'maintenance_records',
        'notifications',
        'reports',
        'sku_master',
        'sources',
        'suppliers',
        'transfers',
        'users'
    ];
    
    foreach ($tablesToAnalyze as $table) {
        // Check if table exists
        $tableCheckResult = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($tableCheckResult) > 0) {
            // Analyze table
            if (mysqli_query($conn, "ANALYZE TABLE $table")) {
                $success[] = "Analyzed table: $table";
            } else {
                $errors[] = "Error analyzing table $table: " . mysqli_error($conn);
            }
        }
    }
    
    // 3. Check MySQL server variables and provide recommendations
    $serverVariables = [
        'innodb_buffer_pool_size',
        'query_cache_size',
        'max_connections',
        'table_open_cache',
        'tmp_table_size',
        'max_heap_table_size'
    ];
    
    $variableResults = mysqli_query($conn, "SHOW VARIABLES WHERE Variable_name IN ('" . implode("','", $serverVariables) . "')");
    $currentSettings = [];
    
    while ($row = mysqli_fetch_assoc($variableResults)) {
        $currentSettings[$row['Variable_name']] = $row['Value'];
    }
    
    // Memory recommendations based on typical LAN deployment
    if (isset($currentSettings['innodb_buffer_pool_size']) && (int)$currentSettings['innodb_buffer_pool_size'] < 134217728) {
        $recommendations[] = "Consider increasing innodb_buffer_pool_size to at least 128M for better performance";
    }
    
    if (isset($currentSettings['query_cache_size']) && (int)$currentSettings['query_cache_size'] < 16777216) {
        $recommendations[] = "Consider setting query_cache_size to at least 16M for frequently accessed queries";
    }
    
    if (isset($currentSettings['max_connections']) && (int)$currentSettings['max_connections'] < 100) {
        $recommendations[] = "Consider increasing max_connections to at least 100 for busy periods";
    }
    
    if (isset($currentSettings['table_open_cache']) && (int)$currentSettings['table_open_cache'] < 400) {
        $recommendations[] = "Consider increasing table_open_cache to at least 400";
    }
    
    // 4. Check slow queries
    $hasSloQueryLog = false;
    $slowQueryLogResult = mysqli_query($conn, "SHOW VARIABLES LIKE 'slow_query_log'");
    if ($slowQueryRow = mysqli_fetch_assoc($slowQueryLogResult)) {
        $hasSloQueryLog = ($slowQueryRow['Value'] == 'ON');
    }
    
    if (!$hasSloQueryLog) {
        $recommendations[] = "Enable slow_query_log to identify problematic queries (add to my.ini: slow_query_log=1, long_query_time=2)";
    }
    
    // 5. Create a query performance summary view if it doesn't exist
    $createPerfViewSql = "
    CREATE OR REPLACE VIEW performance_summary AS
    SELECT 
        CONCAT(t.TABLE_SCHEMA, '.', t.TABLE_NAME) AS table_name,
        t.TABLE_ROWS AS row_count,
        ROUND((t.DATA_LENGTH + t.INDEX_LENGTH) / 1024 / 1024, 2) AS size_mb,
        ROUND(t.INDEX_LENGTH / GREATEST(t.DATA_LENGTH, 1) * 100, 2) AS index_ratio,
        t.CREATE_TIME AS created,
        t.UPDATE_TIME AS last_updated
    FROM 
        information_schema.TABLES t
    WHERE 
        t.TABLE_SCHEMA = DATABASE()
    ORDER BY 
        (t.DATA_LENGTH + t.INDEX_LENGTH) DESC
    ";
    
    if (mysqli_query($conn, $createPerfViewSql)) {
        $success[] = "Created performance_summary view for monitoring";
    } else {
        $errors[] = "Failed to create performance_summary view: " . mysqli_error($conn);
    }
    
    // 6. Create stored procedure for regular maintenance if it doesn't exist
    $createMaintenanceProcSql = "
    CREATE PROCEDURE IF NOT EXISTS perform_regular_maintenance()
    BEGIN
        -- Optimize frequently accessed tables
        OPTIMIZE TABLE consumable_inventory;
        OPTIMIZE TABLE fixed_assets;
        OPTIMIZE TABLE transfers;
        OPTIMIZE TABLE notifications;
        
        -- Analyze tables for query optimization
        ANALYZE TABLE consumable_inventory;
        ANALYZE TABLE fixed_assets;
        ANALYZE TABLE transfers;
        ANALYZE TABLE notifications;
        ANALYZE TABLE audit_logs;
        
        -- Delete old notifications (read and older than 7 days)
        DELETE FROM notifications 
        WHERE is_read = 1 
        AND created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
        
        -- Purge very old audit logs if needed (older than 180 days)
        DELETE FROM audit_logs 
        WHERE log_time < DATE_SUB(NOW(), INTERVAL 180 DAY);
    END;
    ";
    
    if (mysqli_query($conn, $createMaintenanceProcSql)) {
        $success[] = "Created stored procedure for regular maintenance";
    } else {
        $errors[] = "Failed to create maintenance procedure: " . mysqli_error($conn);
    }
    
    // 7. Optimize transfer_approval_view to use LIMIT by default
    // First drop the existing view
    $dropViewSql = "DROP VIEW IF EXISTS transfer_approval_view";
    
    if (mysqli_query($conn, $dropViewSql)) {
        $success[] = "Removed old transfer_approval_view for optimization";
        
        // Create optimized version with built-in limits
        $createOptimizedViewSql = "
        CREATE VIEW transfer_approval_view AS
        SELECT 
            t.transfer_id,
            t.transfer_date,
            CASE
                WHEN t.asset_id IS NOT NULL THEN 'Fixed Asset'
                ELSE 'Inventory'
            END AS item_type,
            COALESCE(
                (SELECT CONCAT(a.asset_name, ' (', s1.sku_name, ')')
                 FROM fixed_assets a
                 JOIN sku_master s1 ON a.sku_id = s1.sku_id
                 WHERE a.asset_id = t.asset_id LIMIT 1),
                (SELECT s2.sku_name
                 FROM consumable_inventory i
                 JOIN sku_master s2 ON i.sku_id = s2.sku_id
                 WHERE i.inventory_id = t.inventory_id LIMIT 1)
            ) AS item_name,
            COALESCE(
                (SELECT s1.sku_code
                 FROM fixed_assets a
                 JOIN sku_master s1 ON a.sku_id = s1.sku_id
                 WHERE a.asset_id = t.asset_id LIMIT 1),
                (SELECT s2.sku_code
                 FROM consumable_inventory i
                 JOIN sku_master s2 ON i.sku_id = s2.sku_id
                 WHERE i.inventory_id = t.inventory_id LIMIT 1)
            ) AS sku_code,
            COALESCE(
                (SELECT c1.category_name
                 FROM fixed_assets a
                 JOIN sku_master s1 ON a.sku_id = s1.sku_id
                 JOIN categories c1 ON s1.category_id = c1.category_id
                 WHERE a.asset_id = t.asset_id LIMIT 1),
                (SELECT c2.category_name
                 FROM consumable_inventory i
                 JOIN sku_master s2 ON i.sku_id = s2.sku_id
                 JOIN categories c2 ON s2.category_id = c2.category_id
                 WHERE i.inventory_id = t.inventory_id LIMIT 1)
            ) AS category_name,
            COALESCE(
                (SELECT c1.requires_himu_approval
                 FROM fixed_assets a
                 JOIN sku_master s1 ON a.sku_id = s1.sku_id
                 JOIN categories c1 ON s1.category_id = c1.category_id
                 WHERE a.asset_id = t.asset_id LIMIT 1),
                (SELECT c2.requires_himu_approval
                 FROM consumable_inventory i
                 JOIN sku_master s2 ON i.sku_id = s2.sku_id
                 JOIN categories c2 ON s2.category_id = c2.category_id
                 WHERE i.inventory_id = t.inventory_id LIMIT 1)
            ) AS requires_himu_approval,
            t.quantity,
            l1.location_name AS source_location,
            l2.location_name AS destination_location,
            t.status,
            u1.full_name AS initiated_by,
            u2.full_name AS logistics_approved_by,
            t.logistics_approval_date,
            u3.full_name AS himu_approved_by,
            t.himu_approval_date,
            u4.full_name AS received_by,
            t.received_date,
            t.transfer_notes,
            t.rejection_reason
        FROM transfers t
        JOIN locations l1 ON t.source_location_id = l1.location_id
        JOIN locations l2 ON t.destination_location_id = l2.location_id
        LEFT JOIN users u1 ON t.initiated_by = u1.user_id
        LEFT JOIN users u2 ON t.logistics_approval_by = u2.user_id
        LEFT JOIN users u3 ON t.himu_approval_by = u3.user_id
        LEFT JOIN users u4 ON t.received_by = u4.user_id
        LIMIT 1000";
        
        if (mysqli_query($conn, $createOptimizedViewSql)) {
            $success[] = "Created optimized transfer_approval_view with LIMIT clause for improved performance";
        } else {
            $errors[] = "Failed to create optimized transfer_approval_view: " . mysqli_error($conn);
        }
    } else {
        $errors[] = "Failed to drop existing transfer_approval_view: " . mysqli_error($conn);
    }
    
    // 8. Add recommendations for php.ini settings
    $recommendations[] = "PHP Configuration: Set memory_limit to at least 256M in php.ini";
    $recommendations[] = "PHP Configuration: Set max_execution_time to at least 60 in php.ini";
    $recommendations[] = "PHP Configuration: Enable opcache in php.ini for better performance";
    
    // 9. Add application-specific performance recommendations
    $recommendations[] = "Implement pagination on all data listing pages (limit to 50 items per page)";
    $recommendations[] = "Add LIMIT clauses to all SELECT queries to prevent large result sets";
    $recommendations[] = "Create a scheduled task to run the maintenance procedure weekly";

} catch (Exception $e) {
    $errors[] = "Performance optimization error: " . $e->getMessage();
}

// Display results
include_once '../includes/header.php';
?>

<div class="container mt-5">
    <div class="card">
        <div class="card-header <?php echo empty($errors) ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h3>
                <i class="fas <?php echo empty($errors) ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> me-2"></i>
                Performance Optimization Results
            </h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success)): ?>
                <h4>Completed Operations:</h4>
                <ul class="list-group mb-4">
                    <?php foreach ($success as $message): ?>
                        <li class="list-group-item list-group-item-success">
                            <i class="fas fa-check me-2"></i> <?php echo htmlspecialchars($message); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <h4>Errors:</h4>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($recommendations)): ?>
                <h4>Recommendations:</h4>
                <div class="alert alert-info">
                    <ul class="mb-0">
                        <?php foreach ($recommendations as $recommendation): ?>
                            <li><?php echo htmlspecialchars($recommendation); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <p class="text-muted">
                    These recommendations should be implemented on the production server for optimal performance.
                </p>
            <?php endif; ?>
            
            <div class="mt-4">
                <a href="/choims/dashboards/superadmin.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i> Return to Dashboard
                </a>
                <a href="/choims/maintenance/db_maintenance.php" class="btn btn-secondary">
                    <i class="fas fa-database me-2"></i> Run Database Maintenance
                </a>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?> 