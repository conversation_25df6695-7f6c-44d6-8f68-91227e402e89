<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Initialize filters
$filters = [
    'category' => isset($_GET['category']) ? $_GET['category'] : '',
    'status' => isset($_GET['status']) ? $_GET['status'] : '',
    'search' => isset($_GET['search']) ? $_GET['search'] : '',
];

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Build query
$where = [];
$params = [];

if ($filters['category']) {
    $where[] = "c.category_id = ?";
    $params[] = $filters['category'];
}

if ($filters['status']) {
    $where[] = "fa.status = ?";
    $params[] = $filters['status'];
}

if ($filters['search']) {
    $where[] = "(i.name LIKE ? OR i.sku LIKE ? OR fa.serial_number LIKE ?)";
    $params[] = "%{$filters['search']}%";
    $params[] = "%{$filters['search']}%";
    $params[] = "%{$filters['search']}%";
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// Get total records for pagination
try {
    $countQuery = "
        SELECT COUNT(*) as total 
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        $whereClause
    ";
    $stmt = $db->prepare($countQuery);
    $stmt->execute($params);
    $totalRecords = $stmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $limit);
} catch (PDOException $e) {
    error_log("Error counting fixed assets: " . $e->getMessage());
    $totalRecords = 0;
    $totalPages = 1;
}

// Fetch fixed assets
try {
    $query = "
        SELECT fa.*, i.name as item_name, i.sku, c.name as category_name,
               s.name as source_name, sup.name as supplier_name,
               u.full_name as assigned_to_name,
               d.name as department_name, hc.name as health_center_name
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN sources s ON fa.source_id = s.source_id
        JOIN suppliers sup ON fa.supplier_id = sup.supplier_id
        LEFT JOIN users u ON fa.assigned_to = u.user_id
        LEFT JOIN departments d ON fa.department_id = d.department_id
        LEFT JOIN health_centers hc ON fa.health_center_id = hc.health_center_id
        $whereClause
        ORDER BY fa.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $assets = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching fixed assets: " . $e->getMessage());
    $assets = [];
}

// Fetch categories for filter
try {
    $stmt = $db->query("SELECT category_id, name FROM categories WHERE status = 'active'");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
    $categories = [];
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Fixed Assets Management</h1>
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Asset
        </a>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="category" class="form-label">Category</label>
                    <select name="category" id="category" class="form-select">
                        <option value="">All Categories</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['category_id']; ?>" 
                                    <?php echo $filters['category'] == $category['category_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="in_use" <?php echo $filters['status'] === 'in_use' ? 'selected' : ''; ?>>In Use</option>
                        <option value="available" <?php echo $filters['status'] === 'available' ? 'selected' : ''; ?>>Available</option>
                        <option value="under_repair" <?php echo $filters['status'] === 'under_repair' ? 'selected' : ''; ?>>Under Repair</option>
                        <option value="defective" <?php echo $filters['status'] === 'defective' ? 'selected' : ''; ?>>Defective</option>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($filters['search']); ?>" 
                           placeholder="Search by name, SKU, or serial number">
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Assets Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>SKU</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Serial Number</th>
                            <th>Status</th>
                            <th>Location</th>
                            <th>Assigned To</th>
                            <th>Purchase Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($assets)): ?>
                            <tr>
                                <td colspan="9" class="text-center">No fixed assets found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($assets as $asset): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($asset['sku']); ?></td>
                                    <td><?php echo htmlspecialchars($asset['item_name']); ?></td>
                                    <td><?php echo htmlspecialchars($asset['category_name']); ?></td>
                                    <td><?php echo htmlspecialchars($asset['serial_number']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $asset['status'] === 'in_use' ? 'success' : 
                                                ($asset['status'] === 'available' ? 'primary' : 
                                                ($asset['status'] === 'under_repair' ? 'warning' : 'danger')); 
                                        ?>">
                                            <?php echo ucwords(str_replace('_', ' ', $asset['status'])); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        if ($asset['department_name']) {
                                            echo htmlspecialchars($asset['department_name']);
                                        } elseif ($asset['health_center_name']) {
                                            echo htmlspecialchars($asset['health_center_name']);
                                        } else {
                                            echo 'Not Assigned';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo $asset['assigned_to_name'] ? htmlspecialchars($asset['assigned_to_name']) : 'Not Assigned'; ?></td>
                                    <td><?php echo formatDate($asset['purchase_date']); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="view.php?id=<?php echo $asset['asset_id']; ?>" 
                                               class="btn btn-sm btn-info" 
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $asset['asset_id']; ?>" 
                                               class="btn btn-sm btn-warning" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($auth->hasRole(ROLE_GODMODE)): ?>
                                                <a href="delete.php?id=<?php echo $asset['asset_id']; ?>" 
                                                   class="btn btn-sm btn-danger" 
                                                   title="Delete"
                                                   onclick="return confirmDelete()">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?>&<?php echo http_build_query($filters); ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($filters); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?>&<?php echo http_build_query($filters); ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once '../../templates/footer.php'; ?>
