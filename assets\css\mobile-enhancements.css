/* Mobile Enhancements for CHOIMS
 * This file contains mobile-specific optimizations
 */

/* Global Mobile Adjustments */
@media (max-width: 767.98px) {
  /* Increase base font size for better readability */
  body {
    font-size: 16px;
  }

  /* Improve container padding */
  .container-fluid {
    padding: 0.75rem !important;
  }

  /* Enhance touch targets */
  button,
  .btn,
  .nav-link,
  .dropdown-item,
  input[type="checkbox"],
  input[type="radio"] {
    min-height: 44px;
    min-width: 44px;
    padding: 0.5rem 1rem;
    touch-action: manipulation;
  }

  /* Improve sidebar toggle button */
  #sidebarMobileToggle {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 44px !important;
    height: 44px !important;
    font-size: 1.25rem !important;
    color: var(--primary-green) !important;
    background-color: rgba(46, 125, 50, 0.1) !important;
    border-radius: 8px !important;
    margin-right: 0.75rem !important;
    border: none !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    z-index: 1080 !important;
  }

  #sidebarMobileToggle:hover {
    background-color: rgba(46, 125, 50, 0.2) !important;
    transform: scale(1.05) !important;
  }

  #sidebarMobileToggle:active {
    transform: scale(0.95) !important;
  }

  /* Style the navbar toggler to match the sidebar toggle */
  .navbar-toggler.custom-toggler {
    display: none !important; /* Hide by default - we'll use the sidebar toggle instead */
  }

  /* Add a visual indicator when sidebar is open */
  body.sidebar-open #sidebarMobileToggle {
    background-color: var(--primary-green) !important;
    color: white !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2) !important;
  }

  /* Improve form controls for touch */
  .form-control,
  .form-select {
    height: 44px;
    padding: 0.5rem 0.75rem;
    font-size: 16px; /* Prevent iOS zoom on focus */
  }

  textarea.form-control {
    height: auto;
    min-height: 44px;
  }

  /* Adjust card padding */
  .card-header,
  .card-body,
  .card-footer {
    padding: 0.75rem !important;
  }

  /* Improve table display on mobile */
  .table-responsive {
    margin: 0 -0.75rem;
    width: calc(100% + 1.5rem);
    border: none;
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem !important;
    white-space: nowrap;
  }

  /* Stack buttons in button groups */
  .btn-group {
    flex-direction: column;
    width: 100%;
  }

  .btn-group .btn {
    width: 100%;
    margin-bottom: 0.25rem;
    border-radius: 0.25rem !important;
  }

  /* Improve dropdown menus */
  .dropdown-menu {
    width: 100%;
    min-width: 100%;
    position: fixed !important;
    left: 0 !important;
    right: 0 !important;
    top: auto !important;
    bottom: 0 !important;
    border-radius: 1rem 1rem 0 0 !important;
    max-height: 80vh;
    overflow-y: auto;
    padding: 1rem;
    box-shadow: 0 -5px 25px rgba(0,0,0,0.15);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    display: block;
    z-index: 1080;
  }

  .dropdown-menu.show {
    transform: translateY(0);
  }

  .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
  }

  /* Mobile-specific form layout */
  .form-row {
    flex-direction: column;
  }

  .form-col {
    flex: 0 0 100%;
    width: 100%;
    padding: 0;
    margin-bottom: 0.75rem;
  }

  /* Improve modal display */
  .modal-dialog {
    margin: 0.5rem;
    max-width: none;
  }

  .modal-content {
    border-radius: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .btn {
    width: 100%;
    margin: 0.25rem 0;
  }
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
  display: none;
}

@media (max-width: 767.98px) {
  .mobile-bottom-nav {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    z-index: 1050;
    height: 60px;
    padding: 0.5rem;
  }

  .mobile-bottom-nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.7rem;
    padding: 0.25rem;
  }

  .mobile-bottom-nav-item i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
  }

  .mobile-bottom-nav-item.active {
    color: var(--primary-green);
  }

  /* Add padding to main content to account for bottom nav */
  .main-content {
    padding-bottom: 70px !important;
  }
}

/* Mobile-optimized DataTables */
@media (max-width: 767.98px) {
  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_filter,
  .dataTables_wrapper .dataTables_info,
  .dataTables_wrapper .dataTables_paginate {
    text-align: left;
    float: none;
    display: block;
    margin-bottom: 0.75rem;
  }

  .dataTables_wrapper .dataTables_filter input {
    width: 100%;
    margin-left: 0;
    margin-top: 0.5rem;
    padding: 0.5rem;
    height: 44px;
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem;
    margin: 0 0.1rem;
  }

  /* Improve table card layout */
  .table-card {
    margin: 0 -0.75rem;
    width: calc(100% + 1.5rem);
    border-radius: 0;
  }

  .table-card .card-header {
    border-radius: 0;
  }
}

/* Mobile-optimized charts */
@media (max-width: 767.98px) {
  .chart-container {
    height: 250px !important;
    margin: 1rem 0;
  }
}

/* Mobile-friendly notifications */
@media (max-width: 767.98px) {
  .dropdown-menu.notification-dropdown {
    max-height: 80vh !important;
    width: 100%;
    left: 0 !important;
    right: 0 !important;
  }

  .notification-item {
    padding: 0.75rem;
  }
}

/* Mobile swipe gestures */
.swipe-container {
  overflow: hidden;
  position: relative;
  width: 100%;
}

.swipe-actions {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.swipe-action {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 1rem;
  color: white;
  min-width: 80px;
}

.swipe-action-delete {
  background-color: #dc3545;
}

.swipe-action-edit {
  background-color: #2E7D32;
}

.swipe-item {
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.swipe-item.swiped {
  transform: translateX(-160px);
}

.swipe-item.swiped .swipe-actions {
  transform: translateX(0);
}
