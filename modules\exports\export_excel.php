<?php
// Include necessary files
require_once '../../config/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: /choims/login.php");
    exit;
}

// Validate request
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['export_data'])) {
    header("Location: /choims/index.php");
    exit;
}

// Get data and filename
$exportData = json_decode($_POST['export_data'], true);
$filename = isset($_POST['filename']) ? $_POST['filename'] : 'export_' . date('Y-m-d');

// Make sure we have data
if (!is_array($exportData) || empty($exportData)) {
    header("Location: /choims/index.php");
    exit;
}

// Set headers for Excel download
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
header('Cache-Control: max-age=0');

// Start output buffering
ob_start();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Export</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            border: 1px solid #000;
            padding: 5px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <table>
        <?php
        // Output table headers
        if (!empty($exportData[0])) {
            echo "<tr>";
            foreach ($exportData[0] as $header) {
                echo "<th>" . htmlspecialchars($header) . "</th>";
            }
            echo "</tr>";
        }
        
        // Output table data (skip first row which is headers)
        for ($i = 1; $i < count($exportData); $i++) {
            echo "<tr>";
            foreach ($exportData[$i] as $cell) {
                echo "<td>" . htmlspecialchars($cell) . "</td>";
            }
            echo "</tr>";
        }
        ?>
    </table>
</body>
</html>
<?php
// End output buffering and flush
ob_end_flush();
exit;
?> 