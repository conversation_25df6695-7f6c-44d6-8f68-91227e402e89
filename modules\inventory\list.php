<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern inventory list CSS
echo '<link rel="stylesheet" href="/choims/assets/css/inventory-list-modern.css">';

// Ensure user is logged in
requireLogin();

// Initialize filter variables
$location_filter = '';
$category_filter = '';
$status_filter = '';
$search = '';

// Get filter parameters
if (isset($_GET['location'])) {
    $location_filter = sanitizeInput($_GET['location']);
} else {
    // Set default location filter based on user role
    if (isset($_GET['filter']) && $_GET['filter'] === 'low_stock') {
        // If viewing low stock items, don't set a default location filter
        $location_filter = '';
    } else if (strtolower($_SESSION['role']) === 'himu' ||
               strtolower($_SESSION['role']) === 'superadmin' ||
               strtolower($_SESSION['role']) === 'godmode' ||
               strtolower($_SESSION['role']) === 'logistics') {
        // Show all locations by default for HIMU, Superadmin, GodMode, and Logistics users
        $location_filter = '';
    }
}
if (isset($_GET['category'])) {
    $category_filter = sanitizeInput($_GET['category']);
}
if (isset($_GET['status'])) {
    $status_filter = sanitizeInput($_GET['status']);
}
if (isset($_GET['search'])) {
    $search = sanitizeInput($_GET['search']);
}

// Check if is_deleted column exists
$checkColumnQuery = "SHOW COLUMNS FROM consumable_inventory LIKE 'is_deleted'";
$checkColumnResult = mysqli_query($conn, $checkColumnQuery);
$isDeletedColumnExists = mysqli_num_rows($checkColumnResult) > 0;

// Build query based on user role and filters
$query = "
    SELECT i.inventory_id, i.current_quantity, i.min_quantity, i.status,
           i.low_stock_threshold, i.critical_threshold, i.last_restock_date,
           s.sku_code, s.sku_name, s.unit_of_measure,
           c.category_name, c.requires_himu_approval,
           l.location_name,
           (SELECT COUNT(*) FROM transfers t WHERE t.inventory_id = i.inventory_id AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')) as has_active_transfer,
           (SELECT t.transfer_id FROM transfers t WHERE t.inventory_id = i.inventory_id AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU') LIMIT 1) as active_transfer_id
";

// Only include the is_deleted column if it exists
if ($isDeletedColumnExists) {
    $query .= ", i.is_deleted";
}

$query .= "
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON i.location_id = l.location_id
    WHERE 1=1
";

// Only filter by is_deleted if the column exists
if ($isDeletedColumnExists) {
    // Only show deleted items to superadmin when explicitly requested
    if (isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && hasRole('Superadmin')) {
        $query .= " AND i.is_deleted = 1";
    } else {
        // For everyone else, only show non-deleted items
        $query .= " AND (i.is_deleted = 0 OR i.is_deleted IS NULL)";
    }
}

$params = [];
$types = '';

// Filter by user's location if not Logistics, HIMU, Superadmin, or GodMode
if (!hasRole('Logistics', 'Superadmin', 'GodMode', 'HIMU')) {
    $userLocationId = getUserLocationId();
    $query .= " AND i.location_id = ?";
    $params[] = $userLocationId;
    $types .= 'i';
}

// Apply filters
if (!empty($location_filter)) {
    $query .= " AND i.location_id = ?";
    $params[] = $location_filter;
    $types .= 'i';
}

if (!empty($category_filter)) {
    $query .= " AND c.category_id = ?";
    $params[] = $category_filter;
    $types .= 'i';
}

if (!empty($status_filter)) {
    $query .= " AND i.status = ?";
    $params[] = $status_filter;
    $types .= 's';
}

if (!empty($search)) {
    $query .= " AND (s.sku_code LIKE ? OR s.sku_name LIKE ? OR l.location_name LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= 'sss';
}

// Special handling for HIMU users
// Make sure we're not applying this filter to superadmins
if (hasRole('HIMU') && strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode') {
    // Get HIMU location ID from the user's session
    $himuLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

    // If we don't have a location ID from the session, try to find it by name
    if (!$himuLocationId) {
        $himuLocationQuery = "SELECT location_id FROM locations WHERE location_name LIKE '%HIMU%' OR location_name LIKE '%IT%' LIMIT 1";
        $himuLocationResult = mysqli_query($conn, $himuLocationQuery);

        if ($himuLocationResult && mysqli_num_rows($himuLocationResult) > 0) {
            $himuLocationRow = mysqli_fetch_assoc($himuLocationResult);
            $himuLocationId = $himuLocationRow['location_id'];
        }
    }

    // For HIMU users:
    // 1. Show all IT Supplies (category_id = 4) regardless of location
    // 2. Only show Office Supplies (category_id = 5) and Medical Supplies (category_id = 6) if they are in HIMU's location
    if (!empty($category_filter)) {
        // If a specific category is selected and it's Office Supplies or Medical Supplies,
        // still restrict to HIMU's location
        if ($category_filter == 5 || $category_filter == 6) {
            if ($himuLocationId) {
                $query .= " AND i.location_id = ?";
                $params[] = $himuLocationId;
                $types .= 'i';
            }
        }
        // For IT Supplies, no location restriction needed
    } else {
        // For no category filter, apply the special HIMU rules
        if ($himuLocationId) {
            $query .= " AND (c.category_id = 4 OR (c.category_id IN (5, 6) AND i.location_id = ?))";
            $params[] = $himuLocationId;
            $types .= 'i';
        } else {
            // If we can't find HIMU's location, just show IT Supplies
            $query .= " AND c.category_id = 4";
        }
    }
}

// Order by active transfers first, then by most recently added
$query .= " ORDER BY has_active_transfer DESC, i.inventory_id DESC";

// Prepare and execute the query
$stmt = mysqli_prepare($conn, $query);
if (!empty($params)) {
    // Backward-compatible approach for parameter binding without spread operator
    $bindParamsRef = array($stmt, $types);
    for($i = 0; $i < count($params); $i++) {
        $bindParamsRef[] = &$params[$i];
    }
    call_user_func_array('mysqli_stmt_bind_param', $bindParamsRef);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get categories for filter dropdown
if (hasRole('HIMU')) {
    // HIMU users should see IT supplies, Office Equipment, and any category that requires HIMU approval
    $categoryQuery = "SELECT category_id, category_name FROM categories
                     WHERE category_id = 4 OR category_id = 2 OR requires_himu_approval = 1
                     ORDER BY category_name";
} elseif (hasRole('Superadmin', 'GodMode')) {
    // Superadmin and GodMode users can see all categories
    $categoryQuery = "SELECT category_id, category_name FROM categories ORDER BY category_name";
} else {
    // Other users can see all consumable categories
    $categoryQuery = "SELECT category_id, category_name FROM categories WHERE category_name LIKE '%Supply%' ORDER BY category_name";
}
$categoryResult = mysqli_query($conn, $categoryQuery);

// Get locations for filter dropdown (for Logistics, Superadmin, GodMode, and HIMU)
$locationResult = null;
if (hasRole('Logistics', 'Superadmin', 'GodMode', 'HIMU')) {
    $locationQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
    $locationResult = mysqli_query($conn, $locationQuery);
}
?>

<!-- Modern styling is now in inventory-list-modern.css -->

<div class="container-fluid">
    <!-- Modern dashboard header -->
    <header class="dashboard-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <div class="dashboard-title-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <h1 class="dashboard-title">Consumables Inventory</h1>
            </div>
            <div class="dashboard-actions">
                <?php if (hasPermission('add_inventory')): ?>
                <a href="/choims/modules/inventory/add_product.php" class="action-btn action-primary">
                    <i class="fas fa-plus-circle"></i> Add Inventory
                </a>
                <?php endif; ?>
                <button class="action-btn" id="exportCSV">
                    <i class="fas fa-file-excel"></i> Export to Excel
                </button>
                <button id="printTable" class="action-btn">
                    <i class="fas fa-print"></i> Print Report
                </button>
            </div>
        </div>
        <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
    </header>

    <?php if (isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && hasRole('Superadmin')): ?>
        <div class="alert alert-warning d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon">
                <i class="fas fa-trash-alt"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>You are viewing deleted inventory items. <a href="/choims/modules/inventory/list.php" class="alert-link fw-bold">View active inventory</a></div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
        <div class="alert alert-success d-flex align-items-center animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="d-flex w-100 justify-content-between align-items-center">
                <div>Operation completed successfully!</div>
                <button type="button" class="btn-close small" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    <?php endif; ?>

    <?php
    // Calculate summary statistics
    mysqli_data_seek($result, 0);
    $total_items = 0;
    $total_quantity = 0;
    $low_stock_items = 0;
    $out_of_stock_items = 0;
    $categories_count = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $total_items++;
        $total_quantity += $row['current_quantity'];

        if ($row['status'] == 'Low Stock') {
            $low_stock_items++;
        } else if ($row['status'] == 'Out of Stock') {
            $out_of_stock_items++;
        }

        // Count items by category
        if (!isset($categories_count[$row['category_name']])) {
            $categories_count[$row['category_name']] = 0;
        }
        $categories_count[$row['category_name']]++;
    }

    // Reset the result pointer for later use
    mysqli_data_seek($result, 0);
    ?>

    <!-- Statistics Cards -->
    <div class="stats-container animate__animated animate__fadeInUp animate__faster">
        <div class="stat-card stat-primary">
            <div class="stat-icon">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $total_items; ?></div>
                <div class="stat-label">Total Items</div>
            </div>
        </div>

        <div class="stat-card stat-success">
            <div class="stat-icon">
                <i class="fas fa-cubes"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $total_quantity; ?></div>
                <div class="stat-label">Total Quantity</div>
            </div>
        </div>

        <div class="stat-card stat-warning">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $low_stock_items; ?></div>
                <div class="stat-label">Low Stock Items</div>
            </div>
        </div>

        <div class="stat-card stat-danger">
            <div class="stat-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $out_of_stock_items; ?></div>
                <div class="stat-label">Out of Stock</div>
            </div>
        </div>
    </div>

    <!-- Category Breakdown Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm rounded-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s;">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-info-bg rounded-3 p-3 me-3">
                                <i class="fas fa-chart-pie text-info"></i>
                            </div>
                            <h5 class="card-title mb-0">Category Breakdown</h5>
                        </div>
                    </div>

                    <div class="row">
                        <?php foreach ($categories_count as $category => $count): ?>
                            <div class="col-md-4 col-sm-6 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="category-icon rounded-circle p-2 me-3" style="background-color: rgba(<?php echo rand(0, 200); ?>, <?php echo rand(0, 200); ?>, <?php echo rand(0, 200); ?>, 0.1);">
                                        <i class="fas fa-folder text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo $category; ?></h6>
                                        <div class="d-flex align-items-center">
                                            <span class="text-muted small"><?php echo $count; ?> items</span>
                                            <div class="ms-2 badge bg-light text-dark"><?php echo round(($count / $total_items) * 100); ?>%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Filter Panel -->
    <div class="card animate__animated animate__fadeInUp animate__faster">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-filter"></i>
                Filter Inventory
                <?php if(!empty($category_filter) || !empty($location_filter) || !empty($status_filter) || !empty($search)): ?>
                    <span class="filter-badge">Active</span>
                <?php endif; ?>
            </div>
            <div>
                <?php if(!empty($category_filter) || !empty($location_filter) || !empty($status_filter) || !empty($search)): ?>
                    <a href="/choims/modules/inventory/list.php" class="btn btn-sm btn-outline-secondary btn-rounded">
                        <i class="fas fa-times me-1"></i> Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <div class="filter-card">
                <form method="get" action="" class="row g-3">
                    <div class="col-md-3">
                        <label for="category" class="form-label small fw-medium">Category</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-tags text-success"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php mysqli_data_seek($categoryResult, 0); ?>
                                <?php while ($category = mysqli_fetch_assoc($categoryResult)): ?>
                                    <option value="<?php echo $category['category_id']; ?>"
                                        <?php echo ($category_filter == $category['category_id']) ? 'selected' : ''; ?>>
                                        <?php echo $category['category_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>

                    <?php if (hasRole('Logistics', 'Superadmin', 'GodMode', 'HIMU') && $locationResult): ?>
                    <div class="col-md-3">
                        <label for="location" class="form-label small fw-medium">Location</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-map-marker-alt text-success"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="location" name="location">
                                <option value="">All Locations</option>
                                <?php mysqli_data_seek($locationResult, 0); ?>
                                <?php while ($location = mysqli_fetch_assoc($locationResult)): ?>
                                    <option value="<?php echo $location['location_id']; ?>"
                                        <?php echo ($location_filter == $location['location_id']) ? 'selected' : ''; ?>>
                                        <?php echo $location['location_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="col-md-2">
                        <label for="status" class="form-label small fw-medium">Status</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-info-circle text-success"></i></span>
                            <select class="form-select rounded-end-3 border-start-0" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="Available" <?php echo ($status_filter == 'Available') ? 'selected' : ''; ?>>Available</option>
                                <option value="Low Stock" <?php echo ($status_filter == 'Low Stock') ? 'selected' : ''; ?>>Low Stock</option>
                                <option value="Out of Stock" <?php echo ($status_filter == 'Out of Stock') ? 'selected' : ''; ?>>Out of Stock</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <label for="search" class="form-label small fw-medium">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-search text-success"></i></span>
                            <input type="text" class="form-control border-start-0 border-end-0" id="search" name="search"
                                   placeholder="SKU Code, Item Name, Location" value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-success rounded-end-3">
                                <i class="fas fa-filter me-1"></i> Apply Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="card animate__animated animate__fadeInUp animate__faster">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-table"></i>
                Inventory Items
            </div>
            <div class="d-flex align-items-center">
                <div class="view-toggle-container">
                    <div class="btn-group view-toggle-group">
                        <button type="button" class="btn btn-sm btn-light active" id="itemsViewBtn">
                            <i class="fas fa-boxes me-1"></i> Items
                        </button>
                        <button type="button" class="btn btn-sm btn-light" id="locationsViewBtn">
                            <i class="fas fa-map-marker-alt me-1"></i> Locations
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Loading State -->
            <div class="table-loading-state" id="tableLoadingState">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>Loading inventory...</p>
            </div>

            <!-- Items View -->
            <div id="itemsView">
                <div class="table-responsive">
                    <table class="table table-hover align-middle" id="inventoryTable" style="display: none;">
                    <thead class="table-light">
                        <tr>
                            <th>SKU Code</th>
                            <th>Item Name</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Quantity</th>
                            <th class="text-center">Status</th>
                            <th>Last Restock</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td><?php echo $row['sku_code']; ?></td>
                                    <td>
                                        <a href="/choims/modules/inventory/view.php?id=<?php echo $row['inventory_id']; ?>">
                                            <?php echo $row['sku_name']; ?>
                                        </a>
                                    </td>
                                    <td><?php echo $row['category_name']; ?></td>
                                    <td><?php echo $row['location_name']; ?></td>
                                    <td>
                                        <?php echo $row['current_quantity']; ?> <span class="text-muted"><?php echo $row['unit_of_measure']; ?></span>
                                        <?php if ($row['min_quantity'] > 0): ?>
                                            <small class="text-muted">(Min: <?php echo $row['min_quantity']; ?>)</small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php
                                        $statusClass = '';
                                        $statusIcon = '';

                                        switch ($row['status']) {
                                            case 'Available':
                                                $statusClass = 'bg-success bg-opacity-10 text-success';
                                                $statusIcon = 'fa-check-circle';
                                                break;
                                            case 'Low Stock':
                                                $statusClass = 'bg-warning bg-opacity-10 text-warning';
                                                $statusIcon = 'fa-exclamation-circle';
                                                break;
                                            case 'Out of Stock':
                                                $statusClass = 'bg-danger bg-opacity-10 text-danger';
                                                $statusIcon = 'fa-times-circle';
                                                break;
                                            default:
                                                $statusClass = 'bg-secondary bg-opacity-10 text-secondary';
                                                $statusIcon = 'fa-question-circle';
                                        }
                                        ?>
                                        <span class="badge rounded-pill <?php echo $statusClass; ?> px-3 py-2">
                                            <i class="fas <?php echo $statusIcon; ?> me-1"></i>
                                            <?php echo $row['status']; ?>
                                        </span>
                                        <?php if ($row['has_active_transfer'] > 0): ?>
                                        <div class="mt-1">
                                            <a href="/choims/modules/transfers/view.php?id=<?php echo $row['active_transfer_id']; ?>" class="text-decoration-none">
                                                <span class="badge bg-info" data-bs-toggle="tooltip" title="Click to view transfer details">
                                                    <i class="fas fa-exchange-alt me-1"></i>
                                                    Transfer Ongoing
                                                </span>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo !empty($row['last_restock_date']) ? formatDate($row['last_restock_date']) : 'N/A'; ?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7">
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-box-open"></i>
                                        </div>
                                        <h4 class="empty-state-title">No inventory items found</h4>
                                        <p class="empty-state-description">There are no consumable items in the current inventory.</p>
                                        <?php if (hasRole('Logistics', 'Superadmin', 'GodMode')): ?>
                                            <a href="/choims/modules/inventory/add.php" class="btn btn-success">
                                                <i class="fas fa-plus me-2"></i> Add New Item
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Locations View -->
        <div id="locationsView" style="display: none;">
            <?php if (hasRole('Logistics', 'HIMU', 'Superadmin', 'GodMode')): ?>
            <div class="locations-container">
                <?php
                // Calculate items by location
                mysqli_data_seek($result, 0);
                $locations_count = [];
                $locations_quantity = [];
                $locations_unit_types = [];

                while ($row = mysqli_fetch_assoc($result)) {
                    if (!isset($locations_count[$row['location_name']])) {
                        $locations_count[$row['location_name']] = 0;
                        $locations_quantity[$row['location_name']] = 0;
                        $locations_unit_types[$row['location_name']] = [];
                    }
                    $locations_count[$row['location_name']]++;
                    $locations_quantity[$row['location_name']] += $row['current_quantity'];

                    // Track unit types for this location
                    if (!empty($row['unit_of_measure']) && !isset($locations_unit_types[$row['location_name']][$row['unit_of_measure']])) {
                        $locations_unit_types[$row['location_name']][$row['unit_of_measure']] = 0;
                    }
                    if (!empty($row['unit_of_measure'])) {
                        $locations_unit_types[$row['location_name']][$row['unit_of_measure']] += $row['current_quantity'];
                    }
                }

                // Reset the result pointer for later use
                mysqli_data_seek($result, 0);

                // Sort locations by item count (descending)
                arsort($locations_count);

                // Get the user's current location
                $userLocationName = '';
                $userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

                if ($userLocationId) {
                    // Get the location name for the user's location ID
                    $userLocationQuery = "SELECT location_name FROM locations WHERE location_id = ?";
                    $userLocationStmt = mysqli_prepare($conn, $userLocationQuery);
                    mysqli_stmt_bind_param($userLocationStmt, 'i', $userLocationId);
                    mysqli_stmt_execute($userLocationStmt);
                    $userLocationResult = mysqli_stmt_get_result($userLocationStmt);

                    if ($userLocationRow = mysqli_fetch_assoc($userLocationResult)) {
                        $userLocationName = $userLocationRow['location_name'];
                    }
                }
                ?>

                <div class="row">
                    <?php foreach ($locations_count as $location => $count):
                        $isUserLocation = ($location == $userLocationName);
                    ?>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="location-card p-3 rounded-3 border <?php echo $isUserLocation ? 'user-location' : ''; ?>">
                                <?php if ($isUserLocation): ?>
                                <div class="user-location-badge">Your Location</div>
                                <?php endif; ?>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="location-icon rounded-circle p-2 me-3 <?php echo $isUserLocation ? 'bg-success-bg' : 'bg-primary-bg'; ?>">
                                        <i class="fas fa-map-marker-alt <?php echo $isUserLocation ? 'text-success' : 'text-primary'; ?>"></i>
                                    </div>
                                    <h6 class="mb-0"><?php echo $location; ?></h6>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <div class="text-muted small">Items: <span class="fw-bold text-dark"><?php echo $count; ?></span></div>
                                        <div class="text-muted small">Quantity:
                                            <span class="fw-bold text-dark">
                                                <?php
                                                if (!empty($locations_unit_types[$location])) {
                                                    $unitStrings = [];
                                                    foreach ($locations_unit_types[$location] as $unit => $qty) {
                                                        $unitStrings[] = $qty . ' ' . $unit;
                                                    }
                                                    echo implode(', ', $unitStrings);
                                                } else {
                                                    echo $locations_quantity[$location];
                                                }
                                                ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="badge <?php echo $isUserLocation ? 'bg-success-bg text-success' : 'bg-primary-bg text-primary'; ?> px-3 py-2 rounded-pill">
                                        <?php echo round(($count / $total_items) * 100); ?>%
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 4px;">
                                    <div class="progress-bar <?php echo $isUserLocation ? 'bg-success' : 'bg-primary'; ?>" role="progressbar" style="width: <?php echo ($total_items > 0) ? ($count / $total_items * 100) : 0; ?>%" aria-valuenow="<?php echo ($total_items > 0) ? ($count / $total_items * 100) : 0; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                You don't have permission to view location breakdown. Please contact your administrator.
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide the table initially and show loading state
    document.getElementById('inventoryTable').style.display = 'none';
    document.getElementById('tableLoadingState').style.display = 'flex';

    // View toggle functionality
    const itemsViewBtn = document.getElementById('itemsViewBtn');
    const locationsViewBtn = document.getElementById('locationsViewBtn');
    const itemsView = document.getElementById('itemsView');
    const locationsView = document.getElementById('locationsView');

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            delay: { show: 300, hide: 100 },
            container: 'body'
        });
    });

    // Check if there are any rows in the table
    const tableRows = document.querySelectorAll('#inventoryTable tbody tr');
    const hasData = tableRows.length > 0 && !tableRows[0].querySelector('td[colspan="7"]');

    // Hide loading state
    document.getElementById('tableLoadingState').style.display = 'none';
    document.getElementById('inventoryTable').style.display = 'table';

    // Set up view toggle event listeners
    itemsViewBtn.addEventListener('click', function() {
        itemsViewBtn.classList.add('active');
        locationsViewBtn.classList.remove('active');
        itemsView.style.display = 'block';
        locationsView.style.display = 'none';

        // Adjust DataTable columns if it exists
        if (typeof table !== 'undefined' && table.columns) {
            table.columns.adjust().draw();
        }

        // Save preference to localStorage
        localStorage.setItem('inventoryViewPreference', 'items');
    });

    locationsViewBtn.addEventListener('click', function() {
        locationsViewBtn.classList.add('active');
        itemsViewBtn.classList.remove('active');
        locationsView.style.display = 'block';
        itemsView.style.display = 'none';

        // Save preference to localStorage
        localStorage.setItem('inventoryViewPreference', 'locations');
    });

    // Check for saved preference
    const savedViewPreference = localStorage.getItem('inventoryViewPreference');
    if (savedViewPreference === 'locations') {
        locationsViewBtn.click();
    }

    // Only initialize DataTable if there's actual data
    if (hasData) {
        // Initialize DataTable
        const table = $('#inventoryTable').DataTable({
        order: [], // Disable client-side ordering to use server-side ordering
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        responsive: true,
        autoWidth: false,
        language: {
            "search": "<i class='fas fa-search'></i> _INPUT_",
            "searchPlaceholder": "Search inventory...",
            "paginate": {
                "previous": "<i class='fas fa-chevron-left'></i>",
                "next": "<i class='fas fa-chevron-right'></i>"
            },
            "info": "Showing _START_ to _END_ of _TOTAL_ items",
            "infoEmpty": "Showing 0 items",
            "zeroRecords": "No matching items found"
        },
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'copy',
                className: 'd-none'
            },
            {
                extend: 'excel',
                className: 'd-none',
                title: 'Consumable Inventory Report',
                orientation: 'landscape',
                pageSize: 'A4',
                exportOptions: {
                    stripHtml: true,
                    columns: ':visible'
                },
                customize: function(xlsx) {
                    // Add styling to Excel export
                    var sheet = xlsx.xl.worksheets['sheet1.xml'];
                    $('row:first c', sheet).attr('s', '32'); // Add header style
                }
            },
            {
                extend: 'print',
                className: 'd-none',
                title: 'Consumable Inventory Report',
                messageTop: null,
                messageBottom: null,
                autoPrint: true,
                orientation: 'landscape',
                exportOptions: {
                    stripHtml: false,
                    columns: ':visible'
                },
                customize: function (win) {
                    // Remove any default headers/footers
                    $(win.document.body).find('h1').remove();
                    $(win.document.body).find('div.dt-print-heading').remove();

                    // Set document title to avoid about:blank
                    $(win.document).attr('title', 'Consumable Inventory Report');

                    // Add padding to the page
                    $(win.document.body).css({
                        'padding-left': '30px',
                        'padding-right': '30px',
                        'padding-top': '20px',
                        'padding-bottom': '20px'
                    });

                    // Get current user and formatted date for print header
                    var currentUser = '<?php echo isset($_SESSION["username"]) ? $_SESSION["username"] : "User"; ?>';
                    var currentDate = new Date().toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'});

                    // Add logo, title, and user info with date
                    $(win.document.body).prepend(
                        '<div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:20px;">' +
                        '<div style="display:flex; align-items:center;">' +
                        '<img src="/choims/assets/img/prqlogo2.png" style="height:80px; margin-right:20px;" />' +
                        '<h1 style="margin:0; color:#2e7d32; font-weight:bold;">Consumable Inventory Report</h1>' +
                        '</div>' +
                        '<div style="text-align:right; color:#2e7d32; font-size:14px; font-weight:500;">' +
                        '<div style="margin-bottom:5px;"><span style="display:inline-block; width:80px;">Printed by:</span>&nbsp;&nbsp;' + currentUser + '</div>' +
                        '<div><span style="display:inline-block; width:80px;">Date:</span>&nbsp;&nbsp;' + currentDate + '</div>' +
                        '</div>' +
                        '</div>'
                    );

                    // Clean up print view and remove date/about:blank
                    $(win.document.head).append(`
                        <style>
                            @page {
                                size: auto;
                                margin: 0mm;
                            }
                            body {
                                margin: 0;
                                padding: 1.6cm;
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                            }
                            table { margin-top: 15px; }
                            /* Add spacing after page breaks */
                            @media print {
                                @page :footer {
                                    display: none
                                }
                                @page :header {
                                    display: none
                                }
                                /* First page has no margin, other pages have 20px margin */
                                @page :first {
                                    margin-top: 0mm;
                                }
                                @page {
                                    margin-top: 20px;
                                }
                                tr {
                                    page-break-inside: avoid;
                                }
                                thead {
                                    display: table-header-group;
                                }
                                tfoot {
                                    display: table-footer-group;
                                }
                                /* Add margin to content after page break */
                                .pagebreak {
                                    page-break-before: always;
                                    margin-top: 20px;
                                }
                                tr.pagebreak {
                                    margin-top: 20px;
                                }
                            }
                        </style>
                    `);

                    // Keep the table styling intact
                    $(win.document.body).find('table')
                        .addClass('table table-bordered')
                        .css('font-size', '12px');

                    // Ensure status badges retain their colors in print
                    $(win.document.body).find('.badge').each(function() {
                        var statusClass = $(this).attr('class').split(' ').filter(function(c) {
                            return c.indexOf('bg-') === 0;
                        })[0];

                        var color = '#777'; // default color
                        if (statusClass === 'bg-success') color = '#2e7d32';
                        if (statusClass === 'bg-warning') color = '#ff9800';
                        if (statusClass === 'bg-danger') color = '#e53935';
                        if (statusClass === 'bg-secondary') color = '#78909c';

                        $(this).css({
                            'background-color': color,
                            'color': statusClass === 'bg-warning' ? '#333' : '#fff',
                            'padding': '3px 8px',
                            'border-radius': '12px',
                            'font-size': '11px',
                            'font-weight': 'bold'
                        });
                    });
                }
            }
        ]
    });

        // Make sure the table is visible with proper width
        table.columns.adjust().draw();

        // Export to CSV - handle both buttons (header and card)
        document.querySelectorAll('#exportCSV').forEach(function(button) {
            button.addEventListener('click', function() {
                table.button('.buttons-excel').trigger();
            });
        });

        // Print Table - handle both buttons (header and card)
        document.querySelectorAll('#printTable').forEach(function(button) {
            button.addEventListener('click', function() {
                table.button('.buttons-print').trigger();
            });
        });
    } else {
        // Handle case when there's no data
        // Disable export and print buttons
        document.querySelectorAll('#exportCSV').forEach(function(button) {
            button.disabled = true;
            button.classList.add('disabled');

            // Add event listeners with alerts
            button.addEventListener('click', function(e) {
                e.preventDefault();
                alert('No data available to export.');
            });
        });

        document.querySelectorAll('#printTable').forEach(function(button) {
            button.disabled = true;
            button.classList.add('disabled');

            // Add event listeners with alerts
            button.addEventListener('click', function(e) {
                e.preventDefault();
                alert('No data available to print.');
            });
        });
    }

    // Set inventory ID for deletion (only if delete buttons exist)
    const deleteButtons = document.querySelectorAll('.delete-inventory');
    if (deleteButtons.length > 0) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('deleteInventoryId').value = this.getAttribute('data-inventory-id');
            });
        });
    }

    // Set inventory ID for restoration (only if restore buttons exist)
    const restoreButtons = document.querySelectorAll('.restore-inventory');
    if (restoreButtons.length > 0) {
        restoreButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('restoreInventoryId').value = this.getAttribute('data-inventory-id');
            });
        });
    }
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>

<!-- Delete Inventory Modal -->
<div class="modal fade" id="deleteInventoryModal" tabindex="-1" aria-labelledby="deleteInventoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown animate__faster">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteInventoryModalLabel">
                    <i class="fas fa-trash-alt text-danger me-2"></i>
                    Confirm Deletion
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-0">Are you sure you want to delete this inventory item? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-rounded" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <form id="deleteInventoryForm" action="/choims/modules/inventory/delete.php" method="post">
                    <input type="hidden" name="inventory_id" id="deleteInventoryId" value="">
                    <button type="submit" class="btn btn-danger btn-rounded">
                        <i class="fas fa-trash-alt me-1"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Restore Inventory Modal -->
<div class="modal fade" id="restoreInventoryModal" tabindex="-1" aria-labelledby="restoreInventoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered animate__animated animate__fadeInDown animate__faster">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="restoreInventoryModalLabel">
                    <i class="fas fa-trash-restore text-success me-2"></i>
                    Confirm Restoration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-0">Are you sure you want to restore this inventory item?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary btn-rounded" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Cancel
                </button>
                <form id="restoreInventoryForm" action="/choims/modules/inventory/restore.php" method="post">
                    <input type="hidden" name="inventory_id" id="restoreInventoryId" value="">
                    <button type="submit" class="btn btn-success btn-rounded">
                        <i class="fas fa-check me-1"></i> Restore
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>