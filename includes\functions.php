<?php
// Only start session if one hasn't been started already
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include the Cache class
require_once __DIR__ . '/Cache.php';

// Define constants for monthly inventory update
define('MONTHLY_UPDATE_DAYS_BEFORE', 2); // Days before month end to show update button
define('MONTHLY_UPDATE_STATUS_PENDING', 'pending');
define('MONTHLY_UPDATE_STATUS_COMPLETED', 'completed');

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check user role
function hasRole() {
    if (!isLoggedIn()) return false;

    // Get roles from function arguments
    $roles = func_get_args();

    // Convert user role to lowercase for case-insensitive comparison
    $userRole = strtolower($_SESSION['role']);

    // Check if we're trying to access edit/modify functionality
    $action = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
    $callerFunction = isset($action[1]['function']) ? $action[1]['function'] : '';
    $callerFile = isset($action[1]['file']) ? basename($action[1]['file']) : '';

    // If the user is superadmin and we're checking for edit/modify operations, don't allow
    $editOperations = [
        'add.php', 'edit.php', 'delete.php', 'create.php', 'stock_in.php', 'stock_out.php',
        'approve', 'reject', 'update', 'save', 'insert', 'delete'
    ];

    $isEditOperation = false;
    foreach ($editOperations as $op) {
        if (stripos($callerFile, $op) !== false || stripos($callerFunction, $op) !== false) {
            $isEditOperation = true;
            break;
        }
    }

    // Check each role
    foreach ($roles as $role) {
        $checkRole = strtolower($role);

        if ($checkRole == 'godmode' && $userRole == 'godmode') {
            return true;
        } else if ($checkRole == 'superadmin' && ($userRole == 'superadmin' || $userRole == 'godmode')) {
            // For superadmin, exclude edit operations
            if ($userRole == 'superadmin' && $isEditOperation) {
                continue; // Skip allowing superadmin for edit operations
            }
            return true;
        } else if ($checkRole == 'logistics' && ($userRole == 'logistics' || $userRole == 'godmode' || ($userRole == 'superadmin' && !$isEditOperation))) {
            return true;
        } else if ($checkRole == 'himu' && ($userRole == 'himu' || $userRole == 'godmode' || ($userRole == 'superadmin' && !$isEditOperation))) {
            return true;
        } else if ($checkRole == 'department' && ($userRole == 'department' || $userRole == 'godmode' || ($userRole == 'superadmin' && !$isEditOperation))) {
            return true;
        } else if ($checkRole == 'healthcenter' && ($userRole == 'healthcenter' || $userRole == 'godmode' || ($userRole == 'superadmin' && !$isEditOperation))) {
            return true;
        }
    }

    return false;
}

// Redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        // Don't try to send headers if output has already started
        if (headers_sent()) {
            echo '<script>window.location.href = "../modules/auth/login.php";</script>';
            exit();
        } else {
            header('Location: ../modules/auth/login.php');
            exit();
        }
    }
}

// Redirect if not authorized for role
function requireRole() {
    requireLogin();

    // Get roles from function arguments
    $roles = func_get_args();
    $authorized = false;

    // Check each role
    foreach ($roles as $role) {
        if (hasRole($role)) {
            $authorized = true;
            break;
        }
    }

    // Redirect if not authorized for any role
    if (!$authorized) {
        // Don't try to send headers if output has already started
        if (headers_sent()) {
            echo '<script>window.location.href = "/choims/index.php?error=unauthorized";</script>';
            exit();
        } else {
            header('Location: /choims/index.php?error=unauthorized');
            exit();
        }
    }
}

// Get user's location name
function getUserLocation($conn, $location_id) {
    $sql = "SELECT location_name FROM locations WHERE location_id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $location_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($row = mysqli_fetch_assoc($result)) {
        return $row['location_name'];
    }

    return 'Unknown Location';
}

// Get the current user's location ID from session
function getUserLocationId() {
    return isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;
}

// Format date for display
function formatDate($date) {
    // Check for null, empty string, or zero dates
    if ($date === null || $date === '' || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
        return 'N/A';
    }

    // Some database drivers may return dates as a DateTime object
    if ($date instanceof DateTime) {
        return $date->format('F j, Y h:i A');
    }

    // Handle string date formats
    $timestamp = strtotime($date);
    if ($timestamp === false || $timestamp < 0) {
        // Invalid date string or date before 1970
        return 'N/A';
    }

    // At this point we have a valid timestamp
    return date('F j, Y h:i A', $timestamp);
}

// Format datetime for display
function formatDateTime($datetime) {
    // Check for null, empty string, or zero dates
    if ($datetime === null || $datetime === '' || $datetime === '0000-00-00' || $datetime === '0000-00-00 00:00:00') {
        return 'N/A';
    }

    // Some database drivers may return dates as a DateTime object
    if ($datetime instanceof DateTime) {
        return $datetime->format('M d, Y h:i A');
    }

    // Handle string date formats
    $timestamp = strtotime($datetime);
    if ($timestamp === false || $timestamp < 0) {
        // Invalid date string or date before 1970
        return 'N/A';
    }

    // At this point we have a valid timestamp
    return date('M d, Y h:i A', $timestamp);
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Log activity
function logActivity($conn, $action, $entity_type, $entity_id, $old_values = null, $new_values = null) {
    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
    $ip_address = $_SERVER['REMOTE_ADDR'];

    $old_values_json = ($old_values === null) ? null : $old_values;
    $new_values_json = ($new_values === null) ? null : $new_values;

    $query = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
              VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'issssss', $user_id, $action, $entity_type, $entity_id, $old_values_json, $new_values_json, $ip_address);
    $result = mysqli_stmt_execute($stmt);

    // Create notification for superadmins
    if ($result) {
        createAuditNotification($conn, $user_id, $action, $entity_type, $entity_id, $new_values_json);
    }

    return $result;
}

// Get category information - with caching
function getCategoryInfo($conn, $category_id) {
    $cache = Cache::getInstance();
    $cacheKey = "category_{$category_id}";

    return $cache->remember($cacheKey, 3600, function() use ($conn, $category_id) {
        $sql = "SELECT * FROM categories WHERE category_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, 'i', $category_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if ($row = mysqli_fetch_assoc($result)) {
            return $row;
        }

        return null;
    });
}

// Get SKU information - with caching
function getSkuInfo($conn, $sku_id) {
    $cache = Cache::getInstance();
    $cacheKey = "sku_{$sku_id}";

    return $cache->remember($cacheKey, 3600, function() use ($conn, $sku_id) {
        $sql = "SELECT s.*, c.category_name
                FROM sku_master s
                JOIN categories c ON s.category_id = c.category_id
                WHERE s.sku_id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, 'i', $sku_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if ($row = mysqli_fetch_assoc($result)) {
            return $row;
        }

        return null;
    });
}

// Check if an SKU requires HIMU approval - with caching
function requiresHimuApproval($conn, $sku_id) {
    $cache = Cache::getInstance();
    $cacheKey = "himu_approval_{$sku_id}";

    return $cache->remember($cacheKey, 3600, function() use ($conn, $sku_id) {
        $sku = getSkuInfo($conn, $sku_id);
        return $sku && isset($sku['requires_himu_approval']) && $sku['requires_himu_approval'] == 1;
    });
}

// Create notification
function createNotification($conn, $user_id, $type, $title, $message, $entity_type, $entity_id) {
    // First check if the user exists
    $checkUserQuery = "SELECT user_id FROM users WHERE user_id = ?";
    $checkUserStmt = mysqli_prepare($conn, $checkUserQuery);
    mysqli_stmt_bind_param($checkUserStmt, 'i', $user_id);
    mysqli_stmt_execute($checkUserStmt);
    mysqli_stmt_store_result($checkUserStmt);

    // If user doesn't exist, return early
    if (mysqli_stmt_num_rows($checkUserStmt) === 0) {
        mysqli_stmt_close($checkUserStmt);
        return false;
    }

    mysqli_stmt_close($checkUserStmt);

    // Enhance notification details based on type
    $enhancedMessage = $message;

    // Get additional details based on entity type
    if ($entity_id) {
        switch ($entity_type) {
            case 'transfer':
                // Get transfer details
                $transferQuery = "SELECT t.*,
                                    sl.location_name as source_location_name,
                                    dl.location_name as destination_location_name,
                                    COALESCE(
                                        (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id),
                                        (SELECT s.sku_name FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
                                    ) as item_name,
                                    u.full_name as requested_by_name
                                FROM transfers t
                                LEFT JOIN locations sl ON t.source_location_id = sl.location_id
                                LEFT JOIN locations dl ON t.destination_location_id = dl.location_id
                                LEFT JOIN users u ON t.initiated_by = u.user_id
                                WHERE t.transfer_id = ?";
                $transferStmt = mysqli_prepare($conn, $transferQuery);
                mysqli_stmt_bind_param($transferStmt, 'i', $entity_id);
                mysqli_stmt_execute($transferStmt);
                $transferResult = mysqli_stmt_get_result($transferStmt);

                if ($transfer = mysqli_fetch_assoc($transferResult)) {
                    // Add more details to the message
                    $enhancedMessage = $message . "\n\n";
                    $enhancedMessage .= "Item: " . $transfer['item_name'] . "\n";
                    $enhancedMessage .= "Quantity: " . $transfer['quantity'] . "\n";
                    $enhancedMessage .= "From: " . $transfer['source_location_name'] . "\n";
                    $enhancedMessage .= "To: " . $transfer['destination_location_name'] . "\n";
                    $enhancedMessage .= "Status: " . ucfirst($transfer['status']) . "\n";
                    $enhancedMessage .= "Requested by: " . $transfer['requested_by_name'] . "\n";
                    $enhancedMessage .= "Date: " . formatDateTime($transfer['created_at']);
                }
                break;

            case 'batch_transfer':
                // Get batch transfer details
                $batchQuery = "SELECT bt.*,
                                u.full_name as created_by_name,
                                sl.location_name as source_location_name,
                                dl.location_name as destination_location_name
                              FROM batch_transfers bt
                              LEFT JOIN users u ON bt.created_by = u.user_id
                              LEFT JOIN locations sl ON bt.source_location_id = sl.location_id
                              LEFT JOIN locations dl ON bt.destination_location_id = dl.location_id
                              WHERE bt.batch_id = ?";
                $batchStmt = mysqli_prepare($conn, $batchQuery);
                mysqli_stmt_bind_param($batchStmt, 'i', $entity_id);
                mysqli_stmt_execute($batchStmt);
                $batchResult = mysqli_stmt_get_result($batchStmt);

                if ($batch = mysqli_fetch_assoc($batchResult)) {
                    // Count items in batch
                    $countQuery = "SELECT COUNT(*) as item_count FROM batch_transfer_items WHERE batch_id = ?";
                    $countStmt = mysqli_prepare($conn, $countQuery);
                    mysqli_stmt_bind_param($countStmt, 'i', $entity_id);
                    mysqli_stmt_execute($countStmt);
                    $countResult = mysqli_stmt_get_result($countStmt);
                    $countRow = mysqli_fetch_assoc($countResult);
                    $itemCount = $countRow ? $countRow['item_count'] : 0;

                    // Add more details to the message
                    $enhancedMessage = $message . "\n\n";
                    $enhancedMessage .= "Batch Code: " . $batch['batch_code'] . "\n";
                    $enhancedMessage .= "Items: " . $itemCount . "\n";
                    $enhancedMessage .= "From: " . $batch['source_location_name'] . "\n";
                    $enhancedMessage .= "To: " . $batch['destination_location_name'] . "\n";
                    $enhancedMessage .= "Status: " . ucfirst($batch['status']) . "\n";
                    $enhancedMessage .= "Created by: " . $batch['created_by_name'] . "\n";
                    $enhancedMessage .= "Date: " . formatDateTime($batch['created_at']);
                }
                break;

            case 'asset':
                // Get asset details
                $assetQuery = "SELECT a.*, c.category_name, l.location_name as location_name
                              FROM fixed_assets a
                              LEFT JOIN categories c ON a.category_id = c.category_id
                              LEFT JOIN locations l ON a.location_id = l.location_id
                              WHERE a.asset_id = ?";
                $assetStmt = mysqli_prepare($conn, $assetQuery);
                mysqli_stmt_bind_param($assetStmt, 'i', $entity_id);
                mysqli_stmt_execute($assetStmt);
                $assetResult = mysqli_stmt_get_result($assetStmt);

                if ($asset = mysqli_fetch_assoc($assetResult)) {
                    // Add more details to the message
                    $enhancedMessage = $message . "\n\n";
                    $enhancedMessage .= "Asset: " . $asset['asset_name'] . "\n";
                    $enhancedMessage .= "Category: " . $asset['category_name'] . "\n";
                    $enhancedMessage .= "Location: " . $asset['location_name'] . "\n";
                    $enhancedMessage .= "Status: " . ucfirst($asset['status']) . "\n";
                    $enhancedMessage .= "Date: " . formatDateTime($asset['created_at']);
                }
                break;

            case 'inventory':
                // Get inventory details
                $inventoryQuery = "SELECT i.*, s.sku_name, l.location_name as location_name
                                  FROM consumable_inventory i
                                  LEFT JOIN sku_master s ON i.sku_id = s.sku_id
                                  LEFT JOIN locations l ON i.location_id = l.location_id
                                  WHERE i.inventory_id = ?";
                $inventoryStmt = mysqli_prepare($conn, $inventoryQuery);
                mysqli_stmt_bind_param($inventoryStmt, 'i', $entity_id);
                mysqli_stmt_execute($inventoryStmt);
                $inventoryResult = mysqli_stmt_get_result($inventoryStmt);

                if ($inventory = mysqli_fetch_assoc($inventoryResult)) {
                    // Add more details to the message
                    $enhancedMessage = $message . "\n\n";
                    $enhancedMessage .= "Item: " . $inventory['sku_name'] . "\n";
                    $enhancedMessage .= "Quantity: " . $inventory['quantity'] . "\n";
                    $enhancedMessage .= "Location: " . $inventory['location_name'] . "\n";
                    $enhancedMessage .= "Date: " . formatDateTime($inventory['created_at']);
                }
                break;
        }
    }

    // User exists, create the notification
    $sql = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id)
            VALUES (?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'issssi', $user_id, $type, $title, $enhancedMessage, $entity_type, $entity_id);

    return mysqli_stmt_execute($stmt);
}

// Get user's unread notifications count - with shorter cache time
function getUnreadNotificationsCount($conn, $user_id) {
    $cache = Cache::getInstance();
    $cacheKey = "unread_notifications_{$user_id}";

    return $cache->remember($cacheKey, 60, function() use ($conn, $user_id) {
        $sql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, 'i', $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if ($row = mysqli_fetch_assoc($result)) {
            return $row['count'];
        }

        return 0;
    });
}

// Alias for logActivity function for backward compatibility
function createAuditLog($user_id, $action, $entity_type, $entity_id, $old_values, $new_values, $ip_address = null) {
    global $conn;

    if ($ip_address === null) {
        $ip_address = $_SERVER['REMOTE_ADDR'];
    }

    $query = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
              VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'issssss', $user_id, $action, $entity_type, $entity_id, $old_values, $new_values, $ip_address);
    $result = mysqli_stmt_execute($stmt);

    // Create notification for superadmins
    if ($result) {
        createAuditNotification($conn, $user_id, $action, $entity_type, $entity_id, $new_values);
    }

    return $result;
}

// Check if user has a specific permission
function hasPermission($permission) {
    if (!isLoggedIn()) return false;

    // Map roles to permissions
    $rolePermissions = [
        'godmode' => ['create_transfer', 'batch_transfer', 'edit_transfer', 'delete_transfer', 'approve_transfer', 'view_all_transfers', 'manage_users', 'manage_locations', 'manage_categories', 'manage_sku', 'view_reports'],
        'superadmin' => ['view_all_transfers', 'view_reports'], // Superadmin only has view permissions
        'logistics' => ['create_transfer', 'batch_transfer', 'edit_transfer', 'approve_transfer', 'view_all_transfers', 'view_reports'],
        'himu' => ['create_transfer', 'batch_transfer', 'edit_transfer', 'approve_transfer', 'view_all_transfers', 'manage_sku', 'view_reports'],
        'department' => ['create_transfer', 'edit_transfer'],
        'healthcenter' => ['create_transfer', 'edit_transfer']
    ];

    $userRole = strtolower($_SESSION['role']);

    // For GodMode, grant all permissions
    if ($userRole == 'godmode') {
        return true;
    }

    // Check if the user's role has the requested permission
    return isset($rolePermissions[$userRole]) && in_array($permission, $rolePermissions[$userRole]);
}

// Format currency with Peso sign
function formatCurrency($amount) {
    if (empty($amount) || !is_numeric($amount)) {
        return '<span class="text-muted">N/A</span>';
    }
    return '₱' . number_format($amount, 2);
}

/**
 * Creates a notification for superadmins when an audit log is created
 *
 * @param int $user_id User ID who performed the action
 * @param string $action Action performed (create, update, delete, etc.)
 * @param string $entity_type Type of entity (asset, inventory, transfer, etc.)
 * @param int $entity_id ID of the entity
 * @param string $details Additional details about the action
 * @return bool True if notification was created, false otherwise
 */
function createAuditNotification($conn, $user_id, $action, $entity_type, $entity_id, $details = '') {
    // Get the username who performed the action
    $usernameQuery = "SELECT username, full_name FROM users WHERE user_id = ?";
    $usernameStmt = mysqli_prepare($conn, $usernameQuery);
    mysqli_stmt_bind_param($usernameStmt, 'i', $user_id);
    mysqli_stmt_execute($usernameStmt);
    $usernameResult = mysqli_stmt_get_result($usernameStmt);
    $userData = mysqli_fetch_assoc($usernameResult);
    $username = $userData ? $userData['username'] : 'Unknown User';
    $fullName = $userData ? $userData['full_name'] : 'Unknown User';

    // Format action for display
    $actionDisplay = ucfirst($action);
    $entityTypeDisplay = ucfirst($entity_type);

    // Create notification title and message
    $title = "{$actionDisplay} {$entityTypeDisplay}";

    // Create a more user-friendly message
    $message = "{$fullName} ";

    // Add the action in past tense
    switch(strtolower($action)) {
        case 'approve':
            $message .= "approved";
            break;
        case 'reject':
            $message .= "rejected";
            break;
        case 'complete':
            $message .= "completed";
            break;
        case 'create':
            $message .= "created";
            break;
        case 'update':
            $message .= "updated";
            break;
        case 'delete':
            $message .= "deleted";
            break;
        default:
            $message .= $action . "d";
    }

    // For user entities, get the full name of the user being modified
    if (strtolower($entity_type) === 'user') {
        // Get the target user's full name
        $targetUserQuery = "SELECT full_name FROM users WHERE user_id = ?";
        $targetUserStmt = mysqli_prepare($conn, $targetUserQuery);
        mysqli_stmt_bind_param($targetUserStmt, 'i', $entity_id);
        mysqli_stmt_execute($targetUserStmt);
        $targetUserResult = mysqli_stmt_get_result($targetUserStmt);
        $targetUserData = mysqli_fetch_assoc($targetUserResult);
        $targetUserName = $targetUserData ? $targetUserData['full_name'] : 'Unknown User';

        $message .= " {$entityTypeDisplay}: {$targetUserName}";
    } else {
        $message .= " {$entityTypeDisplay} #{$entity_id}";
    }

    // Extract status from JSON details if available
    if (!empty($details) && strpos($details, '"status"') !== false) {
        // Try to decode the JSON
        $detailsArray = json_decode($details, true);
        if (is_array($detailsArray) && isset($detailsArray['status'])) {
            $message .= " - Status: " . $detailsArray['status'];
        }
    }

    // Create notifications for all superadmins
    $insertQuery = "
        INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id, created_at)
        SELECT user_id, 'audit_log', ?, ?, ?, ?, NOW()
        FROM users
        WHERE LOWER(role) IN ('superadmin', 'godmode')
    ";

    $insertStmt = mysqli_prepare($conn, $insertQuery);
    mysqli_stmt_bind_param($insertStmt, 'sssi', $title, $message, $entity_type, $entity_id);
    $result = mysqli_stmt_execute($insertStmt);

    return $result;
}

// Get all categories - with caching
function getAllCategories($conn) {
    $cache = Cache::getInstance();
    $cacheKey = "all_categories";

    return $cache->remember($cacheKey, 3600, function() use ($conn) {
        $categories = [];
        $sql = "SELECT * FROM categories WHERE status = 'active' ORDER BY category_name";
        $result = mysqli_query($conn, $sql);

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $categories[] = $row;
            }
        }

        return $categories;
    });
}

// Get all locations - with caching
function getAllLocations($conn) {
    $cache = Cache::getInstance();
    $cacheKey = "all_locations";

    return $cache->remember($cacheKey, 3600, function() use ($conn) {
        $locations = [];
        $sql = "SELECT * FROM locations WHERE status = 'active' ORDER BY name";
        $result = mysqli_query($conn, $sql);

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $locations[] = $row;
            }
        }

        return $locations;
    });
}

// Get all departments - with caching
function getAllDepartments($conn) {
    $cache = Cache::getInstance();
    $cacheKey = "all_departments";

    return $cache->remember($cacheKey, 3600, function() use ($conn) {
        $departments = [];
        $sql = "SELECT * FROM departments WHERE status = 'active' ORDER BY name";
        $result = mysqli_query($conn, $sql);

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $departments[] = $row;
            }
        }

        return $departments;
    });
}

// Get all health centers - with caching
function getAllHealthCenters($conn) {
    $cache = Cache::getInstance();
    $cacheKey = "all_health_centers";

    return $cache->remember($cacheKey, 3600, function() use ($conn) {
        $healthCenters = [];
        $sql = "SELECT * FROM health_centers WHERE status = 'active' ORDER BY name";
        $result = mysqli_query($conn, $sql);

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $healthCenters[] = $row;
            }
        }

        return $healthCenters;
    });
}

// Get all suppliers - with caching
function getAllSuppliers($conn) {
    $cache = Cache::getInstance();
    $cacheKey = "all_suppliers";

    return $cache->remember($cacheKey, 3600, function() use ($conn) {
        $suppliers = [];
        $sql = "SELECT * FROM suppliers WHERE status = 'active' ORDER BY name";
        $result = mysqli_query($conn, $sql);

        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $suppliers[] = $row;
            }
        }

        return $suppliers;
    });
}

// Invalidate cache for specific entities when they are modified
function invalidateCache($entity, $id = null) {
    $cache = Cache::getInstance();

    switch ($entity) {
        case 'category':
            if ($id) {
                $cache->delete("category_{$id}");
            }
            $cache->delete("all_categories");
            break;

        case 'sku':
            if ($id) {
                $cache->delete("sku_{$id}");
                $cache->delete("himu_approval_{$id}");
            }
            break;

        case 'location':
            if ($id) {
                $cache->delete("location_{$id}");
            }
            $cache->delete("all_locations");
            break;

        case 'department':
            if ($id) {
                $cache->delete("department_{$id}");
            }
            $cache->delete("all_departments");
            break;

        case 'health_center':
            if ($id) {
                $cache->delete("health_center_{$id}");
            }
            $cache->delete("all_health_centers");
            break;

        case 'supplier':
            if ($id) {
                $cache->delete("supplier_{$id}");
            }
            $cache->delete("all_suppliers");
            break;

        case 'notifications':
            if ($id) {
                $cache->delete("unread_notifications_{$id}");
            }
            break;

        case 'monthly_inventory':
            if ($id) {
                $cache->delete("monthly_inventory_status_{$id}");
            }
            $cache->delete("monthly_inventory_all");
            break;

        case 'all':
            $cache->clear();
            break;
    }
}

/**
 * Check if it's time for monthly inventory update
 *
 * @param bool $forTesting Set to true to always return true (for testing)
 * @return bool True if it's time for monthly update, false otherwise
 */
function isTimeForMonthlyInventoryUpdate($forTesting = false) {
    // For testing purposes, always return true if $forTesting is true
    if ($forTesting) {
        return true;
    }

    // Get current date
    $currentDate = new DateTime();

    // Get the last day of the month
    $lastDay = new DateTime('last day of this month');

    // Calculate the difference in days
    $diff = $currentDate->diff($lastDay);
    $daysUntilMonthEnd = $diff->days;

    // Return true if we're within MONTHLY_UPDATE_DAYS_BEFORE days of month end
    return $daysUntilMonthEnd <= MONTHLY_UPDATE_DAYS_BEFORE;
}

/**
 * Check if a location has completed their monthly inventory update
 *
 * @param int $locationId The location ID to check
 * @param int $month The month to check (1-12), defaults to current month
 * @param int $year The year to check, defaults to current year
 * @return bool True if the location has completed their update, false otherwise
 */
function hasCompletedMonthlyInventoryUpdate($conn, $locationId, $month = null, $year = null) {
    // Use current month/year if not specified
    if ($month === null) {
        $month = date('n'); // 1-12
    }
    if ($year === null) {
        $year = date('Y');
    }

    $cache = Cache::getInstance();
    $cacheKey = "monthly_inventory_status_{$locationId}_{$month}_{$year}";

    return $cache->remember($cacheKey, 300, function() use ($conn, $locationId, $month, $year) {
        $query = "SELECT status FROM monthly_inventory_updates
                 WHERE location_id = ? AND month = ? AND year = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'iii', $locationId, $month, $year);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if ($row = mysqli_fetch_assoc($result)) {
            return $row['status'] === MONTHLY_UPDATE_STATUS_COMPLETED;
        }

        return false;
    });
}

/**
 * Get all locations' monthly inventory update status
 *
 * @param int $month The month to check (1-12), defaults to current month
 * @param int $year The year to check, defaults to current year
 * @return array Array of locations with their update status
 */
function getAllMonthlyInventoryUpdateStatus($conn, $month = null, $year = null) {
    // Use current month/year if not specified
    if ($month === null) {
        $month = date('n'); // 1-12
    }
    if ($year === null) {
        $year = date('Y');
    }

    $cache = Cache::getInstance();
    $cacheKey = "monthly_inventory_all_{$month}_{$year}";

    return $cache->remember($cacheKey, 300, function() use ($conn, $month, $year) {
        $query = "SELECT l.location_id, l.location_name, l.location_type,
                        COALESCE(m.status, 'pending') as status,
                        m.updated_at, m.updated_by, u.full_name as updated_by_name
                 FROM locations l
                 LEFT JOIN monthly_inventory_updates m ON
                    l.location_id = m.location_id AND
                    m.month = ? AND
                    m.year = ?
                 LEFT JOIN users u ON m.updated_by = u.user_id
                 WHERE l.is_active = 1
                 ORDER BY l.location_name";

        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ii', $month, $year);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        $locations = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $locations[] = $row;
        }

        return $locations;
    });
}

// Set a flash message to be displayed on the next page load
function setFlashMessage($type, $message) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

// Display flash message if one exists and then clear it
function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $type = $_SESSION['flash_message']['type'];
        $message = $_SESSION['flash_message']['message'];

        // Map type to Bootstrap alert class
        $alertClass = 'alert-info';
        switch ($type) {
            case 'success':
                $alertClass = 'alert-success';
                $icon = 'fa-check-circle';
                break;
            case 'error':
                $alertClass = 'alert-danger';
                $icon = 'fa-exclamation-circle';
                break;
            case 'warning':
                $alertClass = 'alert-warning';
                $icon = 'fa-exclamation-triangle';
                break;
            case 'info':
                $alertClass = 'alert-info';
                $icon = 'fa-info-circle';
                break;
        }

        echo "<div class='alert $alertClass alert-dismissible fade show' role='alert'>";
        echo "<i class='fas $icon me-2'></i>$message";
        echo "<button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>";
        echo "</div>";

        // Clear the flash message
        unset($_SESSION['flash_message']);
    }
}
?>