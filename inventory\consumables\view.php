<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_INVENTORY])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get consumable ID from URL
$consumableId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$consumableId) {
    setFlashMessage('error', 'Invalid consumable ID');
    header("Location: index.php");
    exit;
}

// Fetch consumable details
try {
    $query = "
        SELECT c.*, i.name as item_name, i.sku, i.specifications, i.unit_cost,
               cat.name as category_name,
               s.name as source_name,
               sup.name as supplier_name
        FROM consumables c
        JOIN items i ON c.item_id = i.item_id
        JOIN categories cat ON i.category_id = cat.category_id
        JOIN sources s ON c.source_id = s.source_id
        JOIN suppliers sup ON c.supplier_id = sup.supplier_id
        WHERE c.consumable_id = ?
    ";
    $stmt = $db->prepare($query);
    $stmt->execute([$consumableId]);
    $consumable = $stmt->fetch();

    if (!$consumable) {
        setFlashMessage('error', 'Consumable not found');
        header("Location: index.php");
        exit;
    }

    // Fetch transaction history
    $query = "
        SELECT t.*, u.full_name as user_name
        FROM transactions t
        JOIN users u ON t.user_id = u.user_id
        WHERE t.item_id = ?
        ORDER BY t.transaction_date DESC
    ";
    $stmt = $db->prepare($query);
    $stmt->execute([$consumable['item_id']]);
    $transactions = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching consumable details: " . $e->getMessage());
    setFlashMessage('error', 'Error loading consumable details');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Consumable Details</h1>
        <div>
            <a href="edit.php?id=<?php echo $consumableId; ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Consumable Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Item Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="35%">SKU:</th>
                                    <td><?php echo htmlspecialchars($consumable['sku']); ?></td>
                                </tr>
                                <tr>
                                    <th>Item Name:</th>
                                    <td><?php echo htmlspecialchars($consumable['item_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Category:</th>
                                    <td><?php echo htmlspecialchars($consumable['category_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Source:</th>
                                    <td><?php echo htmlspecialchars($consumable['source_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Supplier:</th>
                                    <td><?php echo htmlspecialchars($consumable['supplier_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Unit Cost:</th>
                                    <td><?php echo formatCurrency($consumable['unit_cost']); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="35%">Quantity:</th>
                                    <td>
                                        <?php echo number_format($consumable['quantity']); ?> 
                                        <?php echo htmlspecialchars($consumable['unit']); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $consumable['status'] === 'in_stock' ? 'success' : 
                                                ($consumable['status'] === 'low_stock' ? 'warning' : 'danger'); 
                                        ?>">
                                            <?php echo ucwords(str_replace('_', ' ', $consumable['status'])); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Reorder Point:</th>
                                    <td><?php echo number_format($consumable['reorder_point']); ?></td>
                                </tr>
                                <tr>
                                    <th>Batch Number:</th>
                                    <td><?php echo $consumable['batch_number'] ? htmlspecialchars($consumable['batch_number']) : 'N/A'; ?></td>
                                </tr>
                                <tr>
                                    <th>Expiry Date:</th>
                                    <td><?php echo $consumable['expiry_date'] ? formatDate($consumable['expiry_date']) : 'N/A'; ?></td>
                                </tr>
                                <tr>
                                    <th>Storage Location:</th>
                                    <td><?php echo $consumable['storage_location'] ? htmlspecialchars($consumable['storage_location']) : 'N/A'; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if ($consumable['specifications']): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Specifications:</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($consumable['specifications'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($consumable['remarks']): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Remarks:</h6>
                            <p class="text-muted"><?php echo nl2br(htmlspecialchars($consumable['remarks'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="stock-in.php?id=<?php echo $consumableId; ?>" class="btn btn-success w-100 mb-2">
                                <i class="fas fa-plus-circle"></i> Stock In
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="stock-out.php?id=<?php echo $consumableId; ?>" class="btn btn-danger w-100 mb-2">
                                <i class="fas fa-minus-circle"></i> Stock Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction History -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Transaction History</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($transactions)): ?>
                        <p class="text-muted">No transaction history found.</p>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($transactions as $transaction): ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-<?php 
                                        echo $transaction['type'] === 'in' ? 'success' : 'danger'; 
                                    ?>"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-date">
                                            <?php echo formatDate($transaction['transaction_date'], true); ?>
                                        </div>
                                        <h6><?php echo $transaction['type'] === 'in' ? 'Stock In' : 'Stock Out'; ?></h6>
                                        <p>
                                            Quantity: <?php echo number_format($transaction['quantity']); ?> <?php echo htmlspecialchars($consumable['unit']); ?><br>
                                            Reference: <?php echo htmlspecialchars($transaction['reference_number']); ?><br>
                                            By: <?php echo htmlspecialchars($transaction['user_name']); ?>
                                            <?php if ($transaction['remarks']): ?>
                                                <br>Remarks: <?php echo htmlspecialchars($transaction['remarks']); ?>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 15px;
    height: 15px;
    border-radius: 50%;
}

.timeline-content {
    position: relative;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.timeline-date {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 5px;
}
</style>

<?php require_once '../../templates/footer.php'; ?>
