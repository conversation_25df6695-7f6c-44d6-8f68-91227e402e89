/**
 * Mobile Enhancements for CHOIMS
 * This file contains JavaScript functions to improve mobile experience
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth < 768;

    if (isMobile) {
        // Initialize mobile-specific enhancements
        initMobileBottomNav();
        enhanceTablesForMobile();
        initSwipeGestures();
        optimizeDropdowns();
        enhanceTouchFeedback();
        initMobileSidebarToggle(); // Add sidebar toggle initialization
    }

    // Listen for window resize to apply mobile enhancements when needed
    window.addEventListener('resize', function() {
        const wasMobile = isMobile;
        const isMobileNow = window.innerWidth < 768;

        // Only reinitialize if we've crossed the mobile threshold
        if (wasMobile !== isMobileNow) {
            location.reload();
        }
    });
});

/**
 * Create and initialize the mobile bottom navigation
 */
function initMobileBottomNav() {
    // Check if we're logged in (look for sidebar)
    const sidebar = document.querySelector('.sidebar');
    if (!sidebar) return;

    // Create bottom navigation container
    const bottomNav = document.createElement('div');
    bottomNav.className = 'mobile-bottom-nav';

    // Determine current page to highlight active nav item
    const currentPath = window.location.pathname;

    // Define navigation items
    const navItems = [
        { icon: 'fa-home', text: 'Home', url: '/choims/index.php', match: ['/index.php', '/dashboard'] },
        { icon: 'fa-box', text: 'Inventory', url: '/choims/modules/inventory/list.php', match: ['/inventory/'] },
        { icon: 'fa-laptop', text: 'Assets', url: '/choims/modules/assets/list.php', match: ['/assets/'] },
        { icon: 'fa-exchange-alt', text: 'Transfer', url: '/choims/modules/transfers/create.php', match: ['/transfers/'] },
        { icon: 'fa-user', text: 'Profile', url: '/choims/modules/profile/view.php', match: ['/profile/'] }
    ];

    // Create navigation items
    navItems.forEach(item => {
        // Check if this item should be active
        const isActive = item.match.some(path => currentPath.includes(path));

        const navItem = document.createElement('a');
        navItem.className = `mobile-bottom-nav-item ${isActive ? 'active' : ''}`;
        navItem.href = item.url;
        navItem.innerHTML = `
            <i class="fas ${item.icon}"></i>
            <span>${item.text}</span>
        `;

        bottomNav.appendChild(navItem);
    });

    // Add to the document
    document.body.appendChild(bottomNav);
}

/**
 * Enhance tables for mobile viewing
 */
function enhanceTablesForMobile() {
    // Find all DataTables
    const tables = document.querySelectorAll('.dataTable');

    tables.forEach(table => {
        // Check if DataTable is initialized
        if ($.fn.DataTable.isDataTable(table)) {
            const dataTable = $(table).DataTable();

            // Adjust page length for mobile
            dataTable.page.len(10).draw();

            // Add swipe class to rows for gesture support
            table.querySelectorAll('tbody tr').forEach(row => {
                row.classList.add('swipe-item');
                row.classList.add('swipe-container');

                // Get the ID from the row if available
                const id = row.getAttribute('data-id') || '';
                const editUrl = row.getAttribute('data-edit-url') || '#';

                // Create swipe actions
                const swipeActions = document.createElement('div');
                swipeActions.className = 'swipe-actions';

                // Edit action
                const editAction = document.createElement('a');
                editAction.className = 'swipe-action swipe-action-edit';
                editAction.href = editUrl;
                editAction.innerHTML = '<i class="fas fa-edit"></i>';

                // Delete action (if applicable)
                const deleteAction = document.createElement('a');
                deleteAction.className = 'swipe-action swipe-action-delete';
                deleteAction.href = '#';
                deleteAction.setAttribute('data-id', id);
                deleteAction.innerHTML = '<i class="fas fa-trash-alt"></i>';
                deleteAction.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (confirm('Are you sure you want to delete this item?')) {
                        // Handle delete action
                        console.log('Delete item with ID:', id);
                    }
                });

                swipeActions.appendChild(editAction);
                swipeActions.appendChild(deleteAction);

                row.appendChild(swipeActions);
            });
        }

        // Make sure all tables are responsive
        if (!table.parentElement.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });

    // Handle regular tables too
    const regularTables = document.querySelectorAll('table:not(.dataTable)');
    regularTables.forEach(table => {
        if (!table.parentElement.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
}

/**
 * Initialize swipe gestures for mobile
 */
function initSwipeGestures() {
    const swipeItems = document.querySelectorAll('.swipe-item');

    swipeItems.forEach(item => {
        let startX, moveX, startTime;
        const threshold = 100; // Minimum distance to trigger swipe
        const restraint = 100; // Maximum vertical movement allowed
        const allowedTime = 300; // Maximum time allowed for swipe

        // Touch start event
        item.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = new Date().getTime();

            // Reset any existing swipe
            document.querySelectorAll('.swipe-item.swiped').forEach(swiped => {
                if (swiped !== item) {
                    swiped.classList.remove('swiped');
                }
            });
        }, false);

        // Touch move event
        item.addEventListener('touchmove', function(e) {
            moveX = e.touches[0].clientX;
            moveY = e.touches[0].clientY;

            // Calculate horizontal distance
            const distX = startX - moveX;
            const distY = Math.abs(startY - moveY);

            // If horizontal movement is greater than vertical and within restraint
            if (distX > 0 && distY < restraint) {
                e.preventDefault(); // Prevent scrolling
            }
        }, false);

        // Touch end event
        item.addEventListener('touchend', function(e) {
            const distX = startX - moveX;
            const distY = Math.abs(startY - moveY);
            const elapsedTime = new Date().getTime() - startTime;

            // Check if swipe meets criteria
            if (elapsedTime <= allowedTime && Math.abs(distX) >= threshold && distY <= restraint) {
                // Left swipe (show actions)
                if (distX > 0) {
                    item.classList.toggle('swiped');
                }
                // Right swipe (hide actions)
                else {
                    item.classList.remove('swiped');
                }
            }
        }, false);
    });

    // Close swipe actions when clicking elsewhere
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.swipe-item') && !e.target.closest('.swipe-actions')) {
            document.querySelectorAll('.swipe-item.swiped').forEach(item => {
                item.classList.remove('swiped');
            });
        }
    });
}

/**
 * Optimize dropdowns for mobile
 */
function optimizeDropdowns() {
    // Find all dropdowns
    const dropdowns = document.querySelectorAll('.dropdown');

    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        if (!menu) return;

        // Add a close button to the top of each dropdown menu
        const closeButton = document.createElement('button');
        closeButton.className = 'btn btn-sm btn-light w-100 mb-2';
        closeButton.innerHTML = '<i class="fas fa-times"></i> Close';
        closeButton.addEventListener('click', function() {
            menu.classList.remove('show');
            dropdown.classList.remove('show');
        });

        // Add the close button as the first child
        menu.insertBefore(closeButton, menu.firstChild);
    });
}

/**
 * Enhance touch feedback for better mobile experience
 */
function enhanceTouchFeedback() {
    // Add touch feedback to buttons and links
    const touchElements = document.querySelectorAll('button, .btn, .nav-link, .dropdown-item');

    touchElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        });

        element.addEventListener('touchend', function() {
            this.classList.remove('touch-active');
        });

        element.addEventListener('touchcancel', function() {
            this.classList.remove('touch-active');
        });
    });

    // Add CSS for touch feedback
    const style = document.createElement('style');
    style.textContent = `
        .touch-active {
            opacity: 0.7;
            transform: scale(0.98);
        }
    `;
    document.head.appendChild(style);
}

/**
 * Initialize mobile sidebar toggle functionality
 * Note: This is now a supplementary function as the main functionality
 * is handled by sidebar-toggle.js which loads earlier
 */
function initMobileSidebarToggle() {
    const sidebarMobileToggle = document.getElementById('sidebarMobileToggle');
    const sidebar = document.querySelector('.sidebar');

    if (sidebarMobileToggle && sidebar) {
        console.log('Mobile sidebar toggle enhancement initialized');

        // Handle the navbar-toggler if it exists and remove its functionality
        const navbarToggler = document.querySelector('.navbar-toggler');
        if (navbarToggler) {
            // Remove Bootstrap's data attributes to prevent it from controlling the navbar collapse
            navbarToggler.removeAttribute('data-bs-toggle');
            navbarToggler.removeAttribute('data-bs-target');

            // Clone and replace to remove existing event listeners
            const newNavbarToggler = navbarToggler.cloneNode(true);
            navbarToggler.parentNode.replaceChild(newNavbarToggler, navbarToggler);

            // Add our own event listener to control the sidebar
            newNavbarToggler.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('Navbar toggler clicked - controlling sidebar');

                // Toggle sidebar visibility
                sidebar.classList.toggle('show');

                // Always ensure collapsed class is removed on mobile
                if (window.innerWidth <= 767.98) {
                    sidebar.classList.remove('collapsed');
                }

                document.body.classList.toggle('sidebar-open');

                // Prevent scrolling of the body when sidebar is open
                if (sidebar.classList.contains('show')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });
        }

        // Add additional mobile-specific styles
        const additionalStyle = document.createElement('style');
        additionalStyle.textContent = `
            @media (max-width: 767.98px) {
                /* Fix for text display in sidebar */
                .sidebar .nav-link {
                    white-space: normal !important;
                    overflow: visible !important;
                    text-overflow: clip !important;
                    width: 100% !important;
                }

                /* Ensure proper text display in collapsed state */
                .sidebar.collapsed .nav-link span,
                .sidebar.collapsed .app-brand-text,
                .sidebar.collapsed .app-brand-subtext {
                    display: block !important;
                }

                /* Hide navbar collapse on mobile */
                #navbarNav.collapse {
                    display: none !important;
                }

                /* Improve sidebar toggle button */
                #sidebarMobileToggle {
                    width: 44px !important;
                    height: 44px !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-size: 1.25rem !important;
                    border-radius: 8px !important;
                }
            }
        `;
        document.head.appendChild(additionalStyle);
    }
}
