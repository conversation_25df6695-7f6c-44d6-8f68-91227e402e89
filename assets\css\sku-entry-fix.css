/* Additional styles for the SKU entry toggle buttons */
.btn-group {
    margin-bottom: 10px;
}

.btn-outline-primary {
    border-color: var(--primary);
    color: var(--primary);
}

.btn-outline-primary:hover,
.btn-outline-primary:active,
.btn-outline-primary.active {
    background-color: var(--primary);
    color: white;
}

/* Make sure the input field is properly styled */
#sku_code {
    height: calc(3.5rem + 2px);
    padding: 1.25rem 1rem 0.25rem;
    border-radius: var(--radius-md);
    border-color: var(--gray-300);
    background-color: var(--white);
    box-shadow: none;
    transition: all 0.2s ease;
}

#sku_code:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

/* Fix for the form floating label */
.form-floating > #sku_code:focus ~ label,
.form-floating > #sku_code:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
    color: var(--primary);
}

/* Ensure the help text is visible */
#sku_help_text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-500);
}

/* Add a highlight to the "Enter New SKU" button */
#enter_new_sku {
    position: relative;
    overflow: hidden;
}

#enter_new_sku::after {
    content: '';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background-color: var(--primary);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
}

#enter_new_sku:hover::after {
    opacity: 0.2;
    transform: scale(5);
}

/* Fix alignment issues in Select2 dropdown */
.select2-container--classic {
    width: 100% !important;
}

.select2-container--classic .select2-selection--single {
    height: calc(3.5rem + 2px) !important;
    padding: 1.25rem 1rem 0.25rem !important;
    border-radius: var(--radius-md) !important;
    border-color: var(--gray-300) !important;
    background: #fff !important;
    background-image: none !important;
}

.select2-container--classic .select2-selection--single .select2-selection__rendered {
    color: #333 !important;
    line-height: 1.5 !important;
    padding-left: 0 !important;
    padding-right: 30px !important; /* Make room for the arrow */
    margin-top: 0.5rem !important;
}

.select2-container--classic .select2-selection--single .select2-selection__arrow {
    height: 100% !important;
    width: 30px !important;
    top: 0 !important;
    right: 0 !important;
    border: none !important;
    background: transparent !important;
    background-image: none !important;
}

.select2-container--classic .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 5px 4px 0 4px !important;
    height: 0 !important;
    width: 0 !important;
    margin-left: -4px !important;
    margin-top: -2px !important;
    top: 50% !important;
    left: 50% !important;
}

/* Fix dropdown appearance */
.select2-container--classic .select2-dropdown {
    border-color: var(--primary-light) !important;
    border-radius: 0 0 var(--radius-md) var(--radius-md) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Focus state */
.select2-container--classic.select2-container--open .select2-selection--single {
    border-color: var(--primary-light) !important;
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25) !important;
}

/* Fix the width of the dropdown field to match the parent */
.sku-selection-container {
    position: relative;
    width: 100%;
}

/* Fix issues in the floating label form-control */
.form-floating > .select2-container {
    padding-top: 1.625rem;
}

.form-floating > label {
    z-index: 999;
}
