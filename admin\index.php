<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

// Check if user has admin permissions
$auth = Auth::getInstance();
if (!$auth->hasRole(ROLE_SUPERADMIN) && !$auth->hasRole(ROLE_GODMODE)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Include header
require_once '../templates/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">Administration Dashboard</h1>
            <p class="text-muted">Manage system settings, users, departments, and health centers.</p>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-primary">User Management</div>
                            <p class="mt-2 mb-0">Add, edit, and manage user accounts and their access roles.</p>
                            <div class="mt-3">
                                <a href="users/manage.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-users fa-fw"></i> Manage Users
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-success">Department Management</div>
                            <p class="mt-2 mb-0">Create and manage hospital departments that use the inventory system.</p>
                            <div class="mt-3">
                                <a href="departments/manage.php" class="btn btn-success btn-sm">
                                    <i class="fas fa-hospital fa-fw"></i> Manage Departments
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-hospital fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="h5 mb-0 font-weight-bold text-info">Health Center Management</div>
                            <p class="mt-2 mb-0">Manage external health centers that receive supplies from the hospital.</p>
                            <div class="mt-3">
                                <a href="health-centers/manage.php" class="btn btn-info btn-sm">
                                    <i class="fas fa-clinic-medical fa-fw"></i> Manage Health Centers
                                </a>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clinic-medical fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if ($auth->hasRole(ROLE_GODMODE)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">System Administration</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card border-left-danger">
                                <div class="card-body">
                                    <h5 class="card-title">System Logs</h5>
                                    <p class="card-text">View system activity logs for auditing purposes.</p>
                                    <a href="#" class="btn btn-danger btn-sm">
                                        <i class="fas fa-list fa-fw"></i> View Logs
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card border-left-warning">
                                <div class="card-body">
                                    <h5 class="card-title">Backup Database</h5>
                                    <p class="card-text">Create a backup of the system database.</p>
                                    <a href="#" class="btn btn-warning btn-sm">
                                        <i class="fas fa-database fa-fw"></i> Create Backup
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card border-left-dark">
                                <div class="card-body">
                                    <h5 class="card-title">System Settings</h5>
                                    <p class="card-text">Configure global system settings and parameters.</p>
                                    <a href="#" class="btn btn-dark btn-sm">
                                        <i class="fas fa-cogs fa-fw"></i> Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Links</h6>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="<?php echo BASE_URL; ?>/dashboard.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt fa-fw"></i> Main Dashboard
                        </a>
                        <a href="<?php echo BASE_URL; ?>/inventory/manage.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-boxes fa-fw"></i> Inventory Management
                        </a>
                        <a href="<?php echo BASE_URL; ?>/reports/index.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar fa-fw"></i> Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="row no-gutters">
                        <div class="col-sm-6 mb-2">
                            <div class="font-weight-bold">PHP Version:</div>
                            <div><?php echo phpversion(); ?></div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="font-weight-bold">Server:</div>
                            <div><?php echo $_SERVER['SERVER_SOFTWARE']; ?></div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="font-weight-bold">Database:</div>
                            <div>MySQL</div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="font-weight-bold">Current User:</div>
                            <div><?php echo htmlspecialchars($auth->getCurrentUser()['full_name']); ?></div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="font-weight-bold">Role:</div>
                            <div>
                                <span class="badge bg-<?php echo getRoleBadgeColor($auth->getCurrentUser()['role']); ?>">
                                    <?php echo getRoleName($auth->getCurrentUser()['role']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-sm-6 mb-2">
                            <div class="font-weight-bold">Last Login:</div>
                            <div><?php echo isset($auth->getCurrentUser()['last_login']) ? formatDateTime($auth->getCurrentUser()['last_login']) : 'N/A'; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Helper functions if not already in functions.php
function getRoleBadgeColor($role) {
    switch ($role) {
        case ROLE_GODMODE:
            return 'danger';
        case ROLE_SUPERADMIN:
            return 'primary';
        case ROLE_LOGISTICS:
            return 'warning';
        case ROLE_HIMU:
            return 'info';
        case ROLE_DEPARTMENT:
            return 'success';
        case ROLE_HEALTH_CENTER:
            return 'secondary';
        default:
            return 'light';
    }
}

function getRoleName($role) {
    switch ($role) {
        case ROLE_GODMODE:
            return 'Godmode';
        case ROLE_SUPERADMIN:
            return 'Superadmin';
        case ROLE_LOGISTICS:
            return 'Logistics';
        case ROLE_HIMU:
            return 'HIMU';
        case ROLE_DEPARTMENT:
            return 'Department';
        case ROLE_HEALTH_CENTER:
            return 'Health Center';
        default:
            return 'Unknown';
    }
}

require_once '../templates/footer.php';
?> 