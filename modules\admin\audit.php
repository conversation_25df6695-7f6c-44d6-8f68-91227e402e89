<?php
session_start();

// Check if user is logged in and has superadmin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || ($_SESSION['role'] !== 'superadmin' && $_SESSION['role'] !== 'godmode')) {
    $_SESSION['error'] = "You don't have permission to access this page.";
    header("Location: ../../index.php");
    exit();
}

// Use the standard approach for includes
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireLogin();

// Log activity
logActivity($_SESSION['user_id'], 'Accessed Audit Logs Page');

// Default values
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$user_id = isset($_GET['user_id']) ? $_GET['user_id'] : '';
$activity_type = isset($_GET['activity_type']) ? $_GET['activity_type'] : '';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;

// Build the query
$sql = "SELECT al.*, u.username 
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.user_id
        WHERE al.timestamp BETWEEN ? AND ?";
$params = [$start_date . ' 00:00:00', $end_date . ' 23:59:59'];
$types = 'ss';

if (!empty($user_id)) {
    $sql .= " AND al.user_id = ?";
    $params[] = $user_id;
    $types .= 'i';
}

if (!empty($activity_type)) {
    $sql .= " AND al.activity LIKE ?";
    $params[] = '%' . $activity_type . '%';
    $types .= 's';
}

$sql .= " ORDER BY al.timestamp DESC LIMIT ?";
$params[] = $limit;
$types .= 'i';

// Prepare and execute the query
$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$logs = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $logs[] = $row;
    }
}
$stmt->close();

// Get all users for the filter dropdown
$users_sql = "SELECT user_id, username FROM users ORDER BY username";
$users_result = $conn->query($users_sql);
$users = [];
if ($users_result && $users_result->num_rows > 0) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
}
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>Audit Logs</h5>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form method="get" action="<?php echo $_SERVER['PHP_SELF']; ?>" class="mb-4">
                        <div class="row g-3 align-items-end">
                            <div class="col-md-2">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="user_id" class="form-label">User</label>
                                <select class="form-select" id="user_id" name="user_id">
                                    <option value="">All Users</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['user_id']; ?>" <?php echo ($user_id == $user['user_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['username']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="activity_type" class="form-label">Activity Type</label>
                                <input type="text" class="form-control" id="activity_type" name="activity_type" value="<?php echo htmlspecialchars($activity_type); ?>" placeholder="Enter keyword">
                            </div>
                            <div class="col-md-2">
                                <label for="limit" class="form-label">Limit Results</label>
                                <select class="form-select" id="limit" name="limit">
                                    <option value="100" <?php echo ($limit == 100) ? 'selected' : ''; ?>>100 records</option>
                                    <option value="250" <?php echo ($limit == 250) ? 'selected' : ''; ?>>250 records</option>
                                    <option value="500" <?php echo ($limit == 500) ? 'selected' : ''; ?>>500 records</option>
                                    <option value="1000" <?php echo ($limit == 1000) ? 'selected' : ''; ?>>1000 records</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter me-1"></i> Filter
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Results count and export button -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <p class="mb-0">Showing <?php echo count($logs); ?> audit log entries</p>
                        <button id="exportBtn" class="btn btn-success">
                            <i class="fas fa-file-excel me-1"></i> Export to Excel
                        </button>
                    </div>

                    <!-- Logs Table -->
                    <div class="table-responsive">
                        <table id="auditLogsTable" class="table table-bordered table-striped">
                            <thead class="bg-light">
                                <tr>
                                    <th>Log ID</th>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>IP Address</th>
                                    <th>Activity</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($logs)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">No audit logs found for the selected criteria.</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td><?php echo $log['log_id']; ?></td>
                                        <td><?php echo date('M d, Y h:i:s A', strtotime($log['timestamp'])); ?></td>
                                        <td>
                                            <?php 
                                            if ($log['username']) {
                                                echo htmlspecialchars($log['username']);
                                            } else {
                                                echo '<span class="text-muted">User ID: ' . $log['user_id'] . '</span>';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($log['ip_address']); ?></td>
                                        <td><?php echo htmlspecialchars($log['activity']); ?></td>
                                        <td>
                                            <?php if (!empty($log['details'])): ?>
                                                <button class="btn btn-sm btn-info show-details" data-bs-toggle="modal" data-bs-target="#detailsModal" data-details="<?php echo htmlspecialchars($log['details']); ?>">
                                                    <i class="fas fa-info-circle"></i> View Details
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">No details</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="detailsModalLabel"><i class="fas fa-info-circle me-2"></i>Log Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre id="detailsContent" class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    const table = $('#auditLogsTable').DataTable({
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: 'Bfrtip',
        buttons: [
            'copy', 'excel', 'pdf'
        ]
    });
    
    // Handle log details display
    const detailsButtons = document.querySelectorAll('.show-details');
    detailsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const details = this.getAttribute('data-details');
            // Try to parse JSON and format it, otherwise display as is
            try {
                const parsedDetails = JSON.parse(details);
                document.getElementById('detailsContent').textContent = JSON.stringify(parsedDetails, null, 2);
            } catch (e) {
                document.getElementById('detailsContent').textContent = details;
            }
        });
    });
    
    // Export to Excel
    document.getElementById('exportBtn').addEventListener('click', function() {
        const exportData = [];
        const headers = ['Log ID', 'Timestamp', 'User', 'IP Address', 'Activity', 'Details'];
        
        exportData.push(headers);
        
        <?php foreach ($logs as $log): ?>
        <?php
            // Prepare the user display text to avoid syntax errors
            $userDisplayText = !empty($log['username']) ? $log['username'] : 'User ID: ' . $log['user_id'];
            $detailsText = !empty($log['details']) ? $log['details'] : '';
        ?>
        exportData.push([
            <?php echo $log['log_id']; ?>,
            '<?php echo date('Y-m-d H:i:s', strtotime($log['timestamp'])); ?>',
            '<?php echo addslashes($userDisplayText); ?>',
            '<?php echo addslashes($log['ip_address']); ?>',
            '<?php echo addslashes($log['activity']); ?>',
            '<?php echo addslashes($detailsText); ?>'
        ]);
        <?php endforeach; ?>
        
        // Create a workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(exportData);
        
        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Audit Logs');
        
        // Generate an Excel file
        XLSX.writeFile(wb, 'audit_logs_<?php echo date('Y-m-d'); ?>.xlsx');
    });
});
</script>

<!-- Include SheetJS for Excel export -->
<script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

<?php
// Include footer
include_once '../../includes/footer.php';
?> 