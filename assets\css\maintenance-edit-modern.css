/* Maintenance Edit Page - Modern Design */

:root {
  /* Modern Color Palette - Matching maintenance-modern.css */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --secondary: #1B5E20;
  --text-on-primary: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-accent: #f5f5f5;
  --border-color: #f0f0f0;
  --success: #4CAF50;
  --warning: #ff9800;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #E8F5E9;
  --dark: #1B5E20;

  /* Background Colors */
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --bg-pattern: #f9f9f9;

  /* Shadows & Effects */
  --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 12px 28px rgba(0, 0, 0, 0.15);
  --transition: all 0.2s ease;

  /* Border Radius */
  --radius-sm: 12px;
  --radius: 16px;
  --radius-lg: 20px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
}

/* Base styles and typography */
body {
  background-color: var(--bg-light);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: 0 1.5rem;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

/* Edit Maintenance header section */
.edit-header {
  position: relative;
  background: #ffffff;
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 1.5rem 2rem 1rem;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  color: var(--text-primary);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.edit-header .title-container {
  display: flex;
  align-items: center;
}

.edit-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.025em;
  white-space: nowrap;
}

.edit-title-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-light);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
}

.edit-subtitle {
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

/* Card styling */
.edit-card {
  background: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: var(--transition);
}

.edit-card-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
}

.edit-card-title i {
  margin-right: 0.75rem;
  color: var(--primary);
}

.edit-card-body {
  padding: 1.5rem;
}

/* Asset info section */
.asset-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.info-item {
  margin-bottom: 1rem;
}

.info-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.info-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* Form styling */
.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-white);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-white);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  appearance: none;
}

.form-select:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.75em;
  margin-bottom: 0.125rem;
}

.form-check-input {
  width: 1.25em;
  height: 1.25em;
  margin-top: 0.125em;
  margin-left: -1.75em;
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: 0.25em;
  appearance: none;
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.25);
}

.form-check-label {
  color: var(--text-primary);
}

/* Button styling */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease-in-out;
  box-shadow: var(--shadow-sm);
}

.action-btn i {
  margin-right: 0.5rem;
}

.action-primary {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.action-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.action-secondary {
  color: var(--text-primary);
  background-color: var(--bg-white);
  border-color: var(--border-color);
}

.action-secondary:hover {
  background-color: var(--bg-accent);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

/* Required field indicator */
.required {
  color: var(--danger);
  margin-left: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .edit-header {
    padding: 1.25rem;
  }

  .edit-title {
    font-size: 1.5rem;
  }

  .edit-title-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }
}

@media (max-width: 768px) {
  .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .asset-info-grid,
  .form-row {
    grid-template-columns: 1fr;
  }
}
