<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get asset ID from URL
$assetId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$assetId) {
    setFlashMessage('error', 'Invalid asset ID');
    header("Location: index.php");
    exit;
}

// Verify the asset exists and get its details
try {
    $stmt = $db->prepare("
        SELECT fa.*, i.name as item_name
        FROM fixed_assets fa
        JOIN items i ON fa.item_id = i.item_id
        WHERE fa.asset_id = ?
    ");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();

    if (!$asset) {
        setFlashMessage('error', 'Asset not found');
        header("Location: index.php");
        exit;
    }

    // Check if the asset has any dependencies (e.g., transfer history)
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM asset_transfers WHERE asset_id = ?");
    $stmt->execute([$assetId]);
    $transferCount = $stmt->fetch()['count'];

    if ($transferCount > 0) {
        setFlashMessage('error', 'Cannot delete asset. It has transfer history records.');
        header("Location: view.php?id=" . $assetId);
        exit;
    }

    // Begin transaction
    $db->beginTransaction();

    // Delete the fixed asset record
    $stmt = $db->prepare("DELETE FROM fixed_assets WHERE asset_id = ?");
    $stmt->execute([$assetId]);

    // Delete the item record
    $stmt = $db->prepare("DELETE FROM items WHERE item_id = ?");
    $stmt->execute([$asset['item_id']]);

    // Log the activity
    $auth->logActivity($auth->getCurrentUser()['user_id'], 'delete', 'fixed_assets', $assetId);

    $db->commit();
    setFlashMessage('success', 'Fixed asset deleted successfully');
    header("Location: index.php");
    exit;

} catch (PDOException $e) {
    $db->rollBack();
    error_log("Error deleting fixed asset: " . $e->getMessage());
    setFlashMessage('error', 'Error deleting fixed asset. Please try again.');
    header("Location: view.php?id=" . $assetId);
    exit;
}
