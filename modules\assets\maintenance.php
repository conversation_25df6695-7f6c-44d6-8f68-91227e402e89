<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
// Only GodMode, Superadmin, Logistics, and HIMU can access maintenance records
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics') && !hasRole('HIMU')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to view maintenance records.');

    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Add custom CSS for modern maintenance page
echo '<link rel="stylesheet" href="/choims/assets/css/maintenance-modern.css">';
echo '<link rel="stylesheet" href="/choims/assets/css/custom-modal.css">';
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';
?>
<?php

// Initialize filters
$location_id = isset($_GET['location_id']) ? sanitizeInput($_GET['location_id']) : '';
$status = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$maintenance_type = isset($_GET['maintenance_type']) ? sanitizeInput($_GET['maintenance_type']) : '';
$asset_id = isset($_GET['asset_id']) ? sanitizeInput($_GET['asset_id']) : '';
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$date_from = isset($_GET['date_from']) ? sanitizeInput($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitizeInput($_GET['date_to']) : '';

// Get locations for dropdown
$locationsQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);

// Build the query for maintenance records with filters
$query = "
    SELECT mr.*, fa.asset_name, fa.serial_number, sm.sku_name,
           l.location_name, u.username as technician_name, c.category_name
    FROM maintenance_records mr
    JOIN fixed_assets fa ON mr.asset_id = fa.asset_id
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    LEFT JOIN users u ON mr.technician_id = u.user_id
    WHERE 1=1
";

// For HIMU users, only show IT equipment maintenance records
if (hasRole('HIMU') && !hasRole('GodMode') && !hasRole('Superadmin')) {
    $query .= " AND c.category_id = 1"; // Only IT Equipment (category_id = 1)
}

$params = [];
$types = '';

if (!empty($location_id)) {
    $query .= " AND fa.current_location_id = ?";
    $params[] = $location_id;
    $types .= 'i';
}

if (!empty($status)) {
    $query .= " AND fa.status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($maintenance_type)) {
    $query .= " AND mr.maintenance_type = ?";
    $params[] = $maintenance_type;
    $types .= 's';
}

if (!empty($asset_id)) {
    $query .= " AND mr.asset_id = ?";
    $params[] = $asset_id;
    $types .= 'i';
}

if (!empty($search)) {
    $query .= " AND (fa.asset_name LIKE ? OR fa.serial_number LIKE ? OR sm.sku_name LIKE ? OR mr.description LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= 'ssss';
}

if (!empty($date_from)) {
    $query .= " AND mr.maintenance_date >= ?";
    $params[] = $date_from;
    $types .= 's';
}

if (!empty($date_to)) {
    $query .= " AND mr.maintenance_date <= ?";
    $params[] = $date_to;
    $types .= 's';
}

$query .= " ORDER BY mr.maintenance_date DESC";

// Execute query with error handling
try {
    $stmt = mysqli_prepare($conn, $query);
    if (!$stmt) {
        throw new Exception("Query preparation failed: " . mysqli_error($conn));
    }

    if (!empty($params)) {
        // Use call_user_func_array instead of spread operator for PHP compatibility
        $bindParams = array_merge(array($stmt, $types), $params);
        $bindResult = call_user_func_array('mysqli_stmt_bind_param', $bindParams);
        if (!$bindResult) {
            throw new Exception("Parameter binding failed: " . mysqli_stmt_error($stmt));
        }
    }

    $executeResult = mysqli_stmt_execute($stmt);
    if (!$executeResult) {
        throw new Exception("Statement execution failed: " . mysqli_stmt_error($stmt));
    }

    $result = mysqli_stmt_get_result($stmt);
} catch (Exception $e) {
    echo '<div class="alert alert-danger">' . $e->getMessage() . '</div>';
    // Create an empty result set to avoid further errors
    $result = false;
}

// Get assets for the add maintenance modal
$assetsQuery = "
    SELECT fa.asset_id, fa.asset_name, fa.serial_number, sm.sku_name, c.category_name
    FROM fixed_assets fa
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    WHERE fa.is_active = 1
";

// For HIMU users, only show IT equipment
if (hasRole('HIMU') && !hasRole('GodMode') && !hasRole('Superadmin')) {
    $assetsQuery .= " AND c.category_id = 1"; // Only IT Equipment (category_id = 1)
}

$assetsQuery .= " ORDER BY fa.asset_name";
$assetsResult = mysqli_query($conn, $assetsQuery);

// Get defective assets for HIMU users (IT equipment only)
$defectiveAssetsQuery = "
    SELECT fa.asset_id, fa.asset_name, fa.serial_number, fa.model, fa.status,
           sm.sku_name, c.category_name, l.location_name
    FROM fixed_assets fa
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    WHERE fa.status = 'Defective'
    AND c.category_id = 1 -- Only IT Equipment (category_id = 1)
";

// Add location filter if set
if (!empty($location_id)) {
    $defectiveAssetsQuery .= " AND fa.current_location_id = $location_id";
}

// Add search filter if set
if (!empty($search)) {
    $defectiveAssetsQuery .= " AND (fa.asset_name LIKE '%$search%' OR fa.serial_number LIKE '%$search%' OR sm.sku_name LIKE '%$search%')";
}

$defectiveAssetsQuery .= " ORDER BY fa.asset_id DESC";
$defectiveAssetsResult = mysqli_query($conn, $defectiveAssetsQuery);

// Get current user ID from session
$currentUserId = $_SESSION['user_id'];

// Get technicians (users) for the add maintenance modal
$techniciansQuery = "
    SELECT user_id, username, full_name
    FROM users
    WHERE role IN ('admin', 'HIMU', 'Logistics')
    ORDER BY username
";
$techniciansResult = mysqli_query($conn, $techniciansQuery);

// Get common maintenance types
$maintenanceTypes = [
    'Preventive Maintenance',
    'Corrective Maintenance',
    'Repair',
    'Upgrade',
    'Inspection',
    'Calibration',
    'Software Update',
    'Cleaning',
    'Parts Replacement',
    'Other'
];
?>

<div class="container-fluid">
    <!-- Modern Header Section -->
    <div class="maintenance-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <div class="maintenance-title-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div>
                    <h1 class="maintenance-title">IT Equipment Maintenance</h1>
                    <p class="maintenance-subtitle mt-1">
                        <i class="fas fa-calendar-check me-2"></i> Manage maintenance records and track IT equipment repairs
                    </p>
                </div>
            </div>
            <div class="header-actions">
                <!-- Add Maintenance Record button removed -->
            </div>
        </div>
        <div class="mt-2">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/choims/dashboards/logistics.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/choims/modules/assets/list.php">Assets</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Maintenance</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="filter-card animate__animated animate__fadeInUp animate__delay-1">
        <div class="filter-card-header">
            <div class="filter-title">
                <i class="fas fa-filter"></i> Filter Maintenance Records
                <?php if (!empty($location_id) || !empty($status) || !empty($maintenance_type) || !empty($search) || !empty($date_from) || !empty($date_to)): ?>
                <span class="filter-badge">Filters Applied</span>
                <?php endif; ?>
            </div>
            <div>
                <button type="button" class="action-btn" id="toggleFilters">
                    <i class="fas fa-sliders-h"></i> Adjust Filters
                </button>
            </div>
        </div>
        <div class="filter-card-body" id="filterBody">
            <form method="GET" action="" class="filter-form">
                <div class="filter-group">
                    <label class="filter-label" for="location_id">Location</label>
                    <div class="input-group-icon">
                        <i class="fas fa-map-marker-alt"></i>
                        <select id="location_id" name="location_id" class="filter-control">
                            <option value="">All Locations</option>
                            <?php mysqli_data_seek($locationsResult, 0); ?>
                            <?php while ($location = mysqli_fetch_assoc($locationsResult)): ?>
                                <option value="<?php echo $location['location_id']; ?>" <?php if ($location_id == $location['location_id']) echo 'selected'; ?>>
                                    <?php echo $location['location_name']; ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
                <div class="filter-group">
                    <label class="filter-label" for="status">Asset Status</label>
                    <div class="input-group-icon">
                        <i class="fas fa-tag"></i>
                        <select id="status" name="status" class="filter-control">
                            <option value="">All Statuses</option>
                            <option value="Available" <?php if ($status == 'Available') echo 'selected'; ?>>Available</option>
                            <option value="In Use" <?php if ($status == 'In Use') echo 'selected'; ?>>In Use</option>
                            <option value="Under Repair" <?php if ($status == 'Under Repair') echo 'selected'; ?>>Under Repair</option>
                            <option value="Disposed" <?php if ($status == 'Disposed') echo 'selected'; ?>>Disposed</option>
                            <option value="Lost" <?php if ($status == 'Lost') echo 'selected'; ?>>Lost</option>
                        </select>
                    </div>
                </div>
                <div class="filter-group">
                    <label class="filter-label" for="maintenance_type">Maintenance Type</label>
                    <div class="input-group-icon">
                        <i class="fas fa-tools"></i>
                        <select id="maintenance_type" name="maintenance_type" class="filter-control">
                            <option value="">All Types</option>
                            <?php foreach ($maintenanceTypes as $type): ?>
                                <option value="<?php echo $type; ?>" <?php if ($maintenance_type == $type) echo 'selected'; ?>>
                                    <?php echo $type; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="filter-group">
                    <label class="filter-label" for="search">Search</label>
                    <div class="input-group-icon">
                        <i class="fas fa-search"></i>
                        <input type="text" id="search" name="search" class="filter-control" placeholder="Search assets, serial numbers, etc." value="<?php echo $search; ?>">
                    </div>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Date Range</label>
                    <div class="date-range">
                        <div class="input-group-icon">
                            <i class="fas fa-calendar-alt"></i>
                            <input type="date" id="date_from" name="date_from" class="filter-control" value="<?php echo $date_from; ?>" placeholder="From Date">
                        </div>
                        <span class="date-separator">to</span>
                        <div class="input-group-icon">
                            <i class="fas fa-calendar-alt"></i>
                            <input type="date" id="date_to" name="date_to" class="filter-control" value="<?php echo $date_to; ?>" placeholder="To Date">
                        </div>
                    </div>
                </div>
                <div class="filter-actions">
                    <button type="submit" class="filter-btn filter-btn-primary">
                        <i class="fas fa-filter me-2"></i> Apply Filters
                    </button>
                    <a href="maintenance.php" class="filter-btn filter-btn-secondary">
                        <i class="fas fa-undo me-2"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <?php if (hasRole('HIMU')): ?>
    <!-- Defective Assets Card (Only visible to HIMU users) -->
    <div class="defective-card animate__animated animate__fadeInUp animate__delay-2">
        <div class="defective-card-header">
            <div class="defective-title">
                <i class="fas fa-exclamation-triangle"></i> Defective IT Equipment
            </div>
            <div class="defective-subtitle">
                These IT assets need maintenance or repair
            </div>
        </div>
        <div class="defective-card-body">
            <div class="defective-search">
                <i class="fas fa-search"></i>
                <input type="text" id="defectiveSearch" placeholder="Search defective IT equipment...">
            </div>
            <div class="table-responsive">
                <table class="modern-table" id="defectiveAssetsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Asset</th>
                            <th>Category</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($defectiveAssetsResult) > 0): ?>
                            <?php while ($asset = mysqli_fetch_assoc($defectiveAssetsResult)): ?>
                                <tr>
                                    <td><?php echo $asset['asset_id']; ?></td>
                                    <td>
                                        <a href="/choims/modules/assets/view.php?id=<?php echo $asset['asset_id']; ?>" style="text-decoration: none; font-weight: 600; color: var(--primary);">
                                            <?php echo $asset['asset_name']; ?>
                                        </a>
                                        <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                            <?php echo $asset['sku_name']; ?>
                                            <?php if (!empty($asset['serial_number'])): ?>
                                                <span style="color: var(--text-secondary);">(SN: <?php echo $asset['serial_number']; ?>)</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?php echo $asset['category_name']; ?></td>
                                    <td><?php echo $asset['location_name']; ?></td>
                                    <td>
                                        <span class="status-badge status-defective">
                                            <?php echo $asset['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="action-btn custom-modal-trigger" data-modal-id="markForRepairModal<?php echo $asset['asset_id']; ?>">
                                            <i class="fas fa-tools"></i> Mark for Repair
                                        </button>
                                    </td>
                                </tr>

                                <!-- Mark for Repair Modal -->
                                <div class="modal fade repair-modal" id="markForRepairModal<?php echo $asset['asset_id']; ?>" data-asset-id="<?php echo $asset['asset_id']; ?>" style="display: none;">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="markForRepairModalLabel<?php echo $asset['asset_id']; ?>">Mark Asset for Repair</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form action="/choims/modules/maintenance/add.php" method="post" id="markForRepairForm<?php echo $asset['asset_id']; ?>">
                                                    <input type="hidden" name="asset_id" value="<?php echo $asset['asset_id']; ?>">
                                                    <input type="hidden" name="update_asset_status" value="1">
                                                    <input type="hidden" name="status" value="In Progress">

                                                    <div class="alert alert-info">
                                                        <i class="fas fa-info-circle"></i> You are about to mark this defective asset as "Under Repair" and create a maintenance record.
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <p><strong>Asset:</strong> <?php echo $asset['asset_name']; ?></p>
                                                            <p><strong>Serial Number:</strong> <?php echo $asset['serial_number'] ?: 'N/A'; ?></p>
                                                            <p><strong>Category:</strong> <?php echo $asset['category_name']; ?></p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p><strong>Current Status:</strong> <span class="badge bg-danger"><?php echo $asset['status']; ?></span></p>
                                                            <p><strong>New Status:</strong> <span class="badge bg-warning">Under Repair</span></p>
                                                            <p><strong>Location:</strong> <?php echo $asset['location_name']; ?></p>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-3">
                                                        <div class="col-md-6 mb-3">
                                                            <label for="maintenance_date<?php echo $asset['asset_id']; ?>" class="form-label">Maintenance Date *</label>
                                                            <input type="date" class="form-control" id="maintenance_date<?php echo $asset['asset_id']; ?>" name="maintenance_date" required value="<?php echo date('Y-m-d'); ?>">
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label for="maintenance_type<?php echo $asset['asset_id']; ?>" class="form-label">Maintenance Type *</label>
                                                            <select class="form-select" id="maintenance_type<?php echo $asset['asset_id']; ?>" name="maintenance_type" required onchange="handleMaintenanceTypeChange(this, <?php echo $asset['asset_id']; ?>)">
                                                                <option value="Repair" selected>Repair</option>
                                                                <option value="Corrective Maintenance">Corrective Maintenance</option>
                                                                <option value="Parts Replacement">Parts Replacement</option>
                                                                <option value="Other">Other</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <!-- Parts Replacement Section - Initially Hidden -->
                                                    <div id="partsReplacementSection<?php echo $asset['asset_id']; ?>" class="parts-replacement-section" style="display: none;">
                                                        <div class="custom-alert custom-alert-info">
                                                            <i class="fas fa-info-circle"></i> Select IT parts to use for this repair. The selected parts will be deducted from inventory.
                                                        </div>

                                                        <div class="row">
                                                            <div class="col-md-6 mb-3">
                                                                <label for="parts_inventory_id<?php echo $asset['asset_id']; ?>" class="form-label">Select Part *</label>
                                                                <select class="form-select" id="parts_inventory_id<?php echo $asset['asset_id']; ?>" name="parts_inventory_id">
                                                                    <option value="">Select IT Part</option>
                                                                    <?php
                                                                    // Get IT parts from both fixed assets and consumable inventory
                                                                    // Get current user's location
                                                                    $userLocationQuery = "SELECT location_id FROM users WHERE user_id = ?";
                                                                    $userLocationStmt = mysqli_prepare($conn, $userLocationQuery);
                                                                    mysqli_stmt_bind_param($userLocationStmt, 'i', $currentUserId);
                                                                    mysqli_stmt_execute($userLocationStmt);
                                                                    $userLocationResult = mysqli_stmt_get_result($userLocationStmt);
                                                                    $userLocation = mysqli_fetch_assoc($userLocationResult);
                                                                    $userLocationId = $userLocation ? $userLocation['location_id'] : 0;

                                                                    $partsQuery = "
                                                                        SELECT
                                                                            CONCAT('FA-', fa.asset_id) as inventory_id,
                                                                            CONCAT(fa.asset_name, ' (Fixed Asset)') as sku_name,
                                                                            1 as current_quantity,
                                                                            l.location_name
                                                                        FROM fixed_assets fa
                                                                        JOIN sku_master s ON fa.sku_id = s.sku_id
                                                                        JOIN categories c ON s.category_id = c.category_id
                                                                        JOIN locations l ON fa.current_location_id = l.location_id
                                                                        WHERE c.requires_himu_approval = 1
                                                                        AND fa.status = 'Available'
                                                                        AND (fa.is_deleted = 0 OR fa.is_deleted IS NULL)
                                                                        AND l.location_id = (
                                                                            SELECT current_location_id
                                                                            FROM fixed_assets
                                                                            WHERE asset_id = ?
                                                                        )

                                                                        UNION

                                                                        SELECT
                                                                            CONCAT('CI-', ci.inventory_id) as inventory_id,
                                                                            CONCAT(s.sku_name, ' (Consumable)') as sku_name,
                                                                            ci.current_quantity,
                                                                            l.location_name
                                                                        FROM consumable_inventory ci
                                                                        JOIN sku_master s ON ci.sku_id = s.sku_id
                                                                        JOIN locations l ON ci.location_id = l.location_id
                                                                        JOIN categories c ON s.category_id = c.category_id
                                                                        WHERE c.category_id = 4
                                                                        AND ci.current_quantity > 0
                                                                        AND (ci.is_deleted = 0 OR ci.is_deleted IS NULL)
                                                                        AND l.location_id = (
                                                                            SELECT current_location_id
                                                                            FROM fixed_assets
                                                                            WHERE asset_id = ?
                                                                        )

                                                                        ORDER BY sku_name, location_name
                                                                    ";
                                                                    $partsStmt = mysqli_prepare($conn, $partsQuery);
                                                                    mysqli_stmt_bind_param($partsStmt, 'ii', $asset['asset_id'], $asset['asset_id']);
                                                                    mysqli_stmt_execute($partsStmt);
                                                                    $partsResult = mysqli_stmt_get_result($partsStmt);

                                                                    if (mysqli_num_rows($partsResult) > 0) {
                                                                        while ($part = mysqli_fetch_assoc($partsResult)) {
                                                                            echo '<option value="' . $part['inventory_id'] . '">' .
                                                                                $part['sku_name'] . ' - ' . $part['location_name'] .
                                                                                ' (Available: ' . $part['current_quantity'] . ')</option>';
                                                                        }
                                                                    }
                                                                    ?>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-6 mb-3">
                                                                <label for="parts_quantity<?php echo $asset['asset_id']; ?>" class="form-label">Quantity *</label>
                                                                <input type="number" class="form-control" id="parts_quantity<?php echo $asset['asset_id']; ?>" name="parts_quantity" min="1" value="1">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label for="technician_id<?php echo $asset['asset_id']; ?>" class="form-label">Technician</label>
                                                            <?php
                                                            // Get current user's name
                                                            $currentUserQuery = "SELECT full_name, username FROM users WHERE user_id = ?";
                                                            $currentUserStmt = mysqli_prepare($conn, $currentUserQuery);
                                                            mysqli_stmt_bind_param($currentUserStmt, 'i', $currentUserId);
                                                            mysqli_stmt_execute($currentUserStmt);
                                                            $currentUserResult = mysqli_stmt_get_result($currentUserStmt);
                                                            $currentUser = mysqli_fetch_assoc($currentUserResult);
                                                            $currentUserName = $currentUser ? $currentUser['full_name'] . ' (' . $currentUser['username'] . ')' : 'Current User';
                                                            ?>
                                                            <input type="hidden" name="technician_id" value="<?php echo $currentUserId; ?>">
                                                            <input type="text" class="form-control bg-light" value="<?php echo $currentUserName; ?>" readonly>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label for="cost<?php echo $asset['asset_id']; ?>" class="form-label">Estimated Cost (₱)</label>
                                                            <input type="number" class="form-control" id="cost<?php echo $asset['asset_id']; ?>" name="cost" step="0.01" min="0">
                                                        </div>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="notes<?php echo $asset['asset_id']; ?>" class="form-label">Notes/Description of Issue</label>
                                                        <textarea class="form-control" id="notes<?php echo $asset['asset_id']; ?>" name="description" rows="4" placeholder="Describe the issue and repair details"></textarea>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="submit" form="markForRepairForm<?php echo $asset['asset_id']; ?>" class="btn btn-warning">
                                                    <i class="fas fa-tools"></i> Mark for Repair
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6">
                                    <div class="empty-state">
                                        <i class="fas fa-check-circle"></i>
                                        <h4>No Defective IT Equipment</h4>
                                        <p>There are currently no defective IT equipment that needs repair.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Maintenance Records Card -->
    <div class="records-card animate__animated animate__fadeInUp animate__delay-3">
        <div class="records-card-header">
            <div class="records-title">
                <i class="fas fa-clipboard-list"></i> IT Equipment Maintenance Records
            </div>
            <div>
                <!-- Add Record button removed -->
            </div>
        </div>
        <div class="records-card-body">
            <div class="records-search">
                <i class="fas fa-search"></i>
                <input type="text" id="maintenanceSearch" placeholder="Search IT equipment maintenance records...">
            </div>
            <div class="table-responsive">
                <table class="modern-table" id="maintenanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Date</th>
                            <th>Asset</th>
                            <th>Location</th>
                            <th>Type</th>
                            <th>Cost</th>
                            <th>Technician</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (mysqli_num_rows($result) > 0): ?>
                            <?php while ($record = mysqli_fetch_assoc($result)): ?>
                                <tr>
                                    <td><?php echo $record['record_id']; ?></td>
                                    <td><?php echo formatDate($record['maintenance_date']); ?></td>
                                    <td>
                                        <a href="/choims/modules/assets/view.php?id=<?php echo $record['asset_id']; ?>" style="text-decoration: none; font-weight: 600; color: var(--primary);">
                                            <?php echo $record['asset_name']; ?>
                                        </a>
                                        <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.25rem;">
                                            <?php echo $record['sku_name']; ?>
                                            <?php if (!empty($record['serial_number'])): ?>
                                                <span style="color: var(--text-secondary);">(SN: <?php echo $record['serial_number']; ?>)</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td><?php echo $record['location_name']; ?></td>
                                    <td>
                                        <span style="font-weight: 500;"><?php echo $record['maintenance_type']; ?></span>
                                    </td>
                                    <td>
                                        <span style="font-weight: 500;">₱<?php echo number_format($record['cost'], 2); ?></span>
                                    </td>
                                    <td><?php echo $record['technician_name'] ?: '<em style="color: var(--text-secondary);">Not assigned</em>'; ?></td>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        switch ($record['status']) {
                                            case 'Completed':
                                                $statusClass = 'status-completed';
                                                break;
                                            case 'In Progress':
                                                $statusClass = 'status-in-progress';
                                                break;
                                            case 'Scheduled':
                                                $statusClass = 'status-available';
                                                break;
                                            case 'Cancelled':
                                                $statusClass = 'status-defective';
                                                break;
                                            default:
                                                $statusClass = 'status-disposed';
                                        }
                                        ?>
                                        <span class="status-badge <?php echo $statusClass; ?>">
                                            <?php echo $record['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="display: flex; gap: 0.5rem;">
                                            <?php if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('Logistics') || hasRole('HIMU')): ?>
                                            <a href="/choims/modules/maintenance/edit.php?id=<?php echo $record['record_id']; ?>" class="table-action-btn edit-btn" title="Edit Record">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (hasRole('GodMode') || hasRole('Superadmin') || hasRole('HIMU')): ?>
                                            <button type="button" class="table-action-btn delete-btn" data-bs-toggle="modal" data-bs-target="#deleteMaintenanceModal<?php echo $record['record_id']; ?>" title="Delete Record">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics') && !hasRole('HIMU')): ?>
                                            <span class="text-muted"><i class="fas fa-lock"></i> No Access</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Delete Maintenance Modal -->
                                <div class="modal fade" id="deleteMaintenanceModal<?php echo $record['record_id']; ?>" tabindex="-1" aria-labelledby="deleteMaintenanceModalLabel<?php echo $record['record_id']; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteMaintenanceModalLabel<?php echo $record['record_id']; ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this maintenance record for <strong><?php echo $record['asset_name']; ?></strong>?</p>
                                                <p>This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="/choims/modules/maintenance/delete.php" method="post">
                                                    <input type="hidden" name="record_id" value="<?php echo $record['record_id']; ?>">
                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9">
                                    <div class="empty-state">
                                        <i class="fas fa-clipboard-check"></i>
                                        <h4>No IT Equipment Maintenance Records</h4>
                                        <p>There are no IT equipment maintenance records in the system yet.</p>
                                        <!-- Add Maintenance Record button removed -->
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Maintenance Record Modal -->
<div class="modal fade" id="addMaintenanceModal" tabindex="-1" aria-labelledby="addMaintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMaintenanceModalLabel">Add IT Equipment Maintenance Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/choims/modules/maintenance/add.php" method="post" id="addMaintenanceForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="asset_id" class="form-label">Asset *</label>
                            <select class="form-select" id="asset_id" name="asset_id" required>
                                <option value="">Select Asset</option>
                                <?php mysqli_data_seek($assetsResult, 0); ?>
                                <?php while ($asset = mysqli_fetch_assoc($assetsResult)): ?>
                                    <option value="<?php echo $asset['asset_id']; ?>" <?php if ($asset_id == $asset['asset_id']) echo 'selected'; ?>>
                                        <?php echo $asset['asset_name']; ?> -
                                        <?php echo $asset['sku_name']; ?> (<?php echo $asset['category_name']; ?>)
                                        <?php if (!empty($asset['serial_number'])): ?>
                                            (SN: <?php echo $asset['serial_number']; ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="maintenance_date" class="form-label">Maintenance Date *</label>
                            <input type="date" class="form-control" id="maintenance_date" name="maintenance_date" required value="<?php echo date('Y-m-d'); ?>">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="maintenance_type" class="form-label">Maintenance Type *</label>
                            <select class="form-select" id="maintenance_type" name="maintenance_type" required onchange="handleMainModalTypeChange(this)">
                                <option value="">Select Type</option>
                                <?php foreach ($maintenanceTypes as $type): ?>
                                    <option value="<?php echo $type; ?>">
                                        <?php echo $type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="cost" class="form-label">Cost (₱)</label>
                            <input type="number" class="form-control" id="cost" name="cost" step="0.01" min="0">
                        </div>
                    </div>

                    <!-- Parts Replacement Section - Initially Hidden -->
                    <div id="mainPartsReplacementSection" class="parts-replacement-section" style="display: none;">
                        <div class="custom-alert custom-alert-info">
                            <i class="fas fa-info-circle"></i> Select IT parts to use for this repair. The selected parts will be deducted from inventory.
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="parts_inventory_id" class="form-label">Select Part *</label>
                                <select class="form-select" id="parts_inventory_id" name="parts_inventory_id">
                                    <option value="">Select IT Part</option>
                                    <?php
                                    // Get IT parts from both fixed assets and consumable inventory
                                    // Get current user's location if not already fetched
                                    if (!isset($userLocationId)) {
                                        $userLocationQuery = "SELECT location_id FROM users WHERE user_id = ?";
                                        $userLocationStmt = mysqli_prepare($conn, $userLocationQuery);
                                        mysqli_stmt_bind_param($userLocationStmt, 'i', $currentUserId);
                                        mysqli_stmt_execute($userLocationStmt);
                                        $userLocationResult = mysqli_stmt_get_result($userLocationStmt);
                                        $userLocation = mysqli_fetch_assoc($userLocationResult);
                                        $userLocationId = $userLocation ? $userLocation['location_id'] : 0;
                                    }

                                    $mainPartsQuery = "
                                        SELECT
                                            CONCAT('FA-', fa.asset_id) as inventory_id,
                                            CONCAT(fa.asset_name, ' (Fixed Asset)') as sku_name,
                                            1 as current_quantity,
                                            l.location_name
                                        FROM fixed_assets fa
                                        JOIN sku_master s ON fa.sku_id = s.sku_id
                                        JOIN categories c ON s.category_id = c.category_id
                                        JOIN locations l ON fa.current_location_id = l.location_id
                                        WHERE c.requires_himu_approval = 1
                                        AND fa.status = 'Available'
                                        AND (fa.is_deleted = 0 OR fa.is_deleted IS NULL)
                                        AND l.location_id = ?

                                        UNION

                                        SELECT
                                            CONCAT('CI-', ci.inventory_id) as inventory_id,
                                            CONCAT(s.sku_name, ' (Consumable)') as sku_name,
                                            ci.current_quantity,
                                            l.location_name
                                        FROM consumable_inventory ci
                                        JOIN sku_master s ON ci.sku_id = s.sku_id
                                        JOIN locations l ON ci.location_id = l.location_id
                                        JOIN categories c ON s.category_id = c.category_id
                                        WHERE c.category_id = 4
                                        AND ci.current_quantity > 0
                                        AND (ci.is_deleted = 0 OR ci.is_deleted IS NULL)
                                        AND l.location_id = ?

                                        ORDER BY sku_name, location_name
                                    ";
                                    $mainPartsStmt = mysqli_prepare($conn, $mainPartsQuery);
                                    // Use the user's location ID for both parameters since we don't have a specific asset ID yet
                                    mysqli_stmt_bind_param($mainPartsStmt, 'ii', $userLocationId, $userLocationId);
                                    mysqli_stmt_execute($mainPartsStmt);
                                    $mainPartsResult = mysqli_stmt_get_result($mainPartsStmt);

                                    if (mysqli_num_rows($mainPartsResult) > 0) {
                                        while ($part = mysqli_fetch_assoc($mainPartsResult)) {
                                            echo '<option value="' . $part['inventory_id'] . '">' .
                                                $part['sku_name'] . ' - ' . $part['location_name'] .
                                                ' (Available: ' . $part['current_quantity'] . ')</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="parts_quantity" class="form-label">Quantity *</label>
                                <input type="number" class="form-control" id="parts_quantity" name="parts_quantity" min="1" value="1">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="technician_id" class="form-label">Technician</label>
                            <?php
                            // Get current user's name (reuse the variable if already set)
                            if (!isset($currentUserName)) {
                                $currentUserQuery = "SELECT full_name, username FROM users WHERE user_id = ?";
                                $currentUserStmt = mysqli_prepare($conn, $currentUserQuery);
                                mysqli_stmt_bind_param($currentUserStmt, 'i', $currentUserId);
                                mysqli_stmt_execute($currentUserStmt);
                                $currentUserResult = mysqli_stmt_get_result($currentUserStmt);
                                $currentUser = mysqli_fetch_assoc($currentUserResult);
                                $currentUserName = $currentUser ? $currentUser['full_name'] . ' (' . $currentUser['username'] . ')' : 'Current User';
                            }
                            ?>
                            <input type="hidden" name="technician_id" value="<?php echo $currentUserId; ?>">
                            <input type="text" class="form-control bg-light" value="<?php echo $currentUserName; ?>" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="Scheduled">Scheduled</option>
                                <option value="In Progress">In Progress</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="description" rows="4" placeholder="Enter details about the maintenance performed"></textarea>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="update_asset_status" name="update_asset_status" value="1">
                        <label class="form-check-label" for="update_asset_status">
                            Update asset status to match maintenance status
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addMaintenanceForm" class="btn btn-primary">Save Record</button>
            </div>
        </div>
    </div>
</div>

<script>
// Function to handle maintenance type change in the mark for repair modal
function handleMaintenanceTypeChange(selectElement, assetId) {
    // Find the parts replacement section in both the original modal and custom modal
    const originalPartsSection = document.getElementById('partsReplacementSection' + assetId);
    const customModalId = 'custom-markForRepairModal' + assetId;
    const customModal = document.getElementById(customModalId);

    if (selectElement.value === 'Parts Replacement') {
        // Show parts section in original modal
        if (originalPartsSection) {
            originalPartsSection.style.display = 'block';

            // Make the parts fields required
            const partsInventoryId = document.getElementById('parts_inventory_id' + assetId);
            const partsQuantity = document.getElementById('parts_quantity' + assetId);
            if (partsInventoryId) partsInventoryId.required = true;
            if (partsQuantity) partsQuantity.required = true;
        }

        // Show parts section in custom modal if it exists
        if (customModal) {
            const customPartsSection = customModal.querySelector('.parts-replacement-section');
            if (customPartsSection) {
                customPartsSection.style.display = 'block';

                // Make the parts fields required in custom modal
                const customPartsInventoryId = customModal.querySelector('[name="parts_inventory_id"]');
                const customPartsQuantity = customModal.querySelector('[name="parts_quantity"]');
                if (customPartsInventoryId) customPartsInventoryId.required = true;
                if (customPartsQuantity) customPartsQuantity.required = true;
            }
        }
    } else {
        // Hide parts section in original modal
        if (originalPartsSection) {
            originalPartsSection.style.display = 'none';

            // Make the parts fields not required
            const partsInventoryId = document.getElementById('parts_inventory_id' + assetId);
            const partsQuantity = document.getElementById('parts_quantity' + assetId);
            if (partsInventoryId) partsInventoryId.required = false;
            if (partsQuantity) partsQuantity.required = false;
        }

        // Hide parts section in custom modal if it exists
        if (customModal) {
            const customPartsSection = customModal.querySelector('.parts-replacement-section');
            if (customPartsSection) {
                customPartsSection.style.display = 'none';

                // Make the parts fields not required in custom modal
                const customPartsInventoryId = customModal.querySelector('[name="parts_inventory_id"]');
                const customPartsQuantity = customModal.querySelector('[name="parts_quantity"]');
                if (customPartsInventoryId) customPartsInventoryId.required = false;
                if (customPartsQuantity) customPartsQuantity.required = false;
            }
        }
    }

    // Also update the select in the custom modal to match
    if (customModal) {
        const customSelect = customModal.querySelector('[name="maintenance_type"]');
        if (customSelect && customSelect !== selectElement) {
            customSelect.value = selectElement.value;
        }
    }
}

// Function to handle maintenance type change in the main add maintenance modal
function handleMainModalTypeChange(selectElement) {
    const mainPartsSection = document.getElementById('mainPartsReplacementSection');
    const customModalId = 'custom-addMaintenanceModal';
    const customModal = document.getElementById(customModalId);

    if (selectElement.value === 'Parts Replacement') {
        // Show parts section in original modal
        if (mainPartsSection) {
            mainPartsSection.style.display = 'block';

            // Make the parts fields required
            const partsInventoryId = document.getElementById('parts_inventory_id');
            const partsQuantity = document.getElementById('parts_quantity');
            if (partsInventoryId) partsInventoryId.required = true;
            if (partsQuantity) partsQuantity.required = true;
        }

        // Show parts section in custom modal if it exists
        if (customModal) {
            const customPartsSection = customModal.querySelector('.parts-replacement-section');
            if (customPartsSection) {
                customPartsSection.style.display = 'block';

                // Make the parts fields required in custom modal
                const customPartsInventoryId = customModal.querySelector('[name="parts_inventory_id"]');
                const customPartsQuantity = customModal.querySelector('[name="parts_quantity"]');
                if (customPartsInventoryId) customPartsInventoryId.required = true;
                if (customPartsQuantity) customPartsQuantity.required = true;
            }
        }
    } else {
        // Hide parts section in original modal
        if (mainPartsSection) {
            mainPartsSection.style.display = 'none';

            // Make the parts fields not required
            const partsInventoryId = document.getElementById('parts_inventory_id');
            const partsQuantity = document.getElementById('parts_quantity');
            if (partsInventoryId) partsInventoryId.required = false;
            if (partsQuantity) partsQuantity.required = false;
        }

        // Hide parts section in custom modal if it exists
        if (customModal) {
            const customPartsSection = customModal.querySelector('.parts-replacement-section');
            if (customPartsSection) {
                customPartsSection.style.display = 'none';

                // Make the parts fields not required in custom modal
                const customPartsInventoryId = customModal.querySelector('[name="parts_inventory_id"]');
                const customPartsQuantity = customModal.querySelector('[name="parts_quantity"]');
                if (customPartsInventoryId) customPartsInventoryId.required = false;
                if (customPartsQuantity) customPartsQuantity.required = false;
            }
        }
    }

    // Also update the select in the custom modal to match
    if (customModal) {
        const customSelect = customModal.querySelector('[name="maintenance_type"]');
        if (customSelect && customSelect !== selectElement) {
            customSelect.value = selectElement.value;
        }
    }
}

$(document).ready(function() {
    // Toggle filter section
    $('#toggleFilters').on('click', function() {
        $('#filterBody').slideToggle(300);
        $(this).find('i').toggleClass('fa-sliders-h fa-times');

        if ($(this).find('i').hasClass('fa-times')) {
            $(this).html('<i class="fas fa-times"></i> Close Filters');
        } else {
            $(this).html('<i class="fas fa-sliders-h"></i> Adjust Filters');
        }
    });

    // Check if filters are applied and show/hide filter body accordingly
    const hasFilters = <?php echo (!empty($location_id) || !empty($status) || !empty($maintenance_type) || !empty($search) || !empty($date_from) || !empty($date_to)) ? 'true' : 'false'; ?>;
    if (!hasFilters) {
        $('#filterBody').hide();
    }

    // Add basic search functionality for the maintenance table
    $('#maintenanceSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#maintenanceTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add basic search functionality for the defective assets table
    $('#defectiveSearch').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#defectiveAssetsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Add hover effect to table rows
    $('.modern-table tbody tr').hover(
        function() { $(this).css('background-color', 'var(--bg-secondary)'); },
        function() { $(this).css('background-color', ''); }
    );

    // Modal handling is now done by custom-modal.js

    // Update asset status checkbox logic
    $('#status').on('change', function() {
        var maintenanceStatus = $(this).val();
        var checkbox = $('#update_asset_status');

        if (maintenanceStatus === 'Completed') {
            checkbox.prop('checked', true);
        } else if (maintenanceStatus === 'In Progress') {
            checkbox.prop('checked', true);
        } else {
            checkbox.prop('checked', false);
        }
    });

    // Initialize parts replacement sections for the main modal
    const mainTypeSelect = document.getElementById('maintenance_type');
    if (mainTypeSelect) {
        handleMainModalTypeChange(mainTypeSelect);
    }

    // For each defective asset modal, initialize the parts replacement section
    $('.repair-modal').each(function() {
        const assetId = $(this).data('asset-id');
        if (assetId) {
            const typeSelect = document.getElementById('maintenance_type' + assetId);
            if (typeSelect) {
                handleMaintenanceTypeChange(typeSelect, assetId);
            }
        }
    });

    // Add event listener to custom-modal.js initialization
    document.addEventListener('customModalsInitialized', function() {
        console.log('Custom modals initialized, setting up parts replacement sections');

        // Initialize main modal
        const customMainModal = document.getElementById('custom-addMaintenanceModal');
        if (customMainModal) {
            const customMainTypeSelect = customMainModal.querySelector('[name="maintenance_type"]');
            if (customMainTypeSelect) {
                handleMainModalTypeChange(customMainTypeSelect);
            }
        }

        // Initialize repair modals
        document.querySelectorAll('[id^="custom-markForRepairModal"]').forEach(function(modal) {
            const modalId = modal.id;
            const assetId = modalId.replace('custom-markForRepairModal', '');
            if (assetId) {
                const customTypeSelect = modal.querySelector('[name="maintenance_type"]');
                if (customTypeSelect) {
                    handleMaintenanceTypeChange(customTypeSelect, assetId);
                }
            }
        });
    });

    // Ensure modals are properly initialized
    $('.modal').each(function() {
        $(this).on('shown.bs.modal', function() {
            // Recalculate modal position when shown
            $(this).css({
                'display': 'block',
                'padding-right': '17px'
            });
        });
    });
});
</script>

<!-- Load custom modal scripts -->
<script src="/choims/assets/js/disable-bootstrap-modals.js"></script>
<script src="/choims/assets/js/custom-modal.js"></script>

<!-- Direct fix for modal backdrop issues -->
<script>
// Remove any existing modal backdrops
document.addEventListener('DOMContentLoaded', function() {
    // Remove any existing modal backdrops
    const existingBackdrops = document.querySelectorAll('.modal-backdrop');
    existingBackdrops.forEach(function(backdrop) {
        backdrop.parentNode.removeChild(backdrop);
    });

    // Remove modal-open class from body
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';

    // Force close any open modals
    const openModals = document.querySelectorAll('.modal.show');
    openModals.forEach(function(modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>