<?php
/**
 * Mobile Bottom Navigation
 * This file provides a mobile-specific bottom navigation bar
 */

// Only show for logged-in users
if (!isLoggedIn()) {
    return;
}

// Determine current page to highlight active nav item
$currentPath = $_SERVER['REQUEST_URI'];

// Define navigation items based on user role
$navItems = [];

// Common navigation items for all roles
$navItems[] = [
    'icon' => 'fa-home',
    'text' => 'Home',
    'url' => '/choims/index.php',
    'match' => ['/index.php', '/dashboard']
];

// Role-specific navigation items
switch (strtolower($_SESSION['role'])) {
    case 'admin':
        $navItems[] = [
            'icon' => 'fa-users',
            'text' => 'Users',
            'url' => '/choims/modules/users/list.php',
            'match' => ['/users/']
        ];
        $navItems[] = [
            'icon' => 'fa-box',
            'text' => 'Inventory',
            'url' => '/choims/modules/inventory/list.php',
            'match' => ['/inventory/']
        ];
        $navItems[] = [
            'icon' => 'fa-laptop',
            'text' => 'Assets',
            'url' => '/choims/modules/assets/list.php',
            'match' => ['/assets/']
        ];
        $navItems[] = [
            'icon' => 'fa-user',
            'text' => 'Profile',
            'url' => '/choims/modules/users/profile.php',
            'match' => ['/profile/']
        ];
        break;
        
    case 'himu':
        $navItems[] = [
            'icon' => 'fa-box',
            'text' => 'Inventory',
            'url' => '/choims/modules/inventory/list.php',
            'match' => ['/inventory/']
        ];
        $navItems[] = [
            'icon' => 'fa-laptop',
            'text' => 'Assets',
            'url' => '/choims/modules/assets/list.php',
            'match' => ['/assets/']
        ];
        $navItems[] = [
            'icon' => 'fa-exchange-alt',
            'text' => 'Transfer',
            'url' => '/choims/modules/transfers/create.php',
            'match' => ['/transfers/']
        ];
        $navItems[] = [
            'icon' => 'fa-user',
            'text' => 'Profile',
            'url' => '/choims/modules/users/profile.php',
            'match' => ['/profile/']
        ];
        break;
        
    case 'logistics':
        $navItems[] = [
            'icon' => 'fa-box',
            'text' => 'Inventory',
            'url' => '/choims/modules/inventory/list.php',
            'match' => ['/inventory/']
        ];
        $navItems[] = [
            'icon' => 'fa-exchange-alt',
            'text' => 'Transfers',
            'url' => '/choims/modules/transfers/list.php',
            'match' => ['/transfers/']
        ];
        $navItems[] = [
            'icon' => 'fa-truck',
            'text' => 'Delivery',
            'url' => '/choims/modules/transfers/pending_delivery.php',
            'match' => ['/pending_delivery']
        ];
        $navItems[] = [
            'icon' => 'fa-user',
            'text' => 'Profile',
            'url' => '/choims/modules/users/profile.php',
            'match' => ['/profile/']
        ];
        break;
        
    default: // Health Center and other roles
        $navItems[] = [
            'icon' => 'fa-box',
            'text' => 'Inventory',
            'url' => '/choims/modules/inventory/list.php',
            'match' => ['/inventory/']
        ];
        $navItems[] = [
            'icon' => 'fa-laptop',
            'text' => 'Assets',
            'url' => '/choims/modules/assets/list.php',
            'match' => ['/assets/']
        ];
        $navItems[] = [
            'icon' => 'fa-exchange-alt',
            'text' => 'Request',
            'url' => '/choims/modules/transfers/create.php',
            'match' => ['/transfers/create']
        ];
        $navItems[] = [
            'icon' => 'fa-user',
            'text' => 'Profile',
            'url' => '/choims/modules/users/profile.php',
            'match' => ['/profile/']
        ];
        break;
}

// Limit to 5 items maximum
if (count($navItems) > 5) {
    $navItems = array_slice($navItems, 0, 5);
}
?>

<!-- Mobile Bottom Navigation -->
<div class="mobile-bottom-nav">
    <?php foreach ($navItems as $item): 
        // Check if this item should be active
        $isActive = false;
        foreach ($item['match'] as $path) {
            if (strpos($currentPath, $path) !== false) {
                $isActive = true;
                break;
            }
        }
    ?>
    <a href="<?php echo $item['url']; ?>" class="mobile-bottom-nav-item <?php echo $isActive ? 'active' : ''; ?>">
        <i class="fas <?php echo $item['icon']; ?>"></i>
        <span><?php echo $item['text']; ?></span>
    </a>
    <?php endforeach; ?>
</div>
