<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireRole('HIMU');

// Get some statistics for the dashboard
// Get HIMU location ID from the user's session
$himuLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// If we don't have a location ID from the session, try to find it by name
if (!$himuLocationId) {
    $himuLocationQuery = "SELECT location_id FROM locations WHERE location_name LIKE '%HIMU%' OR location_name LIKE '%IT%' LIMIT 1";
    $himuLocationResult = mysqli_query($conn, $himuLocationQuery);

    if ($himuLocationResult && mysqli_num_rows($himuLocationResult) > 0) {
        $himuLocationRow = mysqli_fetch_assoc($himuLocationResult);
        $himuLocationId = $himuLocationRow['location_id'];
    }
}

// Count assets with special rules:
// 1. All IT Equipment (category_id = 1)
// 2. Only Office Equipment and Medical Equipment in HIMU's location
$itAssetsQuery = "
    SELECT
        COUNT(*) as total,
        COUNT(DISTINCT a.current_location_id) as location_count
    FROM fixed_assets a
    JOIN sku_master s ON a.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    WHERE a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    AND (c.category_id = 1 OR (c.category_id IN (2, 3) AND a.current_location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
";
$itAssetsResult = mysqli_query($conn, $itAssetsQuery);
$itAssetsData = mysqli_fetch_assoc($itAssetsResult);
$itAssetsCount = $itAssetsData['total'];
$itAssetsLocationCount = $itAssetsData['location_count'] ?: 0;

// Count supplies with special rules:
// 1. All IT Supplies (category_id = 4) regardless of location
// 2. Only Office Supplies (category_id = 5) and Medical Supplies (category_id = 6) in HIMU's location
$itSuppliesQuery = "
    SELECT
        COUNT(DISTINCT i.inventory_id) as total,
        SUM(i.current_quantity) as total_quantity
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    WHERE (c.category_id = 4 OR (c.category_id IN (5, 6) AND i.location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
    AND (i.is_deleted = 0 OR i.is_deleted IS NULL)
";
$itSuppliesResult = mysqli_query($conn, $itSuppliesQuery);
$itSuppliesData = mysqli_fetch_assoc($itSuppliesResult);
$itSuppliesCount = $itSuppliesData['total'];
$itSuppliesQuantity = $itSuppliesData['total_quantity'] ?: 0;

// Count pending transfer approvals for HIMU
$pendingTransfersQuery = "
    SELECT COUNT(*) as total
    FROM transfers t
    JOIN transfer_approval_view v ON t.transfer_id = v.transfer_id
    WHERE t.status = 'Approved by Logistics'
    AND v.requires_himu_approval = 1
";

// Count pending batch transfer approvals for HIMU
$pendingBatchTransfersQuery = "
    SELECT COUNT(*) as total
    FROM batch_transfers bt
    WHERE bt.status = 'Approved by Logistics'
    AND bt.requires_himu_approval = 1
";

$pendingTransfersResult = mysqli_query($conn, $pendingTransfersQuery);
$pendingTransfersCount = mysqli_fetch_assoc($pendingTransfersResult)['total'];

$pendingBatchTransfersResult = mysqli_query($conn, $pendingBatchTransfersQuery);
$pendingBatchTransfersCount = mysqli_fetch_assoc($pendingBatchTransfersResult)['total'];

// Combine both counts for total pending approvals
$totalPendingApprovals = $pendingTransfersCount + $pendingBatchTransfersCount;

// Get equipment by location with special rules:
// 1. All IT Equipment (category_id = 1)
// 2. Only Office Equipment and Medical Equipment in HIMU's location
$itByLocationQuery = "
    SELECT l.location_name, COUNT(a.asset_id) as asset_count
    FROM locations l
    LEFT JOIN fixed_assets a ON l.location_id = a.current_location_id AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    LEFT JOIN sku_master s ON a.sku_id = s.sku_id
    LEFT JOIN categories c ON s.category_id = c.category_id
    WHERE (c.category_id = 1 OR (c.category_id IN (2, 3) AND a.current_location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
    GROUP BY l.location_id
    ORDER BY asset_count DESC
    LIMIT 10
";
$itByLocationResult = mysqli_query($conn, $itByLocationQuery);

// Get equipment under repair or defective with special rules:
// 1. All IT Equipment (category_id = 1)
// 2. Only Office Equipment and Medical Equipment in HIMU's location
$equipmentRepairQuery = "
    SELECT a.asset_id, a.asset_name, s.sku_name, a.serial_number, l.location_name, a.status
    FROM fixed_assets a
    JOIN sku_master s ON a.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON a.current_location_id = l.location_id
    WHERE (c.category_id = 1 OR (c.category_id IN (2, 3) AND a.current_location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
    AND (a.status = 'Under Repair' OR a.status = 'Defective')
    ORDER BY
        CASE
            WHEN a.status = 'Defective' THEN 1
            ELSE 2
        END,
        a.updated_at DESC
    LIMIT 5
";
$equipmentRepairResult = mysqli_query($conn, $equipmentRepairQuery);

// Count defective equipment with special rules:
// 1. All IT Equipment (category_id = 1)
// 2. Only Office Equipment and Medical Equipment in HIMU's location
$defectiveEquipmentQuery = "
    SELECT COUNT(*) as count
    FROM fixed_assets a
    JOIN sku_master s ON a.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    WHERE (c.category_id = 1 OR (c.category_id IN (2, 3) AND a.current_location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
    AND a.status = 'Defective'
";
$defectiveEquipmentResult = mysqli_query($conn, $defectiveEquipmentQuery);
$defectiveEquipmentCount = mysqli_fetch_assoc($defectiveEquipmentResult)['count'];

// Count equipment under repair with special rules:
// 1. All IT Equipment (category_id = 1)
// 2. Only Office Equipment and Medical Equipment in HIMU's location
$underRepairEquipmentQuery = "
    SELECT COUNT(*) as count
    FROM fixed_assets a
    JOIN sku_master s ON a.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    WHERE (c.category_id = 1 OR (c.category_id IN (2, 3) AND a.current_location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
    AND a.status = 'Under Repair'
";
$underRepairEquipmentResult = mysqli_query($conn, $underRepairEquipmentQuery);
$underRepairEquipmentCount = mysqli_fetch_assoc($underRepairEquipmentResult)['count'];

// Total maintenance count
$totalMaintenanceCount = $defectiveEquipmentCount + $underRepairEquipmentCount;

// Get user's location ID
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : 0;

// Check if it's time for monthly inventory update
$showMonthlyUpdateButton = isTimeForMonthlyInventoryUpdate(); // Show only 2 days before month end
$hasCompletedMonthlyUpdate = false;

if ($showMonthlyUpdateButton && $userLocationId) {
    // Check if this location has already completed their monthly update
    $hasCompletedMonthlyUpdate = hasCompletedMonthlyInventoryUpdate($conn, $userLocationId);
}

// Generate CSRF token for the monthly update form
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get low stock items with special rules:
// 1. All IT Supplies (category_id = 4) regardless of location
// 2. Only Office Supplies (category_id = 5) and Medical Supplies (category_id = 6) in HIMU's location
$lowStockQuery = "
    SELECT s.sku_name, i.current_quantity, l.location_name,
    (SELECT SUM(ci.current_quantity) FROM consumable_inventory ci WHERE ci.sku_id = i.sku_id) as total_quantity
    FROM consumable_inventory i
    JOIN sku_master s ON i.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    JOIN locations l ON i.location_id = l.location_id
    WHERE (c.category_id = 4 OR (c.category_id IN (5, 6) AND i.location_id = " . ($himuLocationId ? $himuLocationId : 0) . "))
    ORDER BY i.current_quantity ASC
    LIMIT 5
";
$lowStockResult = mysqli_query($conn, $lowStockQuery);

// Get transfers waiting for HIMU approval
$transfersWaitingQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM transfers t
    JOIN locations l1 ON t.source_location_id = l1.location_id
    JOIN locations l2 ON t.destination_location_id = l2.location_id
    JOIN users u1 ON t.initiated_by = u1.user_id
    JOIN transfer_approval_view v ON t.transfer_id = v.transfer_id
    WHERE t.status = 'Approved by Logistics'
    AND v.requires_himu_approval = 1
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM batch_transfers b
    JOIN locations l1 ON b.source_location_id = l1.location_id
    JOIN locations l2 ON b.destination_location_id = l2.location_id
    JOIN users u1 ON b.initiated_by = u1.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.status = 'Approved by Logistics'
    AND b.requires_himu_approval = 1
    GROUP BY b.batch_id
    LIMIT 5)

    ORDER BY transfer_date DESC
";
$transfersWaitingResult = mysqli_query($conn, $transfersWaitingQuery);

// Get transfers waiting for HIMU acceptance (transfers to HIMU)
$transfersForAcceptanceQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(t.quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM transfers t
    JOIN locations l1 ON t.source_location_id = l1.location_id
    JOIN locations l2 ON t.destination_location_id = l2.location_id
    JOIN users u1 ON t.initiated_by = u1.user_id
    JOIN locations l3 ON l2.location_id = l3.location_id
    WHERE t.status = 'Approved by HIMU'
    AND l3.location_type = 'HIMU'
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        l1.location_name AS source_location,
        l2.location_name AS destination_location,
        u1.full_name AS initiated_by
    FROM batch_transfers b
    JOIN locations l1 ON b.source_location_id = l1.location_id
    JOIN locations l2 ON b.destination_location_id = l2.location_id
    JOIN users u1 ON b.initiated_by = u1.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    JOIN locations l3 ON l2.location_id = l3.location_id
    WHERE b.status = 'Approved by HIMU'
    AND l3.location_type = 'HIMU'
    GROUP BY b.batch_id
    LIMIT 5)

    ORDER BY transfer_date DESC
";
$transfersForAcceptanceResult = mysqli_query($conn, $transfersForAcceptanceQuery);
?>

<!-- Include modern dashboard styles -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<link rel="stylesheet" href="/choims/assets/css/logistics-dashboard-modern-white.css">

<style>
    /* Modern dashboard styling with improved aesthetics - Logistics style */
    :root {
        --primary-green: #2E7D32;
        --light-green: #4CAF50;
        --lighter-green: #8BC34A;
        --dark-green: #1B5E20;
        --accent-green: #00C853;
        --green-hover: #388E3C;
        --green-text: #1B5E20;
        --green-border: #81C784;
        --green-bg-light: #E8F5E9;
        --green-bg-ultra-light: #F1F8E9;

        --blue: #1976D2;
        --light-blue: #42A5F5;
        --blue-bg-light: #E3F2FD;
        --orange: #FF9800;
        --orange-bg-light: #FFF3E0;
        --red: #F44336;
        --red-bg-light: #FFEBEE;
        --purple: #6A1B9A;
        --purple-bg-light: #F3E5F5;

        /* Text colors */
        --text-primary: #333333;
        --text-secondary: #666666;
        --text-tertiary: #888888;
        --text-light: #FFFFFF;

        /* Shadow values for subtle, modern look */
        --shadow-sm: 0 2px 5px rgba(0,0,0,0.03), 0 1px 2px rgba(0,0,0,0.05);
        --shadow-md: 0 4px 10px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.03);
        --shadow-lg: 0 8px 16px rgba(0,0,0,0.06), 0 4px 8px rgba(0,0,0,0.04);
        --shadow-hover: 0 12px 24px rgba(0,0,0,0.08), 0 8px 12px rgba(0,0,0,0.06);

        /* Radius values for rounded corners */
        --radius-sm: 8px;
        --radius-md: 12px;
        --radius-lg: 16px;
        --radius-xl: 20px;
        --radius-circle: 50%;

        /* Spacing */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-xxl: 3rem;
    }

    /* We're using the logistics dashboard header styles from the imported CSS */

    @keyframes shine-effect {
        0% { transform: rotate(45deg) translate(0, -100%); }
        20% { transform: rotate(45deg) translate(0, 100%); }
        100% { transform: rotate(45deg) translate(0, 100%); }
    }

    /* Modern dropdown */
    .modern-dropdown {
        border: none;
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        padding: 0.75rem 0;
        min-width: 280px;
        margin-top: 12px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        animation: dropdown-fade 0.2s ease-out;
        backdrop-filter: blur(10px);
        background-color: rgba(255, 255, 255, 0.98);
    }

    @keyframes dropdown-fade {
        from { opacity: 0; transform: translateY(-8px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .modern-dropdown::before {
        content: '';
        position: absolute;
        top: -6px;
        right: 20px;
        width: 12px;
        height: 12px;
        background: white;
        transform: rotate(45deg);
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        border-left: 1px solid rgba(0, 0, 0, 0.05);
        z-index: 1;
    }

    .modern-dropdown .dropdown-header {
        font-weight: 700;
        color: var(--primary-green);
        padding: 0.6rem 1.25rem;
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .modern-dropdown .dropdown-item {
        padding: 0.7rem 1.25rem;
        font-size: 0.9rem;
        color: #555;
        transition: all 0.2s ease;
        position: relative;
        z-index: 2;
    }

    .modern-dropdown .dropdown-item:hover {
        background-color: var(--green-bg-light);
        color: var(--green-text);
    }

    .modern-dropdown .dropdown-item:active {
        background-color: var(--green-bg-light);
        color: var(--green-text);
    }

    .modern-dropdown .dropdown-item i {
        color: var(--primary-green);
        width: 20px;
        text-align: center;
        transition: transform 0.2s ease;
    }

    .modern-dropdown .dropdown-item:hover i {
        transform: translateX(2px);
    }

    .modern-dropdown .dropdown-divider {
        margin: 0.5rem 0;
        opacity: 0.1;
    }

    /* Cards - Logistics style */
    .himu-dashboard .card {
        border-radius: var(--radius-lg);
        border: 1px solid rgba(0,0,0,0.05);
        box-shadow: var(--shadow-sm);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        height: 100%;
        background-color: white;
    }

    .himu-dashboard .card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-md);
    }

    /* Stat Cards - Logistics style */
    .himu-dashboard .stat-card {
        position: relative;
        color: var(--text-primary);
        overflow: hidden;
        min-height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        background: white;
        border: 1px solid rgba(0,0,0,0.05);
        box-shadow: var(--shadow-sm);
    }

    .himu-dashboard .stat-card.equipment {
        border-top: 4px solid var(--blue);
    }

    .himu-dashboard .stat-card.supplies {
        border-top: 4px solid var(--light-green);
    }

    .himu-dashboard .stat-card.approvals {
        border-top: 4px solid var(--orange);
    }

    .himu-dashboard .stat-card.maintenance {
        border-top: 4px solid var(--purple);
    }

    .himu-dashboard .stat-card .icon-bg {
        position: absolute;
        right: var(--spacing-md);
        top: var(--spacing-md);
        font-size: 2.5rem;
        transition: all 0.3s ease;
    }

    .himu-dashboard .stat-card.equipment .icon-bg {
        color: var(--blue-bg-light);
    }

    .himu-dashboard .stat-card.supplies .icon-bg {
        color: var(--green-bg-light);
    }

    .himu-dashboard .stat-card.approvals .icon-bg {
        color: var(--orange-bg-light);
    }

    .himu-dashboard .stat-card.maintenance .icon-bg {
        color: var(--purple-bg-light);
    }

    .himu-dashboard .stat-card:hover .icon-bg {
        transform: scale(1.1);
    }

    .himu-dashboard .stat-card .stat-title {
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: var(--spacing-sm);
        color: var(--text-secondary);
    }

    .himu-dashboard .stat-card .stat-value {
        font-size: 2.2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
    }

    .himu-dashboard .stat-card.equipment .stat-value {
        color: var(--blue);
    }

    .himu-dashboard .stat-card.supplies .stat-value {
        color: var(--primary-green);
    }

    .himu-dashboard .stat-card.approvals .stat-value {
        color: var(--orange);
    }

    .himu-dashboard .stat-card.maintenance .stat-value {
        color: var(--purple);
    }

    .himu-dashboard .stat-card .stat-label {
        font-size: 0.85rem;
        font-weight: 400;
        color: var(--text-secondary);
    }

    /* Interactive stat cards */
    .stat-card-link {
        text-decoration: none;
        display: block;
        height: 100%;
        color: var(--text-primary);
    }

    .stat-card-link:hover {
        text-decoration: none;
        color: var(--text-primary);
    }

    .stat-action {
        position: absolute;
        bottom: var(--spacing-md);
        right: var(--spacing-md);
        background: var(--green-bg-light);
        width: 28px;
        height: 28px;
        border-radius: var(--radius-circle);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: translateX(-10px);
        transition: all 0.3s ease;
    }

    .stat-card-link:hover .stat-action {
        opacity: 1;
        transform: translateX(0);
    }

    .stat-action i {
        font-size: 0.8rem;
        color: var(--primary-green);
    }

    /* Card Headers - Logistics style */
    .himu-dashboard .card-header {
        background: transparent;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: var(--spacing-lg) var(--spacing-lg);
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .himu-dashboard .card-header i {
        margin-right: var(--spacing-sm);
        font-size: 1rem;
        color: var(--primary-green);
    }

    .himu-dashboard .card-header .card-title {
        margin-bottom: 0;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .himu-dashboard .card-body {
        padding: var(--spacing-lg);
    }

    /* Action Buttons - Logistics style */
    .himu-dashboard .action-btn {
        border-radius: 50px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
        background: white;
        color: var(--text-primary);
        border: 1px solid rgba(0,0,0,0.08);
    }

    .himu-dashboard .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
        background: white;
        color: var(--text-primary);
    }

    .himu-dashboard .action-btn i {
        color: var(--primary-green);
    }

    .himu-dashboard .action-btn-sm {
        padding: 0.4rem 1.2rem;
        font-size: 0.85rem;
    }

    .action-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    /* Tables - Logistics style */
    .himu-dashboard .table {
        margin-bottom: 0;
    }

    .himu-dashboard .table th {
        border-top: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        color: var(--text-secondary);
        padding: var(--spacing-md) var(--spacing-sm);
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .himu-dashboard .table td {
        padding: var(--spacing-md) var(--spacing-sm);
        vertical-align: middle;
        border-bottom: 1px solid rgba(0,0,0,0.03);
        color: var(--text-primary);
    }

    .himu-dashboard .table tr:hover {
        background-color: var(--green-bg-ultra-light);
    }

    .himu-dashboard .table tr:last-child td {
        border-bottom: none;
    }

    /* Badges - Logistics style */
    .himu-dashboard .badge {
        padding: 0.4rem 0.8rem;
        font-weight: 500;
        border-radius: 50px;
        box-shadow: var(--shadow-sm);
        font-size: 0.75rem;
    }

    /* Charts - Logistics style */
    .himu-dashboard .chart-container {
        position: relative;
        height: 250px;
        margin: var(--spacing-md) 0;
    }

    /* Landscape chart container for better data visualization */
    .himu-dashboard .chart-container-landscape {
        position: relative;
        height: 250px;
        margin: var(--spacing-md) 0;
        padding: 0 var(--spacing-sm);
    }

    @media (min-width: 992px) {
        .himu-dashboard .chart-container-landscape {
            height: 280px;
        }
    }

    /* Scrollable tables */
    .himu-dashboard .table-scrollable {
        max-height: 300px;
        overflow-y: auto;
        scrollbar-width: thin;
    }

    .himu-dashboard .table-scrollable::-webkit-scrollbar {
        width: 6px;
    }

    .himu-dashboard .table-scrollable::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .himu-dashboard .table-scrollable::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    .himu-dashboard .table-scrollable::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Quick access buttons - Logistics style */
    .quick-access-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .quick-access-btn {
        background: white;
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg) var(--spacing-md);
        text-align: center;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
        color: var(--text-primary);
        border: 1px solid rgba(0,0,0,0.05);
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .quick-access-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-green);
        border-top-left-radius: var(--radius-lg);
        border-top-right-radius: var(--radius-lg);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .quick-access-btn:hover {
        background: white;
        transform: translateY(-5px);
        box-shadow: var(--shadow-md);
        color: var(--text-primary);
        text-decoration: none;
    }

    .quick-access-btn:hover::before {
        opacity: 1;
    }

    .quick-access-btn i {
        font-size: 1.8rem;
        margin-bottom: var(--spacing-md);
        color: var(--primary-green);
        transition: all 0.3s ease;
        background: var(--green-bg-light);
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-circle);
    }

    .quick-access-btn:hover i {
        transform: scale(1.1);
    }

    .quick-access-btn .btn-label {
        font-weight: 600;
        display: block;
        margin-bottom: var(--spacing-xs);
        font-size: 1rem;
    }

    .quick-access-btn .btn-desc {
        font-size: 0.85rem;
        color: var(--text-secondary);
    }

    /* Status indicator circle - Logistics style */
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .under-repair {
        background-color: var(--orange);
        box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.15);
    }

    .critical-stock {
        background-color: var(--red);
        box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.15);
    }

    .status-available {
        background-color: var(--primary-green);
        box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.15);
    }

    .status-in-use {
        background-color: var(--blue);
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.15);
    }

    /* Empty state styling - Logistics style */
    .empty-state {
        text-align: center;
        padding: var(--spacing-xl) var(--spacing-md);
    }

    .empty-state i {
        font-size: 2.5rem;
        color: var(--green-bg-light);
        margin-bottom: var(--spacing-md);
        background: var(--green-bg-ultra-light);
        width: 70px;
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-circle);
        margin: 0 auto var(--spacing-md) auto;
    }

    .empty-state p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .dashboard-header-content {
            padding: 1.25rem;
        }

        .dashboard-title {
            font-size: 1.5rem;
        }

        .header-actions {
            margin-top: 1rem;
            justify-content: flex-start;
        }

        .himu-dashboard .stat-card {
            min-height: 120px;
        }

        .himu-dashboard .stat-card .stat-value {
            font-size: 2rem;
        }

        .quick-access-container {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .quick-access-container {
            grid-template-columns: repeat(2, 1fr);
        }

        .himu-dashboard .card-header .card-title {
            font-size: 1rem;
        }
    }

    @media (max-width: 576px) {
        .dashboard-header-content {
            padding: 1.25rem 1rem;
        }

        .dashboard-icon {
            width: 50px;
            height: 50px;
        }

        .dashboard-icon i {
            font-size: 1.5rem;
        }

        .dashboard-title {
            font-size: 1.4rem;
        }

        .dashboard-subtitle {
            font-size: 0.85rem;
        }

        .header-actions {
            flex-direction: column;
            align-items: stretch;
            width: 100%;
            gap: 8px;
        }

        .header-date, .header-btn {
            justify-content: center;
        }

        .quick-access-container {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="container-fluid py-4">
    <!-- Modern dashboard header -->
    <header class="dashboard-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <?php
                // Extract first name from full name
                $firstName = '';
                if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                    $nameParts = explode(' ', $_SESSION['full_name']);
                    $firstName = $nameParts[0];
                }
                ?>
                <h1 class="dashboard-title">Hi, <?php echo htmlspecialchars($firstName); ?></h1>
            </div>
            <div class="dashboard-actions">
                <a href="/choims/modules/transfers/list.php?filter=1&status=Approved by Logistics" class="action-btn action-primary">
                    <i class="fas fa-clipboard-check"></i> Pending Approvals
                    <?php if($totalPendingApprovals > 0): ?>
                        <span class="badge bg-warning text-dark"><?php echo $totalPendingApprovals; ?></span>
                    <?php endif; ?>
                </a>
                <a href="/choims/modules/transfers/list.php?filter=1&status=Approved by HIMU" class="action-btn">
                    <i class="fas fa-truck-loading"></i> Incoming Transfers
                </a>
                <a href="/choims/modules/assets/maintenance.php" class="action-btn">
                    <i class="fas fa-tools"></i> Maintenance
                </a>
                <?php if ($showMonthlyUpdateButton): ?>
                <a href="#" class="action-btn <?php echo $hasCompletedMonthlyUpdate ? 'bg-success text-white' : 'bg-warning text-dark'; ?>" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                    <i class="fas fa-calendar-check"></i> <?php echo $hasCompletedMonthlyUpdate ? 'Update Completed' : 'Monthly Update'; ?>
                </a>
                <?php endif; ?>
            </div>
        </div>
        <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
    </header>

    <?php if ($showMonthlyUpdateButton && !$hasCompletedMonthlyUpdate): ?>
    <!-- Monthly Inventory Update Alert -->
    <div class="alert alert-warning animate__animated animate__pulse animate__infinite" style="border-radius: var(--radius-lg); box-shadow: var(--shadow-sm);">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-calendar-check me-2"></i> Monthly Inventory Update Required</h5>
                <p class="mb-0">Please update your IT inventory for the month of <?php echo date('F Y'); ?>.</p>
            </div>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal" style="border-radius: 50px;">
                <i class="fas fa-clipboard-check me-1"></i> Update Now
            </button>
        </div>
    </div>
    <?php elseif ($showMonthlyUpdateButton && $hasCompletedMonthlyUpdate): ?>
    <!-- Monthly Inventory Update Completed Alert -->
    <div class="alert alert-success" style="border-radius: var(--radius-lg); box-shadow: var(--shadow-sm);">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-check-circle me-2"></i> Monthly Inventory Update Completed</h5>
                <p class="mb-0">Thank you for completing your inventory update for <?php echo date('F Y'); ?>.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>


    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">HIMU EQUIPMENT</div>
                        <div class="stat-value counter-value"><?php echo number_format($itAssetsCount); ?></div>
                        <div class="stat-description mb-2">Total assets managed</div>
                        <div class="stat-trend">
                            <span class="text-primary"><i class="fas fa-map-marker-alt me-1"></i>Across <?php echo $itAssetsLocationCount; ?> locations</span>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-assets">
                        <i class="fas fa-laptop"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/assets/list.php" class="view-details text-success">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">HIMU SUPPLIES</div>
                        <div class="stat-value counter-value"><?php echo number_format($itSuppliesCount); ?></div>
                        <div class="stat-description mb-2">Consumables in inventory</div>
                        <div class="stat-trend">
                            <span class="text-success"><i class="fas fa-cubes me-1"></i>Total Qty: <?php echo number_format($itSuppliesQuantity); ?></span>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-inventory">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php" class="view-details text-primary">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">PENDING APPROVALS</div>
                        <div class="stat-value counter-value"><?php echo number_format($totalPendingApprovals); ?></div>
                        <div class="stat-description mb-2">Transfers awaiting approval</div>
                        <div class="stat-trend">
                            <?php if($totalPendingApprovals > 0): ?>
                            <span class="text-warning"><i class="fas fa-exclamation-circle me-1"></i>Needs attention</span>
                            <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>All transfers processed</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-warning">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/transfers/list.php?filter=1&status=Approved by Logistics" class="view-details text-warning">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">MAINTENANCE</div>
                        <div class="stat-value counter-value"><?php echo number_format($totalMaintenanceCount); ?></div>
                        <div class="stat-description mb-2">IT equipment needs attention</div>
                        <div class="stat-trend">
                            <?php if($defectiveEquipmentCount > 0): ?>
                            <span class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i><?php echo $defectiveEquipmentCount; ?> defective</span>
                            <?php endif; ?>
                            <?php if($defectiveEquipmentCount > 0 && $underRepairEquipmentCount > 0): ?>
                            <span class="mx-1">•</span>
                            <?php endif; ?>
                            <?php if($underRepairEquipmentCount > 0): ?>
                            <span class="text-primary"><i class="fas fa-tools me-1"></i><?php echo $underRepairEquipmentCount; ?> under repair</span>
                            <?php endif; ?>
                            <?php if($totalMaintenanceCount == 0): ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>No equipment needs attention</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-transfer">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/assets/maintenance.php" class="view-details text-primary">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- IT Equipment by Location Chart -->
        <div class="col-lg-12 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-1s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-map-marker-alt"></i> HIMU Equipment Distribution
                    </div>
                    <div class="chart-actions">
                        <a href="/choims/modules/reports/assets.php" class="action-btn">
                            <i class="fas fa-chart-bar"></i> View Full Report
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($itByLocationResult) > 0): ?>
                        <div class="chart-container" style="position: relative; height: 300px;">
                            <canvas id="itByLocationChart"></canvas>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-chart-bar"></i>
                            <p>No IT equipment distribution data available</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfers Section -->
    <div class="row">
        <!-- Transfers Waiting for Approval -->
        <div class="col-lg-6 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-2s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-clipboard-check"></i> Transfers Waiting for Approval
                    </div>
                    <a href="/choims/modules/transfers/list.php?filter=1&status=Approved by Logistics" class="action-btn">
                        <i class="fas fa-list"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Transfer</th>
                                    <th>Date</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($transfersWaitingResult && mysqli_num_rows($transfersWaitingResult) > 0): ?>
                                    <?php while ($transfer = mysqli_fetch_assoc($transfersWaitingResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'bg-success' : 'bg-primary'; ?> mb-1">
                                                        <?php echo $transfer['transfer_type']; ?>
                                                    </span>
                                                    <small class="text-muted">
                                                    <?php echo !empty($transfer['transaction_code']) ? $transfer['transaction_code'] : 'N/A'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($transfer['transfer_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['source_location']); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['destination_location']); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <?php if ($transfer['transfer_type'] == 'Individual'): ?>
                                                        <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-sm">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                        <a href="/choims/modules/transfers/approve.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-primary action-sm">
                                                            <i class="fas fa-check-circle"></i> Approve
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-sm">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                        <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-primary action-sm">
                                                            <i class="fas fa-check-circle"></i> Approve
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5">
                                            <div class="empty-state">
                                                <i class="fas fa-check-circle"></i>
                                                <p>No transfers waiting for approval</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transfers Waiting for Confirmation -->
        <div class="col-lg-6 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-2s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-truck-loading"></i> Transfers Waiting for Confirmation
                    </div>
                    <a href="/choims/modules/transfers/list.php?filter=1&status=Approved by HIMU" class="action-btn">
                        <i class="fas fa-list"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Transfer</th>
                                    <th>Date</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($transfersForAcceptanceResult && mysqli_num_rows($transfersForAcceptanceResult) > 0): ?>
                                    <?php while ($transfer = mysqli_fetch_assoc($transfersForAcceptanceResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <span class="badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'bg-success' : 'bg-primary'; ?> mb-1">
                                                        <?php echo $transfer['transfer_type']; ?>
                                                    </span>
                                                    <small class="text-muted">
                                                    <?php echo !empty($transfer['transaction_code']) ? $transfer['transaction_code'] : 'N/A'; ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td><?php echo date('M d, Y', strtotime($transfer['transfer_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['source_location']); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['destination_location']); ?></td>
                                            <td>
                                                <div class="btn-group">
                                                    <?php if ($transfer['transfer_type'] == 'Individual'): ?>
                                                        <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-sm">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                        <a href="/choims/modules/transfers/accept.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-primary action-sm">
                                                            <i class="fas fa-check-circle"></i> Accept
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-sm">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                        <a href="/choims/modules/transfers/batch/accept.php?id=<?php echo $transfer['id']; ?>" class="action-btn action-primary action-sm">
                                                            <i class="fas fa-check-circle"></i> Accept
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5">
                                            <div class="empty-state">
                                                <i class="fas fa-check-circle"></i>
                                                <p>No transfers waiting for confirmation</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Equipment Under Repair -->
        <div class="col-lg-6 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-3s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-tools"></i> Equipment Needing Attention
                        <?php if($defectiveEquipmentCount > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $defectiveEquipmentCount; ?> Defective</span>
                        <?php endif; ?>
                        <?php if($underRepairEquipmentCount > 0): ?>
                        <span class="badge bg-warning ms-2"><?php echo $underRepairEquipmentCount; ?> Under Repair</span>
                        <?php endif; ?>
                    </div>
                    <a href="/choims/modules/assets/maintenance.php" class="action-btn">
                        <i class="fas fa-tools"></i> Maintenance Management
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Asset</th>
                                    <th>Serial Number</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($equipmentRepairResult) > 0): ?>
                                    <?php while ($equipment = mysqli_fetch_assoc($equipmentRepairResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator <?php echo $equipment['status'] === 'Defective' ? 'critical-stock' : 'under-repair'; ?>"></span>
                                                    <span><?php echo $equipment['asset_name']; ?></span>
                                                </div>
                                                <small class="text-muted"><?php echo $equipment['sku_name']; ?></small>
                                            </td>
                                            <td><?php echo $equipment['serial_number'] ?: '<em class="text-muted">N/A</em>'; ?></td>
                                            <td><?php echo $equipment['location_name']; ?></td>
                                            <td>
                                                <span class="badge <?php echo $equipment['status'] === 'Defective' ? 'bg-danger' : 'bg-warning'; ?>">
                                                    <?php echo $equipment['status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <a href="/choims/modules/assets/view.php?id=<?php echo $equipment['asset_id']; ?>" class="action-btn action-sm">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <?php if ($equipment['status'] === 'Defective'): ?>
                                                <a href="/choims/modules/assets/maintenance.php" class="action-btn action-primary action-sm">
                                                    <i class="fas fa-tools"></i> Repair
                                                </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5">
                                            <div class="empty-state">
                                                <i class="fas fa-check-circle"></i>
                                                <p>No equipment currently needs attention</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock IT Supplies -->
        <div class="col-lg-6 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-3s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-box-open"></i> Inventory Items
                    </div>
                    <a href="/choims/modules/inventory/list.php" class="action-btn">
                        <i class="fas fa-boxes"></i> Manage Inventory
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Location Qty</th>
                                    <th>Total Qty</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($lowStockResult) > 0): ?>
                                    <?php while ($item = mysqli_fetch_assoc($lowStockResult)): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="status-indicator critical-stock"></span>
                                                    <span><?php echo $item['sku_name']; ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold <?php echo $item['current_quantity'] <= 5 ? 'text-danger' : 'text-warning'; ?>">
                                                    <?php echo $item['current_quantity']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-primary">
                                                    <?php echo $item['total_quantity']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $item['location_name']; ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4">
                                            <div class="empty-state">
                                                <i class="fas fa-check-circle"></i>
                                                <p>No inventory items found</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (mysqli_num_rows($itByLocationResult) > 0): ?>
    // Prepare data for IT by Location chart
    var locationLabels = [];
    var assetData = [];
    var backgroundColors = [
        'rgba(46, 125, 50, 0.7)',    // Primary green
        'rgba(76, 175, 80, 0.7)',    // Light green
        'rgba(139, 195, 74, 0.7)',   // Lighter green
        'rgba(25, 118, 210, 0.7)',   // Blue
        'rgba(66, 165, 245, 0.7)',   // Light blue
        'rgba(255, 152, 0, 0.7)'     // Orange
    ];

    var hoverBackgroundColors = [
        'rgba(46, 125, 50, 0.9)',    // Primary green
        'rgba(76, 175, 80, 0.9)',    // Light green
        'rgba(139, 195, 74, 0.9)',   // Lighter green
        'rgba(25, 118, 210, 0.9)',   // Blue
        'rgba(66, 165, 245, 0.9)',   // Light blue
        'rgba(255, 152, 0, 0.9)'     // Orange
    ];

    <?php
    mysqli_data_seek($itByLocationResult, 0);
    while ($row = mysqli_fetch_assoc($itByLocationResult)) {
        echo "locationLabels.push('" . addslashes($row['location_name']) . "');\n";
        echo "assetData.push(" . $row['asset_count'] . ");\n";
    }
    ?>

    // Create chart with animation
    var ctx = document.getElementById('itByLocationChart').getContext('2d');
    var itByLocationChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: locationLabels,
            datasets: [{
                label: 'HIMU Equipment Count',
                data: assetData,
                backgroundColor: backgroundColors,
                hoverBackgroundColor: hoverBackgroundColors,
                borderColor: 'white',
                borderWidth: 1,
                borderRadius: 12,
                maxBarThickness: 60
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        font: {
                            size: 12
                        },
                        padding: 20
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(46, 125, 50, 0.9)',
                    padding: 12,
                    cornerRadius: 8,
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 14
                    },
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y + ' IT equipment';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        },
                        maxRotation: 45,
                        minRotation: 45,
                        padding: 10,
                        callback: function(value) {
                            return value;
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12,
                            weight: 'bold'
                        },
                        padding: 8
                    }
                }
            }
        }
    });
    <?php endif; ?>

    // Enhanced inventory update modal interactions
    const confirmCheckbox = document.getElementById('confirmUpdate');
    const submitButton = document.getElementById('submitUpdate');
    const inventoryItemRows = document.querySelectorAll('.inventory-item-row');
    const quantityInputs = document.querySelectorAll('.quantity-input');

    // Enable/disable submit button based on checkbox with animation
    if (confirmCheckbox && submitButton) {
        confirmCheckbox.addEventListener('change', function() {
            if (this.checked) {
                submitButton.disabled = false;
                submitButton.classList.add('btn-complete-active');
            } else {
                submitButton.disabled = true;
                submitButton.classList.remove('btn-complete-active');
            }
        });
    }

    // Highlight changed quantity inputs
    if (quantityInputs.length > 0) {
        quantityInputs.forEach(input => {
            const originalValue = input.value;

            input.addEventListener('input', function() {
                if (this.value !== originalValue) {
                    this.classList.add('changed');
                } else {
                    this.classList.remove('changed');
                }
            });
        });
    }

    // Search functionality for inventory items
    const searchInput = document.getElementById('inventorySearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            inventoryItemRows.forEach(row => {
                const itemName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const skuCode = row.querySelector('.sku-code').textContent.toLowerCase();
                const category = row.querySelector('.category-name').textContent.toLowerCase();

                if (itemName.includes(searchTerm) || skuCode.includes(searchTerm) || category.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
});
</script>

<!-- Monthly Inventory Update Modal -->
<div class="modal fade" id="monthlyUpdateModal" tabindex="-1" aria-labelledby="monthlyUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content inventory-modal">
            <div class="modal-header inventory-modal-header">
                <div class="modal-title-wrapper">
                    <div class="modal-icon-circle">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h5 class="modal-title" id="monthlyUpdateModalLabel">
                        Monthly Inventory Update - <?php echo date('F Y'); ?>
                    </h5>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body inventory-modal-body">
                <?php if ($hasCompletedMonthlyUpdate): ?>
                    <div class="alert alert-success inventory-alert">
                        <div class="alert-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="alert-heading">Update Complete!</h6>
                            <p class="mb-0">You have already completed your monthly inventory update for <?php echo date('F Y'); ?>.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="inventory-intro">
                        <h5 class="inventory-intro-title">Time for your monthly inventory check</h5>
                        <p class="inventory-intro-text">
                            Please confirm that you have reviewed and updated your consumables inventory for the month of <?php echo date('F Y'); ?>.
                            This will help ensure accurate inventory tracking and planning.
                        </p>
                    </div>

                    <form action="/choims/modules/inventory/monthly_update.php" method="post" class="inventory-update-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="update_quantities" value="1">

                    <!-- Inventory Items to Update -->
                    <div class="inventory-card">
                        <div class="inventory-card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="inventory-card-title">
                                    <i class="fas fa-edit"></i>
                                    <span>Update Inventory Quantities</span>
                                </div>
                                <div class="inventory-search">
                                    <input type="text" id="inventorySearchInput" class="form-control form-control-sm" placeholder="Search items...">
                                </div>
                            </div>
                        </div>
                        <div class="inventory-card-body">
                            <div class="inventory-table-container">
                                <table class="inventory-table" id="inventoryItemsTable">
                                    <thead>
                                        <tr>
                                            <th>SKU Code</th>
                                            <th>Item Name</th>
                                            <th>Category</th>
                                            <th>Current Qty</th>
                                            <th>New Qty</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Get inventory items with special rules:
                                        // 1. All IT Supplies (category_id = 4) regardless of location
                                        // 2. Only Office Supplies (category_id = 5) and Medical Supplies (category_id = 6) in HIMU's location
                                        $itemsQuery = "
                                            SELECT
                                                i.inventory_id,
                                                i.current_quantity,
                                                i.status,
                                                s.sku_id,
                                                s.sku_code,
                                                s.sku_name,
                                                c.category_name
                                            FROM
                                                consumable_inventory i
                                            JOIN
                                                sku_master s ON i.sku_id = s.sku_id
                                            JOIN
                                                categories c ON s.category_id = c.category_id
                                            WHERE
                                                ((c.category_id = 4) OR (c.category_id IN (5, 6) AND i.location_id = ?)) AND
                                                (i.is_deleted = 0 OR i.is_deleted IS NULL)
                                            ORDER BY
                                                c.category_name ASC,
                                                s.sku_name ASC
                                        ";
                                        $itemsStmt = mysqli_prepare($conn, $itemsQuery);
                                        mysqli_stmt_bind_param($itemsStmt, 'i', $userLocationId);
                                        mysqli_stmt_execute($itemsStmt);
                                        $itemsResult = mysqli_stmt_get_result($itemsStmt);
                                        $rowCount = 0;

                                        while ($item = mysqli_fetch_assoc($itemsResult)):
                                            $rowCount++;
                                        ?>
                                        <tr class="inventory-item-row">
                                            <td><span class="sku-code"><?php echo $item['sku_code']; ?></span></td>
                                            <td><?php echo $item['sku_name']; ?></td>
                                            <td><span class="category-name"><?php echo $item['category_name']; ?></span></td>
                                            <td>
                                                <span class="current-qty"><?php echo $item['current_quantity']; ?></span>
                                            </td>
                                            <td>
                                                <input type="hidden" name="inventory_ids[]" value="<?php echo $item['inventory_id']; ?>">
                                                <input type="hidden" name="old_quantities[]" value="<?php echo $item['current_quantity']; ?>">
                                                <div class="quantity-input-wrapper">
                                                    <input type="number" name="new_quantities[]" class="quantity-input new-qty"
                                                           value="<?php echo $item['current_quantity']; ?>" min="0">
                                                </div>
                                            </td>
                                            <td>
                                                <span class="status-badge <?php echo strtolower(str_replace(' ', '-', $item['status'])); ?>">
                                                    <?php echo $item['status']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>

                                        <?php if ($rowCount === 0): ?>
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <div class="empty-state">
                                                    <i class="fas fa-box-open"></i>
                                                    <p>No inventory items found for your location.</p>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="inventory-tip">
                        <div class="tip-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="tip-content">
                            Update the quantities above to reflect your current inventory. Any changes will be recorded in the audit log.
                        </div>
                    </div>

                    <div class="form-group mt-4">
                        <label for="notes" class="form-label">Notes or Comments (Optional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Enter any notes about your inventory update..."></textarea>
                    </div>

                    <div class="form-check mt-4 mb-4">
                        <input class="form-check-input" type="checkbox" id="confirmUpdate" name="confirm">
                        <label class="form-check-label" for="confirmUpdate">
                            I confirm that I have reviewed and updated the inventory for <?php echo htmlspecialchars($_SESSION['location_name'] ?? 'HIMU'); ?> for the month of <?php echo date('F Y'); ?>.
                        </label>
                    </div>

                    <div class="form-actions">
                        <a href="/choims/modules/inventory/list.php" class="btn-review">
                            <i class="fas fa-list"></i> Review Inventory First
                        </a>
                        <button type="submit" class="btn-complete" id="submitUpdate" disabled>
                            <i class="fas fa-check-circle"></i> Complete Monthly Update
                        </button>
                    </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
/* Monthly Inventory Update Modal Styles */
.inventory-modal {
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: none;
}

.inventory-modal-header {
    background-color: var(--primary-green);
    color: white;
    padding: 1.25rem 1.5rem;
    border-bottom: none;
}

.modal-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-icon-circle i {
    font-size: 1.25rem;
    color: white;
}

.inventory-modal-body {
    padding: 1.5rem;
}

.inventory-intro-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: 0.5rem;
}

.inventory-intro-text {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.inventory-alert {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    border: none;
    background-color: rgba(46, 125, 50, 0.1);
    margin-bottom: 1.5rem;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-green);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.alert-icon i {
    font-size: 1.25rem;
    color: white;
}

.alert-content {
    flex-grow: 1;
}

.alert-heading {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: 0.25rem;
}

/* Inventory Card Styles */
.inventory-card {
    border-radius: var(--radius-lg);
    border: 1px solid rgba(0,0,0,0.05);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.inventory-card-header {
    padding: 1rem 1.25rem;
    background-color: rgba(0,0,0,0.02);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.inventory-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.inventory-card-title i {
    color: var(--primary-green);
}

.inventory-search {
    width: 200px;
}

.inventory-search input {
    border-radius: 50px;
    border: 1px solid rgba(0,0,0,0.1);
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.inventory-search input:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
    outline: none;
}

.inventory-card-body {
    padding: 0;
}

.inventory-table-container {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.inventory-table {
    width: 100%;
    border-collapse: collapse;
}

.inventory-table th {
    position: sticky;
    top: 0;
    background-color: white;
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--text-secondary);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    z-index: 1;
}

.inventory-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    vertical-align: middle;
}

.inventory-table tr:last-child td {
    border-bottom: none;
}

.inventory-table tr:hover {
    background-color: rgba(0,0,0,0.01);
}

.sku-code {
    font-family: monospace;
    font-size: 0.85rem;
    color: var(--text-secondary);
    background-color: rgba(0,0,0,0.03);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.category-name {
    font-size: 0.85rem;
    background-color: var(--green-bg-light);
    color: var(--primary-green);
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
}

.current-qty {
    font-weight: 600;
}

.quantity-input-wrapper {
    position: relative;
    width: 100px;
}

.quantity-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    text-align: center;
    font-weight: 600;
    transition: all 0.2s ease;
}

.quantity-input:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
    outline: none;
}

.quantity-input.changed {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.in-stock {
    background-color: var(--green-bg-light);
    color: var(--primary-green);
}

.status-badge.low-stock {
    background-color: var(--orange-bg-light);
    color: var(--orange);
}

.status-badge.out-of-stock {
    background-color: var(--red-bg-light);
    color: var(--red);
}

/* Inventory Tip */
.inventory-tip {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 1.25rem;
    background-color: var(--blue-bg-light);
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
}

.tip-icon {
    color: var(--blue);
    font-size: 1.25rem;
}

.tip-content {
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 1.5rem;
}

.btn-review {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: white;
    color: var(--text-primary);
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-review:hover {
    background-color: rgba(0,0,0,0.02);
    text-decoration: none;
    color: var(--text-primary);
}

.btn-complete {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-green);
    color: white;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.btn-complete:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-complete-active {
    opacity: 1;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
}

.btn-complete:not(:disabled):hover {
    opacity: 1;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
}
</style>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>