<?php
// Start a session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || 
    (strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode')) {
    $_SESSION['error'] = "You don't have permission to access this page.";
    header("Location: ../../index.php");
    exit();
}

// Log activity
logActivity($conn, 'Accessed SKU Migration Page', 'admin', null);

// Include header
include_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>SKU Migration Tool</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h4 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>Important Information</h4>
                        <p>This tool will migrate all existing SKUs to the new format: <strong>XX-0001</strong></p>
                        <p>Where:</p>
                        <ul>
                            <li><strong>XX</strong> is a 2-letter code representing the category:
                                <ul>
                                    <li>IT Equipment: <strong>IE</strong></li>
                                    <li>Office Equipment: <strong>OE</strong></li>
                                    <li>Medical Equipment: <strong>ME</strong></li>
                                    <li>IT Supply: <strong>IS</strong></li>
                                    <li>Office Supply: <strong>OS</strong></li>
                                    <li>Medical Supply: <strong>MS</strong></li>
                                </ul>
                            </li>
                            <li><strong>0001</strong> is a sequential number starting from 0001 for each category</li>
                        </ul>
                        <p>For example: <code>IE-0001</code>, <code>OE-0001</code>, <code>ME-0001</code>, etc.</p>
                        <hr>
                        <p class="mb-0"><strong>This change is permanent and will affect all existing records.</strong> Please make sure you have a backup of your database before proceeding.</p>
                    </div>

                    <div class="alert alert-info">
                        <h4 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Migration Process</h4>
                        <p>The migration will:</p>
                        <ol>
                            <li>Update all existing SKU codes in the system</li>
                            <li>Generate new SKU codes based on the category</li>
                            <li>Maintain relationships between assets and inventory items</li>
                        </ol>
                        <p>No data will be lost during this process, only the SKU codes will be changed.</p>
                    </div>
                    
                    <div class="alert alert-danger">
                        <h4 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>Similar SKU Handling</h4>
                        <p>We've detected some similar SKU codes in the system (e.g., "I001" and "I001-Laptop") that could cause conflicts during migration.</p>
                        <p><strong>RECOMMENDED ACTION:</strong> Use the "Merge Similar SKUs" tool below to clean up these duplicates before running the migration.</p>
                        <p>This will allow you to choose which of the similar SKUs to keep and update all references to the deleted SKUs.</p>
                        <hr>
                        <p>Alternatively, the improved migration tool provides special handling for similar SKUs by assigning suffixes to them (e.g., "IE-0001" and "IE-0001a"), but cleaning up before migration is preferable.</p>
                    </div>

                    <div class="d-grid gap-2 col-md-6 mx-auto mt-4">
                        <a href="../../database/merge_similar_skus.php?dry_run=1" class="btn btn-warning btn-lg mb-3">
                            <i class="fas fa-compress-alt me-2"></i>Merge Similar SKUs (Recommended First Step)
                        </a>
                        <a href="../../database/update_sku_format_improved.php?dry_run=1&fix_similar=1" class="btn btn-primary btn-lg">
                            <i class="fas fa-check-circle me-2"></i>Start Migration Process (Improved Version)
                        </a>
                        <a href="sku.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Return to SKU Management
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once '../../includes/footer.php';
?> 