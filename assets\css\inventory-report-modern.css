/* Modern Inventory Report Page Styling */
:root {
  /* Colors - <PERSON> Green Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --secondary: #81C784;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease forwards;
}

/* Dashboard Header */
.dashboard-header {
  position: relative;
  background: var(--white);
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 1.5rem 2rem 1rem;
  border-radius: 0 0 20px 20px;
  color: var(--dark);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border-bottom: 1px solid var(--primary-soft);
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.dashboard-header::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 60%);
  pointer-events: none;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 1;
}

.dashboard-header .title-container {
  display: flex;
  align-items: center;
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin: 0;
  letter-spacing: -0.025em;
}

.dashboard-title-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
}

.dashboard-title-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3), rgba(0, 0, 0, 0.05));
  pointer-events: none;
}

.dashboard-actions {
  display: flex;
  gap: 0.75rem;
}

.dashboard-date {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin: 0;
  position: relative;
  z-index: 1;
}

.dashboard-date i {
  margin-right: 0.5rem;
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 1.25rem 1.5rem 0.75rem;
  }

  .header-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .dashboard-actions {
    margin-top: 1rem;
    width: 100%;
    justify-content: space-between;
  }

  .dashboard-title {
    font-size: 1.5rem;
  }

  .dashboard-title-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Base Styles */
body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--gray-700);
  background-color: #f8f9fa;
  line-height: 1.5;
}

/* Page Container */
.container-fluid {
  padding: var(--space-5) var(--space-5);
  background-color: var(--light);
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-5);
  animation: fadeIn 0.5s ease;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.title-icon {
  color: var(--primary);
  margin-right: var(--space-2);
}

.action-buttons {
  display: flex;
  gap: var(--space-3);
}

/* Action Buttons */
.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.85rem 1.75rem;
  font-size: 0.95rem;
  font-weight: 600;
  line-height: 1.5;
  color: var(--white);
  background: var(--primary-gradient);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10px 20px rgba(46, 125, 50, 0.25), 0 6px 6px rgba(46, 125, 50, 0.22), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.action-btn:hover {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  transform: translateY(-3px);
  box-shadow: 0 15px 25px rgba(46, 125, 50, 0.3), 0 10px 10px rgba(46, 125, 50, 0.2), inset 0 -2px 5px rgba(0, 0, 0, 0.2);
  color: var(--white);
}

.action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(46, 125, 50, 0.3), inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.action-btn i {
  margin-right: 0.5rem;
  font-size: 0.9em;
}

.action-primary {
  background: var(--primary-ultra-soft);
  border: 1px solid var(--primary-soft);
  color: var(--primary);
  box-shadow: 0 2px 4px rgba(46, 125, 50, 0.1);
}

.action-primary:hover {
  background: var(--primary-bg);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(46, 125, 50, 0.15);
  color: var(--primary-dark);
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-success {
  background-color: var(--success);
  color: white;
}

.btn-success:hover {
  background-color: #0ca678;
}

.btn-info {
  background-color: var(--info);
  color: white;
}

.btn-info:hover {
  background-color: #2563eb;
}

.btn-warning {
  background-color: var(--warning);
  color: white;
}

.btn-warning:hover {
  background-color: #d97706;
}

.btn-outline-primary {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: white;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Records Card Styling */
.records-card {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.1), 0 6px 12px rgba(46, 125, 50, 0.08);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
  position: relative;
}

.records-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.records-card-header {
  padding: 1.5rem 1.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--primary-soft);
  background-color: rgba(46, 125, 50, 0.03);
  background-image: linear-gradient(135deg, rgba(46, 125, 50, 0.05), rgba(46, 125, 50, 0.01));
  position: relative;
  overflow: hidden;
}

.records-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.records-title i {
  margin-right: 0.85rem;
  color: var(--white);
  font-size: 1.2em;
  background: var(--primary-gradient);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(46, 125, 50, 0.25), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  transform: rotate(-5deg);
}

/* Cards */
.card {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-soft);
  box-shadow: var(--shadow);
  transition: var(--transition-fast);
  margin-bottom: var(--space-5);
  overflow: hidden;
  position: relative;
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-5px);
}

.card-header {
  background-color: rgba(46, 125, 50, 0.03);
  background-image: linear-gradient(135deg, rgba(46, 125, 50, 0.05), rgba(46, 125, 50, 0.01));
  border-bottom: 1px solid var(--primary-soft);
  padding: var(--space-4) var(--space-5);
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: var(--space-5);
  position: relative;
  z-index: 1;
}

/* Stat Cards */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.1), 0 6px 12px rgba(46, 125, 50, 0.08);
  overflow: hidden;
  border: 1px solid var(--primary-soft);
  position: relative;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(46, 125, 50, 0.15), 0 10px 15px rgba(46, 125, 50, 0.1);
}

.stat-primary {
  border-left: 4px solid var(--primary);
}

.stat-warning {
  border-left: 4px solid var(--warning);
}

.stat-info {
  border-left: 4px solid var(--info);
}

.stat-success {
  border-left: 4px solid var(--success);
}

.stat-danger {
  border-left: 4px solid var(--danger);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
}

.stat-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0.3), rgba(0, 0, 0, 0.05));
  pointer-events: none;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-label {
  color: var(--gray-600);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-card.primary .icon-circle {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--primary);
}

.stat-card.success .icon-circle {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-card.warning .icon-circle {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.stat-card.danger .icon-circle {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

/* Filter Card Styling */
.filter-card {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.1), 0 6px 12px rgba(46, 125, 50, 0.08);
  margin-bottom: 2rem;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
  position: relative;
}

.filter-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.filter-card-header {
  padding: 1.5rem 1.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--primary-soft);
  background-color: rgba(46, 125, 50, 0.03);
  background-image: linear-gradient(135deg, rgba(46, 125, 50, 0.05), rgba(46, 125, 50, 0.01));
  position: relative;
  overflow: hidden;
}

.filter-card-body {
  padding: 1.5rem 1.75rem;
  position: relative;
  z-index: 1;
}

.filter-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.4em 0.8em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  color: var(--white);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
  background: var(--primary-gradient);
  margin-left: 0.75rem;
  box-shadow: 0 3px 6px rgba(46, 125, 50, 0.2);
  position: relative;
  overflow: hidden;
}

/* Filter Section */
.filter-section {
  background-color: var(--white);
  background-image: linear-gradient(135deg, var(--white), var(--primary-ultra-soft));
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  box-shadow: var(--shadow);
  animation: slideInUp 0.5s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--primary-soft);
}

.filter-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  pointer-events: none;
}

.filter-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: center;
}

.filter-title i {
  margin-right: 0.85rem;
  color: var(--primary);
  font-size: 1.2em;
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  padding: 0.625rem 1rem;
  font-size: 0.95rem;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-label i {
  color: var(--primary);
}

.input-group {
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.input-group .form-control {
  border-right: none;
  box-shadow: none;
}

.input-group .btn {
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

/* Modern Table Styling */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
}

.modern-table thead th {
  padding: 1rem;
  font-size: 0.9rem;
  font-weight: 700;
  color: var(--primary-dark);
  background-color: var(--primary-ultra-soft);
  border-bottom: 2px solid var(--primary-soft);
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modern-table tbody td {
  padding: 1rem;
  font-size: 0.9rem;
  color: var(--dark);
  border-bottom: 1px solid var(--primary-soft);
  vertical-align: middle;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-ultra-soft);
}

/* Tables */
.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table th {
  font-weight: 600;
  color: var(--primary-dark);
  border-top: none;
  border-bottom: 2px solid var(--primary-soft);
  padding: var(--space-3) var(--space-4);
  background-color: var(--primary-ultra-soft);
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.05em;
}

.table th:first-child {
  border-top-left-radius: var(--radius-md);
}

.table th:last-child {
  border-top-right-radius: var(--radius-md);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-bottom: 1px solid var(--primary-soft);
  color: var(--dark);
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--primary-ultra-soft);
}

.table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-md);
}

.table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-md);
}

.table-responsive {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background-color: var(--white);
}

/* Status Badges */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.badge {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
}

.badge i {
  margin-right: 0.25rem;
}

.badge.bg-primary.bg-opacity-10 {
  background-color: rgba(46, 125, 50, 0.1) !important;
  color: var(--primary) !important;
}

.badge.bg-success.bg-opacity-10 {
  background-color: rgba(0, 200, 83, 0.1) !important;
  color: var(--success) !important;
}

.badge.bg-warning.bg-opacity-10 {
  background-color: rgba(255, 213, 79, 0.1) !important;
  color: var(--warning) !important;
}

.badge.bg-danger.bg-opacity-10 {
  background-color: rgba(255, 82, 82, 0.1) !important;
  color: var(--danger) !important;
}

.badge.bg-info.bg-opacity-10 {
  background-color: rgba(77, 208, 225, 0.1) !important;
  color: var(--info) !important;
}

.badge.bg-secondary.bg-opacity-10 {
  background-color: rgba(129, 199, 132, 0.1) !important;
  color: var(--secondary) !important;
}

.status-available {
  background-color: var(--primary-ultra-soft);
  color: var(--primary-dark);
}

.status-under-repair {
  background-color: rgba(255, 213, 79, 0.1);
  color: #FF8F00;
}

.status-disposed {
  background-color: rgba(255, 82, 82, 0.1);
  color: var(--danger);
}

/* View Toggle */
.view-toggle {
  background-color: var(--primary-ultra-soft);
  border-radius: var(--radius-full);
  padding: 0.25rem;
  display: inline-flex;
  margin-bottom: var(--space-4);
}

.view-toggle-btn {
  border: none;
  background: transparent;
  color: var(--primary-dark);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: var(--transition-fast);
}

.view-toggle-btn.active {
  background-color: var(--white);
  color: var(--primary);
  box-shadow: var(--shadow-sm);
}

.view-toggle-btn:hover:not(.active) {
  background-color: rgba(255, 255, 255, 0.5);
}

/* Charts */
.chart-container {
  position: relative;
  height: 300px;
  margin-top: var(--space-4);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-6) var(--space-4);
  text-align: center;
}

.empty-state i {
  font-size: 3rem;
  color: var(--gray-300);
  margin-bottom: var(--space-4);
}

.empty-state h5 {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.empty-state p {
  color: var(--gray-500);
  max-width: 400px;
  margin-bottom: var(--space-4);
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length select {
  border-radius: var(--radius);
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--gray-300);
}

.dataTables_wrapper .dataTables_filter input {
  border-radius: var(--radius);
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--gray-300);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: var(--radius);
  padding: 0.25rem 0.75rem;
  margin: 0 0.25rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background: var(--primary);
  border-color: var(--primary);
  color: white !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gray-100);
  border-color: var(--gray-300);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white !important;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .container-fluid {
    padding: var(--space-4);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-start;
  }

  .card-header, .card-body {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .page-title {
    font-size: 1.5rem;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .btn {
    padding: 0.5rem 1rem;
  }

  .filter-section {
    padding: var(--space-4);
  }

  .table td, .table th {
    padding: var(--space-2) var(--space-3);
  }
}

/* Print Styles */
@media print {
  .btn, .action-buttons, .dataTables_filter, .dataTables_length, .dataTables_paginate {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }

  .table th {
    background-color: var(--gray-100) !important;
    color: black !important;
  }

  .status-badge {
    border: 1px solid currentColor;
  }
}
