/* Health Center Dashboard - Compact Card Styling */

/* Make stat cards more compact */
.dashboard-card .card-body {
  padding: 1.25rem !important;
}

.stat-value {
  font-size: 1.75rem !important;
  margin-bottom: 0.15rem !important;
}

.stat-description {
  margin-bottom: 0.35rem !important;
  min-height: auto !important;
}

.dashboard-icon {
  width: 45px !important;
  height: 45px !important;
  font-size: 1.2rem !important;
}

.dashboard-card .card-footer {
  padding: 0.5rem 1rem !important;
}

/* Make chart cards more compact */
.chart-card .card-header {
  padding: 1rem 1.25rem !important;
}

.chart-card .card-body {
  padding: 1rem !important;
}

/* Set fixed height for empty cards */
.chart-card {
  height: auto !important;
}

.chart-card .card-body {
  max-height: 300px !important;
  overflow: auto;
}

.chart-card .empty-state {
  max-height: 120px !important;
  overflow: hidden;
}

/* Make tables more compact */
.table th, .table td {
  padding: 0.5rem 1rem !important;
}

/* Adjust empty state padding */
.empty-state {
  padding: 1rem !important;
  min-height: auto !important;
  max-height: 100px !important;
}

.empty-state i {
  font-size: 1.75rem !important;
  margin-bottom: 0.25rem !important;
}

.empty-state p {
  margin-bottom: 0 !important;
  font-size: 0.9rem !important;
}

/* Adjust notifications */
.notifications-body {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 0.75rem !important;
}

/* Adjust spacing */
.mb-4 {
  margin-bottom: 1rem !important;
}

/* Adjust stat trend */
.stat-trend {
  font-size: 0.8rem !important;
  min-height: auto !important;
}

/* Avatar for notifications */
.avatar-sm {
  width: 36px;
  height: 36px;
  font-size: 0.9rem;
}

/* Notification styling */
.notification-item {
  transition: all 0.2s ease;
}

.notification-item:hover {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Enhanced button styling */
.btn-sm {
  padding: 0.5rem 1rem !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  border-radius: 0.75rem !important;
  transition: all 0.3s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
}

.btn-sm i {
  margin-right: 0.5rem !important;
}

.btn-sm:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15) !important;
}

.btn-outline-success,
.btn-outline-primary,
.btn-outline-warning,
.btn-outline-info {
  color: #2E7D32 !important;
  background-color: rgba(46, 125, 50, 0.1) !important;
}

.btn-outline-success:hover,
.btn-outline-primary:hover,
.btn-outline-warning:hover,
.btn-outline-info:hover {
  color: white !important;
  background-color: #2E7D32 !important;
}

.btn-success {
  color: white !important;
  background-color: #2E7D32 !important;
}

.btn-success:hover {
  background-color: #388E3C !important;
}

/* Action buttons in tables */
.btn-group-sm .btn {
  padding: 0.4rem 0.75rem !important;
  font-size: 0.8rem !important;
}
