<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user is logged in and has superadmin role
requireLogin();
if (strtolower($_SESSION['role']) !== 'superadmin' && strtolower($_SESSION['role']) !== 'godmode') {
    // Redirect to unauthorized page
    $_SESSION['error'] = "You don't have permission to access this feature. Only superadmins can import assets.";
    header("Location: /choims/index.php");
    exit();
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Import Fixed Assets</h1>
        <a href="/choims/modules/assets/list.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to Assets
        </a>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> <?php echo $_SESSION['success']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $_SESSION['error']; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="row">
        <!-- Import Instructions Card -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Import Instructions</h6>
                </div>
                <div class="card-body">
                    <p>Follow these steps to import fixed assets into the system:</p>
                    <ol>
                        <li>Download the Excel template</li>
                        <li>Fill in all required fields (SKU Code, Asset Name, Status, Location)</li>
                        <li>Make sure all reference data (SKUs, Locations, etc.) exists in the system</li>
                        <li>Upload the completed Excel file</li>
                        <li>Review import results</li>
                    </ol>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i> <strong>Important:</strong> Make sure to use the correct format for dates (YYYY-MM-DD) and currency values.
                    </div>
                    
                    <div class="mt-3">
                        <a href="javascript:void(0);" onclick="openTemplateGenerator()" class="btn btn-primary">
                            <i class="fas fa-download me-2"></i> Get Excel Template
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upload Form Card -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Upload Excel File</h6>
                </div>
                <div class="card-body">
                    <form action="process_import.php" method="post" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="excelFile">Select Excel File</label>
                            <div class="custom-file">
                                <input type="file" name="excelFile" class="custom-file-input" id="excelFile" accept=".xlsx" required>
                                <label class="custom-file-label" for="excelFile">Choose file...</label>
                            </div>
                            <small class="form-text text-muted">Only .xlsx files are supported.</small>
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="confirmCheck" required>
                                <label class="form-check-label" for="confirmCheck">
                                    I confirm that all data in the Excel file is correct and formatted according to the template.
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success btn-block">
                            <i class="fas fa-upload me-2"></i> Upload and Import
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Reference Data Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Important Reference Information</h6>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs" id="referenceDataTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="status-tab" data-toggle="tab" href="#status" role="tab" aria-controls="status" aria-selected="true">Status Values</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="locations-tab" data-toggle="tab" href="#locations" role="tab" aria-controls="locations" aria-selected="false">Locations</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="skus-tab" data-toggle="tab" href="#skus" role="tab" aria-controls="skus" aria-selected="false">SKUs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="sources-tab" data-toggle="tab" href="#sources" role="tab" aria-controls="sources" aria-selected="false">Sources</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="suppliers-tab" data-toggle="tab" href="#suppliers" role="tab" aria-controls="suppliers" aria-selected="false">Suppliers</a>
                </li>
            </ul>
            <div class="tab-content" id="referenceDataTabContent">
                <div class="tab-pane fade show active" id="status" role="tabpanel" aria-labelledby="status-tab">
                    <div class="mt-3">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Valid Status Values</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Available</td>
                                    <td>Asset is available for use</td>
                                </tr>
                                <tr>
                                    <td>In use</td>
                                    <td>Asset is currently assigned and in use</td>
                                </tr>
                                <tr>
                                    <td>Under Repair</td>
                                    <td>Asset is being repaired</td>
                                </tr>
                                <tr>
                                    <td>Defective</td>
                                    <td>Asset is damaged or not functional</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="locations" role="tabpanel" aria-labelledby="locations-tab">
                    <div class="mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> For a complete list of locations, please use the template generator.
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="skus" role="tabpanel" aria-labelledby="skus-tab">
                    <div class="mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> For a complete list of SKUs, please use the template generator.
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="sources" role="tabpanel" aria-labelledby="sources-tab">
                    <div class="mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> For a complete list of sources, please use the template generator.
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="suppliers" role="tabpanel" aria-labelledby="suppliers-tab">
                    <div class="mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> For a complete list of suppliers, please use the template generator.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Update file input label with selected filename
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);
    });
});

// Function to open the template generator in a new window
function openTemplateGenerator() {
    window.open('/choims/assets_import_template.html', '_blank');
}
</script>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?> 