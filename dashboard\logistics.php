<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has Logistics permission
if (!$auth->hasRole(ROLE_LOGISTICS)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$currentUser = $auth->getCurrentUser();

try {
    // Fetch inventory statistics
    $stmt = $db->query("
        SELECT
            (SELECT COUNT(*) FROM fixed_assets) as total_assets,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'in_use') as assets_in_use,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'under_repair') as assets_under_repair,
            (SELECT COUNT(*) FROM fixed_assets WHERE status = 'defective') as defective_assets,
            (SELECT COUNT(DISTINCT item_id) FROM inventory WHERE quantity > 0) as consumables_in_stock,
            (SELECT COUNT(DISTINCT item_id) FROM inventory WHERE quantity <= minimum_stock) as low_stock_items,
            (SELECT COUNT(*) FROM transfer_requests WHERE status = 'pending' AND logistics_approval_status = 'pending') as pending_approvals
    ");
    $inventoryStats = $stmt->fetch();

    // Fetch pending transfers requiring logistics approval
    $stmt = $db->query("
        SELECT tr.*,
               i.name as item_name,
               CONCAT(u.first_name, ' ', u.last_name) as requester_name,
               sd.name as source_dept,
               shc.name as source_hc,
               dd.name as dest_dept,
               dhc.name as dest_hc,
               c.name as category_name
        FROM transfer_requests tr
        JOIN items i ON tr.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN users u ON tr.requested_by = u.user_id
        LEFT JOIN departments sd ON tr.source_department_id = sd.department_id
        LEFT JOIN health_centers shc ON tr.source_health_center_id = shc.health_center_id
        LEFT JOIN departments dd ON tr.destination_department_id = dd.department_id
        LEFT JOIN health_centers dhc ON tr.destination_health_center_id = dhc.health_center_id
        WHERE tr.status = 'pending'
        AND (
            (tr.requires_himu_approval = 0) OR
            (tr.requires_himu_approval = 1 AND tr.himu_approval_status = 'approved')
        )
        AND tr.logistics_approval_status = 'pending'
        ORDER BY tr.created_at DESC
        LIMIT 5
    ");
    $pendingTransfers = $stmt->fetchAll();

    // Fetch recent stock movements
    $stmt = $db->query("
        SELECT sm.*, i.name as item_name,
               COALESCE(d.name, hc.name) as location
        FROM stock_movements sm
        JOIN items i ON sm.item_id = i.item_id
        LEFT JOIN departments d ON sm.department_id = d.department_id
        LEFT JOIN health_centers hc ON sm.health_center_id = hc.health_center_id
        ORDER BY sm.created_at DESC
        LIMIT 5
    ");
    $recentMovements = $stmt->fetchAll();

    // Fetch low stock items
    $stmt = $db->query("
        SELECT i.name, inv.quantity, inv.minimum_stock,
               COALESCE(d.name, hc.name) as location,
               c.name as category_name
        FROM inventory inv
        JOIN items i ON inv.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        LEFT JOIN departments d ON inv.department_id = d.department_id
        LEFT JOIN health_centers hc ON inv.health_center_id = hc.health_center_id
        WHERE inv.quantity <= inv.minimum_stock
        ORDER BY inv.quantity ASC
        LIMIT 5
    ");
    $lowStockAlerts = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error in Logistics dashboard: " . $e->getMessage());
    setFlashMessage('error', 'Error loading dashboard data');
}

require_once '../templates/header.php';
?>

<!-- Include our modern button styling -->
<link rel="stylesheet" href="/choims/assets/css/logistics-dashboard-buttons.css">

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Logistics Dashboard</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Export Reports
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="../reports/inventory.php">Inventory Report</a></li>
                <li><a class="dropdown-item" href="../reports/transfers.php">Transfer Report</a></li>
                <li><a class="dropdown-item" href="../reports/stock-movement.php">Stock Movement Report</a></li>
            </ul>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="../inventory/fixed-assets/add.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Fixed Asset
                        </a>
                        <a href="../inventory/consumables/add.php" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add Consumable
                        </a>
                        <a href="../inventory/consumables/stock-in.php" class="btn btn-info">
                            <i class="fas fa-box"></i> Stock In
                        </a>
                        <a href="../inventory/consumables/stock-out.php" class="btn btn-warning">
                            <i class="fas fa-box-open"></i> Stock Out
                        </a>
                        <a href="../inventory/transfers/request.php" class="btn btn-secondary">
                            <i class="fas fa-exchange-alt"></i> New Transfer
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <!-- Fixed Assets Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Fixed Assets
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['total_assets']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-laptop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consumables Stats -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Consumables In Stock
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['consumables_in_stock']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Approvals -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Pending Approvals
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['pending_approvals']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($inventoryStats['low_stock_items']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Pending Transfers -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock mr-1"></i>
                        Pending Transfer Approvals
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingTransfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>">
                                            <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                    <td><?php echo htmlspecialchars($transfer['category_name']); ?></td>
                                    <td>
                                        <?php
                                        echo htmlspecialchars(isset($transfer['source_dept']) ? $transfer['source_dept'] : isset($transfer['source_hc']) ? $transfer['source_hc'] : 'N/A');
                                        ?>
                                    </td>
                                    <td>
                                        <?php
                                        echo htmlspecialchars(isset($transfer['dest_dept']) ? $transfer['dest_dept'] : isset($transfer['dest_hc']) ? $transfer['dest_hc'] : 'N/A');
                                        ?>
                                    </td>
                                    <td>
                                        <a href="../inventory/transfers/approve.php?id=<?php echo $transfer['transfer_id']; ?>"
                                           class="btn btn-primary btn-sm">
                                            Review
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/transfers/pending.php" class="btn btn-primary btn-sm">
                            View All Pending Transfers
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Stock Movements -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-exchange-alt mr-1"></i>
                        Recent Stock Movements
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Type</th>
                                    <th>Quantity</th>
                                    <th>Location</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentMovements as $movement): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($movement['item_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $movement['movement_type'] === 'in' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($movement['movement_type']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($movement['quantity']); ?></td>
                                    <td><?php echo htmlspecialchars($movement['location']); ?></td>
                                    <td><?php echo formatDate($movement['created_at'], 'M d, H:i'); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/stock-movements.php" class="btn btn-info btn-sm">
                            View All Stock Movements
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Alerts -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        Low Stock Alerts
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>Location</th>
                                    <th>Current Stock</th>
                                    <th>Min. Stock</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowStockAlerts as $alert): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($alert['name']); ?></td>
                                    <td><?php echo htmlspecialchars($alert['category_name']); ?></td>
                                    <td><?php echo htmlspecialchars($alert['location']); ?></td>
                                    <td class="text-danger"><?php echo number_format($alert['quantity']); ?></td>
                                    <td><?php echo number_format($alert['minimum_stock']); ?></td>
                                    <td>
                                        <a href="../inventory/consumables/stock-in.php?item_id=<?php echo $alert['item_id']; ?>"
                                           class="btn btn-success btn-sm">
                                            Stock In
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/consumables/low-stock.php" class="btn btn-danger btn-sm">
                            View All Low Stock Items
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../templates/footer.php'; ?>
