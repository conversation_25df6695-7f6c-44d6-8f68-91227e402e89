<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Check if user has admin permissions
$auth = Auth::getInstance();
if (!$auth->hasRole(ROLE_SUPERADMIN) && !$auth->hasRole(ROLE_GODMODE)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$db = Database::getInstance()->getConnection();
$message = '';
$users = [];

// Handle user actions: create, update, delete
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Create new user
        if ($_POST['action'] === 'create') {
            $username = isset($_POST['username']) ? trim($_POST['username']) : '';
            $password = isset($_POST['password']) ? $_POST['password'] : '';
            $fullName = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
            $email = isset($_POST['email']) ? trim($_POST['email']) : '';
            $role = isset($_POST['role']) ? $_POST['role'] : '';
            $departmentId = isset($_POST['department_id']) && !empty($_POST['department_id']) ? $_POST['department_id'] : null;
            $healthCenterId = isset($_POST['health_center_id']) && !empty($_POST['health_center_id']) ? $_POST['health_center_id'] : null;
            
            if (empty($username) || empty($password) || empty($fullName) || empty($email) || empty($role)) {
                $message = displayAlert('All required fields must be filled', 'danger');
            } else {
                try {
                    // Check if username already exists
                    $stmt = $db->prepare("SELECT user_id FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = displayAlert('Username already exists', 'danger');
                    } else {
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        
                        $stmt = $db->prepare("
                            INSERT INTO users (username, password, full_name, email, role, department_id, health_center_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())
                        ");
                        $stmt->execute([$username, $hashedPassword, $fullName, $email, $role, $departmentId, $healthCenterId]);
                        
                        $message = displayAlert('User created successfully', 'success');
                    }
                } catch (PDOException $e) {
                    $message = displayAlert('Error creating user: ' . $e->getMessage(), 'danger');
                }
            }
        }
        
        // Update existing user
        else if ($_POST['action'] === 'update' && isset($_POST['user_id'])) {
            $userId = $_POST['user_id'];
            $fullName = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
            $email = isset($_POST['email']) ? trim($_POST['email']) : '';
            $role = isset($_POST['role']) ? $_POST['role'] : '';
            $status = isset($_POST['status']) ? $_POST['status'] : '';
            $departmentId = isset($_POST['department_id']) && !empty($_POST['department_id']) ? $_POST['department_id'] : null;
            $healthCenterId = isset($_POST['health_center_id']) && !empty($_POST['health_center_id']) ? $_POST['health_center_id'] : null;
            
            try {
                // Start with basic fields
                $sql = "UPDATE users SET full_name = ?, email = ?, role = ?, status = ?, department_id = ?, health_center_id = ?";
                $params = [$fullName, $email, $role, $status, $departmentId, $healthCenterId];
                
                // Add password update if provided
                if (isset($_POST['password']) && !empty($_POST['password'])) {
                    $hashedPassword = password_hash($_POST['password'], PASSWORD_DEFAULT);
                    $sql .= ", password = ?";
                    $params[] = $hashedPassword;
                }
                
                $sql .= " WHERE user_id = ?";
                $params[] = $userId;
                
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                $message = displayAlert('User updated successfully', 'success');
            } catch (PDOException $e) {
                $message = displayAlert('Error updating user: ' . $e->getMessage(), 'danger');
            }
        }
        
        // Delete user
        else if ($_POST['action'] === 'delete' && isset($_POST['user_id'])) {
            $userId = $_POST['user_id'];
            
            try {
                // Soft delete by changing status
                $stmt = $db->prepare("UPDATE users SET status = 'inactive' WHERE user_id = ?");
                $stmt->execute([$userId]);
                
                $message = displayAlert('User deactivated successfully', 'success');
            } catch (PDOException $e) {
                $message = displayAlert('Error deactivating user: ' . $e->getMessage(), 'danger');
            }
        }
    }
}

// Initialize filters and pagination
$searchQuery = isset($_GET['search']) ? trim($_GET['search']) : '';
$roleFilter = isset($_GET['role']) ? $_GET['role'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Build query
$where = [];
$params = [];

if (!empty($searchQuery)) {
    $where[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
    $params[] = "%{$searchQuery}%";
    $params[] = "%{$searchQuery}%";
    $params[] = "%{$searchQuery}%";
}

if (!empty($roleFilter)) {
    $where[] = "role = ?";
    $params[] = $roleFilter;
}

if (!empty($statusFilter)) {
    $where[] = "status = ?";
    $params[] = $statusFilter;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// Get total users count for pagination
try {
    $countQuery = "SELECT COUNT(*) as total FROM users $whereClause";
    $stmt = $db->prepare($countQuery);
    $stmt->execute($params);
    $totalRecords = $stmt->fetch()['total'];
    $totalPages = ceil($totalRecords / $limit);
} catch (PDOException $e) {
    $message = displayAlert('Error counting users: ' . $e->getMessage(), 'danger');
    $totalRecords = 0;
    $totalPages = 1;
}

// Get list of users with pagination
try {
    $query = "
        SELECT u.*, 
               d.name as department_name, 
               h.name as health_center_name
        FROM users u
        LEFT JOIN departments d ON u.department_id = d.department_id
        LEFT JOIN health_centers h ON u.health_center_id = h.health_center_id
        $whereClause
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
    ";
    // Add pagination parameters
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
} catch (PDOException $e) {
    $message = displayAlert('Error fetching users: ' . $e->getMessage(), 'danger');
    $users = [];
}

// Get departments for dropdown
try {
    $stmt = $db->query("SELECT department_id, name FROM departments ORDER BY name");
    $departments = $stmt->fetchAll();
} catch (PDOException $e) {
    $departments = [];
}

// Get health centers for dropdown
try {
    $stmt = $db->query("SELECT health_center_id, name FROM health_centers ORDER BY name");
    $healthCenters = $stmt->fetchAll();
} catch (PDOException $e) {
    $healthCenters = [];
}

// Include header
require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">User Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
            <i class="fas fa-plus"></i> Add New User
        </button>
    </div>
    
    <?php echo $message; ?>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($searchQuery); ?>" 
                           placeholder="Search by username, name or email">
                </div>
                <div class="col-md-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-select" id="role" name="role">
                        <option value="">All Roles</option>
                        <option value="godmode" <?php echo $roleFilter === 'godmode' ? 'selected' : ''; ?>>GodMode</option>
                        <option value="superadmin" <?php echo $roleFilter === 'superadmin' ? 'selected' : ''; ?>>Superadmin</option>
                        <option value="logistics" <?php echo $roleFilter === 'logistics' ? 'selected' : ''; ?>>Logistics</option>
                        <option value="himu" <?php echo $roleFilter === 'himu' ? 'selected' : ''; ?>>HIMU</option>
                        <option value="department" <?php echo $roleFilter === 'department' ? 'selected' : ''; ?>>Department</option>
                        <option value="health_center" <?php echo $roleFilter === 'health_center' ? 'selected' : ''; ?>>Health Center</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="manage.php" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">User Accounts</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Department/Health Center</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="9" class="text-center">No users found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['user_id']; ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo getRoleBadgeColor($user['role']); ?>">
                                        <?php echo htmlspecialchars(getRoleName($user['role'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    if (!empty($user['department_name'])) {
                                        echo 'Dept: ' . htmlspecialchars($user['department_name']);
                                    } elseif (!empty($user['health_center_name'])) {
                                        echo 'HC: ' . htmlspecialchars($user['health_center_name']);
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                                        <?php echo ucfirst($user['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo formatDate($user['created_at']); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-info edit-user-btn" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editUserModal"
                                            data-user-id="<?php echo $user['user_id']; ?>"
                                            data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                            data-full-name="<?php echo htmlspecialchars($user['full_name']); ?>"
                                            data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                            data-role="<?php echo $user['role']; ?>"
                                            data-status="<?php echo $user['status']; ?>"
                                            data-department-id="<?php echo $user['department_id']; ?>"
                                            data-health-center-id="<?php echo $user['health_center_id']; ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php if ($user['status'] === 'active'): ?>
                                    <button class="btn btn-sm btn-danger delete-user-btn"
                                            data-bs-toggle="modal"
                                            data-bs-target="#deleteUserModal"
                                            data-user-id="<?php echo $user['user_id']; ?>"
                                            data-username="<?php echo htmlspecialchars($user['username']); ?>">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($searchQuery); ?>&role=<?php echo $roleFilter; ?>&status=<?php echo $statusFilter; ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchQuery); ?>&role=<?php echo $roleFilter; ?>&status=<?php echo $statusFilter; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($searchQuery); ?>&role=<?php echo $roleFilter; ?>&status=<?php echo $statusFilter; ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select Role</option>
                            <option value="<?php echo ROLE_GODMODE; ?>">Godmode</option>
                            <option value="<?php echo ROLE_SUPERADMIN; ?>">Superadmin</option>
                            <option value="<?php echo ROLE_LOGISTICS; ?>">Logistics</option>
                            <option value="<?php echo ROLE_HIMU; ?>">HIMU</option>
                            <option value="<?php echo ROLE_DEPARTMENT; ?>">Department</option>
                            <option value="<?php echo ROLE_HEALTH_CENTER; ?>">Health Center</option>
                        </select>
                    </div>
                    
                    <div class="mb-3 department-field">
                        <label for="department_id" class="form-label">Department</label>
                        <select class="form-select" id="department_id" name="department_id">
                            <option value="">Select Department</option>
                            <?php foreach ($departments as $dept): ?>
                            <option value="<?php echo $dept['department_id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3 health-center-field">
                        <label for="health_center_id" class="form-label">Health Center</label>
                        <select class="form-select" id="health_center_id" name="health_center_id">
                            <option value="">Select Health Center</option>
                            <?php foreach ($healthCenters as $center): ?>
                            <option value="<?php echo $center['health_center_id']; ?>"><?php echo htmlspecialchars($center['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" id="edit_user_id" name="user_id">
                    
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="<?php echo ROLE_GODMODE; ?>">Godmode</option>
                            <option value="<?php echo ROLE_SUPERADMIN; ?>">Superadmin</option>
                            <option value="<?php echo ROLE_LOGISTICS; ?>">Logistics</option>
                            <option value="<?php echo ROLE_HIMU; ?>">HIMU</option>
                            <option value="<?php echo ROLE_DEPARTMENT; ?>">Department</option>
                            <option value="<?php echo ROLE_HEALTH_CENTER; ?>">Health Center</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    
                    <div class="mb-3 edit-department-field">
                        <label for="edit_department_id" class="form-label">Department</label>
                        <select class="form-select" id="edit_department_id" name="department_id">
                            <option value="">Select Department</option>
                            <?php foreach ($departments as $dept): ?>
                            <option value="<?php echo $dept['department_id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3 edit-health-center-field">
                        <label for="edit_health_center_id" class="form-label">Health Center</label>
                        <select class="form-select" id="edit_health_center_id" name="health_center_id">
                            <option value="">Select Health Center</option>
                            <?php foreach ($healthCenters as $center): ?>
                            <option value="<?php echo $center['health_center_id']; ?>"><?php echo htmlspecialchars($center['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">Deactivate User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactivate this user? They will no longer be able to log in.</p>
                <p>Username: <strong id="delete_username"></strong></p>
            </div>
            <form method="post">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" id="delete_user_id" name="user_id">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Deactivate User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#usersTable').DataTable();
    
    // Show/hide department/health center fields based on role selection
    const toggleLocationFields = function(roleSelect, deptField, hcField) {
        const showDept = roleSelect.value === '<?php echo ROLE_DEPARTMENT; ?>';
        const showHC = roleSelect.value === '<?php echo ROLE_HEALTH_CENTER; ?>';
        
        deptField.style.display = showDept ? 'block' : 'none';
        hcField.style.display = showHC ? 'block' : 'none';
        
        // Clear the other field if not needed
        if (showDept) {
            document.querySelector(hcField.querySelector('select').id).value = '';
        }
        if (showHC) {
            document.querySelector(deptField.querySelector('select').id).value = '';
        }
    };
    
    // Create user form role change
    const createRoleSelect = document.getElementById('role');
    const createDeptField = document.querySelector('.department-field');
    const createHCField = document.querySelector('.health-center-field');
    
    createRoleSelect.addEventListener('change', function() {
        toggleLocationFields(this, createDeptField, createHCField);
    });
    
    // Edit user form setup
    const editUserBtns = document.querySelectorAll('.edit-user-btn');
    editUserBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            const username = this.getAttribute('data-username');
            const fullName = this.getAttribute('data-full-name');
            const email = this.getAttribute('data-email');
            const role = this.getAttribute('data-role');
            const status = this.getAttribute('data-status');
            const departmentId = this.getAttribute('data-department-id');
            const healthCenterId = this.getAttribute('data-health-center-id');
            
            document.getElementById('edit_user_id').value = userId;
            document.getElementById('edit_username').value = username;
            document.getElementById('edit_full_name').value = fullName;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_role').value = role;
            document.getElementById('edit_status').value = status;
            
            if (departmentId) {
                document.getElementById('edit_department_id').value = departmentId;
            }
            if (healthCenterId) {
                document.getElementById('edit_health_center_id').value = healthCenterId;
            }
            
            // Show/hide appropriate fields
            const editRoleSelect = document.getElementById('edit_role');
            const editDeptField = document.querySelector('.edit-department-field');
            const editHCField = document.querySelector('.edit-health-center-field');
            
            toggleLocationFields(editRoleSelect, editDeptField, editHCField);
        });
    });
    
    // Delete user confirmation
    const deleteUserBtns = document.querySelectorAll('.delete-user-btn');
    deleteUserBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            const username = this.getAttribute('data-username');
            
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_username').textContent = username;
        });
    });
});
</script>

<?php
// Helper functions
function getRoleBadgeColor($role) {
    switch ($role) {
        case ROLE_GODMODE:
            return 'danger';
        case ROLE_SUPERADMIN:
            return 'primary';
        case ROLE_LOGISTICS:
            return 'warning';
        case ROLE_HIMU:
            return 'info';
        case ROLE_DEPARTMENT:
            return 'success';
        case ROLE_HEALTH_CENTER:
            return 'secondary';
        default:
            return 'light';
    }
}

function getRoleName($role) {
    switch ($role) {
        case ROLE_GODMODE:
            return 'Godmode';
        case ROLE_SUPERADMIN:
            return 'Superadmin';
        case ROLE_LOGISTICS:
            return 'Logistics';
        case ROLE_HIMU:
            return 'HIMU';
        case ROLE_DEPARTMENT:
            return 'Department';
        case ROLE_HEALTH_CENTER:
            return 'Health Center';
        default:
            return 'Unknown';
    }
}

function displayAlert($message, $type = 'info') {
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
}

require_once '../../templates/footer.php';
?> 