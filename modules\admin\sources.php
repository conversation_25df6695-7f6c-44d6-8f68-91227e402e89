<?php
session_start();
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has superadmin role
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || ($_SESSION['role'] !== 'superadmin' && $_SESSION['role'] !== 'godmode')) {
    $_SESSION['error'] = "You don't have permission to access this page.";
    header("Location: ../../index.php");
    exit();
}

// Log activity
logActivity($_SESSION['user_id'], 'Accessed Sources Management Page');

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_source'])) {
        $source_name = trim($_POST['source_name']);
        $description = trim($_POST['description']);
        
        // Validation
        if (empty($source_name)) {
            $_SESSION['error'] = "Source name is required.";
        } else {
            // Check if source name already exists
            $check_sql = "SELECT source_id FROM sources WHERE source_name = ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("s", $source_name);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            
            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Source with this name already exists.";
            } else {
                // Insert new source
                $sql = "INSERT INTO sources (source_name, description) VALUES (?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ss", $source_name, $description);
                
                if ($stmt->execute()) {
                    $_SESSION['success'] = "Source added successfully.";
                    logActivity($_SESSION['user_id'], 'Added new source: ' . $source_name);
                } else {
                    $_SESSION['error'] = "Error adding source: " . $conn->error;
                }
                
                $stmt->close();
            }
            $check_stmt->close();
        }
    } elseif (isset($_POST['edit_source'])) {
        $source_id = $_POST['source_id'];
        $source_name = trim($_POST['source_name']);
        $description = trim($_POST['description']);
        
        // Validation
        if (empty($source_name)) {
            $_SESSION['error'] = "Source name is required.";
        } else {
            // Check if source name already exists (excluding current source)
            $check_sql = "SELECT source_id FROM sources WHERE source_name = ? AND source_id != ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("si", $source_name, $source_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            
            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Source with this name already exists.";
            } else {
                // Update source
                $sql = "UPDATE sources SET source_name = ?, description = ? WHERE source_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssi", $source_name, $description, $source_id);
                
                if ($stmt->execute()) {
                    $_SESSION['success'] = "Source updated successfully.";
                    logActivity($_SESSION['user_id'], 'Updated source: ' . $source_name);
                } else {
                    $_SESSION['error'] = "Error updating source: " . $conn->error;
                }
                
                $stmt->close();
            }
            $check_stmt->close();
        }
    } elseif (isset($_POST['delete_source'])) {
        $source_id = $_POST['source_id'];
        
        // Check if the source is in use
        $check_sql = "SELECT COUNT(*) as count FROM fixed_assets WHERE source_id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $source_id);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $count = $result->fetch_assoc()['count'];
        
        if ($count > 0) {
            $_SESSION['error'] = "Cannot delete source because it is being used by assets.";
        } else {
            // Get source name for logging before deletion
            $name_sql = "SELECT source_name FROM sources WHERE source_id = ?";
            $name_stmt = $conn->prepare($name_sql);
            $name_stmt->bind_param("i", $source_id);
            $name_stmt->execute();
            $name_result = $name_stmt->get_result();
            $source_name = $name_result->fetch_assoc()['source_name'];
            $name_stmt->close();
            
            // Delete source
            $sql = "DELETE FROM sources WHERE source_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $source_id);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = "Source deleted successfully.";
                logActivity($_SESSION['user_id'], 'Deleted source: ' . $source_name);
            } else {
                $_SESSION['error'] = "Error deleting source: " . $conn->error;
            }
            
            $stmt->close();
        }
        $check_stmt->close();
    }
    
    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Get all sources
$sql = "SELECT s.*, IFNULL(a.asset_count, 0) as asset_count 
        FROM sources s 
        LEFT JOIN (
            SELECT source_id, COUNT(*) as asset_count 
            FROM fixed_assets 
            WHERE source_id IS NOT NULL 
            GROUP BY source_id
        ) a ON s.source_id = a.source_id 
        ORDER BY s.source_name ASC";
$result = $conn->query($sql);
$sources = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $sources[] = $row;
    }
}

// Include header
include_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-hand-holding-usd me-2"></i>Sources Management</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['success'])): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error'])): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <button type="button" class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                        <i class="fas fa-plus-circle me-1"></i> Add Source
                    </button>

                    <div class="table-responsive">
                        <table id="sourcesTable" class="table table-bordered table-striped">
                            <thead class="bg-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Source Name</th>
                                    <th>Description</th>
                                    <th>Assets Count</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sources as $source): ?>
                                <tr>
                                    <td><?php echo $source['source_id']; ?></td>
                                    <td><?php echo htmlspecialchars($source['source_name']); ?></td>
                                    <td><?php echo htmlspecialchars(isset($source['description']) ? $source['description'] : ''); ?></td>
                                    <td>
                                        <?php if ($source['asset_count'] > 0): ?>
                                            <span class="badge bg-info"><?php echo $source['asset_count']; ?> asset(s)</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No assets</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('M d, Y h:i A', strtotime($source['created_at'])); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning edit-source" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editSourceModal"
                                                data-id="<?php echo $source['source_id']; ?>"
                                                data-name="<?php echo htmlspecialchars($source['source_name']); ?>"
                                                data-description="<?php echo htmlspecialchars(isset($source['description']) ? $source['description'] : ''); ?>">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        
                                        <?php if ($source['asset_count'] == 0): ?>
                                        <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this source? This action cannot be undone.');">
                                            <input type="hidden" name="source_id" value="<?php echo $source['source_id']; ?>">
                                            <button type="submit" name="delete_source" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash-alt"></i> Delete
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Source Modal -->
<div class="modal fade" id="addSourceModal" tabindex="-1" aria-labelledby="addSourceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addSourceModalLabel"><i class="fas fa-plus-circle me-2"></i>Add New Source</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="source_name" class="form-label">Source Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="source_name" name="source_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="add_source" class="btn btn-primary">Add Source</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Source Modal -->
<div class="modal fade" id="editSourceModal" tabindex="-1" aria-labelledby="editSourceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="editSourceModalLabel"><i class="fas fa-edit me-2"></i>Edit Source</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_source_id" name="source_id">
                    <div class="mb-3">
                        <label for="edit_source_name" class="form-label">Source Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_source_name" name="source_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="edit_source" class="btn btn-warning">Update Source</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    $('#sourcesTable').DataTable({
        order: [[1, 'asc']],
        pageLength: 10,
        responsive: true
    });
    
    // Handle edit source modal data
    const editButtons = document.querySelectorAll('.edit-source');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const description = this.getAttribute('data-description');
            
            document.getElementById('edit_source_id').value = id;
            document.getElementById('edit_source_name').value = name;
            document.getElementById('edit_description').value = description;
        });
    });
});
</script>

<?php
// Include footer
include_once '../../includes/footer.php';
?> 