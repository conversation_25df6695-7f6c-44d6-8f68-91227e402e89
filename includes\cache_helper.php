<?php
/**
 * Query Cache Helper Class
 * 
 * Provides simple caching mechanism for frequently executed queries to improve performance.
 * This reduces database load for common operations like fetching lookup data that rarely changes.
 */

class QueryCache {
    // In-memory cache storage
    private static $cache = [];
    
    // Cache TTL in seconds (default: 5 minutes)
    private static $defaultTTL = 300;
    
    /**
     * Get data from cache or execute query if not cached
     * 
     * @param string $key Cache key
     * @param callable $queryCallback Function to execute if cache miss
     * @param int $ttl Time to live in seconds
     * @return mixed Cached or freshly retrieved data
     */
    public static function get($key, $queryCallback, $ttl = null) {
        if ($ttl === null) {
            $ttl = self::$defaultTTL;
        }
        
        // Check if data exists in cache and is not expired
        if (isset(self::$cache[$key]) && self::$cache[$key]['expires'] > time()) {
            return self::$cache[$key]['data'];
        }
        
        // Cache miss or expired, execute query callback
        $data = $queryCallback();
        
        // Store in cache
        self::$cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
        
        return $data;
    }
    
    /**
     * Force refresh of cached data
     * 
     * @param string $key Cache key
     * @param callable $queryCallback Function to execute
     * @param int $ttl Time to live in seconds
     * @return mixed Fresh data
     */
    public static function refresh($key, $queryCallback, $ttl = null) {
        if ($ttl === null) {
            $ttl = self::$defaultTTL;
        }
        
        // Execute query callback
        $data = $queryCallback();
        
        // Store in cache
        self::$cache[$key] = [
            'data' => $data,
            'expires' => time() + $ttl
        ];
        
        return $data;
    }
    
    /**
     * Check if a key exists in the cache and is not expired
     * 
     * @param string $key Cache key
     * @return bool True if valid cache entry exists
     */
    public static function has($key) {
        return isset(self::$cache[$key]) && self::$cache[$key]['expires'] > time();
    }
    
    /**
     * Remove a specific key from cache
     * 
     * @param string $key Cache key
     * @return bool True if removed, false if not found
     */
    public static function remove($key) {
        if (isset(self::$cache[$key])) {
            unset(self::$cache[$key]);
            return true;
        }
        return false;
    }
    
    /**
     * Clear all cached data
     */
    public static function clear() {
        self::$cache = [];
    }
    
    /**
     * Clear expired cache entries
     * 
     * @return int Number of cleared entries
     */
    public static function clearExpired() {
        $count = 0;
        $now = time();
        
        foreach (self::$cache as $key => $entry) {
            if ($entry['expires'] <= $now) {
                unset(self::$cache[$key]);
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * Get item count in cache
     * 
     * @return int Number of items in cache
     */
    public static function count() {
        return count(self::$cache);
    }
    
    /**
     * Set the default TTL for cache entries
     * 
     * @param int $seconds Time to live in seconds
     */
    public static function setDefaultTTL($seconds) {
        self::$defaultTTL = $seconds;
    }
}

/**
 * Helper function to get cached query results
 * 
 * @param string $key Cache key
 * @param callable $queryCallback Function to execute if cache miss
 * @param int $ttl Time to live in seconds
 * @return mixed Cached or freshly retrieved data
 */
function getCachedQuery($key, $queryCallback, $ttl = null) {
    return QueryCache::get($key, $queryCallback, $ttl);
}

/**
 * Helper function to invalidate a cached query
 * 
 * @param string $key Cache key
 * @return bool True if removed, false if not found
 */
function invalidateCache($key) {
    return QueryCache::remove($key);
}

/**
 * Example usage:
 * 
 * // Instead of directly querying:
 * // $result = mysqli_query($conn, "SELECT * FROM locations");
 * // $locations = mysqli_fetch_all($result, MYSQLI_ASSOC);
 * 
 * // Use cache:
 * $locations = getCachedQuery('all_locations', function() use ($conn) {
 *     $result = mysqli_query($conn, "SELECT * FROM locations");
 *     return mysqli_fetch_all($result, MYSQLI_ASSOC);
 * }, 3600); // Cache for 1 hour
 * 
 * // After adding a new location or updating location data:
 * invalidateCache('all_locations');
 */ 