<?php
// Required database connection and sanitization functions
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/config/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Set content type to JSON
header('Content-Type: application/json');

// Ensure we have both required parameters
if (!isset($_GET['category_id']) || empty($_GET['category_id'])) {
    echo json_encode(['error' => 'Category ID is required']);
    exit;
}

// Get and sanitize input
$category_id = sanitizeInput($_GET['category_id']);
$item_type = isset($_GET['item_type']) ? sanitizeInput($_GET['item_type']) : null;

// Build query based on whether item_type is provided
if ($item_type) {
    $query = "SELECT * FROM sku_master WHERE category_id = ? AND item_type = ? ORDER BY sku_name";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'is', $category_id, $item_type);
} else {
    $query = "SELECT * FROM sku_master WHERE category_id = ? ORDER BY sku_name";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $category_id);
}

// Execute query
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Fetch results
$skus = [];
while ($row = mysqli_fetch_assoc($result)) {
    $skus[] = [
        'sku_id' => $row['sku_id'],
        'sku_code' => $row['sku_code'],
        'sku_name' => $row['sku_name'],
        'category_id' => $row['category_id'],
        'item_type' => $row['item_type']
    ];
}

// Return JSON data
echo json_encode($skus);
?> 