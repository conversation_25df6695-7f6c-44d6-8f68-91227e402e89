<?php
// This file is a dedicated endpoint for retrieving user passwords
// Only accessible by superadmins

// Enable error logging but prevent display
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Log access to this file for debugging
error_log('Password retrieval endpoint accessed');

// Use relative paths
$base_path = dirname(dirname(dirname(__FILE__)));

// Include necessary files
require_once($base_path . '/includes/functions.php');
require_once($base_path . '/includes/database.php');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is authorized - be more flexible with role check
if (!isset($_SESSION['role'])) {
    echo json_encode(['error' => 'Not logged in']);
    exit;
}

// Accept any admin role (case-insensitive)
$role = strtolower($_SESSION['role']);
if ($role !== 'superadmin' && $role !== 'godmode' && $role !== 'admin') {
    echo json_encode(['error' => 'Unauthorized access. Role: ' . $_SESSION['role']]);
    exit;
}

// Log session data for debugging
error_log('Session data: ' . json_encode($_SESSION));

// Check if user_id is provided
if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
    echo json_encode(['error' => 'User ID is required']);
    exit;
}

$user_id = (int)$_GET['user_id'];

// Connect to database
try {
    $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);

    if (!$conn) {
        throw new Exception("Database connection failed: " . mysqli_connect_error());
    }

    // Get the password from the database
    $query = "SELECT password FROM users WHERE user_id = ?";
    $stmt = mysqli_prepare($conn, $query);

    if (!$stmt) {
        throw new Exception("Prepare failed: " . mysqli_error($conn));
    }

    mysqli_stmt_bind_param($stmt, 'i', $user_id);

    if (!mysqli_stmt_execute($stmt)) {
        throw new Exception("Execute failed: " . mysqli_stmt_error($stmt));
    }

    $result = mysqli_stmt_get_result($stmt);

    if (!$result) {
        throw new Exception("Get result failed: " . mysqli_stmt_error($stmt));
    }

    $row = mysqli_fetch_assoc($result);

    if (!$row) {
        echo json_encode(['error' => 'User not found']);
        exit;
    }

    // Get the password from the database
    $password = $row['password'];

    // Check if the password is hashed (starts with $2y$)
    $is_hashed = (strpos($password, '$2y$') === 0);

    // For plain text display, we'll just return the password as is
    // This is what the user requested - to see passwords in plain text

    // Return the password
    echo json_encode([
        'password' => $password,
        'is_hashed' => $is_hashed,
        'plain_text' => true
    ]);

} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($conn) && $conn) {
        mysqli_close($conn);
    }
}
?>
