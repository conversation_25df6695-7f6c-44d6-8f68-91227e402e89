<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to edit maintenance records for office and medical equipment.');
    
    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else if (hasRole('HIMU')) {
        header('Location: /choims/dashboards/himu.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('error', 'Invalid request. Maintenance record ID is required.');
    header('Location: /choims/modules/logistics/maintenance.php');
    exit;
}

$record_id = sanitizeInput($_GET['id']);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $status = sanitizeInput($_POST['status']);
    
    // Validate status
    $validStatuses = ['Scheduled', 'In Progress', 'Completed', 'Cancelled'];
    if (!in_array($status, $validStatuses)) {
        setFlashMessage('error', 'Invalid status value.');
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;
    }
    
    // Start transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Update maintenance record - DIRECT UPDATE WITHOUT CATEGORY RESTRICTION
        $updateQuery = "
            UPDATE maintenance_records
            SET status = ?, updated_at = NOW()
            WHERE record_id = ?
        ";
        
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'si', $status, $record_id);
        $updateResult = mysqli_stmt_execute($updateStmt);
        
        if (!$updateResult) {
            throw new Exception("Failed to update maintenance record: " . mysqli_error($conn));
        }
        
        // Get affected rows
        $affectedRows = mysqli_stmt_affected_rows($updateStmt);
        
        if ($affectedRows === 0) {
            throw new Exception("No rows were updated. The record may not exist.");
        }
        
        // Get asset ID for the maintenance record
        $assetQuery = "SELECT asset_id FROM maintenance_records WHERE record_id = ?";
        $assetStmt = mysqli_prepare($conn, $assetQuery);
        mysqli_stmt_bind_param($assetStmt, 'i', $record_id);
        mysqli_stmt_execute($assetStmt);
        $assetResult = mysqli_stmt_get_result($assetStmt);
        
        if (mysqli_num_rows($assetResult) === 0) {
            throw new Exception("Could not find asset for maintenance record.");
        }
        
        $assetRow = mysqli_fetch_assoc($assetResult);
        $asset_id = $assetRow['asset_id'];
        
        // Update asset status based on maintenance status
        $assetStatus = 'Available'; // Default
        
        if ($status === 'In Progress') {
            $assetStatus = 'Under Repair';
        } else if ($status === 'Scheduled') {
            $assetStatus = 'Under Repair';
        }
        
        $updateAssetQuery = "
            UPDATE fixed_assets
            SET status = ?, updated_at = NOW()
            WHERE asset_id = ?
        ";
        
        $updateAssetStmt = mysqli_prepare($conn, $updateAssetQuery);
        mysqli_stmt_bind_param($updateAssetStmt, 'si', $assetStatus, $asset_id);
        $updateAssetResult = mysqli_stmt_execute($updateAssetStmt);
        
        if (!$updateAssetResult) {
            throw new Exception("Failed to update asset status: " . mysqli_error($conn));
        }
        
        // Commit transaction
        mysqli_commit($conn);
        
        // Set success message
        setFlashMessage('success', 'Maintenance record status updated successfully to ' . $status);
        
        // Redirect to maintenance page
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        
        // Set error message
        setFlashMessage('error', $e->getMessage());
        
        // Redirect to maintenance page
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;
    }
}

// If not a POST request, show a simple form
?>

<div class="container mt-5">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3>Direct Status Update for Maintenance Record #<?php echo $record_id; ?></h3>
        </div>
        <div class="card-body">
            <form action="/choims/modules/logistics/direct_update.php?id=<?php echo $record_id; ?>" method="post">
                <div class="form-group mb-4">
                    <label for="status" class="form-label">New Status:</label>
                    <select class="form-select" id="status" name="status" required>
                        <option value="Scheduled">Scheduled</option>
                        <option value="In Progress">In Progress</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="/choims/modules/logistics/maintenance.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Maintenance
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>
