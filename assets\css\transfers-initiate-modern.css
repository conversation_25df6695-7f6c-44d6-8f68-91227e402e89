/* Modern Transfers Initiate <PERSON> Styling */
:root {
  /* Colors - <PERSON> <PERSON> Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --primary-soft: #C8E6C9;
  --primary-ultra-soft: #F1F8E9;
  --primary-gradient: linear-gradient(135deg, #4CAF50, #2E7D32);
  --secondary: #81C784;
  --secondary-light: #A5D6A7;
  --secondary-dark: #66BB6A;
  --success: #00C853;
  --warning: #FFD54F;
  --danger: #FF5252;
  --info: #4DD0E1;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.25rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Page Header */
.page-header {
  margin-bottom: var(--space-5);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.page-title i {
  color: var(--primary);
  font-size: 1.75rem;
}

.page-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-bottom: 0;
}

/* Card Styling */
.card {
  border: none;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  transition: var(--transition-fast);
  overflow: hidden;
  margin-bottom: var(--space-5);
  background-color: var(--white);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  position: relative;
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  background-color: var(--white);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
}

/* Form Controls */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: var(--transition-fast);
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  padding: 1.25rem 1rem;
}

.form-floating > label {
  padding: 1rem;
  color: var(--gray-500);
}

.form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

/* Buttons */
.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background-color: var(--white);
  border-color: var(--gray-300);
  color: var(--gray-700);
}

.btn-secondary:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-800);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-danger {
  background-color: var(--danger);
  border-color: var(--danger);
}

.btn-danger:hover {
  background-color: #ff3333;
  border-color: #ff3333;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-lg {
  padding: 0.75rem 1.75rem;
  font-size: 1rem;
}

.btn i {
  margin-right: var(--space-2);
}

/* Alert Styling */
.alert {
  border: none;
  border-radius: var(--radius);
  padding: 1rem 1.25rem;
  margin-bottom: var(--space-5);
  display: flex;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.alert-success {
  background-color: rgba(0, 200, 83, 0.1);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(255, 82, 82, 0.1);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(255, 213, 79, 0.1);
  color: #f57c00;
}

.alert-info {
  background-color: rgba(77, 208, 225, 0.1);
  color: var(--info);
}

.alert i {
  margin-right: var(--space-3);
  font-size: 1.25rem;
}

/* Modern Timeline Styling */
.timeline {
  position: relative;
  padding: 0;
  margin: 0;
  list-style: none;
}

.timeline:before {
  content: '';
  position: absolute;
  top: 0;
  left: 25px;
  height: 100%;
  width: 3px;
  background: linear-gradient(to bottom, var(--primary-light), var(--primary-dark));
  border-radius: var(--radius-full);
  opacity: 0.3;
}

.timeline-item {
  position: relative;
  padding-left: 60px;
  margin-bottom: var(--space-6);
  transition: var(--transition);
}

.timeline-marker {
  position: absolute;
  top: 5px;
  left: 0;
  width: 26px;
  height: 26px;
  border-radius: 50%;
  background: var(--white);
  border: 3px solid var(--primary-light);
  z-index: 1;
  box-shadow: 0 0 0 5px rgba(76, 175, 80, 0.1);
  transition: all 0.3s ease;
}

.timeline-item.active .timeline-marker {
  background: var(--primary-gradient);
  border-color: var(--white);
  box-shadow: 0 0 0 8px rgba(76, 175, 80, 0.2), 0 0 15px rgba(0, 0, 0, 0.1);
  transform: scale(1.2);
}

.timeline-item.active .timeline-marker:after {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: white;
  font-size: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.timeline-content {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  background: var(--white);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.timeline-content:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 5px;
  background: var(--primary-gradient);
  opacity: 0.7;
}

.timeline-content:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-3px) translateX(3px);
}

.timeline-title {
  margin: 0 0 var(--space-2);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.timeline-title:before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: var(--primary);
  border-radius: 50%;
  margin-right: var(--space-2);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

/* Timeline item states */
.timeline-item.completed .timeline-marker {
  background: var(--success);
  border-color: var(--white);
}

.timeline-item.completed .timeline-content:before {
  background: linear-gradient(to bottom, var(--success), var(--primary-light));
}

.timeline-item.pending .timeline-marker {
  background: var(--warning);
  border-color: var(--white);
}

.timeline-item.pending .timeline-content:before {
  background: linear-gradient(to bottom, var(--warning), var(--primary-light));
}

/* Timeline connector lines */
.timeline-item:after {
  content: '';
  position: absolute;
  top: 18px;
  left: 26px;
  width: 34px;
  height: 2px;
  background: var(--primary-light);
  opacity: 0.3;
}

/* Timeline animation */
@keyframes pulse-timeline {
  0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
  100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

.timeline-item.active .timeline-marker {
  animation: pulse-timeline 2s infinite;
}

/* Transfer Info Card */
.transfer-info-card {
  background-color: var(--primary-ultra-soft);
  border-radius: var(--radius);
  padding: var(--space-4);
  margin-bottom: var(--space-5);
  border-left: 4px solid var(--primary);
}

.transfer-info-title {
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.transfer-info-title i {
  color: var(--primary);
}

.transfer-info-row {
  display: flex;
  margin-bottom: var(--space-3);
  align-items: center;
}

.transfer-info-label {
  font-weight: 500;
  color: var(--gray-600);
  width: 40%;
}

.transfer-info-value {
  color: var(--dark);
  width: 60%;
}

.transfer-info-value.highlight {
  font-weight: 600;
  color: var(--primary-dark);
}

/* Animations */
.animate__animated {
  animation-duration: 0.5s;
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: var(--space-4);
  }

  .transfer-info-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .transfer-info-label,
  .transfer-info-value {
    width: 100%;
  }

  .transfer-info-label {
    margin-bottom: var(--space-1);
  }

  .timeline-item {
    padding-left: 40px;
  }
}
