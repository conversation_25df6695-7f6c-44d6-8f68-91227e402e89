/* Modern Minimalist Dashboard Styles */
:root {
  /* Modern Color Palette */
  --primary: #2c3e50;
  --secondary: #34495e;
  --success: #27ae60;
  --info: #3498db;
  --warning: #f39c12;
  --danger: #e74c3c;
  --light: #ecf0f1;
  --dark: #2c3e50;
  
  /* Background Colors */
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --bg-accent: #f0f4f8;
  
  /* Shadows & Effects */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.03);
  --transition: all 0.2s ease;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-lg: 0.75rem;
}

/* Dashboard Cards */
.dashboard-card {
  border: none;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.dashboard-card .card-body {
  padding: 1.5rem;
  border-left: 3px solid transparent;
  flex-grow: 1;
  display: flex;
  align-items: center;
}

/* Card Icons */
.dashboard-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  color: white;
  margin-left: auto;
}

.dashboard-icon i {
  font-size: 1.25rem;
}

.icon-assets {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.icon-inventory {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.icon-warning {
  background: linear-gradient(135deg, #f39c12, #f1c40f);
}

.icon-transfer {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

/* Card Content */
.stat-label {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--secondary);
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-description {
  font-size: 0.825rem;
  color: #95a5a6;
  line-height: 1.2;
}

/* Card Footer */
.dashboard-card .card-footer {
  background-color: var(--bg-white);
  border-top: 1px solid rgba(0,0,0,0.05);
  padding: 0.75rem 1.5rem;
  margin-top: auto;
}

.view-details {
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition);
  text-decoration: none;
}

.view-details i {
  transition: var(--transition);
}

.view-details:hover i {
  transform: translateX(3px);
}

/* Chart Cards */
.chart-card {
  border: none;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
}

.chart-card .card-header {
  background-color: var(--bg-white);
  border-bottom: 1px solid rgba(0,0,0,0.05);
  padding: 1rem 1.5rem;
  font-weight: 600;
  color: var(--primary);
  display: flex;
  align-items: center;
}

.chart-card .card-header i {
  margin-right: 0.75rem;
  color: var(--success);
}

/* Badges */
.status-badge {
  padding: 0.4rem 0.7rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 30px;
}

.badge-pending {
  background-color: rgba(243, 156, 18, 0.15);
  color: #f39c12;
}

.badge-approved {
  background-color: rgba(52, 152, 219, 0.15);
  color: #3498db;
}

.badge-completed {
  background-color: rgba(39, 174, 96, 0.15);
  color: #27ae60;
}

.badge-rejected {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
}

/* Table styling */
.dashboard-table th {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--secondary);
  border-top: none;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  padding: 1rem;
}

.dashboard-table td {
  padding: 1rem;
  vertical-align: middle;
  border-color: rgba(0,0,0,0.05);
  font-size: 0.9rem;
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
}

.empty-state i {
  font-size: 2.5rem;
  color: #bdc3c7;
  margin-bottom: 1rem;
}

.empty-state p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
}

/* Button styling */
.btn-action {
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: var(--shadow-sm);
  text-decoration: none;
}

.btn {
  text-decoration: none;
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

/* Remove underlines from all action links */
a.btn, 
.chart-card a.btn, 
.empty-state a.btn,
.btn-group a.btn {
  text-decoration: none;
}

/* Quick Actions */
.quick-action {
  background-color: var(--bg-light);
  transition: var(--transition);
  border-radius: var(--radius);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-sm);
}

.quick-action:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow);
  background-color: #f0f4f8;
}

.quick-action .dashboard-icon {
  margin: 0 auto 1rem;
  width: 56px;
  height: 56px;
}

.quick-action h5 {
  margin-bottom: 0;
  color: var(--dark);
  font-weight: 600;
  font-size: 1rem;
}

/* Low Stock Container */
.low-stock-container {
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  background-color: white;
  overflow: hidden;
}

.low-stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.low-stock-header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--primary);
}

.low-stock-header-title i {
  color: var(--warning);
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.low-stock-table {
  width: 100%;
  border-collapse: collapse;
}

.low-stock-table th {
  background-color: #f0f7f1;
  color: var(--primary);
  font-weight: 600;
  text-align: left;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  border: none;
}

.low-stock-table td {
  padding: 0.75rem 1.5rem;
  border-top: 1px solid rgba(0,0,0,0.05);
  color: var(--secondary);
  font-size: 0.9rem;
}

.low-stock-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
}

.low-stock-empty i {
  background-color: #ecf0f1;
  color: #7f8c8d;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.low-stock-empty p {
  color: #7f8c8d;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.btn-view-all {
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.4rem 1.25rem;
  font-size: 0.85rem;
  font-weight: 500;
  text-decoration: none;
}

.btn-view-inventory {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #27ae60;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0.35rem 1.1rem;
  font-size: 0.85rem;
  font-weight: 500;
  text-decoration: none;
}

.btn-view-inventory i {
  margin-right: 0.5rem;
  font-size: 0.85rem;
} 