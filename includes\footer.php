        </div>
        <!-- End of Main Content -->
    </div>
</div>

<!-- Footer (hidden as per user request) -->
<footer class="fixed-bottom py-1 bg-light border-top small d-none">
    <div class="container-fluid">
        <div class="text-center">
            <span class="text-muted">© PCHIMS <?php echo date('Y'); ?></span>
        </div>
    </div>
</footer>

<!-- Mobile Bottom Navigation -->
<?php include_once($base_path . '/includes/mobile-nav.php'); ?>

<!-- Scroll to Top Button-->
<a class="scroll-to-top rounded" href="#page-top">
    <i class="fas fa-angle-up"></i>
</a>

<!-- Logout Modal-->
<div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
            <div class="modal-footer">
                <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                <a class="btn btn-primary" href="/choims/modules/auth/logout.php">Logout</a>
            </div>
        </div>
    </div>
</div>

<!-- Notification Toast Container -->
<div aria-live="polite" aria-atomic="true" style="position: fixed; top: 70px; right: 20px; min-width: 300px; z-index: 1060;">
    <div id="toastContainer" style="position: absolute; top: 0; right: 0;"></div>
</div>

<!-- Custom styles for notifications -->
<style>
    .text-purple {
        color: #9b59b6 !important;
    }
</style>

<!-- Bootstrap core JavaScript-->
<!-- jQuery already loaded in header -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Chart.js for data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- WebSocket removed as per user request -->

<!-- Security measures -->
<script src="/choims/assets/js/security.js"></script>

<!-- Mobile-optimized DataTables -->
<script src="/choims/assets/js/mobile-datatables.js"></script>

<!-- Custom scripts for sidebar toggle and state persistence -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarMobileToggle');
    const sidebar = document.querySelector('.sidebar');

    // Function to save expanded menu state to localStorage
    function saveMenuState() {
        const expandedMenus = {};
        const expandedElements = document.querySelectorAll('.collapse.show');

        expandedElements.forEach(function(element) {
            if (element.id) {
                expandedMenus[element.id] = true;
            }
        });

        console.log('Saving menu state:', expandedMenus);
        localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus));
    }

    // Function to find and expand the menu containing the active page
    function expandActiveMenus() {
        // Find all active links
        const activeNavLinks = document.querySelectorAll('.sidebar .nav-link.active');
        console.log('Found active links:', activeNavLinks.length);

        activeNavLinks.forEach(function(link) {
            // Find parent collapse element
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                console.log('Found parent collapse for active link:', parentCollapse.id);

                try {
                    // Find the trigger element
                    const trigger = document.querySelector(`[data-bs-target="#${parentCollapse.id}"]`);
                    if (trigger) {
                        console.log('Expanding parent menu for active link:', parentCollapse.id);

                        // Use Bootstrap's API to show the collapse
                        const bsCollapse = new bootstrap.Collapse(parentCollapse, {
                            toggle: false
                        });
                        bsCollapse.show();

                        // Update the aria-expanded attribute
                        trigger.setAttribute('aria-expanded', 'true');

                        // Add the show class explicitly
                        parentCollapse.classList.add('show');

                        // Also expand any grandparent menus
                        const grandparentCollapse = trigger.closest('.collapse');
                        if (grandparentCollapse) {
                            console.log('Expanding grandparent menu:', grandparentCollapse.id);

                            const grandparentTrigger = document.querySelector(`[data-bs-target="#${grandparentCollapse.id}"]`);
                            if (grandparentTrigger) {
                                const grandparentBsCollapse = new bootstrap.Collapse(grandparentCollapse, {
                                    toggle: false
                                });
                                grandparentBsCollapse.show();

                                grandparentTrigger.setAttribute('aria-expanded', 'true');
                                grandparentCollapse.classList.add('show');

                                // Save this expanded state
                                const expandedMenus = JSON.parse(localStorage.getItem('expandedMenus')) || {};
                                expandedMenus[grandparentCollapse.id] = true;
                                localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus));
                            }
                        }

                        // Save this expanded state
                        const expandedMenus = JSON.parse(localStorage.getItem('expandedMenus')) || {};
                        expandedMenus[parentCollapse.id] = true;
                        localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus));
                    }
                } catch (e) {
                    console.error('Error expanding menu for active link:', e);
                }
            }
        });
    }

    // Function to expand all menus by default
    function restoreMenuState() {
        try {
            console.log('Expanding all menus by default');

            // Find all collapsible menus
            const allMenus = document.querySelectorAll('.sidebar .collapse');

            // Expand all menus
            allMenus.forEach(function(menuElement) {
                if (menuElement) {
                    // Find the trigger element
                    const menuId = menuElement.id;
                    const trigger = document.querySelector(`[data-bs-target="#${menuId}"]`);

                    if (trigger) {
                        console.log('Expanding menu:', menuId);

                        // Use Bootstrap's API to show the collapse
                        const bsCollapse = new bootstrap.Collapse(menuElement, {
                            toggle: false
                        });
                        bsCollapse.show();

                        // Update the aria-expanded attribute
                        trigger.setAttribute('aria-expanded', 'true');

                        // Add the show class explicitly
                        menuElement.classList.add('show');

                        // Save to localStorage
                        const expandedMenus = JSON.parse(localStorage.getItem('expandedMenus')) || {};
                        expandedMenus[menuId] = true;
                        localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus));
                    }
                }
            });

            // Also expand menus for active page
            expandActiveMenus();
        } catch (error) {
            console.error('Error expanding menus:', error);
            // Clear potentially corrupted state
            localStorage.removeItem('expandedMenus');

            // Still try to expand active menus even if localStorage is corrupted
            expandActiveMenus();
        }
    }

    // Mobile sidebar toggle functionality is now handled by sidebar-toggle.js
    // This ensures it works immediately without waiting for all scripts to load

    // Setup event listeners for all collapsible menu items
    const submenuToggles = document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]');
    submenuToggles.forEach(function(toggle) {
        // For mobile: prevent sidebar from closing when clicking submenu toggles
        if (window.innerWidth <= 991.98) {
            toggle.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        // Save menu state whenever a toggle is clicked
        toggle.addEventListener('click', function() {
            // Use setTimeout to ensure the collapse animation has completed
            setTimeout(saveMenuState, 350);
        });
    });

    // Add click event listeners to all menu links to save state before navigation
    const menuLinks = document.querySelectorAll('.sidebar .nav-link:not([data-bs-toggle="collapse"])');
    menuLinks.forEach(function(link) {
        link.addEventListener('click', function() {
            // Save the current menu state before navigating
            saveMenuState();
        });
    });

    // Restore menu state on page load with a slight delay to ensure DOM is ready
    setTimeout(restoreMenuState, 100);

    // Also try again after a longer delay to handle slow-loading pages
    setTimeout(expandActiveMenus, 500);

    // Save menu state when navigating away
    window.addEventListener('beforeunload', saveMenuState);

    // Also save menu state periodically
    setInterval(saveMenuState, 2000);
});
</script>

<!-- Check for notifications -->
<script>
$(document).ready(function() {
    // Function to fetch and display notifications
    function checkNotifications() {
        $.ajax({
            url: '/choims/modules/notifications/check.php',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if (data.notifications && data.notifications.length > 0) {
                    // Update notification count badge
                    $('#notificationCount').text(data.count).show();

                    // Update notification dropdown
                    const $dropdown = $('#notificationDropdown');
                    $dropdown.empty();

                    // Add notifications to dropdown
                    data.notifications.forEach(function(notification) {
                        const notificationClass = notification.is_read ? '' : 'bg-light';

                        $dropdown.append(`
                            <a class="dropdown-item d-flex align-items-center ${notificationClass}" href="${notification.link}">
                                <div class="me-3">
                                    <div class="icon-circle bg-primary">
                                        <i class="fas ${notification.icon} text-white"></i>
                                    </div>
                                </div>
                                <div style="min-width: 0; flex: 1;">
                                    <div class="small text-gray-500">${notification.created_at}</div>
                                    <span class="${notification.is_read ? '' : 'fw-bold'}" style="word-wrap: break-word; overflow-wrap: break-word; word-break: break-word; display: block;">${notification.title}</span>
                                    <small class="text-muted" style="word-wrap: break-word; overflow-wrap: break-word; word-break: break-word; display: block;">${notification.message}</small>
                                </div>
                            </a>
                        `);
                    });

                    // Add "View All" link
                    $dropdown.append('<a class="dropdown-item text-center small text-gray-500" href="/choims/modules/notifications/list.php">View All Alerts</a>');

                    // Display toast for new notifications
                    if (data.new_notifications && data.new_notifications.length > 0) {
                        // Log debug information
                        console.log('New notifications found:', data.new_notifications);
                        console.log('Debug info:', data.debug);

                        // Show a summary toast if there are multiple notifications
                        if (data.new_notifications.length > 1) {
                            console.log('Showing summary toast for multiple notifications');
                            showToast(
                                'New Notifications',
                                `You have ${data.new_notifications.length} new notifications`,
                                '/choims/modules/notifications/list.php',
                                'primary'
                            );
                        } else {
                            // Show individual notification
                            const notification = data.new_notifications[0];
                            console.log('Showing toast for single notification:', notification);
                            showToast(
                                notification.title,
                                notification.message,
                                notification.link,
                                notification.type
                            );
                        }
                    } else {
                        console.log('No new notifications found');
                        if (data.debug) {
                            console.log('Debug info:', data.debug);
                        }
                    }
                } else {
                    // No notifications
                    $('#notificationCount').hide();
                    $('#notificationDropdown').html('<a class="dropdown-item text-center small text-gray-500">No new notifications</a>');
                }
            }
        });
    }

    // Function to display toast notification
    function showToast(title, message, link, type = 'primary') {
        // Get only the first line of the message for the toast
        const messageLines = message.split('\n');
        const shortMessage = messageLines[0];
        const hasMoreDetails = messageLines.length > 1;

        // Get icon based on notification type
        let icon = 'fa-bell';
        switch(type) {
            case 'transfer_approval':
                icon = 'fa-exchange-alt';
                break;
            case 'transfer_awaiting_approval':
                icon = 'fa-clipboard-check';
                type = 'purple';
                break;
            case 'transfer_approved':
                icon = 'fa-check-circle';
                type = 'success';
                break;
            case 'transfer_ready':
                icon = 'fa-dolly';
                type = 'info';
                break;
            case 'transfer_completed':
                icon = 'fa-check-double';
                type = 'success';
                break;
            case 'low_stock':
                icon = 'fa-exclamation-triangle';
                break;
            case 'system':
                icon = 'fa-cog';
                break;
            case 'maintenance':
                icon = 'fa-tools';
                break;
            case 'audit_log':
                icon = 'fa-history';
                break;
            case 'asset_deleted':
                icon = 'fa-trash-alt';
                type = 'danger';
                break;
            case 'inventory_deleted':
                icon = 'fa-trash-alt';
                type = 'danger';
                break;
        }

        const toastId = 'toast-' + Math.random().toString(36).substr(2, 9);
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="8000">
                <div class="toast-header">
                    <i class="fas ${icon} me-2 text-${type}"></i>
                    <strong class="me-auto">${title}</strong>
                    <small>${new Date().toLocaleTimeString()}</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body" style="word-wrap: break-word; overflow-wrap: break-word; word-break: break-word; white-space: normal; hyphens: auto; max-width: 300px;">
                    ${shortMessage}
                    ${hasMoreDetails ? '<div class="mt-1 small text-muted">Click for more details</div>' : ''}
                </div>
            </div>
        `;

        $('#toastContainer').append(toastHtml);

        const $toast = $(`#${toastId}`);
        const toast = new bootstrap.Toast($toast[0]);
        toast.show();

        // Make toast clickable to view notification details
        if (link && link !== '#') {
            $toast.css('cursor', 'pointer');
            $toast.click(function(e) {
                // Don't trigger if clicking on close button
                if (e.target.classList.contains('btn-close')) {
                    return;
                }

                // Special handling for transfer notifications
                if (type === 'transfer_awaiting_approval' || type === 'transfer_approved') {
                    // If we're on the logistics dashboard, scroll to the transfers awaiting confirmation widget
                    if (window.location.pathname.includes('/dashboards/logistics.php')) {
                        $('html, body').animate({
                            scrollTop: $('.awaiting-confirmation-widget').offset().top - 70
                        }, 500);

                        // Add a highlight effect to the widget
                        $('.awaiting-confirmation-widget').addClass('highlight-widget');
                        setTimeout(function() {
                            $('.awaiting-confirmation-widget').removeClass('highlight-widget');
                        }, 2000);

                        return;
                    }
                }

                // Default behavior - navigate to the link
                window.location.href = link;
            });
        }

        $toast.on('hidden.bs.toast', function() {
            $(this).remove();
        });

        // Play notification sound
        playNotificationSound();
    }

    // Function to play notification sound
    function playNotificationSound() {
        // Create audio element if it doesn't exist
        if (!window.notificationSound) {
            window.notificationSound = new Audio('/choims/assets/sounds/notification.mp3');
        }

        // Play the sound
        try {
            window.notificationSound.play();
        } catch (e) {
            console.log('Could not play notification sound:', e);
        }
    }

    // Check for notifications on page load
    setTimeout(checkNotifications, 1000);

    // Check for new notifications every 60 seconds
    setInterval(checkNotifications, 60000);
});
</script>

<!-- Mark notifications as read when clicked -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all notification items in the dropdown
    const notificationItems = document.querySelectorAll('.notification-item.unread');

    notificationItems.forEach(function(item) {
        item.addEventListener('click', function(e) {
            // Prevent default to handle the redirect manually
            e.preventDefault();

            // Get the notification ID from the data attribute
            const notificationId = this.getAttribute('data-notification-id');
            const redirectToUrl = this.getAttribute('href');
            const shouldRedirect = this.getAttribute('data-redirect') === 'true';

            // Mark the notification as read via AJAX
            if (notificationId) {
                fetch('/choims/modules/notifications/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'notification_id=' + notificationId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove the unread styling
                        item.classList.remove('unread');

                        // Update the notification counter
                        const badge = document.querySelector('.badge.bg-notification');
                        if (badge) {
                            const currentCount = parseInt(badge.textContent, 10);
                            if (currentCount > 1) {
                                badge.textContent = currentCount - 1;
                            } else {
                                badge.remove();
                            }
                        }

                        // Redirect to the notification link if needed
                        if (shouldRedirect && redirectToUrl) {
                            window.location.href = redirectToUrl;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                    // Still redirect even if there's an error
                    if (shouldRedirect && redirectToUrl) {
                        window.location.href = redirectToUrl;
                    }
                });
            } else if (shouldRedirect && redirectToUrl) {
                // If no notification ID but we should redirect, just go to the URL
                window.location.href = redirectToUrl;
            }
        });
    });
});
</script>

<!-- Mark all notifications as read when clicked -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select the mark all as read button
    const markAllReadBtn = document.querySelector('#markAllReadBtn');

    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Mark all notifications as read via AJAX
            fetch('/choims/modules/notifications/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'mark_all=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all unread styling
                    const unreadItems = document.querySelectorAll('.notification-item.unread');
                    unreadItems.forEach(item => {
                        item.classList.remove('unread');
                    });

                    // Remove notification badge
                    const badge = document.querySelector('.badge.bg-notification');
                    if (badge) {
                        badge.remove();
                    }

                    // Close the dropdown (optional)
                    const dropdownToggle = document.getElementById('notificationsDropdown');
                    if (dropdownToggle) {
                        const dropdown = bootstrap.Dropdown.getInstance(dropdownToggle);
                        if (dropdown) {
                            dropdown.hide();
                        }
                    }

                    // Show success toast
                    const toastContainer = document.getElementById('toastContainer');
                    if (toastContainer) {
                        const toastId = 'toast-' + Math.random().toString(36).substr(2, 9);
                        const toastHtml = `
                            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000">
                                <div class="toast-header">
                                    <i class="fas fa-check-circle me-2 text-success"></i>
                                    <strong class="me-auto">Notifications</strong>
                                    <small>${new Date().toLocaleTimeString()}</small>
                                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    All notifications have been marked as read.
                                </div>
                            </div>
                        `;

                        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

                        const toast = new bootstrap.Toast(document.getElementById(toastId));
                        toast.show();
                    }
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        });
    }
});
</script>

<!-- Date Time Display Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to update the date and time display
    function updateDateTime() {
        const now = new Date();

        // Full date time format for desktop
        const desktopOptions = {
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
            hour12: true,
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        };
        const formattedDesktopDateTime = now.toLocaleString('en-US', desktopOptions);

        // Compact time format for mobile (time only)
        const mobileOptions = {
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        };
        const formattedMobileDateTime = now.toLocaleString('en-US', mobileOptions);

        // Update the desktop date time display
        const desktopDateTimeElement = document.getElementById('currentDateTime');
        if (desktopDateTimeElement) {
            desktopDateTimeElement.textContent = formattedDesktopDateTime;
        }

        // Update the mobile date time display
        const mobileDateTimeElement = document.getElementById('mobileCurrentDateTime');
        if (mobileDateTimeElement) {
            mobileDateTimeElement.textContent = formattedMobileDateTime;
        }
    }

    // Update the time immediately
    updateDateTime();

    // Update the time every second
    setInterval(updateDateTime, 1000);
});
</script>
</body>
</html>