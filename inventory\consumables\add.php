<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db->beginTransaction();

        // Generate SKU
        $stmt = $db->prepare("SELECT category_code FROM categories WHERE category_id = ?");
        $stmt->execute([$_POST['category_id']]);
        $categoryCode = $stmt->fetch()['category_code'];

        $stmt = $db->prepare("SELECT MAX(CAST(SUBSTRING(sku, -6) AS UNSIGNED)) as max_num FROM items WHERE sku LIKE ?");
        $stmt->execute([$categoryCode . '%']);
        $maxNum = $stmt->fetch()['max_num'];
        $nextNum = str_pad(($maxNum + 1), 6, '0', STR_PAD_LEFT);
        $sku = $categoryCode . $nextNum;

        // Insert item
        $stmt = $db->prepare("
            INSERT INTO items (category_id, sku, name, specifications, unit_cost, is_fixed_asset)
            VALUES (?, ?, ?, ?, ?, 0)
        ");
        $stmt->execute([
            $_POST['category_id'],
            $sku,
            $_POST['item_name'],
            $_POST['specifications'],
            $_POST['unit_cost']
        ]);
        $itemId = $db->lastInsertId();

        // Calculate reorder point and status
        $quantity = (int)$_POST['quantity'];
        $reorderPoint = (int)$_POST['reorder_point'];
        $status = $quantity > $reorderPoint ? 'in_stock' : 
                 ($quantity > 0 ? 'low_stock' : 'out_of_stock');

        // Insert consumable
        $stmt = $db->prepare("
            INSERT INTO consumables (
                item_id, source_id, supplier_id, quantity, unit, 
                reorder_point, expiry_date, batch_number, status, 
                storage_location, remarks
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $itemId,
            $_POST['source_id'],
            $_POST['supplier_id'],
            $quantity,
            $_POST['unit'],
            $reorderPoint,
            $_POST['expiry_date'] ?: null,
            $_POST['batch_number'],
            $status,
            $_POST['storage_location'],
            $_POST['remarks']
        ]);
        $consumableId = $db->lastInsertId();

        // Log the activity
        $auth->logActivity($auth->getCurrentUser()['user_id'], 'create', 'consumables', $consumableId);

        $db->commit();
        setFlashMessage('success', 'Consumable added successfully');
        header("Location: view.php?id=" . $consumableId);
        exit;
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Error adding consumable: " . $e->getMessage());
        setFlashMessage('error', 'Error adding consumable. Please try again.');
    }
}

try {
    // Fetch required data for dropdowns
    // Categories
    $stmt = $db->query("SELECT category_id, name FROM categories WHERE status = 'active' ORDER BY name");
    $categories = $stmt->fetchAll();

    // Sources
    $stmt = $db->query("SELECT source_id, name FROM sources WHERE status = 'active' ORDER BY name");
    $sources = $stmt->fetchAll();

    // Suppliers
    $stmt = $db->query("SELECT supplier_id, name FROM suppliers WHERE status = 'active' ORDER BY name");
    $suppliers = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching form data: " . $e->getMessage());
    setFlashMessage('error', 'Error loading form data');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Add New Consumable</h1>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <!-- Item Details -->
                <h5 class="mb-3">Item Details</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="category_id" class="form-label required">Category</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>">
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="item_name" class="form-label required">Item Name</label>
                            <input type="text" class="form-control" id="item_name" name="item_name" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="specifications" class="form-label">Specifications</label>
                            <textarea class="form-control" id="specifications" name="specifications" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Source and Cost -->
                <h5 class="mb-3">Source and Cost</h5>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="source_id" class="form-label required">Source</label>
                            <select class="form-select" id="source_id" name="source_id" required>
                                <option value="">Select Source</option>
                                <?php foreach ($sources as $source): ?>
                                    <option value="<?php echo $source['source_id']; ?>">
                                        <?php echo htmlspecialchars($source['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="supplier_id" class="form-label required">Supplier</label>
                            <select class="form-select" id="supplier_id" name="supplier_id" required>
                                <option value="">Select Supplier</option>
                                <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?php echo $supplier['supplier_id']; ?>">
                                        <?php echo htmlspecialchars($supplier['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="unit_cost" class="form-label required">Unit Cost</label>
                            <input type="number" class="form-control" id="unit_cost" name="unit_cost" 
                                   step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="batch_number" class="form-label">Batch Number</label>
                            <input type="text" class="form-control" id="batch_number" name="batch_number">
                        </div>
                    </div>
                </div>

                <!-- Inventory Details -->
                <h5 class="mb-3">Inventory Details</h5>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="quantity" class="form-label required">Quantity</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   min="0" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="unit" class="form-label required">Unit</label>
                            <input type="text" class="form-control" id="unit" name="unit" 
                                   placeholder="e.g., pcs, boxes, etc." required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="reorder_point" class="form-label required">Reorder Point</label>
                            <input type="number" class="form-control" id="reorder_point" name="reorder_point" 
                                   min="0" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="expiry_date" class="form-label">Expiry Date</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                        </div>
                    </div>
                </div>

                <!-- Additional Details -->
                <h5 class="mb-3">Additional Details</h5>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="storage_location" class="form-label">Storage Location</label>
                            <input type="text" class="form-control" id="storage_location" name="storage_location" 
                                   placeholder="e.g., Shelf A-1, Room 101, etc.">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="text-end">
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Add Consumable</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>

<?php require_once '../../templates/footer.php'; ?>
