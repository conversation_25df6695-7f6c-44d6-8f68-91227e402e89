<?php
/**
 * Detailed Audit Log System
 *
 * This file contains functions for comprehensive audit logging of all system actions
 * with detailed information about fixed assets, transfers, and inventory operations.
 */

/**
 * Log a detailed action to the audit trail
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete, etc.)
 * @param string $entity_type Type of entity (fixed_asset, consumable, etc.)
 * @param int $entity_id ID of the entity
 * @param array $data Additional data to log
 * @return int|bool The log ID if successful, false otherwise
 */
function logDetailedAction($conn, $user_id, $action_type, $entity_type, $entity_id, $data = []) {
    // Get user information
    $user_query = "SELECT username, full_name, role FROM users WHERE user_id = ?";
    $user_stmt = mysqli_prepare($conn, $user_query);
    mysqli_stmt_bind_param($user_stmt, 'i', $user_id);
    mysqli_stmt_execute($user_stmt);
    $user_result = mysqli_stmt_get_result($user_stmt);
    $user = mysqli_fetch_assoc($user_result);

    // Prepare base data
    $log_data = [
        'user_id' => $user_id,
        'username' => $user['username'] ?? null,
        'full_name' => $user['full_name'] ?? null,
        'user_role' => $user['role'] ?? null,
        'action_type' => $action_type,
        'entity_type' => $entity_type,
        'entity_id' => $entity_id,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ];

    // Merge with additional data
    $log_data = array_merge($log_data, $data);

    // Generate changes summary if old_values and new_values are provided
    if (isset($log_data['old_values']) && isset($log_data['new_values'])) {
        $log_data['changes_summary'] = getDetailedChanges($log_data['old_values'], $log_data['new_values']);
    }

    // Build the query dynamically based on available data
    $fields = [];
    $placeholders = [];
    $values = [];
    $types = '';

    foreach ($log_data as $field => $value) {
        if ($value !== null) {
            $fields[] = $field;
            $placeholders[] = '?';
            $values[] = $value;

            // Determine the type for bind_param
            if (is_int($value)) {
                $types .= 'i';
            } elseif (is_float($value)) {
                $types .= 'd';
            } else {
                $types .= 's';
            }
        }
    }

    $query = "INSERT INTO detailed_audit_logs (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";

    $stmt = mysqli_prepare($conn, $query);
    if (!$stmt) {
        error_log("Error preparing audit log statement: " . mysqli_error($conn));
        return false;
    }

    // Dynamically bind parameters
    if (!empty($values)) {
        $bind_params = array_merge([$stmt, $types], $values);
        $bind_params_refs = [];
        foreach ($bind_params as $key => $value) {
            $bind_params_refs[$key] = &$bind_params[$key];
        }
        call_user_func_array('mysqli_stmt_bind_param', $bind_params_refs);
    }

    $result = mysqli_stmt_execute($stmt);
    if (!$result) {
        error_log("Error executing audit log statement: " . mysqli_stmt_error($stmt));
        mysqli_stmt_close($stmt);
        return false;
    }

    $log_id = mysqli_insert_id($conn);
    mysqli_stmt_close($stmt);

    return $log_id;
}

/**
 * Log a fixed asset action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete)
 * @param int $asset_id ID of the fixed asset
 * @param array $old_values Old values (for update/delete)
 * @param array $new_values New values (for create/update)
 * @return int|bool The log ID if successful, false otherwise
 */
function logFixedAssetAction($conn, $user_id, $action_type, $asset_id, $old_values = null, $new_values = null) {
    // Get asset details
    $asset_query = "
        SELECT
            fa.*,
            sm.sku_name AS item_name,
            sm.sku_code AS item_sku,
            c.category_name AS category_name,
            l.location_name AS location_name
        FROM fixed_assets fa
        LEFT JOIN sku_master sm ON fa.sku_id = sm.sku_id
        LEFT JOIN categories c ON sm.category_id = c.category_id
        LEFT JOIN locations l ON fa.current_location_id = l.location_id
        WHERE fa.asset_id = ?
    ";

    $asset_stmt = mysqli_prepare($conn, $asset_query);
    mysqli_stmt_bind_param($asset_stmt, 'i', $asset_id);
    mysqli_stmt_execute($asset_stmt);
    $asset_result = mysqli_stmt_get_result($asset_stmt);
    $asset = mysqli_fetch_assoc($asset_result);

    if (!$asset) {
        // For delete operations, the asset might not exist anymore
        if ($action_type === 'delete' && $old_values) {
            $asset = $old_values;
        } else {
            return false;
        }
    }

    // Prepare data for logging
    $log_data = [
        'entity_name' => $asset['item_name'] ?? 'Unknown Asset',
        'item_id' => $asset['item_id'] ?? null,
        'item_name' => $asset['item_name'] ?? null,
        'item_sku' => $asset['item_sku'] ?? null,
        'item_category' => $asset['category_name'] ?? null,
        'serial_number' => $asset['serial_number'] ?? null,
        'model' => $asset['model'] ?? null,
        'status' => $asset['status'] ?? null,
        'source_location_id' => $asset['department_id'] ?? $asset['health_center_id'] ?? null,
        'source_location_name' => $asset['location_name'] ?? null,
        'old_values' => $old_values ? json_encode($old_values) : null,
        'new_values' => $new_values ? json_encode($new_values) : null
    ];

    return logDetailedAction($conn, $user_id, $action_type, 'fixed_asset', $asset_id, $log_data);
}

/**
 * Log a consumable/inventory action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete, stock_in, stock_out)
 * @param int $inventory_id ID of the inventory item
 * @param array $old_values Old values (for update/delete)
 * @param array $new_values New values (for create/update)
 * @return int|bool The log ID if successful, false otherwise
 */
function logConsumableAction($conn, $user_id, $action_type, $inventory_id, $old_values = null, $new_values = null) {
    // Get inventory details
    $inventory_query = "
        SELECT
            inv.*,
            sm.sku_name AS item_name,
            sm.sku_code AS item_sku,
            c.category_name AS category_name,
            l.location_name AS location_name
        FROM consumable_inventory inv
        LEFT JOIN sku_master sm ON inv.sku_id = sm.sku_id
        LEFT JOIN categories c ON sm.category_id = c.category_id
        LEFT JOIN locations l ON inv.location_id = l.location_id
        WHERE inv.inventory_id = ?
    ";

    $inventory_stmt = mysqli_prepare($conn, $inventory_query);
    mysqli_stmt_bind_param($inventory_stmt, 'i', $inventory_id);
    mysqli_stmt_execute($inventory_stmt);
    $inventory_result = mysqli_stmt_get_result($inventory_stmt);
    $inventory = mysqli_fetch_assoc($inventory_result);

    if (!$inventory) {
        // For delete operations, the inventory might not exist anymore
        if ($action_type === 'delete' && $old_values) {
            $inventory = $old_values;
        } else {
            return false;
        }
    }

    // Prepare data for logging
    $log_data = [
        'entity_name' => $inventory['item_name'] ?? 'Unknown Item',
        'item_id' => $inventory['item_id'] ?? null,
        'item_name' => $inventory['item_name'] ?? null,
        'item_sku' => $inventory['item_sku'] ?? null,
        'item_category' => $inventory['category_name'] ?? null,
        'quantity' => $inventory['quantity'] ?? null,
        'status' => $inventory['status'] ?? null,
        'source_location_id' => $inventory['department_id'] ?? $inventory['health_center_id'] ?? null,
        'source_location_name' => $inventory['location_name'] ?? null,
        'old_values' => $old_values ? json_encode($old_values) : null,
        'new_values' => $new_values ? json_encode($new_values) : null
    ];

    return logDetailedAction($conn, $user_id, $action_type, 'consumable', $inventory_id, $log_data);
}

/**
 * Log a stock transaction
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (stock_in, stock_out)
 * @param int $transaction_id ID of the transaction
 * @param array $transaction_data Transaction data
 * @return int|bool The log ID if successful, false otherwise
 */
function logStockTransaction($conn, $user_id, $action_type, $transaction_id, $transaction_data) {
    // Get transaction details
    $transaction_query = "
        SELECT
            st.*,
            ci.sku_id,
            sm.sku_name AS item_name,
            sm.sku_code AS item_sku,
            c.category_name AS category_name,
            sl.location_name AS source_location_name,
            dl.location_name AS destination_location_name
        FROM consumable_transactions st
        LEFT JOIN consumable_inventory ci ON st.inventory_id = ci.inventory_id
        LEFT JOIN sku_master sm ON ci.sku_id = sm.sku_id
        LEFT JOIN categories c ON sm.category_id = c.category_id
        LEFT JOIN locations sl ON st.source_location_id = sl.location_id
        LEFT JOIN locations dl ON st.destination_location_id = dl.location_id
        WHERE st.transaction_id = ?
    ";

    $transaction_stmt = mysqli_prepare($conn, $transaction_query);
    mysqli_stmt_bind_param($transaction_stmt, 'i', $transaction_id);
    mysqli_stmt_execute($transaction_stmt);
    $transaction_result = mysqli_stmt_get_result($transaction_stmt);
    $transaction = mysqli_fetch_assoc($transaction_result);

    if (!$transaction && !$transaction_data) {
        return false;
    }

    // Use provided data if transaction not found
    if (!$transaction) {
        $transaction = $transaction_data;
    }

    // Prepare data for logging
    $log_data = [
        'entity_name' => $transaction['item_name'] ?? 'Unknown Item',
        'item_id' => $transaction['item_id'] ?? null,
        'item_name' => $transaction['item_name'] ?? null,
        'item_sku' => $transaction['item_sku'] ?? null,
        'item_category' => $transaction['category_name'] ?? null,
        'quantity' => $transaction['quantity'] ?? null,
        'source_location_id' => $transaction['source_department_id'] ?? $transaction['source_health_center_id'] ?? null,
        'source_location_name' => $transaction['source_location_name'] ?? $transaction['source_health_center_name'] ?? null,
        'destination_location_id' => $transaction['destination_department_id'] ?? $transaction['destination_health_center_id'] ?? null,
        'destination_location_name' => $transaction['destination_location_name'] ?? $transaction['destination_health_center_name'] ?? null,
        'transaction_code' => $transaction['reference_number'] ?? null,
        'new_values' => json_encode($transaction_data)
    ];

    return logDetailedAction($conn, $user_id, $action_type, 'consumable', $transaction_id, $log_data);
}

/**
 * Log a transfer action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (transfer_initiate, transfer_approve, transfer_reject, transfer_complete)
 * @param int $transfer_id ID of the transfer
 * @param array $transfer_data Additional transfer data
 * @return int|bool The log ID if successful, false otherwise
 */
function logTransferAction($conn, $user_id, $action_type, $transfer_id, $transfer_data = []) {
    // Get transfer details
    $transfer_query = "
        SELECT
            tr.*,
            COALESCE(fa_sm.sku_name, inv_sm.sku_name) AS item_name,
            COALESCE(fa_sm.sku_code, inv_sm.sku_code) AS item_sku,
            COALESCE(fa_c.category_name, inv_c.category_name) AS category_name,
            sl.location_name AS source_location_name,
            dl.location_name AS destination_location_name,
            u1.full_name AS requested_by_name,
            u2.full_name AS logistics_approved_by_name,
            u3.full_name AS himu_approved_by_name
        FROM transfers tr
        LEFT JOIN fixed_assets fa ON tr.asset_id = fa.asset_id
        LEFT JOIN sku_master fa_sm ON fa.sku_id = fa_sm.sku_id
        LEFT JOIN categories fa_c ON fa_sm.category_id = fa_c.category_id
        LEFT JOIN consumable_inventory inv ON tr.inventory_id = inv.inventory_id
        LEFT JOIN sku_master inv_sm ON inv.sku_id = inv_sm.sku_id
        LEFT JOIN categories inv_c ON inv_sm.category_id = inv_c.category_id
        LEFT JOIN locations sl ON tr.source_location_id = sl.location_id
        LEFT JOIN locations dl ON tr.destination_location_id = dl.location_id
        LEFT JOIN users u1 ON tr.initiated_by = u1.user_id
        LEFT JOIN users u2 ON tr.logistics_approval_by = u2.user_id
        LEFT JOIN users u3 ON tr.himu_approval_by = u3.user_id
        WHERE tr.transfer_id = ?
    ";

    $transfer_stmt = mysqli_prepare($conn, $transfer_query);
    mysqli_stmt_bind_param($transfer_stmt, 'i', $transfer_id);
    mysqli_stmt_execute($transfer_stmt);
    $transfer_result = mysqli_stmt_get_result($transfer_stmt);
    $transfer = mysqli_fetch_assoc($transfer_result);

    if (!$transfer && empty($transfer_data)) {
        return false;
    }

    // Use provided data if transfer not found
    if (!$transfer) {
        $transfer = $transfer_data;
    }

    // Get source and destination location names
    $source_location_name = $transfer['source_location_name'] ?? null;
    $destination_location_name = $transfer['destination_location_name'] ?? null;

    // Get the sku_id from the transfer data or try to determine it from the asset or inventory
    $sku_id = $transfer_data['sku_id'] ?? null;

    // Prepare data for logging
    $log_data = [
        'entity_name' => 'Transfer: ' . ($transfer['item_name'] ?? $transfer_data['item_name'] ?? 'Unknown Item'),
        'item_id' => $sku_id,
        'item_name' => $transfer['item_name'] ?? $transfer_data['item_name'] ?? null,
        'item_sku' => $transfer['item_sku'] ?? $transfer_data['item_sku'] ?? null,
        'item_category' => $transfer['category_name'] ?? $transfer_data['item_category'] ?? null,
        'quantity' => $transfer['quantity'] ?? $transfer_data['quantity'] ?? null,
        'source_location_id' => $transfer['source_location_id'] ?? null,
        'source_location_name' => $source_location_name,
        'destination_location_id' => $transfer['destination_location_id'] ?? null,
        'destination_location_name' => $destination_location_name,
        'transfer_status' => $transfer['status'] ?? null,
        'transaction_code' => $transfer['transaction_code'] ?? $transfer_data['transaction_code'] ?? null,
        'old_values' => isset($transfer_data['old_status']) ? json_encode(['status' => $transfer_data['old_status']]) : null,
        'new_values' => isset($transfer_data['new_status']) ? json_encode(['status' => $transfer_data['new_status']]) : json_encode($transfer)
    ];

    // Add changes summary for status changes or transfer initiation
    if (isset($transfer_data['old_status']) && isset($transfer_data['new_status'])) {
        $log_data['changes_summary'] = "Transfer status changed from '{$transfer_data['old_status']}' to '{$transfer_data['new_status']}'";
    } elseif ($action_type === 'transfer_initiate') {
        $item_desc = $log_data['item_name'] ? $log_data['item_name'] : 'Item';
        if ($log_data['quantity'] && $log_data['quantity'] > 1) {
            $item_desc .= " (Qty: {$log_data['quantity']})";
        }
        $log_data['changes_summary'] = "Transfer initiated for {$item_desc} from {$source_location_name} to {$destination_location_name}";
    }

    return logDetailedAction($conn, $user_id, $action_type, 'transfer', $transfer_id, $log_data);
}

/**
 * Log a user management action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete, etc.)
 * @param int $target_user_id ID of the user being modified
 * @param array $old_values Old values (for update/delete)
 * @param array $new_values New values (for create/update)
 * @return int|bool The log ID if successful, false otherwise
 */
function logUserAction($conn, $user_id, $action_type, $target_user_id, $old_values = null, $new_values = null) {
    // Get user details
    $user_query = "SELECT username, full_name, email, role, location_id, is_active FROM users WHERE user_id = ?";
    $user_stmt = mysqli_prepare($conn, $user_query);
    mysqli_stmt_bind_param($user_stmt, 'i', $target_user_id);
    mysqli_stmt_execute($user_stmt);
    $user_result = mysqli_stmt_get_result($user_stmt);
    $user = mysqli_fetch_assoc($user_result);
    mysqli_stmt_close($user_stmt);

    // Get location name if applicable
    $location_name = null;

    if ($user && $user['location_id']) {
        $location_query = "SELECT location_name FROM locations WHERE location_id = ?";
        $location_stmt = mysqli_prepare($conn, $location_query);
        mysqli_stmt_bind_param($location_stmt, 'i', $user['location_id']);
        mysqli_stmt_execute($location_stmt);
        $location_result = mysqli_stmt_get_result($location_stmt);
        if ($location = mysqli_fetch_assoc($location_result)) {
            $location_name = $location['location_name'];
        }
        mysqli_stmt_close($location_stmt);
    }

    // Prepare data for logging
    $log_data = [
        'entity_name' => $user ? $user['username'] : 'Unknown User',
        'changes_summary' => generateUserChangeSummary($action_type, $user, $old_values, $new_values, $location_name),
        'old_values' => $old_values ? json_encode($old_values) : null,
        'new_values' => $new_values ? json_encode($new_values) : ($user ? json_encode($user) : null),
        'source_location_name' => $location_name
    ];

    // Add IP address and user agent if available in new_values
    if (isset($new_values['ip_address'])) {
        $log_data['ip_address'] = $new_values['ip_address'];
    }
    if (isset($new_values['user_agent'])) {
        $log_data['user_agent'] = $new_values['user_agent'];
    }

    return logDetailedAction($conn, $user_id, $action_type, 'user', $target_user_id, $log_data);
}

/**
 * Generate a human-readable summary of user changes
 */
function generateUserChangeSummary($action_type, $user, $old_values, $new_values, $location_name = null) {
    $location_info = $location_name ? " from {$location_name}" : "";
    $ip_info = "";

    // Add IP address info for login/logout actions
    if (($action_type === 'login' || $action_type === 'logout') && isset($new_values['ip_address'])) {
        $ip_info = " (IP: {$new_values['ip_address']})";
    }

    $username = isset($user['username']) ? $user['username'] : 'Unknown User';
    $role = isset($user['role']) ? $user['role'] : 'Unknown Role';

    if ($action_type === 'create') {
        return "Created new user: {$username} ({$role})";
    } elseif ($action_type === 'update') {

        if (isset($old_values['role']) && isset($new_values['role']) && $old_values['role'] !== $new_values['role']) {
            return "Updated user {$username}: Role changed from {$old_values['role']} to {$new_values['role']}";
        } elseif (isset($new_values['password']) && isset($new_values['password_reset']) && $new_values['password_reset']) {
            return "Password reset for user: {$username}";
        } elseif (isset($new_values['password'])) {
            return "Updated user {$username}: Password changed";
        } else {
            return "Updated user {$username}: Profile information changed";
        }
    } elseif ($action_type === 'delete') {
        return "Deleted user: {$username}";
    } elseif ($action_type === 'login') {
        return "User logged in: {$username}{$location_info}{$ip_info}";
    } elseif ($action_type === 'logout') {
        return "User logged out: {$username}{$location_info}{$ip_info}";
    } else {
        return "User action: {$action_type} for {$username}";
    }
}

/**
 * Log a category management action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete)
 * @param int $category_id ID of the category
 * @param array $old_values Old values (for update/delete)
 * @param array $new_values New values (for create/update)
 * @return int|bool The log ID if successful, false otherwise
 */
function logCategoryAction($conn, $user_id, $action_type, $category_id, $old_values = null, $new_values = null) {
    // Get category details
    $category_query = "SELECT * FROM categories WHERE category_id = ?";
    $category_stmt = mysqli_prepare($conn, $category_query);
    mysqli_stmt_bind_param($category_stmt, 'i', $category_id);
    mysqli_stmt_execute($category_stmt);
    $category_result = mysqli_stmt_get_result($category_stmt);
    $category = mysqli_fetch_assoc($category_result);
    mysqli_stmt_close($category_stmt);

    // Prepare data for logging
    $log_data = [
        'entity_name' => $category ? $category['category_name'] : 'Unknown Category',
        'changes_summary' => generateCategoryChangeSummary($action_type, $category, $old_values, $new_values),
        'old_values' => $old_values ? json_encode($old_values) : null,
        'new_values' => $new_values ? json_encode($new_values) : ($category ? json_encode($category) : null)
    ];

    return logDetailedAction($conn, $user_id, $action_type, 'other', $category_id, $log_data);
}

/**
 * Generate a human-readable summary of category changes
 */
function generateCategoryChangeSummary($action_type, $category, $old_values, $new_values) {
    if ($action_type === 'create') {
        return "Created new category: {$category['category_name']}";
    } elseif ($action_type === 'update') {
        if (isset($old_values['category_name']) && isset($new_values['category_name']) && $old_values['category_name'] !== $new_values['category_name']) {
            return "Updated category: Name changed from {$old_values['category_name']} to {$new_values['category_name']}";
        } else {
            return "Updated category: {$category['category_name']}";
        }
    } elseif ($action_type === 'delete') {
        return "Deleted category: {$category['category_name']}";
    } else {
        return "Category action: {$action_type} for {$category['category_name']}";
    }
}

/**
 * Log a supplier management action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete)
 * @param int $supplier_id ID of the supplier
 * @param array $old_values Old values (for update/delete)
 * @param array $new_values New values (for create/update)
 * @return int|bool The log ID if successful, false otherwise
 */
function logSupplierAction($conn, $user_id, $action_type, $supplier_id, $old_values = null, $new_values = null) {
    // Get supplier details
    $supplier_query = "SELECT * FROM suppliers WHERE supplier_id = ?";
    $supplier_stmt = mysqli_prepare($conn, $supplier_query);
    mysqli_stmt_bind_param($supplier_stmt, 'i', $supplier_id);
    mysqli_stmt_execute($supplier_stmt);
    $supplier_result = mysqli_stmt_get_result($supplier_stmt);
    $supplier = mysqli_fetch_assoc($supplier_result);
    mysqli_stmt_close($supplier_stmt);

    // Prepare data for logging
    $log_data = [
        'entity_name' => $supplier ? $supplier['name'] : 'Unknown Supplier',
        'changes_summary' => generateSupplierChangeSummary($action_type, $supplier, $old_values, $new_values),
        'old_values' => $old_values ? json_encode($old_values) : null,
        'new_values' => $new_values ? json_encode($new_values) : ($supplier ? json_encode($supplier) : null)
    ];

    return logDetailedAction($conn, $user_id, $action_type, 'other', $supplier_id, $log_data);
}

/**
 * Generate a human-readable summary of supplier changes
 */
function generateSupplierChangeSummary($action_type, $supplier, $old_values, $new_values) {
    if ($action_type === 'create') {
        return "Created new supplier: {$supplier['name']}";
    } elseif ($action_type === 'update') {
        if (isset($old_values['name']) && isset($new_values['name']) && $old_values['name'] !== $new_values['name']) {
            return "Updated supplier: Name changed from {$old_values['name']} to {$new_values['name']}";
        } else {
            return "Updated supplier: {$supplier['name']}";
        }
    } elseif ($action_type === 'delete') {
        return "Deleted supplier: {$supplier['name']}";
    } else {
        return "Supplier action: {$action_type} for {$supplier['name']}";
    }
}

/**
 * Log a location/department management action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (create, update, delete)
 * @param string $location_type Type of location (department, health_center)
 * @param int $location_id ID of the location
 * @param array $old_values Old values (for update/delete)
 * @param array $new_values New values (for create/update)
 * @return int|bool The log ID if successful, false otherwise
 */
function logLocationAction($conn, $user_id, $action_type, $location_type, $location_id, $old_values = null, $new_values = null) {
    // Get location details
    $table_name = $location_type === 'department' ? 'departments' : 'health_centers';
    $name_field = $location_type === 'department' ? 'name' : 'name';
    $id_field = $location_type === 'department' ? 'department_id' : 'health_center_id';

    $location_query = "SELECT * FROM {$table_name} WHERE {$id_field} = ?";
    $location_stmt = mysqli_prepare($conn, $location_query);
    mysqli_stmt_bind_param($location_stmt, 'i', $location_id);
    mysqli_stmt_execute($location_stmt);
    $location_result = mysqli_stmt_get_result($location_stmt);
    $location = mysqli_fetch_assoc($location_result);
    mysqli_stmt_close($location_stmt);

    // Prepare data for logging
    $log_data = [
        'entity_name' => $location ? $location[$name_field] : 'Unknown Location',
        'changes_summary' => generateLocationChangeSummary($action_type, $location_type, $location, $old_values, $new_values),
        'old_values' => $old_values ? json_encode($old_values) : null,
        'new_values' => $new_values ? json_encode($new_values) : ($location ? json_encode($location) : null)
    ];

    return logDetailedAction($conn, $user_id, $action_type, 'other', $location_id, $log_data);
}

/**
 * Generate a human-readable summary of location changes
 */
function generateLocationChangeSummary($action_type, $location_type, $location, $old_values, $new_values) {
    $location_type_display = $location_type === 'department' ? 'Department' : 'Health Center';
    $name_field = $location_type === 'department' ? 'name' : 'name';

    if ($action_type === 'create') {
        return "Created new {$location_type_display}: {$location[$name_field]}";
    } elseif ($action_type === 'update') {
        if (isset($old_values[$name_field]) && isset($new_values[$name_field]) && $old_values[$name_field] !== $new_values[$name_field]) {
            return "Updated {$location_type_display}: Name changed from {$old_values[$name_field]} to {$new_values[$name_field]}";
        } else {
            return "Updated {$location_type_display}: {$location[$name_field]}";
        }
    } elseif ($action_type === 'delete') {
        return "Deleted {$location_type_display}: {$location[$name_field]}";
    } else {
        return "{$location_type_display} action: {$action_type} for {$location[$name_field]}";
    }
}

/**
 * Log a batch transfer action
 *
 * @param mysqli $conn Database connection
 * @param int $user_id ID of the user performing the action
 * @param string $action_type Type of action (batch_transfer, transfer_approve, transfer_reject, transfer_complete)
 * @param int $batch_id ID of the batch transfer
 * @param array $batch_data Additional batch data
 * @return int|bool The log ID if successful, false otherwise
 */
function logBatchTransferAction($conn, $user_id, $action_type, $batch_id, $batch_data = []) {
    // Get batch transfer details
    $batch_query = "
        SELECT
            bt.*,
            sl.location_name AS source_location_name,
            dl.location_name AS destination_location_name,
            u1.full_name AS initiated_by_name,
            u2.full_name AS logistics_approval_by_name,
            u3.full_name AS himu_approval_by_name,
            u4.full_name AS received_by_name
        FROM batch_transfers bt
        LEFT JOIN locations sl ON bt.source_location_id = sl.location_id
        LEFT JOIN locations dl ON bt.destination_location_id = dl.location_id
        LEFT JOIN users u1 ON bt.initiated_by = u1.user_id
        LEFT JOIN users u2 ON bt.logistics_approval_by = u2.user_id
        LEFT JOIN users u3 ON bt.himu_approval_by = u3.user_id
        LEFT JOIN users u4 ON bt.received_by = u4.user_id
        WHERE bt.batch_id = ?
    ";

    $batch_stmt = mysqli_prepare($conn, $batch_query);
    mysqli_stmt_bind_param($batch_stmt, 'i', $batch_id);
    mysqli_stmt_execute($batch_stmt);
    $batch_result = mysqli_stmt_get_result($batch_stmt);
    $batch = mysqli_fetch_assoc($batch_result);

    if (!$batch && empty($batch_data)) {
        return false;
    }

    // Use provided data if batch not found
    if (!$batch) {
        $batch = $batch_data;
    }

    // Get batch items
    $items_query = "
        SELECT
            'asset' AS type,
            bta.asset_id AS id,
            fa.serial_number,
            sm1.sku_name AS item_name,
            sm1.sku_code AS item_sku,
            c1.category_name AS category_name,
            1 AS quantity
        FROM batch_transfer_assets bta
        JOIN fixed_assets fa ON bta.asset_id = fa.asset_id
        JOIN sku_master sm1 ON fa.sku_id = sm1.sku_id
        JOIN categories c1 ON sm1.category_id = c1.category_id
        WHERE bta.batch_id = ?

        UNION ALL

        SELECT
            'inventory' AS type,
            bti.inventory_id AS id,
            NULL AS serial_number,
            sm2.sku_name AS item_name,
            sm2.sku_code AS item_sku,
            c2.category_name AS category_name,
            bti.quantity
        FROM batch_transfer_inventory bti
        JOIN consumable_inventory inv ON bti.inventory_id = inv.inventory_id
        JOIN sku_master sm2 ON inv.sku_id = sm2.sku_id
        JOIN categories c2 ON sm2.category_id = c2.category_id
        WHERE bti.batch_id = ?
    ";

    $items_stmt = mysqli_prepare($conn, $items_query);
    mysqli_stmt_bind_param($items_stmt, 'ii', $batch_id, $batch_id);
    mysqli_stmt_execute($items_stmt);
    $items_result = mysqli_stmt_get_result($items_stmt);
    $items = [];

    while ($item = mysqli_fetch_assoc($items_result)) {
        $items[] = $item;
    }

    // Prepare data for logging
    $log_data = [
        'entity_name' => 'Batch Transfer #' . $batch_id,
        'source_location_id' => $batch['source_location_id'] ?? null,
        'source_location_name' => $batch['source_location_name'] ?? null,
        'destination_location_id' => $batch['destination_location_id'] ?? null,
        'destination_location_name' => $batch['destination_location_name'] ?? null,
        'transaction_code' => $batch['transaction_code'] ?? null,
        'batch_id' => $batch_id,
        'transfer_status' => $batch['status'] ?? null,
        'old_values' => isset($batch_data['old_status']) ? json_encode(['status' => $batch_data['old_status']]) : null,
        'new_values' => json_encode([
            'batch' => $batch,
            'items' => $items
        ])
    ];

    // Add changes summary for status changes
    if (isset($batch_data['old_status']) && isset($batch_data['new_status'])) {
        $log_data['changes_summary'] = "Batch transfer status changed from '{$batch_data['old_status']}' to '{$batch_data['new_status']}'";
    } else {
        // Create a summary of the batch transfer
        $item_count = count($items);
        $log_data['changes_summary'] = "Batch transfer initiated from {$batch['source_location_name']} to {$batch['destination_location_name']} with {$item_count} items";
    }

    return logDetailedAction($conn, $user_id, $action_type, 'batch_transfer', $batch_id, $log_data);
}

/**
 * Generate a human-readable summary of changes between old and new values
 *
 * @param string|array $old_values Old values (JSON string or array)
 * @param string|array $new_values New values (JSON string or array)
 * @return string Human-readable changes summary
 */
function getDetailedChanges($old_values, $new_values) {
    // Convert JSON strings to arrays if needed
    if (is_string($old_values)) {
        $old_values = json_decode($old_values, true) ?? [];
    }

    if (is_string($new_values)) {
        $new_values = json_decode($new_values, true) ?? [];
    }

    // If either is empty, handle accordingly
    if (empty($old_values)) {
        return "New record created";
    }

    if (empty($new_values)) {
        return "Record deleted";
    }

    // Compare and generate changes
    $changes = [];

    foreach ($new_values as $key => $new_value) {
        // Skip internal fields
        if (in_array($key, ['created_at', 'updated_at'])) {
            continue;
        }

        $old_value = $old_values[$key] ?? null;

        // Skip if values are the same
        if ($old_value === $new_value) {
            continue;
        }

        // Format the change
        $field_name = ucwords(str_replace('_', ' ', $key));

        if ($old_value === null) {
            $changes[] = "{$field_name} set to '{$new_value}'";
        } elseif ($new_value === null) {
            $changes[] = "{$field_name} cleared (was '{$old_value}')";
        } else {
            $changes[] = "{$field_name} changed from '{$old_value}' to '{$new_value}'";
        }
    }

    return empty($changes) ? "No changes detected" : implode("; ", $changes);
}
