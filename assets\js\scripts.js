/**
 * Main JavaScript file for CHOIMS
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Back to top button
    var backToTopButton = document.createElement('div');
    backToTopButton.classList.add('back-to-top');
    backToTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
    document.body.appendChild(backToTopButton);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.style.display = 'block';
        } else {
            backToTopButton.style.display = 'none';
        }
    });
    
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Sidebar toggle for mobile
    var sidebarToggle = document.querySelector('[data-bs-toggle="collapse"][data-bs-target=".sidebar"]');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-toggled');
        });
    }
    
    // Add confirm dialog to delete buttons
    var deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
    
    // Form validation example
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // Auto-hide alerts after 5 seconds
    var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            var fadeEffect = setInterval(function() {
                if (!alert.style.opacity) {
                    alert.style.opacity = 1;
                }
                if (alert.style.opacity > 0) {
                    alert.style.opacity -= 0.1;
                } else {
                    clearInterval(fadeEffect);
                    alert.style.display = 'none';
                }
            }, 50);
        }, 5000);
    });
});

/**
 * Format number as currency
 * @param {number} num - Number to format
 * @returns {string} Formatted currency string
 */
function formatCurrency(num) {
    return '₱' + parseFloat(num).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
}

/**
 * Format date string to local format
 * @param {string} dateString - Date string to format
 * @returns {string} Formatted date string
 */
function formatDateString(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}

/**
 * Calculate total from quantity and unit price
 * @param {number} quantity - Item quantity
 * @param {number} unitPrice - Price per unit
 * @returns {number} Total price
 */
function calculateTotal(quantity, unitPrice) {
    return quantity * unitPrice;
}

/**
 * Check if a string is empty or contains only whitespace
 * @param {string} str - String to check
 * @returns {boolean} True if empty, false otherwise
 */
function isEmpty(str) {
    return (!str || str.trim().length === 0);
}

/**
 * Debounce function to limit how often a function can be called
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Update inventory status based on current quantity and thresholds
 * @param {Element} element - Element to update with status
 * @param {number} currentQuantity - Current quantity
 * @param {number} lowThreshold - Low stock threshold
 * @param {number} criticalThreshold - Critical threshold
 */
function updateInventoryStatus(element, currentQuantity, lowThreshold, criticalThreshold) {
    element.classList.remove('stock-normal', 'stock-warning', 'stock-critical');
    element.innerHTML = '';
    
    if (currentQuantity <= criticalThreshold) {
        element.classList.add('stock-critical');
        element.innerHTML = '<i class="fas fa-exclamation-circle"></i> Out of Stock';
    } else if (currentQuantity <= lowThreshold) {
        element.classList.add('stock-warning');
        element.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Low Stock';
    } else {
        element.classList.add('stock-normal');
        element.innerHTML = '<i class="fas fa-check-circle"></i> Available';
    }
}

/**
 * Fetch data from server using Fetch API
 * @param {string} url - URL to fetch data from
 * @param {Object} options - Fetch options
 * @returns {Promise} Promise with response data
 */
async function fetchData(url, options = {}) {
    try {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Fetch error:', error);
        throw error;
    }
}