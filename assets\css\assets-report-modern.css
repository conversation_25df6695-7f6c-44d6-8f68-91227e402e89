/* Modern Assets Report Page Styling */
:root {
  /* Colors */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --primary-bg: #E8F5E9;
  --secondary: #607D8B;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #f8f9fa;
  --dark: #1e293b;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius: 0.75rem;
  --radius-md: 1rem;
  --radius-lg: 1.25rem;
  --radius-full: 9999px;
}

/* Page Container */
.assets-report-container {
  padding: var(--space-5) var(--space-4);
  max-width: 1600px;
  margin: 0 auto;
}

/* Page Header */
.page-header {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  padding: var(--space-5);
  margin-bottom: var(--space-5);
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.page-title i {
  color: var(--primary);
  font-size: 1.5rem;
}

.page-subtitle {
  color: var(--gray-500);
  font-size: 1rem;
  margin-bottom: 0;
  margin-top: var(--space-1);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--space-2);
}

.btn {
  font-weight: 500;
  padding: 0.6rem 1.5rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-sm);
}

.btn-sm {
  padding: 0.4rem 1rem;
  font-size: 0.875rem;
  border-radius: var(--radius);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-success {
  background-color: var(--success);
  border-color: var(--success);
  color: var(--white);
}

.btn-success:hover {
  background-color: #0ca678;
  border-color: #0ca678;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-info {
  background-color: var(--info);
  border-color: var(--info);
  color: var(--white);
}

.btn-info:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background-color: var(--white);
  border-color: var(--gray-300);
  color: var(--gray-500);
}

.btn-secondary:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-700);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-outline-primary {
  background-color: transparent;
  border-color: var(--primary);
  color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: var(--space-5);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-header-title i {
  color: var(--primary);
  font-size: 1.25rem;
}

.card-body {
  padding: var(--space-5);
}

/* Stat Cards */
.stat-card {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.stat-card::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  border-radius: 10px 0 0 10px;
}

.stat-card.primary::before {
  background-color: var(--primary);
}

.stat-card.success::before {
  background-color: var(--success);
}

.stat-card.warning::before {
  background-color: var(--warning);
}

.stat-card.danger::before {
  background-color: var(--danger);
}

.stat-card .card-body {
  padding: var(--space-4);
}

.stat-card .icon-circle {
  height: 3.5rem;
  width: 3.5rem;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.stat-card:hover .icon-circle {
  transform: scale(1.1);
}

.stat-card.primary .icon-circle {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--primary);
}

.stat-card.success .icon-circle {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.stat-card.warning .icon-circle {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.stat-card.danger .icon-circle {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

.stat-card .text-xs {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.stat-card .h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0;
}

/* Filter Card */
.filter-card {
  margin-bottom: var(--space-5);
}

.filter-card .card-body {
  padding: var(--space-4);
}

.filter-card .form-label {
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: var(--space-2);
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.filter-card .form-label i {
  color: var(--primary);
}

.filter-card .form-control,
.filter-card .form-select {
  border-radius: var(--radius);
  padding: 0.6rem 1rem;
  border: 1px solid var(--gray-300);
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.filter-card .form-control:focus,
.filter-card .form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.filter-card .input-group .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Chart Cards */
.chart-card {
  height: 100%;
}

.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: var(--space-3);
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-3);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  background-color: var(--gray-100);
  font-size: 0.85rem;
}

.legend-color {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  display: inline-block;
}

/* Table Styling */
.table-card {
  margin-bottom: var(--space-5);
}

.table {
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  background-color: var(--primary-bg);
  border-top: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: var(--space-3) var(--space-4);
  color: var(--primary-dark);
}

.table th:first-child {
  border-top-left-radius: var(--radius);
}

.table th:last-child {
  border-top-right-radius: var(--radius);
}

.table td {
  padding: var(--space-3) var(--space-4);
  vertical-align: middle;
  border-top: 1px solid var(--gray-200);
}

.table-hover tbody tr {
  transition: all 0.2s ease;
}

.table-hover tbody tr:hover {
  background-color: rgba(46, 125, 50, 0.05);
}

.table a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.table a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.table .small {
  font-size: 0.8rem;
  color: var(--gray-500);
}

/* Status Badges */
.status-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: var(--radius-full);
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.status-in-use {
  background-color: var(--primary) !important;
  color: var(--white) !important;
}

.status-available {
  background-color: var(--success) !important;
  color: var(--white) !important;
}

.status-under-repair {
  background-color: var(--warning) !important;
  color: var(--dark) !important;
}

.status-disposed, .status-lost {
  background-color: var(--danger) !important;
  color: var(--white) !important;
}

/* DataTables Custom Styling */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
  color: var(--gray-500);
  font-size: 0.9rem;
  margin-bottom: var(--space-3);
}

.dataTables_wrapper .dataTables_length select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.25rem 2rem 0.25rem 0.5rem;
  background-color: var(--white);
  margin: 0 var(--space-2);
}

.dataTables_wrapper .dataTables_filter input {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  margin-left: var(--space-2);
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  margin: 0 var(--space-1);
  border: 1px solid var(--gray-300);
  background-color: var(--white);
  color: var(--gray-500) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: var(--primary-bg) !important;
  border-color: var(--primary) !important;
  color: var(--primary) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--gray-100) !important;
  border-color: var(--gray-300) !important;
  color: var(--primary) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
  color: var(--gray-400) !important;
  border-color: var(--gray-200) !important;
  background-color: var(--white) !important;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--space-8) var(--space-4);
}

.empty-state-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: var(--space-4);
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-500);
  margin-bottom: var(--space-2);
}

.empty-state-description {
  color: var(--gray-400);
  margin-bottom: var(--space-4);
}

/* Animations */
.animate__animated {
  animation-duration: 0.2s; /* Reduced from 0.5s to 0.2s */
}

.animate__fadeIn {
  animation-name: fadeIn;
}

.animate__fadeInUp {
  animation-name: fadeInUp;
}

.animate__delay-1 {
  animation-delay: 0.05s; /* Very short delay */
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0); /* Reduced from 20px to 10px */
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-end;
  }

  .card-header {
    padding: var(--space-3) var(--space-4);
  }

  .card-body {
    padding: var(--space-4);
  }

  .stat-card .h3 {
    font-size: 1.5rem;
  }

  .stat-card .icon-circle {
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 768px) {
  .assets-report-container {
    padding: var(--space-4) var(--space-3);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .action-buttons .btn {
    flex: 1;
    min-width: 120px;
  }

  .btn {
    padding: 0.5rem 1rem;
  }

  .table td, .table th {
    padding: var(--space-2) var(--space-3);
  }

  .dataTables_wrapper .dataTables_length,
  .dataTables_wrapper .dataTables_filter,
  .dataTables_wrapper .dataTables_info,
  .dataTables_wrapper .dataTables_paginate {
    text-align: left;
    float: none;
    display: block;
    margin-bottom: var(--space-3);
  }

  .dataTables_wrapper .dataTables_filter input {
    width: 100%;
    margin-left: 0;
    margin-top: var(--space-2);
  }
}
