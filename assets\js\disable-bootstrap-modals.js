/**
 * This script completely disables Bootstrap modals for specific elements
 * to prevent conflicts with our custom modal implementation
 */
document.addEventListener('DOMContentLoaded', function() {
  // Find all repair modals
  const repairModals = document.querySelectorAll('.repair-modal');
  
  // Disable Bootstrap modal functionality for these elements
  repairModals.forEach(function(modal) {
    // Remove all Bootstrap modal event listeners
    const modalElement = modal;
    const modalId = modalElement.id;
    
    // Remove data attributes that <PERSON><PERSON><PERSON> uses
    modalElement.removeAttribute('data-bs-backdrop');
    modalElement.removeAttribute('data-bs-keyboard');
    modalElement.removeAttribute('tabindex');
    modalElement.removeAttribute('aria-labelledby');
    modalElement.removeAttribute('aria-hidden');
    
    // Set display to none to ensure it's hidden
    modalElement.style.display = 'none';
    
    // Find all buttons that trigger this modal
    const triggerButtons = document.querySelectorAll(`[data-bs-target="#${modalId}"]`);
    triggerButtons.forEach(function(button) {
      // Remove Bootstrap's data attributes
      button.removeAttribute('data-bs-toggle');
      button.removeAttribute('data-bs-target');
      
      // Add our custom click handler
      button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Call our custom modal open function
        if (typeof openCustomModal === 'function') {
          openCustomModal(modalId);
        }
        
        return false;
      });
    });
  });
  
  // Remove any existing modal backdrops
  const existingBackdrops = document.querySelectorAll('.modal-backdrop');
  existingBackdrops.forEach(function(backdrop) {
    backdrop.parentNode.removeChild(backdrop);
  });
  
  // Remove modal-open class from body
  document.body.classList.remove('modal-open');
  document.body.style.overflow = '';
  document.body.style.paddingRight = '';
});

// Override Bootstrap's modal function for these specific modals
window.preventBootstrapModal = function(event) {
  if (event.target.classList.contains('repair-modal')) {
    event.preventDefault();
    event.stopPropagation();
    return false;
  }
};

// Add event listeners to prevent Bootstrap modal behavior
document.addEventListener('show.bs.modal', window.preventBootstrapModal, true);
document.addEventListener('shown.bs.modal', window.preventBootstrapModal, true);
document.addEventListener('hide.bs.modal', window.preventBootstrapModal, true);
document.addEventListener('hidden.bs.modal', window.preventBootstrapModal, true);
