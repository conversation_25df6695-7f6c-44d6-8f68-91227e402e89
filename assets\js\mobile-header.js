/**
 * Mobile Header JavaScript
 * This file handles the mobile header toolbar and panels
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth < 768;

    if (isMobile) {
        initMobileHeader();
    }

    // Listen for window resize to apply mobile header when needed
    window.addEventListener('resize', function() {
        const wasMobile = isMobile;
        const isMobileNow = window.innerWidth < 768;

        // Only reinitialize if we've crossed the mobile threshold
        if (wasMobile !== isMobileNow && isMobileNow) {
            initMobileHeader();
        }
    });
});

/**
 * Initialize the mobile header functionality
 */
function initMobileHeader() {
    // Mobile Search
    const mobileSearchToggle = document.getElementById('mobileSearchToggle');
    const mobileSearchPanel = document.getElementById('mobileSearchPanel');
    const closeSearchPanel = document.getElementById('closeSearchPanel');
    const mobileGlobalSearch = document.getElementById('mobileGlobalSearch');
    const mobileGlobalSearchResults = document.getElementById('mobileGlobalSearchResults');

    // Mobile Notifications
    const mobileNotificationToggle = document.getElementById('mobileNotificationToggle');
    const mobileNotificationsPanel = document.getElementById('mobileNotificationsPanel');
    const closeNotificationsPanel = document.getElementById('closeNotificationsPanel');
    const mobileMmarkAllReadBtn = document.getElementById('mobileMmarkAllReadBtn');

    // Mobile Profile
    const mobileProfileToggle = document.getElementById('mobileProfileToggle');
    const mobileProfilePanel = document.getElementById('mobileProfilePanel');
    const closeProfilePanel = document.getElementById('closeProfilePanel');

    // Mobile Panel Overlay
    const mobilePanelOverlay = document.getElementById('mobilePanelOverlay');

    // Initialize panels if they exist
    if (mobileSearchToggle && mobileSearchPanel) {
        initMobileSearchPanel(mobileSearchToggle, mobileSearchPanel, closeSearchPanel, mobilePanelOverlay, mobileGlobalSearch, mobileGlobalSearchResults);
    }

    if (mobileNotificationToggle && mobileNotificationsPanel) {
        initMobileNotificationsPanel(mobileNotificationToggle, mobileNotificationsPanel, closeNotificationsPanel, mobilePanelOverlay, mobileMmarkAllReadBtn);
    }

    if (mobileProfileToggle && mobileProfilePanel) {
        initMobileProfilePanel(mobileProfileToggle, mobileProfilePanel, closeProfilePanel, mobilePanelOverlay);
    }
}

/**
 * Initialize the mobile search panel
 */
function initMobileSearchPanel(toggle, panel, closeBtn, overlay, searchInput, searchResults) {
    // Open search panel
    toggle.addEventListener('click', function(e) {
        e.preventDefault();

        // Close any other open panels
        closeAllPanels();

        // Show search panel
        panel.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Focus search input
        setTimeout(function() {
            searchInput.focus();
        }, 300);
    });

    // Close search panel
    closeBtn.addEventListener('click', function() {
        panel.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    });

    // Handle search input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        if (query.length >= 2) {
            // Perform search
            performMobileSearch(query, searchResults);
        } else {
            // Clear results
            searchResults.innerHTML = '';
        }
    });
}

/**
 * Perform mobile search
 */
function performMobileSearch(query, resultsContainer) {
    // Show loading state
    resultsContainer.innerHTML = `
        <div class="mobile-search-loading">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Searching for "${query}"...</p>
        </div>
    `;

    // Use the correct API endpoint
    fetch('/choims/modules/api/global_search.php?q=' + encodeURIComponent(query))
        .then(response => response.json())
        .then(data => {
            // Clear previous results
            resultsContainer.innerHTML = '';

            if (data.error) {
                resultsContainer.innerHTML = `
                    <div class="mobile-search-error">
                        <i class="fas fa-exclamation-circle text-danger mb-2"></i>
                        <p>${data.error}</p>
                    </div>
                `;
                return;
            }

            if (data.total === 0) {
                resultsContainer.innerHTML = `
                    <div class="mobile-search-no-results">
                        <i class="fas fa-search mb-2"></i>
                        <p>No results found for "${query}"</p>
                        <p class="text-muted small">Try a different search term or check your spelling</p>
                    </div>
                `;
                return;
            }

            // Build the results HTML
            let resultsHTML = `
                <div class="mobile-search-results-header">
                    <span class="mobile-search-results-count">${data.total} results found</span>
                </div>
            `;

            // Assets section
            if (data.assets && data.assets.length > 0) {
                resultsHTML += `
                    <div class="mobile-search-section">
                        <div class="mobile-search-section-title">
                            <i class="fas fa-laptop me-2"></i> Assets (${data.assets.length})
                        </div>
                `;

                data.assets.forEach(asset => {
                    // Determine status badge class
                    let statusClass = 'badge-info';
                    if (asset.status === 'In use') statusClass = 'badge-success';
                    if (asset.status === 'Under Repair') statusClass = 'badge-warning';
                    if (asset.status === 'Defective') statusClass = 'badge-danger';

                    resultsHTML += `
                        <a href="${asset.url}" class="mobile-search-item">
                            <div class="mobile-search-item-icon">
                                <i class="fas fa-laptop"></i>
                            </div>
                            <div class="mobile-search-item-content">
                                <div class="mobile-search-item-title">${asset.name}</div>
                                <div class="mobile-search-item-subtitle">
                                    <span>${asset.sku}</span>
                                    <span class="badge bg-secondary">${asset.category}</span>
                                    <span class="badge bg-${statusClass.replace('badge-', '')}">${asset.status}</span>
                                </div>
                            </div>
                        </a>
                    `;
                });

                resultsHTML += `</div>`;
            }

            // Consumables section
            if (data.consumables && data.consumables.length > 0) {
                resultsHTML += `
                    <div class="mobile-search-section">
                        <div class="mobile-search-section-title">
                            <i class="fas fa-box me-2"></i> Consumables (${data.consumables.length})
                        </div>
                `;

                data.consumables.forEach(item => {
                    // Determine status badge class
                    let statusClass = 'badge-info';
                    if (item.status === 'Out of Stock') statusClass = 'badge-danger';
                    else if (item.status === 'Low Stock') statusClass = 'badge-warning';
                    else statusClass = 'badge-success';

                    resultsHTML += `
                        <a href="${item.url}" class="mobile-search-item">
                            <div class="mobile-search-item-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="mobile-search-item-content">
                                <div class="mobile-search-item-title">${item.name}</div>
                                <div class="mobile-search-item-subtitle">
                                    <span>${item.sku}</span>
                                    <span class="badge bg-secondary">${item.category}</span>
                                    <span class="badge bg-${statusClass.replace('badge-', '')}">${item.status} (${item.quantity})</span>
                                </div>
                            </div>
                        </a>
                    `;
                });

                resultsHTML += `</div>`;
            }

            // Add other sections (transactions, locations, etc.) if needed

            // Add "View All" link if there are more results
            if (data.total > 10) {
                resultsHTML += `
                    <div class="mobile-search-view-all">
                        <a href="/choims/modules/search/index.php?q=${encodeURIComponent(query)}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-search me-2"></i> View all ${data.total} results
                        </a>
                    </div>
                `;
            }

            resultsContainer.innerHTML = resultsHTML;
        })
        .catch(error => {
            console.error('Error performing search:', error);
            resultsContainer.innerHTML = `
                <div class="mobile-search-error">
                    <i class="fas fa-exclamation-triangle text-danger mb-2"></i>
                    <p>An error occurred while searching</p>
                    <p class="text-muted small">Please try again later</p>
                </div>
            `;
        });
}

/**
 * Initialize the mobile notifications panel
 */
function initMobileNotificationsPanel(toggle, panel, closeBtn, overlay, markAllReadBtn) {
    // Open notifications panel
    toggle.addEventListener('click', function(e) {
        e.preventDefault();

        // Close any other open panels
        closeAllPanels();

        // Show notifications panel
        panel.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Mark notifications as read
        const badge = toggle.querySelector('.mobile-notification-badge');
        if (badge) {
            // Mark all notifications as read via AJAX
            fetch('/choims/modules/notifications/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'mark_all=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all unread styling
                    const unreadItems = document.querySelectorAll('.mobile-notification-item.unread');
                    unreadItems.forEach(item => {
                        item.classList.remove('unread');
                    });

                    // Remove notification badge
                    badge.remove();

                    // Also remove the desktop notification badge
                    const desktopBadge = document.querySelector('.badge.bg-notification:not(.mobile-notification-badge)');
                    if (desktopBadge) {
                        desktopBadge.remove();
                    }
                }
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
        }
    });

    // Close notifications panel
    closeBtn.addEventListener('click', function() {
        panel.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    });

    // Mark all read button
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Mark all notifications as read via AJAX
            fetch('/choims/modules/notifications/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'mark_all=1'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove all unread styling
                    const unreadItems = document.querySelectorAll('.mobile-notification-item.unread');
                    unreadItems.forEach(item => {
                        item.classList.remove('unread');
                    });

                    // Remove notification badge
                    const badge = toggle.querySelector('.mobile-notification-badge');
                    if (badge) {
                        badge.remove();
                    }

                    // Also remove the desktop notification badge
                    const desktopBadge = document.querySelector('.badge.bg-notification:not(.mobile-notification-badge)');
                    if (desktopBadge) {
                        desktopBadge.remove();
                    }

                    // Hide the mark all read button
                    markAllReadBtn.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
        });
    }
}

/**
 * Initialize the mobile profile panel
 */
function initMobileProfilePanel(toggle, panel, closeBtn, overlay) {
    // Open profile panel
    toggle.addEventListener('click', function(e) {
        e.preventDefault();

        // Close any other open panels
        closeAllPanels();

        // Show profile panel
        panel.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    });

    // Close profile panel
    closeBtn.addEventListener('click', function() {
        panel.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    });
}

/**
 * Close all mobile panels
 */
function closeAllPanels() {
    const panels = document.querySelectorAll('.mobile-search-panel, .mobile-notifications-panel, .mobile-profile-panel');
    const overlay = document.getElementById('mobilePanelOverlay');

    panels.forEach(panel => {
        panel.classList.remove('show');
    });

    if (overlay) {
        overlay.classList.remove('show');
    }

    document.body.style.overflow = '';
}

/**
 * Handle overlay click to close panels
 */
document.addEventListener('DOMContentLoaded', function() {
    const overlay = document.getElementById('mobilePanelOverlay');

    if (overlay) {
        overlay.addEventListener('click', function() {
            closeAllPanels();
        });
    }
});
