/* Custom Modal Implementation */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.custom-modal-overlay.show {
  opacity: 1;
  display: block;
  pointer-events: auto;
}

.custom-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background-color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  z-index: 2001;
  display: none;
  opacity: 0;
  transition: all 0.3s ease;
  visibility: hidden;
  pointer-events: none;
}

.custom-modal.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  display: block;
  visibility: visible;
  pointer-events: auto;
}

.custom-modal-header {
  background-color: #ffffff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.custom-modal-title {
  font-weight: 600;
  font-size: 1.25rem;
  color: #1e293b;
  margin: 0;
}

.custom-modal-close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.custom-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #1e293b;
}

.custom-modal-body {
  padding: 1.5rem;
  max-height: calc(90vh - 130px);
  overflow-y: auto;
}

.custom-modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Custom Modal Buttons */
.custom-modal-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  background-color: #ffffff;
  color: #000000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  line-height: 1.5;
}

.custom-modal-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.05);
  text-decoration: none;
}

.custom-modal-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.custom-modal-btn-secondary {
  background-color: #f5f5f5;
  color: #000000;
}

.custom-modal-btn-primary {
  background-color: #ffffff;
  color: #000000;
}

/* Lock body when modal is open */
body.modal-open-custom {
  overflow: hidden;
}

/* Override Bootstrap modal backdrop */
.modal-backdrop {
  display: none !important;
  opacity: 0 !important;
  z-index: -1 !important;
}

body.modal-open {
  overflow: auto !important;
  padding-right: 0 !important;
}

/* Badge styling in custom modals */
.custom-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50px;
}

.custom-badge-danger {
  background-color: #fee2e2;
  color: #ef4444;
}

.custom-badge-warning {
  background-color: #fff8e1;
  color: #ff9800;
}

/* Alert styling in custom modals */
.custom-alert {
  position: relative;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
}

.custom-alert-info {
  background-color: #e3f2fd;
  color: #0c63e4;
}

.custom-alert .fas {
  margin-right: 0.75rem;
  font-size: 1.125rem;
}

/* Parts Replacement Section */
.parts-replacement-section {
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.parts-replacement-section .row {
  margin-top: 1rem;
}

.parts-replacement-section select,
.parts-replacement-section input {
  border-radius: 10px;
  border: 1px solid #cbd5e1;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.parts-replacement-section select:focus,
.parts-replacement-section input:focus {
  border-color: #94a3b8;
  box-shadow: 0 0 0 3px rgba(148, 163, 184, 0.2);
  outline: none;
}
