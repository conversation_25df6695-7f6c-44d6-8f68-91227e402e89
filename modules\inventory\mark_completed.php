<?php
// Mark Monthly Inventory as Completed (Admin Override)
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Ensure user is logged in and has GodMode role
requireRole('GodMode');

// Get parameters
$locationId = isset($_GET['location_id']) ? intval($_GET['location_id']) : 0;
$month = isset($_GET['month']) ? intval($_GET['month']) : date('n');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

// Validate parameters
if (!$locationId) {
    $_SESSION['error'] = "Invalid location ID.";
    header('Location: /choims/modules/inventory/monthly_status.php');
    exit();
}

if ($month < 1 || $month > 12) {
    $month = date('n');
}

if ($year < 2000 || $year > 2100) {
    $year = date('Y');
}

// Check if location exists
$locationQuery = "SELECT location_name FROM locations WHERE location_id = ?";
$locationStmt = mysqli_prepare($conn, $locationQuery);
mysqli_stmt_bind_param($locationStmt, 'i', $locationId);
mysqli_stmt_execute($locationStmt);
$locationResult = mysqli_stmt_get_result($locationStmt);

if (mysqli_num_rows($locationResult) === 0) {
    $_SESSION['error'] = "Location not found.";
    header('Location: /choims/modules/inventory/monthly_status.php');
    exit();
}

$location = mysqli_fetch_assoc($locationResult);
$locationName = $location['location_name'];

// Check if there's an existing record
$checkQuery = "SELECT update_id FROM monthly_inventory_updates 
              WHERE location_id = ? AND month = ? AND year = ?";
$checkStmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($checkStmt, 'iii', $locationId, $month, $year);
mysqli_stmt_execute($checkStmt);
$checkResult = mysqli_stmt_get_result($checkStmt);

if (mysqli_num_rows($checkResult) > 0) {
    // Update existing record
    $row = mysqli_fetch_assoc($checkResult);
    $updateId = $row['update_id'];
    
    $updateQuery = "UPDATE monthly_inventory_updates 
                   SET status = ?, updated_by = ?, updated_at = NOW(), notes = ? 
                   WHERE update_id = ?";
    $updateStmt = mysqli_prepare($conn, $updateQuery);
    $status = MONTHLY_UPDATE_STATUS_COMPLETED;
    $notes = "Marked as completed by administrator.";
    mysqli_stmt_bind_param($updateStmt, 'sisi', $status, $_SESSION['user_id'], $notes, $updateId);
    $success = mysqli_stmt_execute($updateStmt);
} else {
    // Insert new record
    $insertQuery = "INSERT INTO monthly_inventory_updates 
                   (location_id, month, year, status, updated_by, updated_at, notes) 
                   VALUES (?, ?, ?, ?, ?, NOW(), ?)";
    $insertStmt = mysqli_prepare($conn, $insertQuery);
    $status = MONTHLY_UPDATE_STATUS_COMPLETED;
    $notes = "Marked as completed by administrator.";
    mysqli_stmt_bind_param($insertStmt, 'iiisis', $locationId, $month, $year, $status, $_SESSION['user_id'], $notes);
    $success = mysqli_stmt_execute($insertStmt);
}

if ($success) {
    // Clear cache for this location's inventory status
    invalidateCache('monthly_inventory', $locationId);
    
    // Log the action
    logActivity($conn, "Admin Override: Monthly Inventory Update", "location", $locationId, null, json_encode([
        'month' => $month,
        'year' => $year,
        'status' => $status,
        'notes' => $notes
    ]));
    
    $_SESSION['success'] = "$locationName has been marked as completed for " . date('F Y', mktime(0, 0, 0, $month, 1, $year));
} else {
    $_SESSION['error'] = "Failed to update monthly inventory status: " . mysqli_error($conn);
}

// Redirect back to status page
header("Location: /choims/modules/inventory/monthly_status.php?month=$month&year=$year");
exit();
?>
