/* Sidebar Override Styles - To be included in all pages */
.sidebar {
    position: fixed !important;
    top: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    z-index: 1050 !important;
    padding: 0 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1) !important;
    height: 100vh !important;
    overflow-y: auto !important;
    background: linear-gradient(180deg, #0A2E10 0%, #1B5E20 100%) !important;
    border-right: none !important;
}

/* Adjust navbar to account for sidebar */
.navbar {
    padding-left: 270px !important; /* Sidebar width + some padding */
}

/* Adjust for mobile */
@media (max-width: 767.98px) {
    .sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
        width: 85% !important; /* Reduce width on mobile */
        max-width: 300px !important;
        z-index: 1070 !important; /* Higher z-index to appear above content */
        top: 56px !important; /* Start below navbar */
        height: calc(100vh - 56px) !important; /* Adjust height to account for navbar */
        overflow-x: hidden !important; /* Prevent horizontal overflow */
    }

    .sidebar.show {
        transform: translateX(0) !important;
        box-shadow: 0 0 25px rgba(0, 0, 0, 0.3) !important;
    }

    /* Fix for text truncation when closing */
    .sidebar .nav-link {
        white-space: normal !important; /* Allow text to wrap */
        overflow: visible !important; /* Ensure text is not cut off */
        text-overflow: clip !important; /* Don't use ellipsis */
        width: 100% !important; /* Ensure full width */
        font-size: 0.9rem !important; /* Consistent font size */
    }

    /* Fix for collapsed state on mobile */
    .sidebar.collapsed {
        transform: translateX(-100%) !important; /* Ensure it's hidden */
        width: 85% !important; /* Keep same width as non-collapsed */
    }

    /* Ensure text is visible in mobile sidebar */
    .sidebar.collapsed .nav-link span,
    .sidebar.collapsed .app-brand-text,
    .sidebar.collapsed .app-brand-subtext,
    .sidebar.collapsed .user-info {
        display: block !important; /* Override the collapsed hiding */
    }

    /* Fix for submenu display */
    .sidebar .collapse {
        position: static !important;
        width: 100% !important;
        box-shadow: none !important;
    }

    .navbar {
        padding-left: 1rem !important; /* Reset padding on mobile */
    }

    /* Add overlay when sidebar is open */
    body.sidebar-open::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1060;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Ensure main content doesn't shift when sidebar opens */
    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    /* Fix for sidebar toggle button positioning */
    .sidebar-toggle {
        display: none !important; /* Hide the internal sidebar toggle on mobile */
    }
}
