<?php
// Use relative paths
$base_path = dirname(dirname(dirname(__FILE__)));
require_once($base_path . '/includes/detailed_audit_log.php');
require_once($base_path . '/includes/header.php');

// Ensure user has appropriate role
requireRole('superadmin');

// Process form submissions
$message = '';
$error = '';

// Add new location
if (isset($_POST['add_location'])) {
    $location_name = sanitizeInput($_POST['location_name']);
    $location_type = sanitizeInput($_POST['location_type']);
    $address = sanitizeInput($_POST['address']);

    // Validate inputs
    if (empty($location_name) || empty($location_type)) {
        $error = "Please fill all required fields";
    } else {
        // Check if location name already exists
        $checkQuery = "SELECT location_id FROM locations WHERE location_name = ?";
        $checkStmt = mysqli_prepare($conn, $checkQuery);
        mysqli_stmt_bind_param($checkStmt, 's', $location_name);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_store_result($checkStmt);

        if (mysqli_stmt_num_rows($checkStmt) > 0) {
            $error = "Location name already exists";
        } else {
            // Check if is_active column exists
            $checkColumnQuery = "SHOW COLUMNS FROM locations LIKE 'is_active'";
            $columnResult = mysqli_query($conn, $checkColumnQuery);
            $hasIsActiveColumn = (mysqli_num_rows($columnResult) > 0);

            // Prepare the appropriate query based on column existence
            if ($hasIsActiveColumn) {
                $query = "INSERT INTO locations (location_name, location_type, address, is_active) VALUES (?, ?, ?, 1)";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'sss', $location_name, $location_type, $address);
            } else {
                $query = "INSERT INTO locations (location_name, location_type, address) VALUES (?, ?, ?)";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'sss', $location_name, $location_type, $address);
            }

            if (mysqli_stmt_execute($stmt)) {
                $locationId = mysqli_insert_id($conn);

                // Log to regular audit log
                logActivity($conn, 'Create', 'Location', $locationId, null, "Created location: $location_name");

                // Log to detailed audit system
                $new_values = [
                    'location_name' => $location_name,
                    'location_type' => $location_type,
                    'address' => $address,
                    'is_active' => 1
                ];
                logLocationAction($conn, $_SESSION['user_id'], 'create', 'location', $locationId, null, $new_values);

                $message = "Location added successfully";
            } else {
                $error = "Error adding location: " . mysqli_error($conn);
            }
        }
    }
}

// Edit location
if (isset($_POST['edit_location'])) {
    $location_id = (int)$_POST['location_id'];
    $location_name = sanitizeInput($_POST['location_name']);
    $location_type = sanitizeInput($_POST['location_type']);
    $address = sanitizeInput($_POST['address']);

    // Validate inputs
    if (empty($location_name) || empty($location_type)) {
        $error = "Please fill all required fields";
    } else {
        // Get old values for audit log
        $getOldQuery = "SELECT * FROM locations WHERE location_id = ?";
        $getOldStmt = mysqli_prepare($conn, $getOldQuery);
        mysqli_stmt_bind_param($getOldStmt, 'i', $location_id);
        mysqli_stmt_execute($getOldStmt);
        $oldResult = mysqli_stmt_get_result($getOldStmt);
        $oldValues = mysqli_fetch_assoc($oldResult);

        // Check if new location name exists and is not the current one
        $checkQuery = "SELECT location_id FROM locations WHERE location_name = ? AND location_id != ?";
        $checkStmt = mysqli_prepare($conn, $checkQuery);
        mysqli_stmt_bind_param($checkStmt, 'si', $location_name, $location_id);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_store_result($checkStmt);

        if (mysqli_stmt_num_rows($checkStmt) > 0) {
            $error = "Location name already exists";
        } else {
            // Update location
            $query = "UPDATE locations SET location_name = ?, location_type = ?, address = ? WHERE location_id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'sssi', $location_name, $location_type, $address, $location_id);

            if (mysqli_stmt_execute($stmt)) {
                // Log to regular audit log
                logActivity($conn, 'Update', 'Location', $location_id, json_encode($oldValues), "Updated location details");

                // Log to detailed audit system
                $old_values = [
                    'location_name' => $oldValues['location_name'],
                    'location_type' => $oldValues['location_type'],
                    'address' => $oldValues['address']
                ];

                $new_values = [
                    'location_name' => $location_name,
                    'location_type' => $location_type,
                    'address' => $address
                ];

                logLocationAction($conn, $_SESSION['user_id'], 'update', 'location', $location_id, $old_values, $new_values);

                $message = "Location updated successfully";
            } else {
                $error = "Error updating location: " . mysqli_error($conn);
            }
        }
    }
}

// Toggle location status (activate/deactivate)
if (isset($_POST['toggle_status'])) {
    $location_id = (int)$_POST['location_id'];
    $status = $_POST['status'] == '1' ? 1 : 0;
    $status_text = $status ? 'activated' : 'deactivated';

    // Check if location is in use
    if ($status == 0) {
        $checkQuery = "SELECT COUNT(*) as count FROM users WHERE location_id = ?";
        $checkStmt = mysqli_prepare($conn, $checkQuery);
        mysqli_stmt_bind_param($checkStmt, 'i', $location_id);
        mysqli_stmt_execute($checkStmt);
        $checkResult = mysqli_stmt_get_result($checkStmt);
        $count = mysqli_fetch_assoc($checkResult)['count'];

        if ($count > 0) {
            $error = "Cannot deactivate location because it is assigned to users";
            goto skip_toggle;
        }
    }

    // Check if is_active column exists
    $checkColumnQuery = "SHOW COLUMNS FROM locations LIKE 'is_active'";
    $columnResult = mysqli_query($conn, $checkColumnQuery);

    if (mysqli_num_rows($columnResult) == 0) {
        // Column doesn't exist, add it
        $addColumnQuery = "ALTER TABLE locations ADD COLUMN is_active TINYINT(1) NOT NULL DEFAULT 1";
        if (!mysqli_query($conn, $addColumnQuery)) {
            $error = "Error adding is_active column: " . mysqli_error($conn);
            goto skip_toggle;
        }
    }

    // Update status
    $query = "UPDATE locations SET is_active = ? WHERE location_id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'ii', $status, $location_id);

    // Get location details for logging
    $locationQuery = "SELECT * FROM locations WHERE location_id = ?";
    $locationStmt = mysqli_prepare($conn, $locationQuery);
    mysqli_stmt_bind_param($locationStmt, 'i', $location_id);
    mysqli_stmt_execute($locationStmt);
    $locationResult = mysqli_stmt_get_result($locationStmt);
    $locationData = mysqli_fetch_assoc($locationResult);
    mysqli_stmt_close($locationStmt);

    if (mysqli_stmt_execute($stmt)) {
        // Log to regular audit log
        logActivity($conn, 'Update', 'Location', $location_id, null, "Location $status_text");

        // Log to detailed audit system
        $old_values = [
            'is_active' => $status == 1 ? 0 : 1
        ];

        $new_values = [
            'is_active' => $status
        ];

        logLocationAction($conn, $_SESSION['user_id'], 'update', 'location', $location_id, $old_values, $new_values);

        $message = "Location $status_text successfully";
    } else {
        $error = "Error changing location status: " . mysqli_error($conn);
    }

    skip_toggle:
}

// Get all locations
$hasIsActiveColumn = false;
$checkColumnQuery = "SHOW COLUMNS FROM locations LIKE 'is_active'";
$columnResult = mysqli_query($conn, $checkColumnQuery);
$hasIsActiveColumn = (mysqli_num_rows($columnResult) > 0);

if ($hasIsActiveColumn) {
    $query = "SELECT *, COALESCE(is_active, 1) AS is_active FROM locations ORDER BY location_name";
} else {
    $query = "SELECT *, 1 AS is_active FROM locations ORDER BY location_name";
}
$result = mysqli_query($conn, $query);
?>

<div class="container-fluid py-4">
    <h1 class="h3 mb-4 text-gray-800">Location Management</h1>

    <?php if (!empty($message)): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>

    <!-- Add Location Button -->
    <div class="mb-4 d-flex justify-content-between align-items-center">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addLocationModal">
            <i class="fas fa-map-marker-alt"></i> Add New Location
        </button>

        <div class="btn-group" role="group" aria-label="Filter locations">
            <button type="button" class="btn btn-outline-primary filter-btn active" data-filter="all">All</button>
            <button type="button" class="btn btn-outline-primary filter-btn" data-filter="Department">Departments</button>
            <button type="button" class="btn btn-outline-primary filter-btn" data-filter="HealthCenter">Health Centers</button>
        </div>
    </div>

    <!-- Locations Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Locations</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="locationsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($location = mysqli_fetch_assoc($result)): ?>
                            <tr data-type="<?php echo htmlspecialchars($location['location_type']); ?>">
                                <td><?php echo htmlspecialchars($location['location_name']); ?></td>
                                <td><?php echo htmlspecialchars($location['location_type']); ?></td>
                                <td>
                                    <?php
                                    $isActive = isset($location['is_active']) ? $location['is_active'] : true;
                                    if ($isActive):
                                    ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-location"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editLocationModal"
                                            data-id="<?php echo $location['location_id']; ?>"
                                            data-name="<?php echo htmlspecialchars($location['location_name']); ?>"
                                            data-type="<?php echo htmlspecialchars($location['location_type']); ?>"
                                            data-address="<?php echo htmlspecialchars(isset($location['address']) ? $location['address'] : ''); ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>

                                    <form method="post" action="" class="d-inline" onsubmit="return confirm('Are you sure you want to <?php echo (isset($location['is_active']) && $location['is_active']) ? 'deactivate' : 'activate'; ?> this location?');">
                                        <input type="hidden" name="location_id" value="<?php echo $location['location_id']; ?>">
                                        <input type="hidden" name="status" value="<?php echo (isset($location['is_active']) && $location['is_active']) ? '0' : '1'; ?>">
                                        <button type="submit" name="toggle_status" class="btn btn-sm <?php echo (isset($location['is_active']) && $location['is_active']) ? 'btn-danger' : 'btn-success'; ?>">
                                            <i class="fas <?php echo (isset($location['is_active']) && $location['is_active']) ? 'fa-times' : 'fa-check'; ?>"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Location Modal -->
<div class="modal fade" id="addLocationModal" tabindex="-1" aria-labelledby="addLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLocationModalLabel">Add New Location</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="location_name" class="form-label">Location Name*</label>
                        <input type="text" class="form-control" id="location_name" name="location_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="location_type" class="form-label">Location Type*</label>
                        <select class="form-select" id="location_type" name="location_type" required>
                            <option value="">Select Type</option>
                            <option value="Office">Office</option>
                            <option value="Department">Department</option>
                            <option value="Health Center">Health Center</option>
                            <option value="Warehouse">Warehouse</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_location" class="btn btn-primary">Add Location</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Location Modal -->
<div class="modal fade" id="editLocationModal" tabindex="-1" aria-labelledby="editLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editLocationModalLabel">Edit Location</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <input type="hidden" name="location_id" id="edit_location_id">
                    <div class="mb-3">
                        <label for="edit_location_name" class="form-label">Location Name*</label>
                        <input type="text" class="form-control" id="edit_location_name" name="location_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_location_type" class="form-label">Location Type*</label>
                        <select class="form-select" id="edit_location_type" name="location_type" required>
                            <option value="">Select Type</option>
                            <option value="Office">Office</option>
                            <option value="Department">Department</option>
                            <option value="Health Center">Health Center</option>
                            <option value="Warehouse">Warehouse</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">Address</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="edit_location" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Fill edit location modal with location data
document.addEventListener('DOMContentLoaded', function() {
    const editButtons = document.querySelectorAll('.edit-location');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const locationId = this.getAttribute('data-id');
            const locationName = this.getAttribute('data-name');
            const locationType = this.getAttribute('data-type');
            const address = this.getAttribute('data-address');

            document.getElementById('edit_location_id').value = locationId;
            document.getElementById('edit_location_name').value = locationName;
            document.getElementById('edit_location_type').value = locationType;
            document.getElementById('edit_address').value = address;
        });
    });

    // Initialize DataTable
    let locationsTable;
    if (typeof $.fn.DataTable !== 'undefined') {
        locationsTable = $('#locationsTable').DataTable({
            "order": [[0, "asc"]],
            "language": {
                "search": "Search locations:"
            }
        });
    }

    // Handle location type filtering
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            const filterValue = this.getAttribute('data-filter');

            if (locationsTable) {
                if (filterValue === 'all') {
                    locationsTable.column(1).search('').draw();
                } else {
                    locationsTable.column(1).search(filterValue).draw();
                }
            } else {
                // If DataTable is not available, use basic DOM filtering
                const rows = document.querySelectorAll('#locationsTable tbody tr');
                rows.forEach(row => {
                    const type = row.getAttribute('data-type');
                    if (filterValue === 'all' || type === filterValue) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
        });
    });
});
</script>

<?php
require_once($base_path . '/includes/footer.php');
?>