<?php
// Use relative paths instead of $_SERVER['DOCUMENT_ROOT']
$base_path = dirname(dirname(__FILE__));
require_once($base_path . '/includes/header.php');

// Ensure user has appropriate role
requireRole('logistics');

// Get system statistics
// Get user's location ID
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : 0;

// Check if it's time for monthly inventory update
$showMonthlyUpdateButton = isTimeForMonthlyInventoryUpdate(); // Show only 2 days before month end
$hasCompletedMonthlyUpdate = false;

if ($showMonthlyUpdateButton && $userLocationId) {
    // Check if this location has already completed their monthly update
    $hasCompletedMonthlyUpdate = hasCompletedMonthlyInventoryUpdate($conn, $userLocationId);
}

// Generate CSRF token for the monthly update form
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Total fixed assets count
$assetsCount = 0;
$assetsCountInLocation = 0;
try {
    // Total assets count
    $assetsQuery = "SELECT COUNT(*) as total FROM fixed_assets WHERE (is_deleted = 0 OR is_deleted IS NULL)";
    $assetsResult = mysqli_query($conn, $assetsQuery);
    if ($assetsResult) {
        $assetsCount = mysqli_fetch_assoc($assetsResult)['total'];
    }

    // Assets count in user's location
    if ($userLocationId) {
        $assetsLocationQuery = "SELECT COUNT(*) as total FROM fixed_assets
                              WHERE (is_deleted = 0 OR is_deleted IS NULL)
                              AND current_location_id = $userLocationId";
        $assetsLocationResult = mysqli_query($conn, $assetsLocationQuery);
        if ($assetsLocationResult) {
            $assetsCountInLocation = mysqli_fetch_assoc($assetsLocationResult)['total'];
        }
    }
} catch (Exception $e) {
    error_log("Error counting fixed assets: " . $e->getMessage());
}

// Total consumable inventory count
$inventoryCount = 0;
$inventoryCountInLocation = 0;
$totalConsumableQty = 0;
$totalConsumableQtyInLocation = 0;
try {
    // Total consumables count (all categories)
    $inventoryQuery = "SELECT COUNT(*) as total, SUM(i.current_quantity) as total_qty
                      FROM consumable_inventory i
                      JOIN sku_master s ON i.sku_id = s.sku_id
                      WHERE s.item_type = 'Consumable' AND (i.is_deleted = 0 OR i.is_deleted IS NULL)";
    $inventoryResult = mysqli_query($conn, $inventoryQuery);
    if ($inventoryResult) {
        $inventoryData = mysqli_fetch_assoc($inventoryResult);
        $inventoryCount = $inventoryData['total'];
        $totalConsumableQty = $inventoryData['total_qty'] ?: 0;
    }

    // Inventory count in user's location
    if ($userLocationId) {
        $inventoryLocationQuery = "SELECT COUNT(*) as total, SUM(i.current_quantity) as total_qty
                                 FROM consumable_inventory i
                                 JOIN sku_master s ON i.sku_id = s.sku_id
                                 WHERE (i.is_deleted = 0 OR i.is_deleted IS NULL)
                                 AND s.item_type = 'Consumable'
                                 AND i.location_id = $userLocationId";
        $inventoryLocationResult = mysqli_query($conn, $inventoryLocationQuery);
        if ($inventoryLocationResult) {
            $inventoryLocationData = mysqli_fetch_assoc($inventoryLocationResult);
            $inventoryCountInLocation = $inventoryLocationData['total'];
            $totalConsumableQtyInLocation = $inventoryLocationData['total_qty'] ?: 0;
        }
    }
} catch (Exception $e) {
    error_log("Error counting inventory: " . $e->getMessage());
}

// Low stock items count
$lowStockCount = 0;
try {
    $lowStockQuery = "SELECT COUNT(*) as total FROM consumable_inventory WHERE (status = 'Low Stock' OR status = 'Out of Stock') AND (is_deleted = 0 OR is_deleted IS NULL)";
    $lowStockResult = mysqli_query($conn, $lowStockQuery);
    if ($lowStockResult) {
        $lowStockCount = mysqli_fetch_assoc($lowStockResult)['total'];
    }
} catch (Exception $e) {
    error_log("Error counting low stock: " . $e->getMessage());
}

// Pending transfers count
$pendingTransfersCount = 0;
try {
    // Count both regular transfers and batch transfers
    $transfersQuery = "
        SELECT
            (SELECT COUNT(*) FROM transfers WHERE status = 'Pending') +
            (SELECT COUNT(*) FROM batch_transfers WHERE status = 'Pending') AS total
    ";
    $transfersResult = mysqli_query($conn, $transfersQuery);
    if ($transfersResult) {
        $pendingTransfersCount = mysqli_fetch_assoc($transfersResult)['total'];
    }
} catch (Exception $e) {
    error_log("Error counting pending transfers: " . $e->getMessage());
}

// Count maintenance records for office and medical equipment
$maintenanceCount = 0;
$defectiveEquipmentCount = 0;
try {
    // Count maintenance records for office and medical equipment
    $maintenanceQuery = "
        SELECT COUNT(*) as total
        FROM maintenance_records mr
        JOIN fixed_assets fa ON mr.asset_id = fa.asset_id
        JOIN sku_master sm ON fa.sku_id = sm.sku_id
        JOIN categories c ON sm.category_id = c.category_id
        WHERE c.category_id IN (2, 3) -- Only Office Equipment (2) and Medical Equipment (3)
    ";
    $maintenanceResult = mysqli_query($conn, $maintenanceQuery);
    if ($maintenanceResult) {
        $maintenanceCount = mysqli_fetch_assoc($maintenanceResult)['total'];
    }

    // Count defective office and medical equipment
    $defectiveQuery = "
        SELECT COUNT(*) as total
        FROM fixed_assets fa
        JOIN sku_master sm ON fa.sku_id = sm.sku_id
        JOIN categories c ON sm.category_id = c.category_id
        WHERE fa.status = 'Defective'
        AND c.category_id IN (2, 3) -- Only Office Equipment (2) and Medical Equipment (3)
    ";
    $defectiveResult = mysqli_query($conn, $defectiveQuery);
    if ($defectiveResult) {
        $defectiveEquipmentCount = mysqli_fetch_assoc($defectiveResult)['total'];
    }
} catch (Exception $e) {
    error_log("Error counting maintenance records: " . $e->getMessage());
}

// Assets by category for pie chart
$assetsByCategoryQuery = "
    SELECT c.category_name, COUNT(a.asset_id) as asset_count
    FROM categories c
    LEFT JOIN sku_master s ON c.category_id = s.category_id
    LEFT JOIN fixed_assets a ON s.sku_id = a.sku_id AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    GROUP BY c.category_id
    ORDER BY asset_count DESC";

// Add error handling
$assetsByCategoryResult = false;
try {
    $assetsByCategoryResult = mysqli_query($conn, $assetsByCategoryQuery);
} catch (Exception $e) {
    error_log("Error fetching assets by category: " . $e->getMessage());
}

$categoryLabels = [];
$categoryData = [];
$categoryColors = [
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#9C27B0', // Purple
    '#FF9800', // Orange
    '#F44336', // Red
    '#00BCD4', // Cyan
    '#FFEB3B', // Yellow
    '#795548', // Brown
    '#607D8B', // Blue Grey
    '#E91E63'  // Pink
];

if ($assetsByCategoryResult && mysqli_num_rows($assetsByCategoryResult) > 0) {
    $colorIndex = 0;
    $categoryColorMap = [];

    // First pass: collect all categories
    while ($row = mysqli_fetch_assoc($assetsByCategoryResult)) {
        $categoryLabels[] = $row['category_name'];
        $categoryData[] = (int)$row['asset_count'];
        $categoryColorMap[] = $categoryColors[$colorIndex];
        $colorIndex = ($colorIndex + 1) % count($categoryColors);
    }

    // Replace the colors array with our mapped colors
    $categoryColors = $categoryColorMap;
}

// Recent transfers
$recentTransfersQuery = "
    SELECT t.*,
           src.location_name as source_location,
           dst.location_name as destination_location,
           u.full_name as initiated_by_name,
           CASE
             WHEN t.asset_id IS NOT NULL THEN (SELECT asset_name FROM fixed_assets WHERE asset_id = t.asset_id)
             ELSE (SELECT sku_name FROM sku_master WHERE sku_id = (SELECT sku_id FROM consumable_inventory WHERE inventory_id = t.inventory_id))
           END as item_name,
           t.transfer_date
    FROM transfers t
    JOIN locations src ON t.source_location_id = src.location_id
    JOIN locations dst ON t.destination_location_id = dst.location_id
    JOIN users u ON t.initiated_by = u.user_id
    ORDER BY t.transfer_date DESC
    LIMIT 3";
$recentTransfersResult = false;
try {
    $recentTransfersResult = mysqli_query($conn, $recentTransfersQuery);
} catch (Exception $e) {
    error_log("Error fetching recent transfers: " . $e->getMessage());
}

// Incoming transfers (where logistics is destination)
$incomingTransfersQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        src.location_name AS source_location,
        dst.location_name AS destination_location,
        u.full_name AS initiated_by,
        'incoming' AS direction,
        COALESCE(
            (SELECT c1.requires_himu_approval FROM fixed_assets a JOIN sku_master s1 ON a.sku_id = s1.sku_id JOIN categories c1 ON s1.category_id = c1.category_id WHERE a.asset_id = t.asset_id),
            (SELECT c2.requires_himu_approval FROM consumable_inventory i JOIN sku_master s2 ON i.sku_id = s2.sku_id JOIN categories c2 ON s2.category_id = c2.category_id WHERE i.inventory_id = t.inventory_id),
            0
        ) AS requires_himu_approval
    FROM transfers t
    JOIN locations src ON t.source_location_id = src.location_id
    JOIN locations dst ON t.destination_location_id = dst.location_id
    JOIN users u ON t.initiated_by = u.user_id
    WHERE t.status IN ('Approved by Logistics', 'Approved by HIMU')
    AND t.destination_location_id = $userLocationId
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        src.location_name AS source_location,
        dst.location_name AS destination_location,
        u.full_name AS initiated_by,
        'incoming' AS direction,
        b.requires_himu_approval
    FROM batch_transfers b
    JOIN locations src ON b.source_location_id = src.location_id
    JOIN locations dst ON b.destination_location_id = dst.location_id
    JOIN users u ON b.initiated_by = u.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.status IN ('Approved by Logistics', 'Approved by HIMU')
    AND b.destination_location_id = $userLocationId
    GROUP BY b.batch_id
    LIMIT 5)
    ORDER BY transfer_date DESC";

// Outgoing transfers (where logistics is source)
$outgoingTransfersQuery = "
    (SELECT
        'Individual' AS transfer_type,
        t.transfer_id AS id,
        t.transaction_code,
        t.transfer_date,
        t.status,
        CASE
            WHEN t.asset_id IS NOT NULL THEN (SELECT a.asset_name FROM fixed_assets a WHERE a.asset_id = t.asset_id)
            WHEN t.inventory_id IS NOT NULL THEN (SELECT CONCAT(ci.current_quantity, ' x ', s.sku_name) FROM consumable_inventory ci JOIN sku_master s ON ci.sku_id = s.sku_id WHERE ci.inventory_id = t.inventory_id)
            ELSE 'Unknown'
        END AS item_name,
        src.location_name AS source_location,
        dst.location_name AS destination_location,
        u.full_name AS initiated_by,
        'outgoing' AS direction,
        COALESCE(
            (SELECT c1.requires_himu_approval FROM fixed_assets a JOIN sku_master s1 ON a.sku_id = s1.sku_id JOIN categories c1 ON s1.category_id = c1.category_id WHERE a.asset_id = t.asset_id),
            (SELECT c2.requires_himu_approval FROM consumable_inventory i JOIN sku_master s2 ON i.sku_id = s2.sku_id JOIN categories c2 ON s2.category_id = c2.category_id WHERE i.inventory_id = t.inventory_id),
            0
        ) AS requires_himu_approval
    FROM transfers t
    JOIN locations src ON t.source_location_id = src.location_id
    JOIN locations dst ON t.destination_location_id = dst.location_id
    JOIN users u ON t.initiated_by = u.user_id
    WHERE t.status IN ('Approved by Logistics', 'Approved by HIMU')
    AND t.source_location_id = $userLocationId
    LIMIT 5)

    UNION

    (SELECT
        'Batch' AS transfer_type,
        b.batch_id AS id,
        b.transaction_code,
        b.transfer_date,
        b.status,
        CONCAT(COUNT(DISTINCT ba.asset_id), ' assets, ', COUNT(DISTINCT bi.inventory_id), ' inventory items') AS item_name,
        src.location_name AS source_location,
        dst.location_name AS destination_location,
        u.full_name AS initiated_by,
        'outgoing' AS direction,
        b.requires_himu_approval
    FROM batch_transfers b
    JOIN locations src ON b.source_location_id = src.location_id
    JOIN locations dst ON b.destination_location_id = dst.location_id
    JOIN users u ON b.initiated_by = u.user_id
    LEFT JOIN batch_transfer_assets ba ON b.batch_id = ba.batch_id
    LEFT JOIN batch_transfer_inventory bi ON b.batch_id = bi.batch_id
    WHERE b.status IN ('Approved by Logistics', 'Approved by HIMU')
    AND b.source_location_id = $userLocationId
    GROUP BY b.batch_id
    LIMIT 5)
    ORDER BY transfer_date DESC";

$incomingTransfersResult = false;
$outgoingTransfersResult = false;
try {
    $incomingTransfersResult = mysqli_query($conn, $incomingTransfersQuery);
    $outgoingTransfersResult = mysqli_query($conn, $outgoingTransfersQuery);
} catch (Exception $e) {
    error_log("Error fetching transfers: " . $e->getMessage());
}

// Get inventory activity over time (last 7 days)
$inventoryActivityQuery = "
    SELECT
        DATE(transaction_date) as date,
        COUNT(CASE WHEN transaction_type = 'Stock In' THEN 1 END) as stock_in,
        COUNT(CASE WHEN transaction_type IN ('Stock Out', 'Transfer', 'Adjustment') THEN 1 END) as stock_out,
        COUNT(CASE WHEN transaction_type = 'Transfer' THEN 1 END) as transfer
    FROM consumable_transactions
    WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY DATE(transaction_date)
    ORDER BY date";

// Add error handling
$inventoryActivityResult = false;
try {
    $inventoryActivityResult = mysqli_query($conn, $inventoryActivityQuery);
} catch (Exception $e) {
    // If table doesn't exist or query fails, we'll handle in the next section
    error_log("Error fetching inventory activity: " . $e->getMessage());
}

$activityDates = [];
$stockInData = [];
$stockOutData = [];
$transferData = [];

// Initialize with empty data for all 7 days
for ($i = 6; $i >= 0; $i--) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $formattedDate = date('M d', strtotime($date));
    $activityDates[] = $formattedDate;
    $dateData[$date] = [
        'date' => $formattedDate,
        'stock_in' => 0,
        'stock_out' => 0,
        'transfer' => 0
    ];
}

// Populate with real data if available
if ($inventoryActivityResult && mysqli_num_rows($inventoryActivityResult) > 0) {
    while ($row = mysqli_fetch_assoc($inventoryActivityResult)) {
        $date = $row['date'];
        if (isset($dateData[$date])) {
            $dateData[$date]['stock_in'] = (int)$row['stock_in'];
            $dateData[$date]['stock_out'] = (int)$row['stock_out'];
            $dateData[$date]['transfer'] = (int)$row['transfer'];
        }
    }
}

// Extract the data in order
foreach ($dateData as $data) {
    $stockInData[] = $data['stock_in'];
    $stockOutData[] = $data['stock_out'];
    $transferData[] = $data['transfer'];
}

// Get low stock items
$lowStockItemsQuery = "
    SELECT
        i.inventory_id,
        s.sku_name,
        l.location_name,
        i.current_quantity,
        i.low_stock_threshold,
        i.critical_threshold,
        i.status
    FROM
        consumable_inventory i
        JOIN sku_master s ON i.sku_id = s.sku_id
        JOIN locations l ON i.location_id = l.location_id
    WHERE
        i.status = 'Low Stock' OR i.status = 'Out of Stock'
    ORDER BY
        CASE
            WHEN i.status = 'Out of Stock' THEN 1
            WHEN i.status = 'Low Stock' THEN 2
            ELSE 3
        END,
        s.sku_name ASC
    LIMIT 10";
$lowStockItemsResult = false;
try {
    $lowStockItemsResult = mysqli_query($conn, $lowStockItemsQuery);
} catch (Exception $e) {
    error_log("Error fetching low stock items: " . $e->getMessage());
}

// Get monthly inventory update status
$month = date('n');
$year = date('Y');
$monthlyUpdateStats = [
    'total' => 0,
    'completed' => 0,
    'pending' => 0,
    'locations' => []
];

try {
    // Get all locations that should update their inventory (all locations)
    $locationsQuery = "
        SELECT l.location_id, l.location_name, l.location_type,
               COALESCE(m.status, 'pending') as status,
               m.updated_at, m.updated_by, u.full_name as updated_by_name
        FROM locations l
        LEFT JOIN monthly_inventory_updates m ON
            l.location_id = m.location_id AND
            m.month = ? AND
            m.year = ?
        LEFT JOIN users u ON m.updated_by = u.user_id
        WHERE l.is_active = 1
        ORDER BY l.location_name
    ";

    $locationsStmt = mysqli_prepare($conn, $locationsQuery);
    mysqli_stmt_bind_param($locationsStmt, 'ii', $month, $year);
    mysqli_stmt_execute($locationsStmt);
    $locationsResult = mysqli_stmt_get_result($locationsStmt);

    while ($location = mysqli_fetch_assoc($locationsResult)) {
        $monthlyUpdateStats['total']++;
        if ($location['status'] === 'completed') {
            $monthlyUpdateStats['completed']++;
        } else {
            $monthlyUpdateStats['pending']++;
        }

        // Add to locations array (limit to 5 for display)
        if (count($monthlyUpdateStats['locations']) < 5) {
            $monthlyUpdateStats['locations'][] = $location;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching monthly inventory update status: " . $e->getMessage());
}
?>

<!-- Include our modern dashboard CSS -->
<link rel="stylesheet" href="/choims/assets/css/logistics-dashboard-modern-white.css">
<link rel="stylesheet" href="/choims/assets/css/logistics-dashboard-buttons.css">
<link rel="stylesheet" href="/choims/assets/css/monthly-inventory-update-modern.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

<!-- Modern dashboard header -->
<header class="dashboard-header animate__animated animate__fadeIn">
    <div class="header-row">
        <div class="title-container">
            <?php
            // Extract first name from full name
            $firstName = '';
            if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                $nameParts = explode(' ', $_SESSION['full_name']);
                $firstName = $nameParts[0];
            }
            ?>
            <h1 class="dashboard-title">Hi, <?php echo htmlspecialchars($firstName); ?></h1>
        </div>
        <div class="dashboard-actions">
            <a href="/choims/modules/assets/add.php" class="action-btn action-primary">
                <i class="fas fa-plus-circle"></i> Add Asset
            </a>
            <a href="/choims/modules/inventory/add_product.php" class="action-btn">
                <i class="fas fa-cubes"></i> Add Inventory
            </a>
            <a href="/choims/modules/transfers/create.php" class="action-btn">
                <i class="fas fa-exchange-alt"></i> Transfer
            </a>
            <a href="/choims/modules/logistics/maintenance.php" class="action-btn">
                <i class="fas fa-tools"></i> Equipment Maintenance
            </a>
            <?php if ($showMonthlyUpdateButton && $userLocationId): ?>
            <a href="#" class="action-btn <?php echo $hasCompletedMonthlyUpdate ? 'bg-success text-white' : 'bg-warning text-dark'; ?>" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                <i class="fas fa-calendar-check"></i> <?php echo $hasCompletedMonthlyUpdate ? 'Update Completed' : 'Monthly Update'; ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
    <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
</header>

<div class="container-fluid">
    <?php if ($showMonthlyUpdateButton && !$hasCompletedMonthlyUpdate && $userLocationId): ?>
    <!-- Monthly Inventory Update Alert -->
    <div class="alert alert-warning mb-4" style="border-left: 4px solid #f6c23e;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-calendar-check me-2"></i> Monthly Inventory Update Required</h5>
                <p class="mb-0">Please update your consumables inventory for the month of <?php echo date('F Y'); ?>.</p>
            </div>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#monthlyUpdateModal">
                <i class="fas fa-clipboard-check me-1"></i> Update Now
            </button>
        </div>
    </div>
    <?php elseif ($showMonthlyUpdateButton && $hasCompletedMonthlyUpdate && $userLocationId): ?>
    <!-- Monthly Inventory Update Completed Alert -->
    <div class="alert alert-success mb-4" style="border-left: 4px solid #1cc88a;">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="alert-heading mb-1"><i class="fas fa-check-circle me-2"></i> Monthly Inventory Update Completed</h5>
                <p class="mb-0">Thank you for completing your inventory update for <?php echo date('F Y'); ?>.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">FIXED ASSETS</div>
                        <div class="stat-value counter-value"><?php echo number_format($assetsCount); ?></div>
                        <div class="stat-description mb-2">Total equipment count</div>
                        <div class="stat-trend">
                            <?php if($userLocationId && $assetsCountInLocation > 0): ?>
                            <span class="text-primary"><i class="fas fa-map-marker-alt me-1"></i><?php echo number_format($assetsCountInLocation); ?> in your location</span>
                            <?php else: ?>
                            <span class="text-muted"><!-- Placeholder for alignment --></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-assets">
                        <i class="fas fa-laptop"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/assets/list.php<?php echo $userLocationId ? '?location='.$userLocationId : ''; ?>" class="view-details text-success">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">CONSUMABLE ITEMS</div>
                        <div class="stat-value counter-value"><?php echo number_format($inventoryCount); ?></div>
                        <div class="stat-description mb-2">Total consumables in system</div>
                        <div class="stat-trend">
                            <span class="text-success"><i class="fas fa-cubes me-1"></i>Total Qty: <?php echo number_format($totalConsumableQty); ?></span>
                        </div>
                        <div class="stat-trend mt-2">
                            <?php if($userLocationId && $totalConsumableQtyInLocation > 0): ?>
                            <span class="text-primary"><i class="fas fa-map-marker-alt me-1"></i><?php echo number_format($totalConsumableQtyInLocation); ?> in your location</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-inventory">
                        <i class="fas fa-boxes"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php" class="view-details text-primary">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">EQUIPMENT MAINTENANCE</div>
                        <div class="stat-value counter-value"><?php echo number_format($maintenanceCount); ?></div>
                        <div class="stat-description mb-2">Office & Medical Equipment</div>
                        <div class="stat-trend">
                            <?php if($defectiveEquipmentCount > 0): ?>
                            <span class="text-danger"><i class="fas fa-exclamation-circle me-1"></i><?php echo number_format($defectiveEquipmentCount); ?> defective equipment</span>
                            <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>No defective equipment</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-maintenance">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/logistics/maintenance.php" class="view-details text-success">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">LOW STOCK ITEMS</div>
                        <div class="stat-value counter-value"><?php echo number_format($lowStockCount); ?></div>
                        <div class="stat-description mb-2">Items needing restocking</div>
                        <div class="stat-trend">
                            <?php if($lowStockCount > 0): ?>
                            <span class="text-warning"><i class="fas fa-exclamation-circle me-1"></i>Items need attention</span>
                            <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>All items in stock</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php?filter=low_stock" class="view-details text-warning">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="dashboard-card h-100 animate__animated animate__fadeInUp">
                <div class="card-body d-flex justify-content-between">
                    <div class="d-flex flex-column">
                        <div class="stat-label">PENDING TRANSFERS</div>
                        <div class="stat-value counter-value"><?php echo number_format($pendingTransfersCount); ?></div>
                        <div class="stat-description mb-2">Awaiting approval/confirmation</div>
                        <div class="stat-trend">
                            <?php if($pendingTransfersCount > 0): ?>
                            <span class="text-primary"><i class="fas fa-clock me-1"></i>Needs attention</span>
                            <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>All transfers processed</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="dashboard-icon icon-transfer">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/transfers/list.php?status=pending" class="view-details text-primary">
                        <span>View details</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Inventory Activity Chart -->
        <div class="col-lg-8 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-1s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chart-line"></i> Consumable Inventory Activity (Last 7 Days)
                    </div>
                    <div class="chart-actions">
                        <button class="btn active" data-chart-period="7d" data-chart="inventoryActivityChart">7D</button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($inventoryActivityResult && mysqli_num_rows($inventoryActivityResult) > 0): ?>
                        <div class="chart-container" style="position: relative; height: 300px;">
                            <canvas id="inventoryActivityChart"></canvas>
                        </div>
                        <div class="chart-legend mt-3 d-flex flex-wrap justify-content-center gap-3">
                            <div class="legend-item">
                                <span class="legend-dot" style="background-color: rgba(16, 185, 129, 0.7);"></span>
                                <span class="legend-label">Stock In</span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-dot" style="background-color: rgba(239, 68, 68, 0.7);"></span>
                                <span class="legend-label">Stock Out</span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-dot" style="background-color: rgba(14, 165, 233, 0.7);"></span>
                                <span class="legend-label">Transfers</span>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-chart-line" style="color: #4CAF50; filter: drop-shadow(0 4px 6px rgba(76, 175, 80, 0.3));"></i>
                            <p>No consumable inventory activity data is available for the last 7 days.</p>
                            <div class="mt-4 d-flex gap-4 justify-content-center">
                                <a href="/choims/modules/inventory/stock_in.php" class="btn-icon-circle" title="Stock In">
                                    <i class="fas fa-arrow-up"></i>
                                </a>
                                <a href="/choims/modules/inventory/stock_out.php" class="btn-icon-circle" title="Stock Out">
                                    <i class="fas fa-arrow-down"></i>
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Asset Distribution by Category -->
        <div class="col-lg-4 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-1s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chart-pie"></i> Assets by Category
                    </div>
                    <div class="chart-actions">
                        <button class="btn active chart-type-toggle" data-chart="assetsByCategoryChart" data-type="doughnut"><i class="fas fa-chart-pie"></i></button>
                        <button class="btn chart-type-toggle" data-chart="assetsByCategoryChart" data-type="bar"><i class="fas fa-chart-bar"></i></button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($categoryLabels) && !empty($categoryData)): ?>
                        <div class="chart-container" style="position: relative; height: 300px;">
                            <canvas id="assetsByCategoryChart"></canvas>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-chart-pie"></i>
                            <p>No asset category data is available.</p>
                            <a href="/choims/modules/assets/add.php" class="btn btn-with-icon btn-sm btn-success">
                                <i class="fas fa-plus-circle"></i> Add Assets
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Transfer Management -->
        <div class="col-lg-7 mb-4">
            <div class="chart-card mb-4 animate__animated animate__fadeIn animate__delay-2s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exchange-alt"></i> Transfer Management
                    </div>
                    <a href="/choims/modules/transfers/list.php" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All Transfers
                    </a>
                </div>

                <?php
                $incomingCount = $incomingTransfersResult ? mysqli_num_rows($incomingTransfersResult) : 0;
                $outgoingCount = $outgoingTransfersResult ? mysqli_num_rows($outgoingTransfersResult) : 0;
                ?>

                <ul class="nav nav-tabs" id="transferTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="incoming-tab" data-bs-toggle="tab" data-bs-target="#incoming" type="button" role="tab" aria-controls="incoming" aria-selected="true">
                            Incoming Transfers <?php if($incomingCount > 0): ?><span class="transfer-count"><?php echo $incomingCount; ?></span><?php endif; ?>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="outgoing-tab" data-bs-toggle="tab" data-bs-target="#outgoing" type="button" role="tab" aria-controls="outgoing" aria-selected="false">
                            Outgoing Transfers <?php if($outgoingCount > 0): ?><span class="transfer-count"><?php echo $outgoingCount; ?></span><?php endif; ?>
                        </button>
                    </li>
                </ul>

                <div class="tab-content p-0">
                    <!-- Incoming Transfers Tab (Action Required) -->
                    <div class="tab-pane fade show active" id="incoming" role="tabpanel" aria-labelledby="incoming-tab">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Transfer</th>
                                        <th>Date</th>
                                        <th>From</th>
                                        <th>Items</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($incomingTransfersResult && mysqli_num_rows($incomingTransfersResult) > 0): ?>
                                        <?php while ($transfer = mysqli_fetch_assoc($incomingTransfersResult)): ?>
                                            <tr class="action-required">
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <div>
                                                            <span class="badge badge-direction badge-incoming">
                                                                <i class="fas fa-arrow-down"></i> Incoming
                                                            </span>
                                                            <span class="badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'bg-success' : 'bg-primary'; ?> mb-1">
                                                                <?php echo $transfer['transfer_type']; ?>
                                                            </span>
                                                        </div>
                                                        <small class="text-muted">
                                                        <?php echo !empty($transfer['transaction_code']) ? $transfer['transaction_code'] : 'N/A'; ?>
                                                        </small>
                                                    </div>
                                                </td>
                                                <td><?php echo isset($transfer['transfer_date']) ? date('M d, Y', strtotime($transfer['transfer_date'])) : 'N/A'; ?></td>
                                                <td><?php echo htmlspecialchars($transfer['source_location'] ?? 'Unknown'); ?></td>
                                                <td><?php echo htmlspecialchars($transfer['item_name'] ?? 'Unknown Item'); ?></td>
                                                <td>
                                                    <?php
                                                    // Check if this transfer requires HIMU approval based on the database field
                                                    $requiresHimuApproval = $transfer['requires_himu_approval'] == 1;

                                                    if ($requiresHimuApproval && $transfer['status'] == 'Approved by Logistics'): ?>
                                                        <span class="status-badge badge-pending">Awaiting HIMU Approval</span>
                                                    <?php elseif ($transfer['status'] == 'Approved by HIMU' || ($transfer['status'] == 'Approved by Logistics' && !$requiresHimuApproval)): ?>
                                                        <span class="status-badge badge-approved">Ready for Acceptance</span>
                                                    <?php else: ?>
                                                        <span class="status-badge badge-approved">Ready for Acceptance</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php
                                                        // Check if this transfer requires HIMU approval based on the database field
                                                        $requiresHimuApproval = isset($transfer['requires_himu_approval']) && $transfer['requires_himu_approval'] == 1;

                                                        // Only show the Receive button if:
                                                        // 1. The transfer doesn't require HIMU approval, OR
                                                        // 2. The transfer has already received HIMU approval (status is "Approved by HIMU")
                                                        if ($requiresHimuApproval && $transfer['status'] == 'Approved by Logistics'): ?>
                                                            <?php if ($transfer['transfer_type'] == 'Individual'): ?>
                                                                <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-primary">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            <?php else: ?>
                                                                <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-primary">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <?php if ($transfer['transfer_type'] == 'Individual'): ?>
                                                                <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-success">
                                                                    <i class="fas fa-check-circle"></i> Receive
                                                                </a>
                                                            <?php else: ?>
                                                                <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-success">
                                                                    <i class="fas fa-check-circle"></i> Receive
                                                                </a>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6">
                                                <div class="empty-state">
                                                    <i class="fas fa-inbox"></i>
                                                    <p>No incoming transfers waiting for your confirmation.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Outgoing Transfers Tab (No Action Required) -->
                    <div class="tab-pane fade" id="outgoing" role="tabpanel" aria-labelledby="outgoing-tab">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Transfer</th>
                                        <th>Date</th>
                                        <th>To</th>
                                        <th>Items</th>
                                        <th>Status</th>
                                        <th>Info</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($outgoingTransfersResult && mysqli_num_rows($outgoingTransfersResult) > 0): ?>
                                        <?php while ($transfer = mysqli_fetch_assoc($outgoingTransfersResult)): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex flex-column">
                                                        <div>
                                                            <span class="badge badge-direction badge-outgoing">
                                                                <i class="fas fa-arrow-up"></i> Outgoing
                                                            </span>
                                                            <span class="badge <?php echo ($transfer['transfer_type'] == 'Batch') ? 'bg-success' : 'bg-primary'; ?> mb-1">
                                                                <?php echo $transfer['transfer_type']; ?>
                                                            </span>
                                                        </div>
                                                        <small class="text-muted">
                                                        <?php echo !empty($transfer['transaction_code']) ? $transfer['transaction_code'] : 'N/A'; ?>
                                                        </small>
                                                    </div>
                                                </td>
                                                <td><?php echo isset($transfer['transfer_date']) ? date('M d, Y', strtotime($transfer['transfer_date'])) : 'N/A'; ?></td>
                                                <td><?php echo htmlspecialchars($transfer['destination_location'] ?? 'Unknown'); ?></td>
                                                <td><?php echo htmlspecialchars($transfer['item_name'] ?? 'Unknown Item'); ?></td>
                                                <td>
                                                    <?php
                                                    // Check if this transfer requires HIMU approval based on the database field
                                                    $requiresHimuApproval = isset($transfer['requires_himu_approval']) && $transfer['requires_himu_approval'] == 1;

                                                    if ($requiresHimuApproval && $transfer['status'] == 'Approved by Logistics'): ?>
                                                        <span class="status-badge badge-pending">Awaiting HIMU Approval</span>
                                                    <?php elseif ($transfer['status'] == 'Approved by HIMU'): ?>
                                                        <span class="status-badge badge-approved">Awaiting Confirmation</span>
                                                    <?php else: ?>
                                                        <span class="status-badge badge-approved">Awaiting Confirmation</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($transfer['transfer_type'] == 'Individual'): ?>
                                                        <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="/choims/modules/transfers/batch/view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i> View
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6">
                                                <div class="empty-state">
                                                    <i class="fas fa-paper-plane"></i>
                                                    <p>No outgoing transfers waiting for confirmation.</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items -->
        <div class="col-lg-5 mb-4">
            <div class="low-stock-container animate__animated animate__fadeIn animate__delay-2s">
                <div class="low-stock-header">
                    <div class="low-stock-header-title">
                        <i class="fas fa-exclamation-triangle"></i> Low Stock Items
                    </div>
                    <a href="/choims/modules/inventory/list.php?filter=low_stock" class="btn-view-all">
                        View All <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
                <div>
                    <?php if ($lowStockItemsResult && mysqli_num_rows($lowStockItemsResult) > 0): ?>
                        <table class="low-stock-table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Location</th>
                                    <th>Quantity</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($item = mysqli_fetch_assoc($lowStockItemsResult)): ?>
                                    <tr>
                                        <td class="text-truncate" style="max-width: 150px;"><?php echo htmlspecialchars($item['sku_name']); ?></td>
                                        <td><?php echo htmlspecialchars($item['location_name']); ?></td>
                                        <td><?php echo number_format($item['current_quantity']); ?></td>
                                        <td>
                                            <?php if ($item['status'] == 'Out of Stock'): ?>
                                                <span class="status-badge badge-rejected">Out of Stock</span>
                                            <?php else: ?>
                                                <span class="status-badge badge-pending">Low Stock</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="low-stock-empty">
                            <i class="fas fa-check-circle"></i>
                            <p>No low stock items at the moment.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Inventory Update Status -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="inventory-update-card animate__animated animate__fadeIn animate__delay-3s">
                <div class="card-header">
                    <div class="header-title">
                        <i class="fas fa-calendar-check"></i>
                        <h6 class="m-0">Monthly Inventory Update Status - <?php echo date('F Y'); ?></h6>
                    </div>
                    <a href="/choims/modules/inventory/monthly_status.php" class="view-report-btn">
                        <i class="fas fa-list"></i> View Full Report
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="monthly-update-stats">
                                <div class="progress-container">
                                    <?php
                                    $completionPercentage = $monthlyUpdateStats['total'] > 0 ?
                                        round(($monthlyUpdateStats['completed'] / $monthlyUpdateStats['total']) * 100) : 0;
                                    ?>
                                    <div class="progress-circle" data-percentage="<?php echo $completionPercentage; ?>">
                                        <div class="progress-circle-inner">
                                            <div class="progress-value"><?php echo $completionPercentage; ?>%</div>
                                            <div class="progress-text">Completed</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="stats-summary">
                                    <div class="stat-item">
                                        <div class="stat-label">Total Locations</div>
                                        <div class="stat-value"><?php echo $monthlyUpdateStats['total']; ?></div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Completed</div>
                                        <div class="stat-value text-success"><?php echo $monthlyUpdateStats['completed']; ?></div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Pending</div>
                                        <div class="stat-value text-warning"><?php echo $monthlyUpdateStats['pending']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="inventory-status-table">
                                    <thead>
                                        <tr>
                                            <th>Location</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Updated By</th>
                                            <th>Updated At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($monthlyUpdateStats['locations'])): ?>
                                            <?php foreach ($monthlyUpdateStats['locations'] as $location): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($location['location_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($location['location_type']); ?></td>
                                                    <td>
                                                        <?php if ($location['status'] === 'completed'): ?>
                                                            <span class="status-badge completed">Completed</span>
                                                        <?php else: ?>
                                                            <span class="status-badge pending">Pending</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo $location['updated_by_name'] ? htmlspecialchars($location['updated_by_name']) : 'N/A'; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo $location['updated_at'] ? date('M d, Y H:i', strtotime($location['updated_at'])) : 'N/A'; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center">No locations found</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Transfers -->
        <div class="col-lg-7 mb-4">
            <div class="chart-card animate__animated animate__fadeIn animate__delay-3s">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-exchange-alt"></i> Recent Transfers
                    </div>
                    <a href="/choims/modules/transfers/list.php" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-list me-1"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Source</th>
                                    <th>Destination</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($recentTransfersResult && mysqli_num_rows($recentTransfersResult) > 0): ?>
                                    <?php while ($transfer = mysqli_fetch_assoc($recentTransfersResult)): ?>
                                        <tr>
                                            <td class="text-truncate" style="max-width: 150px;"><?php echo htmlspecialchars($transfer['item_name'] ?? 'Unknown Item'); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['source_location'] ?? 'Unknown'); ?></td>
                                            <td><?php echo htmlspecialchars($transfer['destination_location'] ?? 'Unknown'); ?></td>
                                            <td><?php echo isset($transfer['transfer_date']) ? date('M d, Y', strtotime($transfer['transfer_date'])) : 'N/A'; ?></td>
                                            <td>
                                                <?php
                                                if (isset($transfer['status'])) {
                                                    switch ($transfer['status']) {
                                                        case 'Pending':
                                                            echo '<span class="status-badge badge-pending">Pending</span>';
                                                            break;
                                                        case 'Approved by Logistics':
                                                            echo '<span class="status-badge badge-approved">Logistics Approved</span>';
                                                            break;
                                                        case 'Approved by HIMU':
                                                            echo '<span class="status-badge badge-approved">HIMU Approved</span>';
                                                            break;
                                                        case 'Completed':
                                                            echo '<span class="status-badge badge-completed">Completed</span>';
                                                            break;
                                                        case 'Rejected':
                                                            echo '<span class="status-badge badge-rejected">Rejected</span>';
                                                            break;
                                                        default:
                                                            echo '<span class="status-badge">Unknown</span>';
                                                    }
                                                } else {
                                                    echo '<span class="status-badge">Unknown</span>';
                                                }
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5">
                                            <div class="empty-state">
                                                <i class="fas fa-exchange-alt"></i>
                                                <p>No recent transfers found.</p>
                                                <a href="/choims/modules/transfers/create.php" class="btn btn-sm btn-success btn-with-icon">
                                                    <i class="fas fa-plus-circle me-1"></i> Initiate Transfer
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Chart.js for modern data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

<!-- Pass chart data to JavaScript -->
<script>
    // Prepare data for charts
    const chartData = {
        activityDates: <?php echo json_encode($activityDates); ?>,
        stockInData: <?php echo json_encode($stockInData); ?>,
        stockOutData: <?php echo json_encode($stockOutData); ?>,
        transferData: <?php echo json_encode($transferData); ?>
    };

    const categoryChartData = {
        categoryLabels: <?php echo json_encode($categoryLabels); ?>,
        categoryData: <?php echo json_encode($categoryData); ?>,
        categoryColors: <?php echo json_encode($categoryColors); ?>
    };

    // Monthly inventory update progress circle
    document.addEventListener('DOMContentLoaded', function() {
        const progressCircle = document.querySelector('.progress-circle');
        if (progressCircle) {
            const percentage = progressCircle.getAttribute('data-percentage');
            progressCircle.style.setProperty('--percentage', percentage + '%');
        }
    });
</script>

<!-- Include our modern dashboard JavaScript -->
<script src="/choims/assets/js/logistics-dashboard.js"></script>

<?php if ($showMonthlyUpdateButton && $userLocationId): ?>
<!-- Monthly Inventory Update Modal -->
<div class="modal fade" id="monthlyUpdateModal" tabindex="-1" aria-labelledby="monthlyUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content inventory-modal">
            <div class="modal-header inventory-modal-header">
                <div class="modal-title-wrapper">
                    <div class="modal-icon-circle">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h5 class="modal-title" id="monthlyUpdateModalLabel">
                        Monthly Inventory Update - <?php echo date('F Y'); ?>
                    </h5>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body inventory-modal-body">
                <?php if ($hasCompletedMonthlyUpdate): ?>
                    <div class="alert alert-success inventory-alert">
                        <div class="alert-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h6 class="alert-heading">Update Complete!</h6>
                            <p class="mb-0">You have already completed your monthly inventory update for <?php echo date('F Y'); ?>.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="inventory-intro">
                        <h5 class="inventory-intro-title">Time for your monthly inventory check</h5>
                        <p class="inventory-intro-text">
                            Please confirm that you have reviewed and updated your consumables inventory for the month of <?php echo date('F Y'); ?>.
                            This will help ensure accurate inventory tracking and planning.
                        </p>
                    </div>

                    <form action="/choims/modules/inventory/monthly_update.php" method="post" class="inventory-update-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="update_quantities" value="1">

                    <!-- Inventory Items to Update -->
                    <div class="inventory-card">
                        <div class="inventory-card-header">
                            <div class="inventory-card-title">
                                <i class="fas fa-edit"></i>
                                <span>Update Inventory Quantities</span>
                            </div>
                            <div class="inventory-search">
                                <input type="text" id="inventorySearchInput" class="inventory-search-input" placeholder="Search items...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="inventory-card-body">
                            <div class="inventory-table-container">
                                <table class="inventory-table" id="inventoryItemsTable">
                                    <thead>
                                        <tr>
                                            <th>SKU Code</th>
                                            <th>Item Name</th>
                                            <th>Category</th>
                                            <th>Current Qty</th>
                                            <th>New Qty</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Get inventory items for this location
                                        $itemsQuery = "
                                            SELECT
                                                i.inventory_id,
                                                i.current_quantity,
                                                i.status,
                                                s.sku_id,
                                                s.sku_code,
                                                s.sku_name,
                                                c.category_name
                                            FROM
                                                consumable_inventory i
                                            JOIN
                                                sku_master s ON i.sku_id = s.sku_id
                                            JOIN
                                                categories c ON s.category_id = c.category_id
                                            WHERE
                                                i.location_id = ?
                                            ORDER BY
                                                c.category_name, s.sku_name
                                        ";
                                        $itemsStmt = mysqli_prepare($conn, $itemsQuery);
                                        mysqli_stmt_bind_param($itemsStmt, 'i', $userLocationId);
                                        mysqli_stmt_execute($itemsStmt);
                                        $itemsResult = mysqli_stmt_get_result($itemsStmt);

                                        $rowCount = 0;

                                        while ($item = mysqli_fetch_assoc($itemsResult)):
                                            $rowCount++;
                                        ?>
                                        <tr class="inventory-item-row">
                                            <td><span class="sku-code"><?php echo $item['sku_code']; ?></span></td>
                                            <td><?php echo $item['sku_name']; ?></td>
                                            <td><span class="category-name"><?php echo $item['category_name']; ?></span></td>
                                            <td>
                                                <span class="current-qty"><?php echo $item['current_quantity']; ?></span>
                                            </td>
                                            <td>
                                                <input type="hidden" name="inventory_ids[]" value="<?php echo $item['inventory_id']; ?>">
                                                <input type="hidden" name="old_quantities[]" value="<?php echo $item['current_quantity']; ?>">
                                                <div class="quantity-input-wrapper">
                                                    <input type="number" name="new_quantities[]" class="quantity-input new-qty"
                                                           value="<?php echo $item['current_quantity']; ?>" min="0">
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($item['status'] == 'Available'): ?>
                                                    <span class="status-badge status-available">Available</span>
                                                <?php elseif ($item['status'] == 'Low Stock'): ?>
                                                    <span class="status-badge status-low">Low Stock</span>
                                                <?php else: ?>
                                                    <span class="status-badge status-out">Out of Stock</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="inventory-tip">
                        <div class="tip-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="tip-content">
                            <h6>Tip</h6>
                            <p>Update the quantities above to reflect your current inventory. Any changes will be recorded in the audit log.</p>
                        </div>
                    </div>

                    <div class="form-group notes-group">
                        <label for="notes" class="form-label">Notes or Comments (Optional)</label>
                        <textarea class="form-control notes-input" id="notes" name="notes" rows="3" placeholder="Enter any notes about your inventory update..."></textarea>
                    </div>

                    <div class="confirm-checkbox">
                        <input class="confirm-input" type="checkbox" id="confirmUpdate" required>
                        <label class="confirm-label" for="confirmUpdate">
                            I confirm that I have reviewed and updated the inventory for the Logistics department for the month of <?php echo date('F Y'); ?>.
                        </label>
                    </div>

                    <div class="form-actions">
                        <a href="/choims/modules/inventory/list.php" class="btn-review">
                            <i class="fas fa-list"></i> Review Inventory First
                        </a>
                        <button type="submit" class="btn-complete" id="submitUpdate" disabled>
                            <i class="fas fa-check-circle"></i> Complete Monthly Update
                        </button>
                    </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Enhanced inventory update modal interactions
    document.addEventListener('DOMContentLoaded', function() {
        const confirmCheckbox = document.getElementById('confirmUpdate');
        const submitButton = document.getElementById('submitUpdate');
        const inventoryItemRows = document.querySelectorAll('.inventory-item-row');

        // Enable/disable submit button based on checkbox with animation
        if (confirmCheckbox && submitButton) {
            confirmCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    submitButton.disabled = false;
                    submitButton.classList.add('btn-complete-active');
                } else {
                    submitButton.disabled = true;
                    submitButton.classList.remove('btn-complete-active');
                }
            });
        }

        // Enhanced quantity input interactions
        const newQtyInputs = document.querySelectorAll('.new-qty');
        if (newQtyInputs) {
            newQtyInputs.forEach(input => {
                // Add focus effects
                input.addEventListener('focus', function() {
                    this.closest('.quantity-input-wrapper').classList.add('input-focused');
                });

                input.addEventListener('blur', function() {
                    this.closest('.quantity-input-wrapper').classList.remove('input-focused');
                });

                // Highlight changed quantities with animation
                input.addEventListener('change', function() {
                    const row = this.closest('tr');
                    const currentQty = parseInt(row.querySelector('.current-qty').textContent);
                    const newQty = parseInt(this.value);

                    if (newQty !== currentQty) {
                        // Add changed state
                        this.classList.add('qty-changed');
                        row.classList.add('row-changed');

                        // Add visual indicator for increase/decrease
                        if (newQty > currentQty) {
                            this.classList.add('qty-increased');
                            this.classList.remove('qty-decreased');
                        } else if (newQty < currentQty) {
                            this.classList.add('qty-decreased');
                            this.classList.remove('qty-increased');
                        }
                    } else {
                        // Remove all change indicators
                        this.classList.remove('qty-changed', 'qty-increased', 'qty-decreased');
                        row.classList.remove('row-changed');
                    }
                });
            });
        }

        // Add animation to the modal when it opens
        const monthlyUpdateModal = document.getElementById('monthlyUpdateModal');
        if (monthlyUpdateModal) {
            monthlyUpdateModal.addEventListener('shown.bs.modal', function() {
                const modalContent = this.querySelector('.modal-content');
                modalContent.classList.add('modal-animate-in');
            });
        }
    });
</script>
<?php endif; ?>

<?php
require_once($base_path . '/includes/footer.php');
?>