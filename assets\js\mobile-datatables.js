/**
 * Mobile-optimized DataTables Configuration
 * This file provides responsive DataTables settings for mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth < 768;
    
    // Find all DataTables
    const tables = document.querySelectorAll('.dataTable');
    
    tables.forEach(table => {
        // Check if DataTable is initialized
        if ($.fn.DataTable.isDataTable(table)) {
            const dataTable = $(table).DataTable();
            
            if (isMobile) {
                // Adjust page length for mobile
                dataTable.page.len(10).draw();
                
                // Enhance mobile display
                enhanceMobileTable(table);
            }
        } else {
            // Initialize with mobile-friendly options
            initMobileDataTable(table);
        }
    });
});

/**
 * Initialize a DataTable with mobile-friendly options
 */
function initMobileDataTable(tableElement) {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth < 768;
    
    // Default options
    const options = {
        responsive: true,
        autoWidth: false,
        language: {
            "search": "<i class='fas fa-search'></i> ",
            "searchPlaceholder": "Search...",
            "paginate": {
                "previous": "<i class='fas fa-chevron-left'></i>",
                "next": "<i class='fas fa-chevron-right'></i>"
            },
            "info": "Showing _START_ to _END_ of _TOTAL_",
            "infoEmpty": "No records available",
            "zeroRecords": "No matching records found"
        },
        dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
             "<'row'<'col-sm-12'tr>>" +
             "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        initComplete: function() {
            // Add custom styling to the DataTables elements
            $('.dataTables_wrapper .dataTables_filter input').addClass('form-control form-control-sm ms-2');
            $('.dataTables_wrapper .dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_info').addClass('text-muted small pt-3');
            
            // Mobile-specific enhancements
            if (isMobile) {
                enhanceMobileTable(this);
            }
        }
    };
    
    // Mobile-specific options
    if (isMobile) {
        options.pageLength = 10;
        options.lengthMenu = [[5, 10, 25, -1], [5, 10, 25, "All"]];
        options.dom = "<'row'<'col-12'f>>" +
                      "<'row'<'col-12'tr>>" +
                      "<'row'<'col-6'i><'col-6'p>>" +
                      "<'row'<'col-12'l>>";
    } else {
        options.pageLength = 25;
        options.lengthMenu = [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]];
    }
    
    // Initialize DataTable
    $(tableElement).DataTable(options);
}

/**
 * Enhance a table for mobile display
 */
function enhanceMobileTable(tableElement) {
    // Get the table jQuery object
    const $table = $(tableElement);
    
    // Make sure the table is in a responsive wrapper
    if (!$table.parent().hasClass('table-responsive')) {
        $table.wrap('<div class="table-responsive"></div>');
    }
    
    // Optimize column visibility for mobile
    const dataTable = $table.DataTable();
    
    // If the table has more than 4 columns, hide some less important ones
    const columnCount = dataTable.columns().nodes().length;
    
    if (columnCount > 4) {
        // Find columns that might be less important (status, dates, etc.)
        // This is a heuristic approach - adjust based on your specific tables
        dataTable.columns().every(function(index) {
            const columnHeader = $(this.header()).text().toLowerCase();
            
            // Keep the first column (usually ID or name) and action columns
            if (index === 0 || columnHeader.includes('action')) {
                return;
            }
            
            // Hide less important columns
            if (columnHeader.includes('date') || 
                columnHeader.includes('created') || 
                columnHeader.includes('updated') ||
                columnHeader.includes('status') ||
                columnHeader.includes('description')) {
                dataTable.column(index).visible(false);
            }
        });
    }
    
    // Add touch-friendly styles to rows
    $table.find('tbody tr').addClass('mobile-table-row');
    
    // Add custom styles
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 767.98px) {
            .mobile-table-row {
                transition: background-color 0.2s;
            }
            
            .mobile-table-row:active {
                background-color: rgba(46, 125, 50, 0.1);
            }
            
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter,
            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_paginate {
                text-align: left;
                float: none;
                display: block;
                margin-bottom: 0.75rem;
            }
            
            .dataTables_wrapper .dataTables_filter input {
                width: 100%;
                margin-left: 0;
                margin-top: 0.5rem;
                padding: 0.5rem;
                height: 44px;
            }
            
            .dataTables_wrapper .dataTables_paginate .paginate_button {
                padding: 0.5rem 0.75rem;
                margin: 0 0.1rem;
            }
        }
    `;
    document.head.appendChild(style);
}
