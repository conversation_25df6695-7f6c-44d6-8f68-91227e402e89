<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has Health Center permission
if (!$auth->hasRole(ROLE_HEALTH_CENTER)) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

$currentUser = $auth->getCurrentUser();
$healthCenterId = $currentUser['health_center_id'];

try {
    // Get health center details
    $stmt = $db->prepare("SELECT * FROM health_centers WHERE health_center_id = ?");
    $stmt->execute([$healthCenterId]);
    $healthCenter = $stmt->fetch();

    // Fetch health center inventory statistics
    $stmt = $db->prepare("
        SELECT 
            (SELECT COUNT(*) FROM fixed_assets 
             WHERE health_center_id = ? AND status = 'in_use') as assets_in_use,
            (SELECT COUNT(*) FROM fixed_assets 
             WHERE health_center_id = ? AND status = 'under_repair') as assets_under_repair,
            (SELECT COUNT(*) FROM fixed_assets 
             WHERE health_center_id = ? AND status = 'defective') as defective_assets,
            (SELECT COUNT(DISTINCT item_id) FROM inventory 
             WHERE health_center_id = ? AND quantity > 0) as items_in_stock,
            (SELECT COUNT(DISTINCT item_id) FROM inventory 
             WHERE health_center_id = ? AND quantity <= minimum_stock) as low_stock_items,
            (SELECT COUNT(DISTINCT item_id) FROM inventory inv
             JOIN items i ON inv.item_id = i.item_id
             JOIN categories c ON i.category_id = c.category_id
             WHERE inv.health_center_id = ? AND c.code = 'MED') as medical_supplies_count
    ");
    $stmt->execute([$healthCenterId, $healthCenterId, $healthCenterId, $healthCenterId, $healthCenterId, $healthCenterId]);
    $centerStats = $stmt->fetch();

    // Fetch pending outgoing transfers
    $stmt = $db->prepare("
        SELECT tr.*, 
               i.name as item_name,
               CONCAT(u.first_name, ' ', u.last_name) as requester_name,
               dd.name as dest_dept,
               dhc.name as dest_hc,
               c.name as category_name
        FROM transfer_requests tr
        JOIN items i ON tr.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN users u ON tr.requested_by = u.user_id
        LEFT JOIN departments dd ON tr.destination_department_id = dd.department_id
        LEFT JOIN health_centers dhc ON tr.destination_health_center_id = dhc.health_center_id
        WHERE tr.source_health_center_id = ?
        AND tr.status = 'pending'
        ORDER BY tr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$healthCenterId]);
    $outgoingTransfers = $stmt->fetchAll();

    // Fetch pending incoming transfers
    $stmt = $db->prepare("
        SELECT tr.*, 
               i.name as item_name,
               CONCAT(u.first_name, ' ', u.last_name) as requester_name,
               sd.name as source_dept,
               shc.name as source_hc,
               c.name as category_name
        FROM transfer_requests tr
        JOIN items i ON tr.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        JOIN users u ON tr.requested_by = u.user_id
        LEFT JOIN departments sd ON tr.source_department_id = sd.department_id
        LEFT JOIN health_centers shc ON tr.source_health_center_id = shc.health_center_id
        WHERE tr.destination_health_center_id = ?
        AND tr.status = 'pending'
        ORDER BY tr.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$healthCenterId]);
    $incomingTransfers = $stmt->fetchAll();

    // Fetch low stock medical supplies
    $stmt = $db->prepare("
        SELECT i.name, i.item_id, inv.quantity, inv.minimum_stock,
               c.name as category_name
        FROM inventory inv
        JOIN items i ON inv.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        WHERE inv.health_center_id = ?
        AND inv.quantity <= inv.minimum_stock
        AND c.code = 'MED'
        ORDER BY inv.quantity ASC
        LIMIT 5
    ");
    $stmt->execute([$healthCenterId]);
    $lowMedicalSupplies = $stmt->fetchAll();

    // Fetch recent stock movements
    $stmt = $db->prepare("
        SELECT sm.*, i.name as item_name,
               c.name as category_name
        FROM stock_movements sm
        JOIN items i ON sm.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        WHERE sm.health_center_id = ?
        ORDER BY sm.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$healthCenterId]);
    $recentMovements = $stmt->fetchAll();

    // Fetch expiring medical supplies
    $stmt = $db->prepare("
        SELECT i.name, inv.quantity, inv.expiry_date,
               c.name as category_name
        FROM inventory inv
        JOIN items i ON inv.item_id = i.item_id
        JOIN categories c ON i.category_id = c.category_id
        WHERE inv.health_center_id = ?
        AND c.code = 'MED'
        AND inv.expiry_date IS NOT NULL
        AND inv.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 3 MONTH)
        ORDER BY inv.expiry_date ASC
        LIMIT 5
    ");
    $stmt->execute([$healthCenterId]);
    $expiringSupplies = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Error in Health Center dashboard: " . $e->getMessage());
    setFlashMessage('error', 'Error loading dashboard data');
}

require_once '../templates/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo htmlspecialchars($healthCenter['name']); ?></h1>
            <p class="text-muted">Health Center Dashboard</p>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i> Export Reports
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="../reports/health-center-inventory.php">Health Center Inventory</a></li>
                <li><a class="dropdown-item" href="../reports/medical-supplies.php">Medical Supplies Report</a></li>
                <li><a class="dropdown-item" href="../reports/expiring-supplies.php">Expiring Supplies Report</a></li>
                <li><a class="dropdown-item" href="../reports/health-center-transfers.php">Transfer History</a></li>
            </ul>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <a href="../inventory/transfers/request.php" class="btn btn-primary">
                            <i class="fas fa-exchange-alt"></i> Request Transfer
                        </a>
                        <a href="../inventory/medical-supplies/index.php" class="btn btn-success">
                            <i class="fas fa-medkit"></i> Medical Supplies
                        </a>
                        <a href="../inventory/fixed-assets/health-center.php" class="btn btn-info">
                            <i class="fas fa-laptop"></i> Fixed Assets
                        </a>
                        <a href="../inventory/consumables/health-center.php" class="btn btn-warning">
                            <i class="fas fa-boxes"></i> Other Supplies
                        </a>
                        <a href="../inventory/transfers/health-center.php" class="btn btn-secondary">
                            <i class="fas fa-history"></i> Transfer History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <!-- Medical Supplies -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Medical Supplies
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($centerStats['medical_supplies_count']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-medkit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fixed Assets -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Fixed Assets
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($centerStats['assets_in_use']); ?>
                            </div>
                            <div class="text-xs text-warning">
                                <?php echo number_format($centerStats['assets_under_repair']); ?> Under Repair
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-laptop fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other Supplies -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Other Supplies
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($centerStats['items_in_stock'] - $centerStats['medical_supplies_count']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?php echo number_format($centerStats['low_stock_items']); ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Pending Transfers -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt mr-1"></i>
                        Transfer Requests
                    </h6>
                </div>
                <div class="card-body">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs mb-3">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#outgoing">Outgoing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#incoming">Incoming</a>
                        </li>
                    </ul>

                    <!-- Tab content -->
                    <div class="tab-content">
                        <!-- Outgoing Transfers -->
                        <div class="tab-pane active" id="outgoing">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Reference</th>
                                            <th>Item</th>
                                            <th>To</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($outgoingTransfers as $transfer): ?>
                                        <tr>
                                            <td>
                                                <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>">
                                                    <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                            <td>
                                                <?php 
                                                echo htmlspecialchars(isset($transfer['dest_dept']) ? $transfer['dest_dept'] : isset($transfer['dest_hc']) ? $transfer['dest_hc'] : 'N/A');
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status = [];
                                                if ($transfer['requires_himu_approval']) {
                                                    $status[] = "HIMU: " . ucfirst($transfer['himu_approval_status']);
                                                }
                                                $status[] = "Logistics: " . ucfirst($transfer['logistics_approval_status']);
                                                echo implode("<br>", $status);
                                                ?>
                                            </td>
                                            <td>
                                                <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                                   class="btn btn-primary btn-sm">
                                                    View
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Incoming Transfers -->
                        <div class="tab-pane" id="incoming">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Reference</th>
                                            <th>Item</th>
                                            <th>From</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($incomingTransfers as $transfer): ?>
                                        <tr>
                                            <td>
                                                <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>">
                                                    <?php echo htmlspecialchars($transfer['reference_number']); ?>
                                                </a>
                                            </td>
                                            <td><?php echo htmlspecialchars($transfer['item_name']); ?></td>
                                            <td>
                                                <?php 
                                                echo htmlspecialchars(isset($transfer['source_dept']) ? $transfer['source_dept'] : isset($transfer['source_hc']) ? $transfer['source_hc'] : 'N/A');
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status = [];
                                                if ($transfer['requires_himu_approval']) {
                                                    $status[] = "HIMU: " . ucfirst($transfer['himu_approval_status']);
                                                }
                                                $status[] = "Logistics: " . ucfirst($transfer['logistics_approval_status']);
                                                echo implode("<br>", $status);
                                                ?>
                                            </td>
                                            <td>
                                                <a href="../inventory/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>" 
                                                   class="btn btn-primary btn-sm">
                                                    View
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <a href="../inventory/transfers/health-center.php" class="btn btn-primary btn-sm">
                            View All Transfers
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Stock Movements -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-exchange-alt mr-1"></i>
                        Recent Stock Movements
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>Type</th>
                                    <th>Quantity</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentMovements as $movement): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($movement['item_name']); ?></td>
                                    <td><?php echo htmlspecialchars($movement['category_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $movement['movement_type'] === 'in' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($movement['movement_type']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo number_format($movement['quantity']); ?></td>
                                    <td><?php echo formatDate($movement['created_at'], 'M d, H:i'); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/stock-movements.php" class="btn btn-info btn-sm">
                            View All Stock Movements
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Medical Supplies Section -->
    <div class="row">
        <!-- Low Medical Supplies -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        Low Medical Supplies
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>Current Stock</th>
                                    <th>Min. Stock</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($lowMedicalSupplies as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['category_name']); ?></td>
                                    <td class="text-danger"><?php echo number_format($item['quantity']); ?></td>
                                    <td><?php echo number_format($item['minimum_stock']); ?></td>
                                    <td>
                                        <a href="../inventory/transfers/request.php?item_id=<?php echo $item['item_id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            Request Transfer
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/medical-supplies/low-stock.php" class="btn btn-danger btn-sm">
                            View All Low Stock Medical Supplies
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiring Medical Supplies -->
        <div class="col-xl-6 col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock mr-1"></i>
                        Expiring Medical Supplies
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Category</th>
                                    <th>Quantity</th>
                                    <th>Expiry Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($expiringSupplies as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['category_name']); ?></td>
                                    <td><?php echo number_format($item['quantity']); ?></td>
                                    <td class="<?php echo strtotime($item['expiry_date']) < strtotime('+1 month') ? 'text-danger' : 'text-warning'; ?>">
                                        <?php echo formatDate($item['expiry_date'], 'M d, Y'); ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="../inventory/medical-supplies/expiring.php" class="btn btn-warning btn-sm">
                            View All Expiring Medical Supplies
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../templates/footer.php'; ?>
</rewritten_file>
