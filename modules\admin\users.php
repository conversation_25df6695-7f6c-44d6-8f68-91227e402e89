<?php
// Use relative paths
$base_path = dirname(dirname(dirname(__FILE__)));
require_once($base_path . '/includes/detailed_audit_log.php');

// AJAX endpoint moved to get_password.php

require_once($base_path . '/includes/header.php');

// Add modern React-like UI styles
?>
<link rel="stylesheet" href="/choims/assets/css/admin-users-modern-enhanced.css">
<link rel="stylesheet" href="/choims/assets/css/admin-users-modal-enhanced.css">
<script src="/choims/assets/js/modern-forms.js" defer></script>
<script src="/choims/assets/js/admin-users-modal-enhanced.js" defer></script>
<!-- Animate.css is already loaded in header.php -->
<?php

// Ensure user has appropriate role
requireRole('superadmin');

// Function to get user password (for superadmin only)
function getUserPassword($conn, $user_id) {
    $query = "SELECT password FROM users WHERE user_id = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'i', $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($row = mysqli_fetch_assoc($result)) {
        return $row['password'];
    }

    return null;
}

// Process form submissions
$message = '';
$error = '';

// Add new user
if (isset($_POST['add_user'])) {
    $username = sanitizeInput($_POST['username']);
    $password = sanitizeInput($_POST['password']);
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $full_name = $first_name . ' ' . $last_name; // Combine for backward compatibility
    $email = sanitizeInput($_POST['email']);
    $role = sanitizeInput($_POST['role']);
    $location_id = !empty($_POST['location_id']) ? (int)$_POST['location_id'] : null;

    // Validate inputs
    if (empty($username) || empty($password) || empty($full_name) || empty($role)) {
        $error = "Please fill all required fields";
    } else {
        // Check if username already exists
        $checkQuery = "SELECT user_id FROM users WHERE username = ?";
        $checkStmt = mysqli_prepare($conn, $checkQuery);
        mysqli_stmt_bind_param($checkStmt, 's', $username);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_store_result($checkStmt);

        if (mysqli_stmt_num_rows($checkStmt) > 0) {
            $error = "Username already exists";
        } else {
            // Store password in plain text as requested
            // NOTE: This is not secure and should only be used for development/testing

            // Insert new user
            $query = "INSERT INTO users (username, password, full_name, first_name, last_name, email, role, location_id, is_active, created_at)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'sssssssi', $username, $password, $full_name, $first_name, $last_name, $email, $role, $location_id);

            if (mysqli_stmt_execute($stmt)) {
                $userId = mysqli_insert_id($conn);
                // Log to regular audit log
                logActivity($conn, 'Create', 'User', $userId, null, "Created user: $username");

                // Log to detailed audit system
                $new_values = [
                    'username' => $username,
                    'full_name' => $full_name,
                    'first_name' => $first_name,
                    'last_name' => $last_name,
                    'email' => $email,
                    'role' => $role,
                    'location_id' => $location_id
                ];
                logUserAction($conn, $_SESSION['user_id'], 'create', $userId, null, $new_values);

                $message = "User added successfully";
            } else {
                $error = "Error adding user: " . mysqli_error($conn);
            }
        }
    }
}

// Edit user
if (isset($_POST['edit_user'])) {
    $user_id = (int)$_POST['user_id'];
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $full_name = $first_name . ' ' . $last_name; // Combine for backward compatibility
    $email = sanitizeInput($_POST['email']);
    $role = sanitizeInput($_POST['role']);
    $location_id = !empty($_POST['location_id']) ? (int)$_POST['location_id'] : null;

    // Validate inputs
    if (empty($first_name) || empty($last_name) || empty($role)) {
        $error = "Please fill all required fields";
    } else {
        // Get old values for audit log
        $getOldQuery = "SELECT * FROM users WHERE user_id = ?";
        $getOldStmt = mysqli_prepare($conn, $getOldQuery);
        mysqli_stmt_bind_param($getOldStmt, 'i', $user_id);
        mysqli_stmt_execute($getOldStmt);
        $oldResult = mysqli_stmt_get_result($getOldStmt);
        $oldValues = mysqli_fetch_assoc($oldResult);

        // Check if user exists
        if (!$oldValues) {
            $error = "User not found";
            // Skip the rest of the code
            goto skip_user_update;
        }

        // Update user
        $query = "UPDATE users SET full_name = ?, first_name = ?, last_name = ?, email = ?, role = ?, location_id = ? WHERE user_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'sssssii', $full_name, $first_name, $last_name, $email, $role, $location_id, $user_id);

        if (mysqli_stmt_execute($stmt)) {
            // Log to regular audit log
            logActivity($conn, 'Update', 'User', $user_id, json_encode($oldValues), "Updated user details");

            // Log to detailed audit system
            $new_values = [
                'full_name' => $full_name,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email,
                'role' => $role,
                'location_id' => $location_id
            ];
            $old_values = [
                'full_name' => $oldValues['full_name'],
                'first_name' => $oldValues['first_name'] ?? '',
                'last_name' => $oldValues['last_name'] ?? '',
                'email' => $oldValues['email'],
                'role' => $oldValues['role'],
                'location_id' => $oldValues['location_id']
            ];
            logUserAction($conn, $_SESSION['user_id'], 'update', $user_id, $old_values, $new_values);

            $message = "User updated successfully";
        } else {
            $error = "Error updating user: " . mysqli_error($conn);
        }

        skip_user_update:
    }
}

// Change password
if (isset($_POST['change_password'])) {
    $user_id = (int)$_POST['user_id'];
    $password = sanitizeInput($_POST['password']);

    if (empty($password)) {
        $error = "Please enter a password";
    } else {
        // Store password in plain text as requested
        // NOTE: This is not secure and should only be used for development/testing

        $query = "UPDATE users SET password = ? WHERE user_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'si', $password, $user_id);

        if (mysqli_stmt_execute($stmt)) {
            // Log to regular audit log
            logActivity($conn, 'Update', 'User', $user_id, null, "Changed user password");

            // Log to detailed audit system
            $new_values = [
                'password' => $password, // Store actual password for admin viewing
                'password_reset' => true   // Flag to indicate this is a password reset
            ];
            logUserAction($conn, $_SESSION['user_id'], 'update', $user_id, null, $new_values);

            $message = "Password updated successfully";
        } else {
            $error = "Error updating password: " . mysqli_error($conn);
        }
    }
}

// Toggle user status (activate/deactivate)
if (isset($_POST['toggle_status'])) {
    $user_id = (int)$_POST['user_id'];
    $status = $_POST['status'] == '1' ? 1 : 0;
    $status_text = $status ? 'activated' : 'deactivated';

    // Don't allow deactivating own account
    if ($user_id == $_SESSION['user_id'] && $status == 0) {
        $error = "You cannot deactivate your own account";
    } else {
        $query = "UPDATE users SET is_active = ? WHERE user_id = ?";
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ii', $status, $user_id);

        if (mysqli_stmt_execute($stmt)) {
            // Log to regular audit log
            logActivity($conn, 'Update', 'User', $user_id, null, "User $status_text");

            // Get user details for the log
            $userQuery = "SELECT * FROM users WHERE user_id = ?";
            $userStmt = mysqli_prepare($conn, $userQuery);
            mysqli_stmt_bind_param($userStmt, 'i', $user_id);
            mysqli_stmt_execute($userStmt);
            $userResult = mysqli_stmt_get_result($userStmt);
            $userData = mysqli_fetch_assoc($userResult);

            // Log to detailed audit system
            $old_values = [
                'is_active' => $status ? 0 : 1
            ];
            $new_values = [
                'is_active' => $status
            ];
            logUserAction($conn, $_SESSION['user_id'], 'update', $user_id, $old_values, $new_values);

            $message = "User $status_text successfully";
        } else {
            $error = "Error changing user status: " . mysqli_error($conn);
        }
    }
}

// Get all users
$query = "SELECT u.*, l.location_name
          FROM users u
          LEFT JOIN locations l ON u.location_id = l.location_id
          ORDER BY u.username";
$result = mysqli_query($conn, $query);

// Get all locations for dropdown
$locationsQuery = "SELECT location_id, location_name FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);
$locations = [];
while ($location = mysqli_fetch_assoc($locationsResult)) {
    $locations[] = $location;
}
?>

<!-- We're using the external CSS file for all styles now -->

<div class="container-fluid animate__animated animate__fadeIn">
    <h1 class="page-title">
        <i class="fas fa-users"></i>
        User Management
    </h1>

    <?php if (!empty($message)): ?>
        <div class="alert alert-success animate__animated animate__fadeIn">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger animate__animated animate__fadeIn">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <?php
    // Get user statistics
    $totalUsersQuery = "SELECT COUNT(*) as total FROM users";
    $activeUsersQuery = "SELECT COUNT(*) as active FROM users WHERE is_active = 1";
    $inactiveUsersQuery = "SELECT COUNT(*) as inactive FROM users WHERE is_active = 0";
    $recentLoginQuery = "SELECT COUNT(*) as recent FROM users WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)";

    $totalUsers = mysqli_fetch_assoc(mysqli_query($conn, $totalUsersQuery))['total'];
    $activeUsers = mysqli_fetch_assoc(mysqli_query($conn, $activeUsersQuery))['active'];
    $inactiveUsers = mysqli_fetch_assoc(mysqli_query($conn, $inactiveUsersQuery))['inactive'];
    $recentLogins = mysqli_fetch_assoc(mysqli_query($conn, $recentLoginQuery))['recent'];
    ?>

    <div class="stats-container animate__animated animate__fadeInUp animate__faster">
        <div class="stat-card stat-primary">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $totalUsers; ?></div>
                <div class="stat-label">Total Users</div>
            </div>
        </div>

        <div class="stat-card stat-info">
            <div class="stat-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $activeUsers; ?></div>
                <div class="stat-label">Active Users</div>
            </div>
        </div>

        <div class="stat-card stat-danger">
            <div class="stat-icon">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $inactiveUsers; ?></div>
                <div class="stat-label">Inactive Users</div>
            </div>
        </div>

        <div class="stat-card stat-warning">
            <div class="stat-icon">
                <i class="fas fa-sign-in-alt"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo $recentLogins; ?></div>
                <div class="stat-label">Recent Logins (7 days)</div>
            </div>
        </div>
    </div>

    <!-- Filters - Modern React-like design -->
    <div class="filter-card animate__animated animate__fadeInUp animate__faster">
        <div class="filter-card-header">
            <div class="filter-title">
                <i class="fas fa-filter"></i> Filter Users
            </div>
            <div>
                <button type="button" class="action-btn action-btn-primary" id="toggleFilters">
                    <i class="fas fa-sliders-h"></i> Adjust Filters
                </button>
            </div>
        </div>
        <div class="filter-card-body" id="filterBody">
            <div class="filter-form" style="display: none;">
                <div class="filter-group">
                    <label class="filter-label">Role</label>
                    <select class="filter-control" id="filterRole">
                        <option value="">All Roles</option>
                        <option value="GodMode">GodMode</option>
                        <option value="Superadmin">Superadmin</option>
                        <option value="Logistics">Logistics</option>
                        <option value="HIMU">HIMU</option>
                        <option value="Department">Department</option>
                        <option value="HealthCenter">Health Center</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Status</label>
                    <select class="filter-control" id="filterStatus">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Location</label>
                    <select class="filter-control" id="filterLocation">
                        <option value="">All Locations</option>
                        <?php foreach ($locations as $location): ?>
                            <option value="<?php echo $location['location_id']; ?>"><?php echo htmlspecialchars($location['location_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Last Login</label>
                    <select class="filter-control" id="filterLastLogin">
                        <option value="">Any Time</option>
                        <option value="7">Last 7 Days</option>
                        <option value="30">Last 30 Days</option>
                        <option value="90">Last 90 Days</option>
                        <option value="never">Never Logged In</option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button type="button" class="action-btn action-btn-secondary" id="resetFilters">
                        <i class="fas fa-undo"></i> Reset Filters
                    </button>
                    <button type="button" class="action-btn action-btn-primary" id="applyFilters">
                        <i class="fas fa-check"></i> Apply Filters
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results - Modern React-like design -->
    <div class="records-card animate__animated animate__fadeInUp animate__delay-3">
        <div class="records-card-header">
            <div class="records-title">
                <i class="fas fa-users"></i> User List
                <span class="filter-badge"><?php echo mysqli_num_rows($result); ?> users found</span>
            </div>
            <div>
                <button type="button" class="action-btn action-btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-user-plus"></i> Add New User
                </button>
            </div>
        </div>
        <div class="records-card-body">
            <div class="records-search">
                <i class="fas fa-search"></i>
                <input type="text" id="userSearch" placeholder="Search users...">
            </div>

            <div class="table-responsive">
                <table class="modern-table" id="usersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Role</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($user = mysqli_fetch_assoc($result)): ?>
                            <tr>
                                <td>
                                    <div class="user-card">
                                        <div class="user-avatar"><?php echo strtoupper(substr($user['full_name'], 0, 1)); ?></div>
                                        <div class="user-info">
                                            <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                                            <div class="user-email">
                                                <?php echo htmlspecialchars($user['full_name']); ?> •
                                                <?php echo htmlspecialchars($user['email']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="role-badge role-<?php echo $user['role']; ?>">
                                        <i class="fas fa-user-tag"></i> <?php echo htmlspecialchars($user['role']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars(isset($user['location_name']) ? $user['location_name'] : 'N/A'); ?></td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                        <span class="status-badge status-active"><i class="fas fa-check-circle"></i> Active</span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive"><i class="fas fa-times-circle"></i> Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $user['last_login'] ? formatDate($user['last_login']) : 'Never'; ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="table-action-btn edit-btn" title="Edit User"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editUserModal"
                                                data-id="<?php echo $user['user_id']; ?>"
                                                data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                                data-fullname="<?php echo htmlspecialchars($user['full_name']); ?>"
                                                data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                data-role="<?php echo htmlspecialchars($user['role']); ?>"
                                                data-location="<?php echo $user['location_id']; ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <button class="table-action-btn view-btn" title="Change Password"
                                                data-bs-toggle="modal"
                                                data-bs-target="#changePasswordModal"
                                                data-id="<?php echo $user['user_id']; ?>"
                                                data-username="<?php echo htmlspecialchars($user['username']); ?>">
                                            <i class="fas fa-key"></i>
                                        </button>

                                        <?php if ($user['user_id'] != $_SESSION['user_id']): // Don't allow toggling own account ?>
                                            <form method="post" action="" class="d-inline" onsubmit="return confirm('Are you sure you want to <?php echo $user['is_active'] ? 'deactivate' : 'activate'; ?> this user?');">
                                                <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                                <input type="hidden" name="status" value="<?php echo $user['is_active'] ? '0' : '1'; ?>">
                                                <button type="submit" name="toggle_status" class="table-action-btn <?php echo $user['is_active'] ? 'delete-btn' : 'edit-btn'; ?>" title="<?php echo $user['is_active'] ? 'Deactivate User' : 'Activate User'; ?>">
                                                    <i class="fas <?php echo $user['is_active'] ? 'fa-user-times' : 'fa-user-check'; ?>"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-enhanced">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus"></i> Add New User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="" id="addUserForm">
                <div class="modal-body">
                    <div class="user-avatar-container">
                        <div class="user-avatar-large">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>

                    <input type="hidden" id="full_name" name="full_name">

                    <div class="form-section">
                        <h6><i class="fas fa-shield-alt"></i> Account Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3 required-field">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required
                                       placeholder="Enter username">
                            </div>
                            <div class="col-md-6 mb-3 required-field">
                                <label for="password" class="form-label">
                                    <i class="fas fa-key"></i> Password
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required
                                           placeholder="Enter password">
                                    <button class="btn btn-outline-secondary password-toggle" type="button" tabindex="-1" title="Show password">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>

                                <div class="form-text">Enter a secure password</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h6><i class="fas fa-id-card"></i> Personal Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3 required-field">
                                <label for="first_name" class="form-label">
                                    <i class="fas fa-user-edit"></i> First Name
                                </label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required
                                       placeholder="Enter first name">
                            </div>
                            <div class="col-md-6 mb-3 required-field">
                                <label for="last_name" class="form-label">
                                    <i class="fas fa-user-edit"></i> Last Name
                                </label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required
                                       placeholder="Enter last name">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i> Email
                            </label>
                            <input type="email" class="form-control" id="email" name="email"
                                   placeholder="Enter email address">
                            <div class="form-text">We'll never share your email with anyone else</div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h6><i class="fas fa-lock"></i> System Access</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3 required-field">
                                <label for="role" class="form-label">
                                    <i class="fas fa-user-tag"></i> Role
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <?php if (hasRole('godmode')): ?>
                                        <option value="GodMode">GodMode</option>
                                        <option value="Superadmin">Superadmin</option>
                                    <?php endif; ?>
                                    <option value="Logistics">Logistics</option>
                                    <option value="HIMU">HIMU</option>
                                    <option value="Department">Department</option>
                                    <option value="HealthCenter">Health Center</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="location_id" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> Location
                                </label>
                                <select class="form-select" id="location_id" name="location_id">
                                    <option value="">None</option>
                                    <?php foreach ($locations as $location): ?>
                                        <option value="<?php echo $location['location_id']; ?>"><?php echo htmlspecialchars($location['location_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Required for Department and Health Center roles</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" name="add_user" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-enhanced">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">
                    <i class="fas fa-user-edit"></i> Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="user-avatar-container">
                        <div class="user-avatar-large" id="edit_user_avatar">U</div>
                    </div>

                    <input type="hidden" name="user_id" id="edit_user_id">
                    <input type="hidden" id="edit_full_name" name="full_name">

                    <div class="form-section">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-user me-2"></i>Username
                            </label>
                            <input type="text" class="form-control" id="edit_username" readonly>
                            <div class="form-text">Username cannot be changed</div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h6 class="mb-3">Personal Information</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_first_name" class="form-label">
                                    <i class="fas fa-id-card me-2"></i>First Name*
                                </label>
                                <input type="text" class="form-control" id="edit_first_name" name="first_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_last_name" class="form-label">
                                    <i class="fas fa-id-card me-2"></i>Last Name*
                                </label>
                                <input type="text" class="form-control" id="edit_last_name" name="last_name" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>Email
                            </label>
                            <input type="email" class="form-control" id="edit_email" name="email" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-section">
                        <h6 class="mb-3">System Access</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_role" class="form-label">
                                    <i class="fas fa-user-tag me-2"></i>Role*
                                </label>
                                <select class="form-select" id="edit_role" name="role" required>
                                    <option value="">Select Role</option>
                                    <?php if (hasRole('godmode')): ?>
                                        <option value="GodMode">GodMode</option>
                                        <option value="Superadmin">Superadmin</option>
                                    <?php endif; ?>
                                    <option value="Logistics">Logistics</option>
                                    <option value="HIMU">HIMU</option>
                                    <option value="Department">Department</option>
                                    <option value="HealthCenter">Health Center</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_location_id" class="form-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>Location
                                </label>
                                <select class="form-select" id="edit_location_id" name="location_id">
                                    <option value="">None</option>
                                    <?php foreach ($locations as $location): ?>
                                        <option value="<?php echo $location['location_id']; ?>"><?php echo htmlspecialchars($location['location_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">Required for Department and Health Center roles</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" name="edit_user" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-enhanced">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">
                    <i class="fas fa-key"></i> Change Password
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="">
                <div class="modal-body">
                    <div class="user-avatar-container">
                        <div class="user-avatar-large" id="password_user_avatar">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>

                    <input type="hidden" name="user_id" id="password_user_id">

                    <div class="form-section">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-user me-2"></i>Username
                            </label>
                            <input type="text" class="form-control" id="password_username" readonly>
                        </div>
                    </div>

                    <div class="form-section">
                        <h6 class="mb-3">Security Information</h6>
                        <div class="mb-4">
                            <label for="current_password" class="form-label">
                                <i class="fas fa-key me-2"></i>Current Password
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="current_password" readonly placeholder="Loading current password...">
                            </div>
                        </div>
                        <div class="mb-4">
                            <label for="new_password" class="form-label">
                                <i class="fas fa-key me-2"></i>New Password*
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" name="password" required placeholder="Enter new password">
                                <button class="btn btn-outline-secondary toggle-password" type="button" tabindex="-1">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" name="change_password" class="btn btn-primary">
                        <i class="fas fa-key me-2"></i>Change Password
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle filters visibility
$(document).ready(function() {
    $('#toggleFilters').on('click', function() {
        $('.filter-form').slideToggle(300);
        $(this).find('i').toggleClass('fa-sliders-h fa-times');
    });

    // Reset filters
    $('#resetFilters').on('click', function() {
        $('#filterRole, #filterStatus, #filterLocation, #filterLastLogin').val('');
    });

    // Apply filters
    $('#applyFilters').on('click', function() {
        const role = $('#filterRole').val();
        const status = $('#filterStatus').val();
        const location = $('#filterLocation').val();
        const lastLogin = $('#filterLastLogin').val();

        // Get DataTable instance
        const table = $('#usersTable').DataTable();

        // Clear any existing filters
        table.search('').columns().search('').draw();

        // Apply filters
        if (role) {
            table.column(1).search(role).draw();
        }

        if (status) {
            const statusText = status === 'active' ? 'Active' : 'Inactive';
            table.column(3).search(statusText).draw();
        }

        if (location) {
            // Find the location name from the location ID
            const locationName = $('#filterLocation option:selected').text();
            table.column(2).search(locationName).draw();
        }

        if (lastLogin) {
            // This is more complex and might require custom filtering
            // For now, just search for 'Never' if lastLogin is 'never'
            if (lastLogin === 'never') {
                table.column(4).search('Never').draw();
            }
        }
    });

    // Password toggle functionality is handled in admin-users-modal-enhanced.js
});

// Fill edit user modal with user data
document.addEventListener('DOMContentLoaded', function() {
    // Update full_name when first_name or last_name changes
    const updateFullName = (prefix) => {
        const firstNameInput = document.getElementById(prefix + 'first_name');
        const lastNameInput = document.getElementById(prefix + 'last_name');
        const fullNameInput = document.getElementById(prefix + 'full_name');

        if (firstNameInput && lastNameInput && fullNameInput) {
            const updateValue = () => {
                fullNameInput.value = (firstNameInput.value + ' ' + lastNameInput.value).trim();
            };

            firstNameInput.addEventListener('input', updateValue);
            lastNameInput.addEventListener('input', updateValue);

            // Initial update
            updateValue();
        }
    };

    // Apply to both add and edit forms
    updateFullName('');
    updateFullName('edit_');

    // Password strength checker
    const checkPasswordStrength = (password) => {
        let strength = 0;
        const strengthBar = document.getElementById('password-strength-bar');
        const strengthText = document.getElementById('password-strength-text');

        if (!strengthBar || !strengthText) return;

        // Remove existing classes
        strengthBar.className = 'password-strength-bar';

        if (password.length === 0) {
            strengthBar.style.width = '0';
            strengthText.textContent = 'Password strength';
            return;
        }

        // Check password strength
        if (password.length >= 8) strength += 1;
        if (password.match(/[a-z]+/)) strength += 1;
        if (password.match(/[A-Z]+/)) strength += 1;
        if (password.match(/[0-9]+/)) strength += 1;
        if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;

        // Update strength bar and text
        switch (strength) {
            case 0:
            case 1:
                strengthBar.classList.add('password-weak');
                strengthText.textContent = 'Weak password';
                strengthText.style.color = '#ef4444';
                break;
            case 2:
                strengthBar.classList.add('password-fair');
                strengthText.textContent = 'Fair password';
                strengthText.style.color = '#f59e0b';
                break;
            case 3:
            case 4:
                strengthBar.classList.add('password-good');
                strengthText.textContent = 'Good password';
                strengthText.style.color = '#3b82f6';
                break;
            case 5:
                strengthBar.classList.add('password-strong');
                strengthText.textContent = 'Strong password';
                strengthText.style.color = '#10b981';
                break;
        }
    };

    // Add event listener to password field in the add user modal only
    const addPasswordField = document.getElementById('password');
    if (addPasswordField) {
        addPasswordField.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }

    // Handle edit user modal
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            const fullName = this.getAttribute('data-fullname');
            const email = this.getAttribute('data-email');
            const role = this.getAttribute('data-role');
            const locationId = this.getAttribute('data-location');

            document.getElementById('edit_user_id').value = userId;
            document.getElementById('edit_username').value = username;

            // Split full name into first and last name
            let nameParts = fullName.split(' ');
            let firstName = nameParts[0] || '';
            let lastName = nameParts.slice(1).join(' ') || '';

            document.getElementById('edit_first_name').value = firstName;
            document.getElementById('edit_last_name').value = lastName;
            document.getElementById('edit_full_name').value = fullName; // Keep full_name for backward compatibility
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_role').value = role;
            document.getElementById('edit_location_id').value = locationId || '';

            // Update avatar with first letter of username
            const userAvatar = document.getElementById('edit_user_avatar');
            if (userAvatar) {
                userAvatar.textContent = username.charAt(0).toUpperCase();
            }
        });
    });

    // Fill change password modal
    const passwordButtons = document.querySelectorAll('.view-btn');
    passwordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');

            document.getElementById('password_user_id').value = userId;
            document.getElementById('password_username').value = username;

            // Reset password field
            const newPasswordField = document.getElementById('new_password');
            if (newPasswordField) {
                newPasswordField.value = '';
            }

            // Update avatar with first letter of username
            const userAvatar = document.getElementById('password_user_avatar');
            if (userAvatar) {
                userAvatar.innerHTML = `<i class="fas fa-lock"></i>`;
            }

            // Fetch and display current password
            const currentPasswordField = document.getElementById('current_password');
            if (currentPasswordField) {
                currentPasswordField.value = 'Loading...';

                // Make AJAX request to get the password
                // Use a dedicated endpoint for getting the password
                const url = `/choims/modules/admin/get_password.php?user_id=${userId}`;
                console.log('Fetching password from:', url);

                fetch(url)
                    .then(response => {
                        console.log('Response status:', response.status);

                        // Check if the response is JSON
                        const contentType = response.headers.get('content-type');
                        if (!contentType || !contentType.includes('application/json')) {
                            // If not JSON, get the text and log it for debugging
                            return response.text().then(text => {
                                console.error('Received non-JSON response:', text.substring(0, 500) + '...');
                                throw new Error('Server returned HTML instead of JSON. Check server logs.');
                            });
                        }

                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }

                        return response.json();
                    })
                    .then(data => {
                        console.log('Response data:', data);
                        if (data.error) {
                            currentPasswordField.value = 'Error: ' + data.error;
                            console.error('Server error:', data.error);
                        } else if (data.password) {
                            // Always display the password as plain text
                            currentPasswordField.value = data.password;
                        } else {
                            currentPasswordField.value = 'No password data received';
                            console.error('No password in response:', data);
                        }
                    })
                    .catch(error => {
                        currentPasswordField.value = 'Error: ' + error.message;
                        console.error('Fetch error:', error);
                    });
            }
        });
    });

    // Password toggle functionality is handled in admin-users-modal-enhanced.js

    // Show appropriate location dropdown based on role
    const roleSelects = document.querySelectorAll('#role, #edit_role');
    roleSelects.forEach(select => {
        select.addEventListener('change', function() {
            const locationSelect = this.id === 'role' ?
                document.getElementById('location_id') :
                document.getElementById('edit_location_id');

            if (this.value === 'Department' || this.value === 'HealthCenter') {
                locationSelect.required = true;
                locationSelect.parentElement.classList.add('required-field');
            } else {
                locationSelect.required = false;
                locationSelect.parentElement.classList.remove('required-field');
            }
        });
    });

    // Manual search functionality
    const userSearch = document.getElementById('userSearch');
    if (userSearch) {
        userSearch.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const table = document.getElementById('usersTable');
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchText)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Initialize DataTable with modern styling
    if (typeof $.fn.DataTable !== 'undefined') {
        // Use custom search box instead of default DataTables search
        const userSearch = document.getElementById('userSearch');

        var table = $('#usersTable').DataTable({
            "ordering": true,
            "order": [[0, "asc"]],
            "paging": true,
            "searching": true,
            "info": true,
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            "responsive": true,
            "autoWidth": false,
            "dom": '<"row"<"col-sm-12"tr>><"row"<"col-sm-5"i><"col-sm-7"p>>',
            "language": {
                "paginate": {
                    "previous": "<i class='fas fa-chevron-left'></i>",
                    "next": "<i class='fas fa-chevron-right'></i>"
                },
                "info": "Showing _START_ to _END_ of _TOTAL_ users",
                "infoEmpty": "Showing 0 users",
                "zeroRecords": "No matching users found"
            },
            "drawCallback": function() {
                // Add hover effect to rows
                $(this).find('tbody tr').hover(
                    function() { $(this).addClass('hover-effect'); },
                    function() { $(this).removeClass('hover-effect'); }
                );
            }
        });

        // Search functionality for the custom search box
        $('#userSearch').on('keyup', function() {
            table.search(this.value).draw();
        });
    }

    // Add card hover effects
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.1)';
            this.style.transition = 'all 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
});
</script>

<?php
// AJAX endpoint moved to the top of the file

require_once($base_path . '/includes/footer.php');
?>