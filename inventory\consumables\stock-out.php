<?php
require_once '../../config/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = Auth::getInstance();
$db = Database::getInstance()->getConnection();

// Check if user has permission
if (!in_array($auth->getCurrentUser()['role'], [ROLE_GODMODE, ROLE_SUPERADMIN, ROLE_LOGISTICS, ROLE_INVENTORY])) {
    header("Location: " . BASE_URL . "/access-denied.php");
    exit;
}

// Get consumable ID from URL
$consumableId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$consumableId) {
    setFlashMessage('error', 'Invalid consumable ID');
    header("Location: index.php");
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db->beginTransaction();

        // Get current quantity and reorder point
        $stmt = $db->prepare("SELECT quantity, reorder_point FROM consumables WHERE consumable_id = ?");
        $stmt->execute([$consumableId]);
        $current = $stmt->fetch();

        $requestedQuantity = (int)$_POST['quantity'];
        
        // Check if there's enough stock
        if ($requestedQuantity > $current['quantity']) {
            throw new Exception("Insufficient stock. Available: " . $current['quantity']);
        }

        $newQuantity = $current['quantity'] - $requestedQuantity;
        $status = $newQuantity > $current['reorder_point'] ? 'in_stock' : 
                 ($newQuantity > 0 ? 'low_stock' : 'out_of_stock');

        // Update consumable quantity and status
        $stmt = $db->prepare("
            UPDATE consumables 
            SET quantity = ?,
                status = ?
            WHERE consumable_id = ?
        ");
        $stmt->execute([
            $newQuantity,
            $status,
            $consumableId
        ]);

        // Record the transaction
        $stmt = $db->prepare("
            INSERT INTO transactions (
                item_id, type, quantity, reference_number, 
                transaction_date, user_id, remarks,
                department_id, health_center_id
            ) VALUES (
                (SELECT item_id FROM consumables WHERE consumable_id = ?),
                'out',
                ?,
                ?,
                NOW(),
                ?,
                ?,
                NULLIF(?, ''),
                NULLIF(?, '')
            )
        ");
        $stmt->execute([
            $consumableId,
            $requestedQuantity,
            $_POST['reference_number'],
            $auth->getCurrentUser()['user_id'],
            $_POST['remarks'],
            $_POST['department_id'],
            $_POST['health_center_id']
        ]);

        // Log the activity
        $auth->logActivity($auth->getCurrentUser()['user_id'], 'stock_out', 'consumables', $consumableId);

        $db->commit();
        setFlashMessage('success', 'Stock deducted successfully');
        header("Location: view.php?id=" . $consumableId);
        exit;
    } catch (Exception $e) {
        $db->rollBack();
        error_log("Error deducting stock: " . $e->getMessage());
        setFlashMessage('error', $e->getMessage());
    }
}

// Fetch consumable details
try {
    $query = "
        SELECT c.*, i.name as item_name, i.sku
        FROM consumables c
        JOIN items i ON c.item_id = i.item_id
        WHERE c.consumable_id = ?
    ";
    $stmt = $db->prepare($query);
    $stmt->execute([$consumableId]);
    $consumable = $stmt->fetch();

    if (!$consumable) {
        setFlashMessage('error', 'Consumable not found');
        header("Location: index.php");
        exit;
    }

    // Fetch departments
    $stmt = $db->query("SELECT department_id, name FROM departments WHERE status = 'active' ORDER BY name");
    $departments = $stmt->fetchAll();

    // Fetch health centers
    $stmt = $db->query("SELECT health_center_id, name FROM health_centers WHERE status = 'active' ORDER BY name");
    $healthCenters = $stmt->fetchAll();

} catch (PDOException $e) {
    error_log("Error fetching data: " . $e->getMessage());
    setFlashMessage('error', 'Error loading data');
    header("Location: index.php");
    exit;
}

require_once '../../templates/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Stock Out - <?php echo htmlspecialchars($consumable['item_name']); ?></h1>
        <div>
            <a href="view.php?id=<?php echo $consumableId; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Details
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label required">Quantity</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="quantity" name="quantity" 
                                               min="1" max="<?php echo $consumable['quantity']; ?>" required>
                                        <span class="input-group-text"><?php echo htmlspecialchars($consumable['unit']); ?></span>
                                    </div>
                                    <div class="form-text">Maximum available: <?php echo number_format($consumable['quantity']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reference_number" class="form-label required">Reference Number</label>
                                    <input type="text" class="form-control" id="reference_number" name="reference_number" 
                                           placeholder="e.g., REQ-2024-001" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department_id" class="form-label">Department</label>
                                    <select class="form-select" id="department_id" name="department_id">
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?php echo $dept['department_id']; ?>">
                                                <?php echo htmlspecialchars($dept['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="health_center_id" class="form-label">Health Center</label>
                                    <select class="form-select" id="health_center_id" name="health_center_id">
                                        <option value="">Select Health Center</option>
                                        <?php foreach ($healthCenters as $center): ?>
                                            <option value="<?php echo $center['health_center_id']; ?>">
                                                <?php echo htmlspecialchars($center['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                        </div>

                        <div class="text-end">
                            <a href="view.php?id=<?php echo $consumableId; ?>" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-danger">Deduct Stock</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Current Stock Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Current Stock Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th width="40%">SKU:</th>
                            <td><?php echo htmlspecialchars($consumable['sku']); ?></td>
                        </tr>
                        <tr>
                            <th>Current Quantity:</th>
                            <td>
                                <?php echo number_format($consumable['quantity']); ?> 
                                <?php echo htmlspecialchars($consumable['unit']); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <span class="badge bg-<?php 
                                    echo $consumable['status'] === 'in_stock' ? 'success' : 
                                        ($consumable['status'] === 'low_stock' ? 'warning' : 'danger'); 
                                ?>">
                                    <?php echo ucwords(str_replace('_', ' ', $consumable['status'])); ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>Reorder Point:</th>
                            <td><?php echo number_format($consumable['reorder_point']); ?></td>
                        </tr>
                        <tr>
                            <th>Current Batch:</th>
                            <td><?php echo $consumable['batch_number'] ? htmlspecialchars($consumable['batch_number']) : 'N/A'; ?></td>
                        </tr>
                        <tr>
                            <th>Current Expiry:</th>
                            <td><?php echo $consumable['expiry_date'] ? formatDate($consumable['expiry_date']) : 'N/A'; ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()

// Mutual exclusivity between department and health center
document.getElementById('department_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('health_center_id').value = ''
        document.getElementById('health_center_id').disabled = true
    } else {
        document.getElementById('health_center_id').disabled = false
    }
})

document.getElementById('health_center_id').addEventListener('change', function() {
    if (this.value) {
        document.getElementById('department_id').value = ''
        document.getElementById('department_id').disabled = true
    } else {
        document.getElementById('department_id').disabled = false
    }
})
</script>

<?php require_once '../../templates/footer.php'; ?>
