<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';

try {
    // Create connection without database selected
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database created successfully\n";
    
    // Select the database
    $pdo->exec("USE " . DB_NAME);
    
    // Read and execute schema.sql
    $sql = file_get_contents(__DIR__ . '/schema.sql');
    $pdo->exec($sql);
    echo "Database schema created successfully\n";
    
    // Create default admin user
    $username = 'admin';
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $email = '<EMAIL>';
    $fullName = 'System Administrator';
    
    $stmt = $pdo->prepare("INSERT INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$username, $password, $email, $fullName, ROLE_GODMODE]);
    echo "Default admin user created successfully\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    
    echo "\nDatabase setup completed successfully!\n";
    
} catch(PDOException $e) {
    die("ERROR: Could not set up database. " . $e->getMessage());
}
