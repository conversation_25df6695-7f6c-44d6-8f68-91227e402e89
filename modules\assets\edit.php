<?php
// Start output buffering at the beginning
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireRole('Logistics');

$success_message = '';
$error_message = '';

// Get asset ID from URL
$asset_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : 0;

// Check for missing fields from URL parameter
$missing_fields = [];
$coming_from_transfer = false;
if (isset($_GET['missing'])) {
    $missing_fields = explode(',', $_GET['missing']);
    $error_message = 'Please complete the required fields highlighted in red before transferring this asset.';
    $coming_from_transfer = true;
}

// Check if asset exists
$checkQuery = "SELECT * FROM fixed_assets WHERE asset_id = ?";
$checkStmt = mysqli_prepare($conn, $checkQuery);
mysqli_stmt_bind_param($checkStmt, 'i', $asset_id);
mysqli_stmt_execute($checkStmt);
$checkResult = mysqli_stmt_get_result($checkStmt);

if (mysqli_num_rows($checkResult) == 0) {
    // Asset not found
    echo '<div class="container-fluid"><div class="alert alert-danger">Asset not found.</div></div>';
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
    exit();
}

$asset = mysqli_fetch_assoc($checkResult);

// Check if Logistics user is trying to edit an asset at a different location
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;
if (hasRole('Logistics') && $userLocationId && $asset['current_location_id'] != $userLocationId) {
    // Redirect to view page with error message
    $_SESSION['error'] = "You cannot edit assets located at a different location from your assigned location.";
    header("Location: /choims/modules/assets/view.php?id=" . $asset_id);
    exit();
}

// Check for active transfers
$activeTransferQuery = "SELECT COUNT(*) as count FROM transfers
                       WHERE asset_id = ?
                       AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')";
$activeTransferStmt = mysqli_prepare($conn, $activeTransferQuery);
mysqli_stmt_bind_param($activeTransferStmt, 'i', $asset_id);
mysqli_stmt_execute($activeTransferStmt);
$activeTransferResult = mysqli_stmt_get_result($activeTransferStmt);
$activeTransfer = mysqli_fetch_assoc($activeTransferResult);

if ($activeTransfer['count'] > 0) {
    // Redirect to view page with error message
    $_SESSION['error'] = "This asset has an active transfer in progress and cannot be edited.";
    header("Location: /choims/modules/assets/view.php?id=" . $asset_id);
    exit();
}

// Get categories for dropdown
$categoriesQuery = "SELECT * FROM categories WHERE category_id IN (1, 2, 3) ORDER BY category_name"; // Only IT, Office, Medical Equipment
$categoriesResult = mysqli_query($conn, $categoriesQuery);

// Get sources for dropdown
$sourcesQuery = "SELECT * FROM sources ORDER BY source_name";
$sourcesResult = mysqli_query($conn, $sourcesQuery);

// Get suppliers for dropdown
$suppliersQuery = "SELECT * FROM suppliers ORDER BY supplier_name";
$suppliersResult = mysqli_query($conn, $suppliersQuery);

// Get locations for dropdown
$locationsQuery = "SELECT * FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);

// Get current SKU details
$skuQuery = "SELECT sm.*, c.category_id FROM sku_master sm
             JOIN categories c ON sm.category_id = c.category_id
             WHERE sm.sku_id = ?";
$skuStmt = mysqli_prepare($conn, $skuQuery);
mysqli_stmt_bind_param($skuStmt, 'i', $asset['sku_id']);
mysqli_stmt_execute($skuStmt);
$skuResult = mysqli_stmt_get_result($skuStmt);
$skuInfo = mysqli_fetch_assoc($skuResult);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate form inputs
    $sku_id = sanitizeInput($_POST['sku_id']);
    $asset_name = sanitizeInput($_POST['asset_name']);
    $serial_number = sanitizeInput($_POST['serial_number']);
    $model = sanitizeInput($_POST['model']);
    $specifications = sanitizeInput($_POST['specifications']);
    $status = sanitizeInput($_POST['status']);
    $assigned_to = sanitizeInput($_POST['assigned_to']);
    $current_location_id = sanitizeInput($_POST['current_location_id']);
    $unit_section = sanitizeInput(isset($_POST['unit_section']) ? $_POST['unit_section'] : '');
    $local_mr = sanitizeInput($_POST['local_mr']);
    $purchase_date = !empty($_POST['purchase_date']) ? sanitizeInput($_POST['purchase_date']) : null;
    $unit_cost = sanitizeInput($_POST['unit_cost']);
    $receipt_type = sanitizeInput($_POST['receipt_type']);

    // Map "Local MR" to "D.R" for database compatibility
    if ($receipt_type === 'Local MR') {
        $receipt_type = 'D.R';
    }

    $series_number = sanitizeInput($_POST['series_number']);
    $supplier_id = sanitizeInput($_POST['supplier_id']);
    $warranty_expiry = !empty($_POST['warranty_expiry']) ? sanitizeInput($_POST['warranty_expiry']) : null;
    $source_id = sanitizeInput($_POST['source_id']);
    $remarks = sanitizeInput($_POST['remarks']);

    // Get old values for audit log
    $oldValues = json_encode($asset);

    // Use a direct SQL approach without prepared statements for debugging
    try {
        // First, escape all values to prevent SQL injection
        $sku_id_safe = mysqli_real_escape_string($conn, $sku_id);
        $asset_name_safe = mysqli_real_escape_string($conn, $asset_name);
        $serial_number_safe = mysqli_real_escape_string($conn, $serial_number);
        $model_safe = mysqli_real_escape_string($conn, $model);
        $specifications_safe = mysqli_real_escape_string($conn, $specifications);
        $status_safe = mysqli_real_escape_string($conn, $status);
        $assigned_to_safe = mysqli_real_escape_string($conn, $assigned_to);
        $current_location_id_safe = mysqli_real_escape_string($conn, $current_location_id);
        $unit_section_safe = mysqli_real_escape_string($conn, $unit_section);
        $local_mr_safe = mysqli_real_escape_string($conn, $local_mr);

        // Ensure purchase date is NULL if empty or zero date
        if (empty($purchase_date) || $purchase_date === '0000-00-00') {
            $purchase_date_sql = "NULL";
        } else {
            $purchase_date_safe = mysqli_real_escape_string($conn, $purchase_date);
            $purchase_date_sql = "'$purchase_date_safe'";
        }

        // Handle empty unit cost by setting to NULL
        if (empty($unit_cost) || $unit_cost === '') {
            $unit_cost_sql = "NULL";
        } else {
            $unit_cost_safe = mysqli_real_escape_string($conn, $unit_cost);
            $unit_cost_sql = "'$unit_cost_safe'";
        }

        // Handle empty receipt type by setting to NULL
        if (empty($receipt_type) || $receipt_type === '') {
            $receipt_type_sql = "NULL";
        } else {
            $receipt_type_safe = mysqli_real_escape_string($conn, $receipt_type);
            $receipt_type_sql = "'$receipt_type_safe'";
        }

        $series_number_safe = mysqli_real_escape_string($conn, $series_number);
        $supplier_id_safe = mysqli_real_escape_string($conn, $supplier_id);

        // Ensure warranty date is NULL if empty or zero date
        if (empty($warranty_expiry) || $warranty_expiry === '0000-00-00') {
            $warranty_expiry_sql = "NULL";
        } else {
            $warranty_expiry_safe = mysqli_real_escape_string($conn, $warranty_expiry);
            $warranty_expiry_sql = "'$warranty_expiry_safe'";
        }

        $source_id_safe = mysqli_real_escape_string($conn, $source_id);
        $remarks_safe = mysqli_real_escape_string($conn, $remarks);
        $asset_id_safe = mysqli_real_escape_string($conn, $asset_id);

        // Debug log
        error_log("Edit Asset - purchase_date: " . var_export($purchase_date, true) . ", SQL: $purchase_date_sql");
        error_log("Edit Asset - warranty_expiry: " . var_export($warranty_expiry, true) . ", SQL: $warranty_expiry_sql");
        error_log("Edit Asset - unit_cost: " . var_export($unit_cost, true) . ", SQL: $unit_cost_sql");
        error_log("Edit Asset - receipt_type: " . var_export($receipt_type, true) . ", SQL: $receipt_type_sql");

        // Build and execute direct SQL query
        $directSQL = "
            UPDATE fixed_assets
            SET
                sku_id = '$sku_id_safe',
                asset_name = '$asset_name_safe',
                serial_number = '$serial_number_safe',
                model = '$model_safe',
                specifications = '$specifications_safe',
                status = '$status_safe',
                assigned_to = '$assigned_to_safe',
                current_location_id = '$current_location_id_safe',
                unit_section = '$unit_section_safe',
                local_mr = '$local_mr_safe',
                purchase_date = $purchase_date_sql,
                unit_cost = $unit_cost_sql,
                receipt_type = $receipt_type_sql,
                series_number = '$series_number_safe',
                supplier_id = " . (empty($supplier_id) ? "NULL" : "'$supplier_id_safe'") . ",
                warranty_expiry = $warranty_expiry_sql,
                source_id = " . (empty($source_id) ? "NULL" : "'$source_id_safe'") . ",
                remarks = '$remarks_safe',
                updated_at = NOW()
            WHERE asset_id = '$asset_id_safe'
        ";

        // Execute the direct SQL query
        $executed = mysqli_query($conn, $directSQL);

        if ($executed) {
            // Get updated values for audit log
            $newQuery = "SELECT * FROM fixed_assets WHERE asset_id = '$asset_id_safe'";
            $newResult = mysqli_query($conn, $newQuery);
            $newValues = json_encode(mysqli_fetch_assoc($newResult));

            // Create audit log
            logActivity($conn, "Updated asset", "fixed_assets", $asset_id, $oldValues, $newValues);

            $success_message = "Asset updated successfully!";

            // If user was redirected from transfer validation and required fields are now filled, go to transfer page
            if ($coming_from_transfer) {
                // Check if all previously missing fields are now filled
                $newMissingFields = [];
                if (empty($local_mr)) $newMissingFields[] = 'local_mr';
                if (empty($receipt_type)) $newMissingFields[] = 'receipt_type';
                if (empty($assigned_to)) $newMissingFields[] = 'assigned_to';

                // If all required fields are now filled, proceed to transfer page
                if (empty($newMissingFields)) {
                    header("Location: /choims/modules/transfers/create.php?type=asset&asset_id=$asset_id");
                    exit();
                }
            }

            // Default redirect after successful update
            $redirectUrl = isset($_GET['return_url']) ? urldecode($_GET['return_url']) : "view.php?id=$asset_id";
            header("Location: $redirectUrl");
            exit();

            // Refresh asset data
            mysqli_stmt_execute($checkStmt);
            $checkResult = mysqli_stmt_get_result($checkStmt);
            $asset = mysqli_fetch_assoc($checkResult);

            // Refresh SKU data
            mysqli_stmt_execute($skuStmt);
            $skuResult = mysqli_stmt_get_result($skuStmt);
            $skuInfo = mysqli_fetch_assoc($skuResult);
        } else {
            $error_message = "Error updating asset: " . mysqli_error($conn);
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }

    // Add debug comment with error information
    if (!empty($error_message)) {
        echo "<!-- Debug Error: " . htmlspecialchars($error_message) . " -->";
    }
}

// Get SKUs for the selected category
$selectedCategoryId = $skuInfo['category_id'];
$skusQuery = "SELECT * FROM sku_master WHERE category_id = ? AND item_type = 'Fixed' ORDER BY sku_name";
$skusStmt = mysqli_prepare($conn, $skusQuery);
mysqli_stmt_bind_param($skusStmt, 'i', $selectedCategoryId);
mysqli_stmt_execute($skusStmt);
$skusResult = mysqli_stmt_get_result($skusStmt);

?>

<!-- Add our modern CSS after the main stylesheet -->
<link rel="stylesheet" href="/choims/assets/css/modern-forms.css">
<!-- Add our modern JS file -->
<script src="/choims/assets/js/modern-forms.js"></script>

<div class="container-fluid px-3 py-2">
    <div class="d-flex justify-content-between align-items-center mb-2">
        <h2 class="fw-bold text-dark mb-0 fs-5"><i class="fas fa-edit me-1 text-success"></i>Edit Fixed Asset</h2>
        <div>
            <?php
            // Use return URL if provided, otherwise default to view page
            $backUrl = isset($_GET['return_url']) ? urldecode($_GET['return_url']) : "view.php?id=$asset_id";
            ?>
            <a href="<?php echo htmlspecialchars($backUrl); ?>" class="react-btn react-btn-secondary btn-sm">
                <i class="fas fa-arrow-left react-btn-icon"></i>Back
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="react-alert react-alert-success">
            <i class="fas fa-check-circle react-alert-icon"></i>
            <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="react-alert react-alert-danger">
            <i class="fas fa-exclamation-circle react-alert-icon"></i>
            <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <form action="" method="post">
        <!-- Ultra compact view with three cards per row -->
        <div class="compact-view-3">
            <!-- Basic Information Card -->
            <div class="react-card shadow-sm">
                <div class="react-card-header bg-light">
                    <h3 class="react-card-title"><i class="fas fa-info-circle me-2 text-primary"></i>Basic Information</h3>
                </div>
                <div class="react-card-body">
                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="category_id" name="category_id" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <?php
                                    mysqli_data_seek($categoriesResult, 0);
                                    while ($category = mysqli_fetch_assoc($categoriesResult)): ?>
                                        <option value="<?php echo $category['category_id']; ?>" <?php if ($category['category_id'] == $skuInfo['category_id']) echo 'selected'; ?>>
                                            <?php echo $category['category_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <label for="category_id">Category</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="sku_id" name="sku_id" class="form-control" required>
                                    <?php while ($sku = mysqli_fetch_assoc($skusResult)): ?>
                                        <option value="<?php echo $sku['sku_id']; ?>" <?php if ($sku['sku_id'] == $asset['sku_id']) echo 'selected'; ?>>
                                            <?php echo $sku['sku_code'] . ' - ' . $sku['sku_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <label for="sku_id">SKU</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="asset_name" name="asset_name" class="form-control" value="<?php echo $asset['asset_name']; ?>" required>
                                <label for="asset_name">Equipment/Item Name</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="model" name="model" class="form-control" value="<?php echo $asset['model']; ?>">
                                <label for="model">Model</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-floating">
                                <textarea id="specifications" name="specifications" class="form-control" style="height: 50px"><?php echo $asset['specifications']; ?></textarea>
                                <label for="specifications">Specifications</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Information Card -->
            <div class="react-card shadow-sm">
                <div class="react-card-header bg-light">
                    <h3 class="react-card-title"><i class="fas fa-map-marker-alt me-2 text-danger"></i>Location</h3>
                </div>
                <div class="react-card-body">
                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="current_location_id" name="current_location_id" class="form-control" required>
                                    <?php
                                    mysqli_data_seek($locationsResult, 0);
                                    while ($location = mysqli_fetch_assoc($locationsResult)): ?>
                                        <option value="<?php echo $location['location_id']; ?>" <?php if ($location['location_id'] == $asset['current_location_id']) echo 'selected'; ?>>
                                            <?php echo $location['location_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <label for="current_location_id">Current Location</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="unit_section" name="unit_section" class="form-control" value="<?php echo $asset['unit_section']; ?>">
                                <label for="unit_section">Unit/Section/Department</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="assigned_to" name="assigned_to"
                                    class="form-control <?php if ($coming_from_transfer && in_array('assigned_to', $missing_fields)) echo 'is-invalid'; ?>"
                                    value="<?php echo $asset['assigned_to']; ?>">
                                <label for="assigned_to">
                                    Assigned to
                                    <?php if ($coming_from_transfer && in_array('assigned_to', $missing_fields)): ?>
                                    <span class="text-danger">*</span>
                                    <?php endif; ?>
                                </label>
                                <?php if ($coming_from_transfer && in_array('assigned_to', $missing_fields)): ?>
                                    <div class="invalid-feedback">Please specify who this asset is assigned to</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="status" name="status" class="form-control" required>
                                    <option value="In use" <?php if ($asset['status'] == 'In use') echo 'selected'; ?>>In use</option>
                                    <option value="Available" <?php if ($asset['status'] == 'Available') echo 'selected'; ?>>Available</option>
                                    <option value="Under Repair" <?php if ($asset['status'] == 'Under Repair') echo 'selected'; ?>>Under Repair</option>
                                    <option value="Defective" <?php if ($asset['status'] == 'Defective') echo 'selected'; ?>>Defective</option>
                                </select>
                                <label for="status">Status</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Serial Number Card -->
            <div class="react-card shadow-sm">
                <div class="react-card-header bg-light">
                    <h3 class="react-card-title"><i class="fas fa-barcode me-2 text-info"></i>Serial & ID</h3>
                </div>
                <div class="react-card-body">
                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="serial_number" name="serial_number" class="form-control" value="<?php echo $asset['serial_number']; ?>">
                                <label for="serial_number">Serial Number/MEI#</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="local_mr" name="local_mr"
                                    class="form-control <?php if ($coming_from_transfer && in_array('local_mr', $missing_fields)) echo 'is-invalid'; ?>"
                                    value="<?php echo $asset['local_mr']; ?>">
                                <label for="local_mr">
                                    Local MR
                                    <?php if ($coming_from_transfer && in_array('local_mr', $missing_fields)): ?>
                                    <span class="text-danger">*</span>
                                    <?php endif; ?>
                                </label>
                                <?php if ($coming_from_transfer && in_array('local_mr', $missing_fields)): ?>
                                    <div class="invalid-feedback">Please enter Local MR</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="source_id" name="source_id" class="form-control">
                                    <option value="">Select Source</option>
                                    <?php
                                    mysqli_data_seek($sourcesResult, 0);
                                    while ($source = mysqli_fetch_assoc($sourcesResult)): ?>
                                        <option value="<?php echo $source['source_id']; ?>" <?php if ($source['source_id'] == $asset['source_id']) echo 'selected'; ?>>
                                            <?php echo $source['source_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <label for="source_id">Source</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="compact-view-3">
            <!-- Receipt Information Card -->
            <div class="react-card shadow-sm">
                <div class="react-card-header bg-light">
                    <h3 class="react-card-title"><i class="fas fa-receipt me-2 text-warning"></i>Receipt Info</h3>
                </div>
                <div class="react-card-body">
                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="receipt_type" name="receipt_type"
                                    class="form-control <?php if ($coming_from_transfer && in_array('receipt_type', $missing_fields)) echo 'is-invalid'; ?>">
                                    <option value="">Select Receipt Type</option>
                                    <option value="Local MR" <?php if ($asset['receipt_type'] == 'D.R' || $asset['receipt_type'] == 'Local MR') echo 'selected'; ?>>Local MR</option>
                                    <option value="P.T.R" <?php if ($asset['receipt_type'] == 'P.T.R') echo 'selected'; ?>>P.T.R</option>
                                    <option value="O.R" <?php if ($asset['receipt_type'] == 'O.R') echo 'selected'; ?>>O.R</option>
                                </select>
                                <label for="receipt_type">
                                    Receipt Type
                                    <?php if ($coming_from_transfer && in_array('receipt_type', $missing_fields)): ?>
                                    <span class="text-danger">*</span>
                                    <?php endif; ?>
                                </label>
                                <?php if ($coming_from_transfer && in_array('receipt_type', $missing_fields)): ?>
                                    <div class="invalid-feedback">Please select a Receipt Type</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="text" id="series_number" name="series_number" class="form-control" value="<?php echo $asset['series_number']; ?>">
                                <label for="series_number">Series #</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-floating">
                                <select id="supplier_id" name="supplier_id" class="form-control">
                                    <option value="">Select Supplier</option>
                                    <?php
                                    mysqli_data_seek($suppliersResult, 0);
                                    while ($supplier = mysqli_fetch_assoc($suppliersResult)): ?>
                                        <option value="<?php echo $supplier['supplier_id']; ?>" <?php if ($supplier['supplier_id'] == $asset['supplier_id']) echo 'selected'; ?>>
                                            <?php echo $supplier['supplier_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <label for="supplier_id">Supplier</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Financial Information Card -->
            <div class="react-card shadow-sm">
                <div class="react-card-header bg-light">
                    <h3 class="react-card-title"><i class="fas fa-dollar-sign me-2 text-success"></i>Financial</h3>
                </div>
                <div class="react-card-body">
                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="date" id="purchase_date" name="purchase_date" class="form-control"
                                    value="<?php echo ($asset['purchase_date'] !== '0000-00-00' && !empty($asset['purchase_date'])) ? $asset['purchase_date'] : ''; ?>">
                                <label for="purchase_date">
                                    Purchase Date
                                    <?php if (hasRole('Logistics', 'SuperAdmin')): ?>
                                    <small class="text-muted d-none">Raw: <?php echo $asset['purchase_date']; ?></small>
                                    <?php endif; ?>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row form-row-compact">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="number" id="unit_cost" name="unit_cost" class="form-control" step="0.01" value="<?php echo $asset['unit_cost']; ?>" min="0">
                                <label for="unit_cost">Unit Cost</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-floating">
                                <input type="date" id="warranty_expiry" name="warranty_expiry" class="form-control"
                                    value="<?php echo ($asset['warranty_expiry'] !== '0000-00-00' && !empty($asset['warranty_expiry'])) ? $asset['warranty_expiry'] : ''; ?>">
                                <label for="warranty_expiry">
                                    Warranty Expiry
                                    <?php if (hasRole('Logistics', 'SuperAdmin')): ?>
                                    <small class="text-muted d-none">Raw: <?php echo $asset['warranty_expiry']; ?></small>
                                    <?php endif; ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Remarks Card -->
            <div class="react-card shadow-sm">
                <div class="react-card-header bg-light">
                    <h3 class="react-card-title"><i class="fas fa-comment-alt me-2 text-secondary"></i>Additional</h3>
                </div>
                <div class="react-card-body">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-floating">
                                <textarea id="remarks" name="remarks" class="form-control" style="height: 118px"><?php echo $asset['remarks']; ?></textarea>
                                <label for="remarks">Remarks</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Buttons -->
        <div class="d-flex justify-content-between mt-3">
            <a href="view.php?id=<?php echo $asset_id; ?>" class="react-btn react-btn-secondary btn-sm">
                <i class="fas fa-times react-btn-icon"></i>Cancel
            </a>
            <button type="submit" class="react-btn react-btn-primary">
                <i class="fas fa-save react-btn-icon"></i>Update Asset
            </button>
        </div>
    </form>
</div>

<style>
/* Additional styles specific to this page */
.form-floating input[type="date"].form-control {
    padding-top: 1.75rem;
}

.form-floating select.form-control option {
    color: #212529;
}

.form-floating select.has-value ~ label,
.form-floating input:not(:placeholder-shown) ~ label,
.form-floating textarea:not(:placeholder-shown) ~ label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    opacity: 0.65;
}

/* Small utility to highlight required fields */
label .text-danger {
    margin-left: 2px;
    font-weight: bold;
}

/* Card focus effect */
.react-card {
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
}

.card-focus {
    box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.3), 0 4px 20px rgba(0,0,0,0.1) !important;
}

/* Improve input readability */
.form-floating input.form-control,
.form-floating select.form-control,
.form-floating textarea.form-control {
    font-weight: 500;
    border-radius: 6px;
    border-color: rgba(0, 0, 0, 0.15);
    font-size: 0.85rem; /* Smaller font size for inputs */
}

/* Smooth transitions for all interactive elements */
.form-control, .react-btn, .react-card, .form-floating label {
    transition: all 0.2s ease-in-out !important;
}

/* Hide raw date values to save space */
.text-muted.d-none {
    display: none !important;
}

/* Button size adjustments */
.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem; /* Smaller font size for buttons */
}

/* Make sure all cards in a row have the same height */
.compact-view-3 {
    align-items: stretch;
    gap: 0.75rem; /* Reduced gap between cards */
}

/* Custom height for the remarks textarea to match the height of other cards */
.compact-view-3 textarea {
    min-height: 118px; /* Reduced height */
}

/* Card header styling */
.react-card-header {
    border-bottom: none;
    padding: 0.5rem 1rem; /* Reduced padding */
}

.react-card-title {
    font-weight: 600;
    font-size: 0.85rem; /* Smaller font size for card titles */
}

.react-card-title i {
    font-size: 0.85rem; /* Smaller icon size */
}

/* Add subtle hover effect to cards */
.react-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08) !important;
}

/* Focused input styling */
.form-floating input.form-control:focus,
.form-floating select.form-control:focus,
.form-floating textarea.form-control:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Reduced vertical spacing */
.react-card-body {
    padding: 0.75rem 1rem; /* Reduced padding */
}

.form-floating {
    margin-bottom: 0.5rem; /* Reduced margin */
}

.form-floating > label {
    font-size: 0.8rem; /* Smaller label text */
}

.form-floating input.form-control,
.form-floating select.form-control,
.form-floating textarea.form-control {
    height: calc(2.5rem + 2px); /* Reduced input height */
    padding: 1rem 0.75rem 0.25rem; /* Adjusted padding */
}

.form-floating > label {
    height: calc(2.5rem + 2px); /* Match the input height */
    padding: 1rem 0.75rem 0.25rem; /* Match the input padding */
}

/* Smaller heading */
h2.fw-bold {
    font-size: 1.25rem; /* Smaller heading */
}

/* Compact container */
.container-fluid.px-4.py-3 {
    padding: 0.5rem 1rem !important; /* Less padding */
}

/* Smaller buttons */
.react-btn {
    font-size: 0.85rem;
    padding: 0.35rem 0.85rem;
}

/* Alert styles */
.react-alert {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
}

/* Smaller invalid feedback */
.invalid-feedback {
    font-size: 0.7rem;
    margin-top: 0.1rem;
}

/* Compact margins */
.mb-3 {
    margin-bottom: 0.75rem !important;
}

.mt-3 {
    margin-top: 0.75rem !important;
}

/* Smaller form controls for more compact appearance */
.form-control {
    min-height: unset;
}
</style>

<script>
$(document).ready(function() {
    // Handle category change
    $('#category_id').change(function() {
        var categoryId = $(this).val();

        // Clear SKU dropdown
        $('#sku_id').empty();

        if (categoryId) {
            // Get SKUs for selected category via AJAX
            $.ajax({
                url: '../admin/get_skus.php',
                type: 'GET',
                data: {category_id: categoryId, item_type: 'Fixed'},
                dataType: 'json',
                success: function(data) {
                    // Populate SKU dropdown
                    if (data.length > 0) {
                        $.each(data, function(index, sku) {
                            $('#sku_id').append('<option value="' + sku.sku_id + '">' + sku.sku_code + ' - ' + sku.sku_name + '</option>');
                        });
                    } else {
                        $('#sku_id').append('<option value="">No SKUs found</option>');
                    }

                    // Trigger change to update floating label styling
                    $('#sku_id').trigger('change');
                },
                error: function() {
                    alert('Error fetching SKUs');
                }
            });
        }
    });

    // Handle location change - auto-populate unit/section/department
    $('#current_location_id').change(function() {
        var selectedOption = $(this).find('option:selected');
        var locationName = selectedOption.text();

        // Set the unit/section field to match the location name
        $('#unit_section').val(locationName);

        // Trigger change event to update floating label styling
        $('#unit_section').trigger('change');
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add focus effect for card sections when clicking inside them
    $('.react-card').each(function() {
        const card = $(this);
        const inputs = card.find('input, select, textarea');

        inputs.on('focus', function() {
            card.addClass('card-focus');
        }).on('blur', function() {
            if (!inputs.is(':focus')) {
                card.removeClass('card-focus');
            }
        });
    });
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure dates are properly formatted for database storage
    document.querySelector('form').addEventListener('submit', function() {
        const purchaseDate = document.getElementById('purchase_date');
        const warrantyExpiry = document.getElementById('warranty_expiry');

        // Format dates in YYYY-MM-DD format for database storage
        if (purchaseDate.value) {
            const date = new Date(purchaseDate.value);
            if (date instanceof Date && !isNaN(date)) {
                purchaseDate.value = date.toISOString().split('T')[0];
            }
        }

        if (warrantyExpiry.value) {
            const date = new Date(warrantyExpiry.value);
            if (date instanceof Date && !isNaN(date)) {
                warrantyExpiry.value = date.toISOString().split('T')[0];
            }
        }
    });

    // Scroll to the first error field if there are missing fields
    <?php if ($coming_from_transfer && !empty($missing_fields)): ?>
    const firstErrorField = document.querySelector('.is-invalid');
    if (firstErrorField) {
        firstErrorField.focus();
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    <?php endif; ?>

    // Real-time validation for required fields - only when coming from transfer
    <?php if ($coming_from_transfer): ?>
    const fieldsToValidate = ['local_mr', 'receipt_type', 'assigned_to'];
    const alertElement = document.querySelector('.react-alert-danger');

    function checkAllFieldsValid() {
        // Check if all required fields have values
        return fieldsToValidate.every(fieldId => {
            const field = document.getElementById(fieldId);
            return field && field.value.trim() !== '';
        });
    }

    fieldsToValidate.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                // If the field now has a value, remove the error styling and message
                if (field.value.trim() !== '') {
                    field.classList.remove('is-invalid');
                    // Find and hide the validation message
                    const errorMsg = field.nextElementSibling;
                    if (errorMsg && errorMsg.classList.contains('invalid-feedback')) {
                        errorMsg.style.display = 'none';
                    }

                    // Check if all fields are valid, if so, hide the alert
                    if (alertElement && checkAllFieldsValid()) {
                        alertElement.style.display = 'none';
                    }
                } else {
                    // If the field is emptied again, restore the error styling
                    field.classList.add('is-invalid');
                    // Show the validation message again
                    const errorMsg = field.nextElementSibling;
                    if (errorMsg && errorMsg.classList.contains('invalid-feedback')) {
                        errorMsg.style.display = '';
                    }

                    // Show the alert again if any field is invalid
                    if (alertElement) {
                        alertElement.style.display = '';
                    }
                }
            });
        }
    });
    <?php endif; ?>
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
// Flush output buffer at the end
ob_end_flush();
?>