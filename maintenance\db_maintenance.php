<?php
// Database Maintenance Script
// Purpose: Handle audit logs, notifications, and optimize database
// Usage: Run weekly via scheduled task or cron job

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
require_once '../config/database.php';
require_once '../includes/functions.php';

// Security check - only system admins should access this
if (!isset($_SESSION['user_id']) || !hasRole('godmode')) {
    header("Location: /choims/index.php");
    exit;
}

// Operations success/error tracking
$success = [];
$errors = [];

// Configuration options
$auditLogsRetentionDays = 90; // Keep audit logs for 90 days
$notificationsRetentionDays = 30; // Keep notifications for 30 days
$readNotificationsRetentionDays = 7; // Keep read notifications for 7 days

// Set query timeout to prevent long-running queries from causing issues
$queryTimeoutSeconds = 30; // 30 seconds timeout for queries

// Apply query timeout setting - only works with MySQLi
if (defined('MYSQLI_OPT_CONNECT_TIMEOUT')) {
    mysqli_options($conn, MYSQLI_OPT_CONNECT_TIMEOUT, $queryTimeoutSeconds);
}

// Set PHP execution time limit
set_time_limit($queryTimeoutSeconds * 2); // Double the timeout for PHP execution

// Set MySQL session wait_timeout as an additional safeguard
mysqli_query($conn, "SET SESSION wait_timeout = $queryTimeoutSeconds");

// Database connection is established in database.php as $conn (mysqli)

// Only proceed if confirmation is provided
$confirmed = isset($_POST['confirm']) && $_POST['confirm'] === 'yes';

// Display the confirmation form if not confirmed
if (!$confirmed) {
    include_once '../includes/header.php';
    ?>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-database me-2"></i>Database Maintenance</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p><strong>This tool will perform the following operations:</strong></p>
                    <ul>
                        <li>Archive/delete audit logs older than <?php echo $auditLogsRetentionDays; ?> days</li>
                        <li>Archive/delete detailed audit logs older than <?php echo $auditLogsRetentionDays; ?> days</li>
                        <li>Delete notifications older than <?php echo $notificationsRetentionDays; ?> days</li>
                        <li>Delete read notifications older than <?php echo $readNotificationsRetentionDays; ?> days</li>
                        <li>Optimize database tables for better performance</li>
                    </ul>
                </div>

                <form method="post">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                        <label class="form-check-label" for="confirmCheck">
                            I understand the operations this maintenance tool will perform
                        </label>
                    </div>

                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-primary">Run Maintenance</button>
                    <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    exit;
}

// If confirmed, proceed with maintenance tasks
try {
    // Start transaction
    mysqli_autocommit($conn, FALSE);

    // 1. Archive and clean up audit logs
    $auditLogsCutoff = date('Y-m-d H:i:s', strtotime("-$auditLogsRetentionDays days"));

    // Option 1: Archive to a different table first (if needed)
    $archiveQuery = "INSERT INTO audit_logs_archive
                    SELECT * FROM audit_logs
                    WHERE log_time < ?";

    // Check if archive table exists, if not create it
    $tableCheckResult = mysqli_query($conn, "SHOW TABLES LIKE 'audit_logs_archive'");
    if (mysqli_num_rows($tableCheckResult) == 0) {
        $createArchiveTable = "CREATE TABLE audit_logs_archive LIKE audit_logs";
        if (mysqli_query($conn, $createArchiveTable)) {
            $success[] = "Created audit_logs_archive table";
        } else {
            $errors[] = "Error creating archive table: " . mysqli_error($conn);
        }
    }

    // Archive old logs
    $archiveStmt = mysqli_prepare($conn, $archiveQuery);
    mysqli_stmt_bind_param($archiveStmt, 's', $auditLogsCutoff);

    if (mysqli_stmt_execute($archiveStmt)) {
        $archivedRows = mysqli_stmt_affected_rows($archiveStmt);
        $success[] = "Archived $archivedRows audit logs to audit_logs_archive";

        // Delete the archived logs
        $deleteOldLogsQuery = "DELETE FROM audit_logs WHERE log_time < ?";
        $deleteStmt = mysqli_prepare($conn, $deleteOldLogsQuery);
        mysqli_stmt_bind_param($deleteStmt, 's', $auditLogsCutoff);

        if (mysqli_stmt_execute($deleteStmt)) {
            $deletedRows = mysqli_stmt_affected_rows($deleteStmt);
            $success[] = "Deleted $deletedRows old audit logs";
        } else {
            $errors[] = "Error deleting old audit logs: " . mysqli_error($conn);
        }
    } else {
        $errors[] = "Error archiving audit logs: " . mysqli_error($conn);
    }

    // 1.5. Archive and clean up detailed audit logs
    // Check if detailed_audit_logs_archive table exists, if not create it
    $tableCheckResult = mysqli_query($conn, "SHOW TABLES LIKE 'detailed_audit_logs_archive'");
    if (mysqli_num_rows($tableCheckResult) == 0) {
        $createArchiveTable = "CREATE TABLE detailed_audit_logs_archive LIKE detailed_audit_logs";
        if (mysqli_query($conn, $createArchiveTable)) {
            $success[] = "Created detailed_audit_logs_archive table";
        } else {
            $errors[] = "Error creating detailed audit logs archive table: " . mysqli_error($conn);
        }
    }

    // Archive old detailed logs
    $detailedArchiveQuery = "INSERT INTO detailed_audit_logs_archive
                           SELECT * FROM detailed_audit_logs
                           WHERE created_at < ?";
    $detailedArchiveStmt = mysqli_prepare($conn, $detailedArchiveQuery);
    mysqli_stmt_bind_param($detailedArchiveStmt, 's', $auditLogsCutoff);

    if (mysqli_stmt_execute($detailedArchiveStmt)) {
        $archivedRows = mysqli_stmt_affected_rows($detailedArchiveStmt);
        $success[] = "Archived $archivedRows detailed audit logs to detailed_audit_logs_archive";

        // Delete the archived detailed logs
        $deleteOldLogsQuery = "DELETE FROM detailed_audit_logs WHERE created_at < ?";
        $deleteStmt = mysqli_prepare($conn, $deleteOldLogsQuery);
        mysqli_stmt_bind_param($deleteStmt, 's', $auditLogsCutoff);

        if (mysqli_stmt_execute($deleteStmt)) {
            $deletedRows = mysqli_stmt_affected_rows($deleteStmt);
            $success[] = "Deleted $deletedRows old detailed audit logs";
        } else {
            $errors[] = "Error deleting old detailed audit logs: " . mysqli_error($conn);
        }
    } else {
        $errors[] = "Error archiving detailed audit logs: " . mysqli_error($conn);
    }

    // 2. Clean up notifications
    // Delete old notifications
    $notificationsCutoff = date('Y-m-d H:i:s', strtotime("-$notificationsRetentionDays days"));
    $deleteOldNotificationsQuery = "DELETE FROM notifications WHERE created_at < ?";
    $deleteNotifStmt = mysqli_prepare($conn, $deleteOldNotificationsQuery);
    mysqli_stmt_bind_param($deleteNotifStmt, 's', $notificationsCutoff);

    if (mysqli_stmt_execute($deleteNotifStmt)) {
        $deletedNotifs = mysqli_stmt_affected_rows($deleteNotifStmt);
        $success[] = "Deleted $deletedNotifs old notifications";
    } else {
        $errors[] = "Error deleting old notifications: " . mysqli_error($conn);
    }

    // Delete read notifications that are older than the shorter retention period
    $readNotificationsCutoff = date('Y-m-d H:i:s', strtotime("-$readNotificationsRetentionDays days"));
    $deleteReadNotificationsQuery = "DELETE FROM notifications WHERE is_read = 1 AND created_at < ?";
    $deleteReadNotifStmt = mysqli_prepare($conn, $deleteReadNotificationsQuery);
    mysqli_stmt_bind_param($deleteReadNotifStmt, 's', $readNotificationsCutoff);

    if (mysqli_stmt_execute($deleteReadNotifStmt)) {
        $deletedReadNotifs = mysqli_stmt_affected_rows($deleteReadNotifStmt);
        $success[] = "Deleted $deletedReadNotifs old read notifications";
    } else {
        $errors[] = "Error deleting old read notifications: " . mysqli_error($conn);
    }

    // 3. Optimize database tables
    $tablesToOptimize = [
        'audit_logs',
        'audit_logs_archive',
        'detailed_audit_logs',
        'detailed_audit_logs_archive',
        'batch_transfers',
        'batch_transfer_assets',
        'batch_transfer_inventory',
        'categories',
        'consumable_inventory',
        'consumable_transactions',
        'fixed_assets',
        'locations',
        'maintenance_records',
        'notifications',
        'reports',
        'sku_master',
        'sources',
        'suppliers',
        'transfers',
        'users'
    ];

    foreach ($tablesToOptimize as $table) {
        // Check if table exists before optimizing
        $tableCheckResult = mysqli_query($conn, "SHOW TABLES LIKE '$table'");
        if (mysqli_num_rows($tableCheckResult) > 0) {
            if (mysqli_query($conn, "OPTIMIZE TABLE $table")) {
                $success[] = "Optimized table: $table";
            } else {
                $errors[] = "Error optimizing table $table: " . mysqli_error($conn);
            }
        }
    }

    // Commit transaction if no errors
    if (empty($errors)) {
        mysqli_commit($conn);
    } else {
        mysqli_rollback($conn);
    }

    // Re-enable autocommit
    mysqli_autocommit($conn, TRUE);
} catch (Exception $e) {
    $errors[] = "Database maintenance error: " . $e->getMessage();
    mysqli_rollback($conn);
    mysqli_autocommit($conn, TRUE);
}

// Display results
include_once '../includes/header.php';
?>

<div class="container mt-5">
    <div class="card">
        <div class="card-header <?php echo empty($errors) ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h3>
                <i class="fas <?php echo empty($errors) ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> me-2"></i>
                Database Maintenance Results
            </h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success)): ?>
                <h4>Successful Operations:</h4>
                <ul class="list-group mb-4">
                    <?php foreach ($success as $message): ?>
                        <li class="list-group-item list-group-item-success">
                            <i class="fas fa-check me-2"></i> <?php echo htmlspecialchars($message); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <h4>Errors:</h4>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <p>Some operations failed. The database has been rolled back to prevent partial maintenance.</p>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> Database maintenance completed successfully.
                </div>
            <?php endif; ?>

            <div class="mt-4">
                <a href="/choims/dashboards/superadmin.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i> Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>