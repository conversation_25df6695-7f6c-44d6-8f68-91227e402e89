<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.3"/>
    </pattern>
    <pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="5" cy="5" r="1" fill="white" fill-opacity="0.3"/>
      <circle cx="15" cy="15" r="1" fill="white" fill-opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="100" height="100" fill="url(#grid)"/>
  <rect width="100" height="100" fill="url(#dots)"/>
</svg>
