<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/auth.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/database.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/functions.php');

// Ensure user is logged in
requireLogin();

// Get the last notification ID from the request
$lastId = isset($_GET['last_id']) ? intval($_GET['last_id']) : 0;
$userId = $_SESSION['user_id'];

// Check for new notifications
$query = "
    SELECT 
        COUNT(*) as new_count,
        MAX(notification_id) as last_id,
        SUM(CASE WHEN notification_type IN ('asset_deleted', 'inventory_deleted') THEN 1 ELSE 0 END) as deletion_count
    FROM 
        notifications
    WHERE 
        user_id = ? AND 
        notification_id > ? AND
        is_read = 0
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'ii', $userId, $lastId);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$data = mysqli_fetch_assoc($result);

// Prepare the response
$response = [
    'success' => true,
    'new_notifications' => intval($data['new_count']),
    'last_id' => intval($data['last_id']),
    'has_deletion_notifications' => intval($data['deletion_count']) > 0
];

// Send the response as JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
?>
