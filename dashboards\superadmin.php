<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireRole('Superadmin');

// Get system statistics

// Total fixed assets count
$assetsQuery = "SELECT COUNT(*) as total FROM fixed_assets WHERE is_active = 1 AND (is_deleted = 0 OR is_deleted IS NULL)";
$assetsResult = mysqli_query($conn, $assetsQuery);
$assetsCount = mysqli_fetch_assoc($assetsResult)['total'];

// Total consumables count and quantity
$inventoryQuery = "SELECT COUNT(*) as total, SUM(i.current_quantity) as total_qty
                  FROM consumable_inventory i
                  JOIN sku_master s ON i.sku_id = s.sku_id
                  WHERE s.item_type = 'Consumable' AND (i.is_deleted = 0 OR i.is_deleted IS NULL)";
$inventoryResult = mysqli_query($conn, $inventoryQuery);
$inventoryData = mysqli_fetch_assoc($inventoryResult);
$inventoryCount = $inventoryData['total'];
$totalConsumableQty = $inventoryData['total_qty'] ?: 0;

// Total number of locations
$locationsQuery = "SELECT COUNT(*) as total FROM locations";
$locationsResult = mysqli_query($conn, $locationsQuery);
$locationsCount = mysqli_fetch_assoc($locationsResult)['total'];

// Total number of users
$usersQuery = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
$usersResult = mysqli_query($conn, $usersQuery);
$usersCount = mysqli_fetch_assoc($usersResult)['total'];

// Low stock IT supplies count
$lowStockQuery = "SELECT COUNT(*) as total FROM consumable_inventory i
                JOIN sku_master s ON i.sku_id = s.sku_id
                WHERE (i.status = 'Low Stock' OR i.status = 'Out of Stock')
                AND s.category_id = 4
                AND (i.is_deleted = 0 OR i.is_deleted IS NULL)";
$lowStockResult = mysqli_query($conn, $lowStockQuery);
$lowStockCount = mysqli_fetch_assoc($lowStockResult)['total'];

// Get specific low stock items for alerts
$lowStockItemsQuery = "
    SELECT ci.*, l.location_name, s.sku_name, s.sku_code
    FROM consumable_inventory ci
    JOIN locations l ON ci.location_id = l.location_id
    JOIN sku_master s ON ci.sku_id = s.sku_id
    WHERE (ci.status = 'Low Stock' OR ci.status = 'Out of Stock') AND (ci.is_deleted = 0 OR ci.is_deleted IS NULL)
    ORDER BY ci.status ASC, ci.current_quantity ASC
    LIMIT 5
";
$lowStockItemsResult = mysqli_query($conn, $lowStockItemsQuery);

// Pending transfers count
$transfersQuery = "
    SELECT
        (SELECT COUNT(*) FROM transfers WHERE status = 'Pending' OR status = 'Approved by Logistics') +
        (SELECT COUNT(*) FROM batch_transfers WHERE status = 'Pending' OR status = 'Approved by Logistics') AS total
    ";
$transfersResult = mysqli_query($conn, $transfersQuery);
$pendingTransfersCount = mysqli_fetch_assoc($transfersResult)['total'];

// Recent system activity with more detailed information
$activityQuery = "
    SELECT dal.log_id, dal.user_id, dal.action_type as action, dal.entity_type, dal.entity_id, dal.ip_address, dal.created_at as log_time,
           dal.old_values, dal.new_values as details, dal.changes_summary, dal.username, dal.full_name
    FROM detailed_audit_logs dal
    ORDER BY dal.created_at DESC
    LIMIT 3";
$activityResult = mysqli_query($conn, $activityQuery);

// Assets by location chart data
$assetsByLocationQuery = "
    SELECT l.location_name, COUNT(a.asset_id) as asset_count,
           l.location_type
    FROM locations l
    LEFT JOIN fixed_assets a ON l.location_id = a.current_location_id AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    GROUP BY l.location_id
    ORDER BY l.location_type, l.location_name";
$assetsByLocationResult = mysqli_query($conn, $assetsByLocationQuery);
$locationLabels = [];
$assetData = [];
$locationTypes = [];
$backgroundColors = [];

// Color palette based on location type
$colorByType = [
    'Health Center' => '#4CAF50',
    'Department' => '#2196F3',
    'Office' => '#FFC107',
    'Warehouse' => '#9C27B0',
    'Other' => '#FF5722'
];

// Check if query executed successfully
if ($assetsByLocationResult) {
    while ($row = mysqli_fetch_assoc($assetsByLocationResult)) {
        $locationLabels[] = $row['location_name'];
        $assetData[] = (int)$row['asset_count'];
        $locationType = $row['location_type'] ?? 'Other';
        $locationTypes[] = $locationType;

        // Assign color based on location type
        $typeColor = isset($colorByType[$locationType]) ? $colorByType[$locationType] : $colorByType['Other'];

        // Create a slightly transparent version for the chart
        $backgroundColors[] = $typeColor;
    }
}

// Ensure we have at least some data for the chart
if (empty($locationLabels)) {
    $locationLabels = ['No Data'];
    $assetData = [0];
    $locationTypes = ['Other'];
    $backgroundColors = ['#e0e0e0'];
}

// Assets by category
$assetsByCategoryQuery = "
    SELECT c.category_name, COUNT(a.asset_id) as asset_count
    FROM categories c
    LEFT JOIN sku_master s ON c.category_id = s.category_id
    LEFT JOIN fixed_assets a ON s.sku_id = a.sku_id AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    GROUP BY c.category_id
    ORDER BY asset_count DESC";
$assetsByCategoryResult = mysqli_query($conn, $assetsByCategoryQuery);
$categoryLabels = [];
$categoryData = [];

// Check if query executed successfully
if ($assetsByCategoryResult) {
    while ($row = mysqli_fetch_assoc($assetsByCategoryResult)) {
        $categoryLabels[] = $row['category_name'];
        $categoryData[] = (int)$row['asset_count'];
    }
}

// Ensure we have at least some data for the chart
if (empty($categoryLabels)) {
    $categoryLabels = ['No Data'];
    $categoryData = [0];
}

// Get consumables levels by category for chart
$consumablesCategoryQuery = "
    SELECT c.category_name, SUM(ci.current_quantity) as total_quantity
    FROM consumable_inventory ci
    JOIN sku_master s ON ci.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    WHERE (ci.is_deleted = 0 OR ci.is_deleted IS NULL)
    GROUP BY c.category_id
    ORDER BY total_quantity DESC
    LIMIT 5
";
$consumablesCategoryResult = mysqli_query($conn, $consumablesCategoryQuery);
$consumablesCategoryLabels = [];
$consumablesCategoryData = [];

// Check if query executed successfully
if ($consumablesCategoryResult) {
    while ($row = mysqli_fetch_assoc($consumablesCategoryResult)) {
        $consumablesCategoryLabels[] = $row['category_name'];
        $consumablesCategoryData[] = (int)$row['total_quantity'];
    }
}

// Ensure we have at least some data for the chart
if (empty($consumablesCategoryLabels)) {
    $consumablesCategoryLabels = ['No Data'];
    $consumablesCategoryData = [0];
}

// Get latest transfer activities for activity feed
$recentTransfersQuery = "
    SELECT t.*,
           src.location_name as source_location_name,
           dst.location_name as destination_location_name,
           CASE WHEN t.asset_id IS NOT NULL THEN 'Fixed Asset' ELSE 'Consumable' END as item_type,
           u.full_name as user_name
    FROM transfers t
    JOIN locations src ON t.source_location_id = src.location_id
    JOIN locations dst ON t.destination_location_id = dst.location_id
    JOIN users u ON t.initiated_by = u.user_id
    ORDER BY t.transfer_date DESC
    LIMIT 5
";
$recentTransfersResult = mysqli_query($conn, $recentTransfersQuery);

// Get monthly inventory update status
$month = date('n');
$year = date('Y');
$monthlyUpdateStats = [
    'total' => 0,
    'completed' => 0,
    'pending' => 0,
    'locations' => []
];

try {
    // Get all locations that should update their inventory (all locations)
    $locationsQuery = "
        SELECT l.location_id, l.location_name, l.location_type,
               COALESCE(m.status, 'pending') as status,
               m.updated_at, m.updated_by, u.full_name as updated_by_name
        FROM locations l
        LEFT JOIN monthly_inventory_updates m ON
            l.location_id = m.location_id AND
            m.month = ? AND
            m.year = ?
        LEFT JOIN users u ON m.updated_by = u.user_id
        WHERE l.is_active = 1
        ORDER BY l.location_name
    ";

    $locationsStmt = mysqli_prepare($conn, $locationsQuery);
    mysqli_stmt_bind_param($locationsStmt, 'ii', $month, $year);
    mysqli_stmt_execute($locationsStmt);
    $locationsResult = mysqli_stmt_get_result($locationsStmt);

    while ($location = mysqli_fetch_assoc($locationsResult)) {
        $monthlyUpdateStats['total']++;
        if ($location['status'] === 'completed') {
            $monthlyUpdateStats['completed']++;
        } else {
            $monthlyUpdateStats['pending']++;
        }

        // Add to locations array (limit to 5 for display)
        if (count($monthlyUpdateStats['locations']) < 5) {
            $monthlyUpdateStats['locations'][] = $location;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching monthly inventory update status: " . $e->getMessage());
}
?>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link rel="stylesheet" href="/choims/assets/css/monthly-inventory-update-modern.css">

<style>
    /* Modern UI Dashboard Theme - Updated Color Scheme */
    :root {
        --primary: #2E7D32;
        --primary-light: #4CAF50;
        --primary-dark: #1B5E20;
        --primary-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        --primary-transparent: rgba(46, 125, 50, 0.1);

        --secondary: #3949AB;
        --secondary-light: #5C6BC0;
        --secondary-dark: #283593;
        --secondary-gradient: linear-gradient(135deg, #3949AB 0%, #5C6BC0 100%);

        --danger: #D32F2F;
        --danger-light: #FFCDD2;
        --danger-gradient: linear-gradient(135deg, #D32F2F 0%, #EF5350 100%);

        --warning: #F57C00;
        --warning-light: #FFE0B2;
        --warning-gradient: linear-gradient(135deg, #F57C00 0%, #FFB74D 100%);

        --info: #0288D1;
        --info-light: #B3E5FC;
        --info-gradient: linear-gradient(135deg, #0288D1 0%, #29B6F6 100%);

        --success: #388E3C;
        --success-light: #C8E6C9;
        --success-gradient: linear-gradient(135deg, #388E3C 0%, #66BB6A 100%);

        --text-primary: #37474F;
        --text-secondary: #546E7A;
        --text-muted: #78909C;

        --light: #ECEFF1;
        --border: #CFD8DC;
        --card-bg: #FFFFFF;
        --bg-light: #f8f9fa;
        --bg-white: #ffffff;

        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        --shadow-md: 0 8px 20px rgba(0, 0, 0, 0.12);
        --shadow-lg: 0 12px 28px rgba(0, 0, 0, 0.15);

        --border-radius: 0.75rem;
        --border-radius-lg: 1rem;
        --border-radius-sm: 0.5rem;
        --radius: 16px;
        --radius-lg: 20px;

        --transition: all 0.3s ease;
    }

    .dashboard-page {
        background-color: var(--bg-white);
        padding-top: 1.5rem;
        padding-bottom: 2rem;
        min-height: calc(100vh - 120px);
    }

    .dashboard-header {
        position: relative;
        background: var(--bg-white);
        margin: -1.5rem -1.5rem 2rem -1.5rem;
        padding: 1.5rem 2rem 1rem;
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
        color: var(--text-primary);
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .dashboard-header .title-container {
        display: flex;
        align-items: center;
    }

    .dashboard-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
        letter-spacing: -0.025em;
        white-space: nowrap;
    }

    .dashboard-date {
        color: var(--text-secondary);
        font-weight: 500;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .dashboard-date i {
        margin-right: 0.5rem;
        color: var(--primary);
    }

    /* Dashboard actions */
    .dashboard-actions {
        display: flex;
        gap: 0.75rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.7rem 1.25rem;
        background-color: var(--bg-white);
        color: var(--text-primary);
        border-radius: var(--radius);
        font-weight: 600;
        transition: all 0.2s ease;
        text-decoration: none;
        box-shadow: var(--shadow);
        font-size: 0.9rem;
    }

    .action-btn:hover {
        background-color: var(--bg-white);
        color: var(--primary);
        transform: translateY(-3px);
        box-shadow: var(--shadow-lg);
    }

    .action-btn i {
        font-size: 1rem;
        color: var(--primary);
    }

    .action-primary {
        background-color: var(--primary);
        color: white;
    }

    .action-primary:hover {
        background-color: var(--primary-dark);
        color: white;
    }

    .action-primary i {
        color: white;
    }

    /* Dashboard cards */
    .dashboard-card {
        background-color: var(--bg-white);
        border-radius: var(--radius);
        border: none;
        box-shadow: var(--shadow);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border-radius: var(--radius);
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .dashboard-card .card-body {
        padding: 1.75rem;
        flex: 1;
    }

    .stats-card {
        border: none;
        border-radius: var(--radius);
        box-shadow: var(--shadow);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-card .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .icon-primary {
        background-color: var(--primary-transparent);
        color: var(--primary);
    }

    .icon-warning {
        background-color: rgba(245, 124, 0, 0.1);
        color: var(--warning);
    }

    .icon-danger {
        background-color: rgba(211, 47, 47, 0.1);
        color: var(--danger);
    }

    .icon-info {
        background-color: rgba(2, 136, 209, 0.1);
        color: var(--info);
    }

    .stats-card .stats-info {
        flex-grow: 1;
    }

    .stats-card .stats-title {
        color: var(--text-secondary);
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .stats-card .stats-value {
        color: var(--text-primary);
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0;
        line-height: 1.2;
    }

    .stats-card .stats-description {
        color: var(--text-secondary);
        font-size: 0.85rem;
        margin-top: 0.25rem;
    }

    .stats-card .trend-indicator {
        display: flex;
        align-items: center;
        font-size: 0.85rem;
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .trend-indicator i {
        font-size: 0.9rem;
        margin-right: 0.25rem;
    }

    .trend-up {
        color: var(--success);
    }

    .trend-down {
        color: var(--danger);
    }

    .primary-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--primary-gradient);
    }

    .warning-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--warning-gradient);
    }

    .danger-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--danger-gradient);
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--info-gradient);
    }

    .stats-card .card-footer {
        background-color: var(--bg-white);
        border-top: 1px solid rgba(0,0,0,0.05);
        padding: 0.75rem 1.5rem;
        margin-top: auto;
    }

    .view-details {
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: var(--transition);
        text-decoration: none;
    }

    .view-details i {
        transition: var(--transition);
        font-size: 0.8rem;
        margin-left: 0.5rem;
    }

    .view-details:hover i {
        transform: translateX(3px);
    }

    .text-primary {
        color: var(--primary) !important;
    }

    .text-info {
        color: var(--info) !important;
    }

    .text-danger {
        color: var(--danger) !important;
    }

    .text-warning {
        color: var(--warning) !important;
    }

    .action-btn {
        border-radius: 50px;
        padding: 0.6rem 1.25rem;
        font-weight: 600;
        font-size: 0.875rem;
        transition: var(--transition);
        box-shadow: var(--shadow-sm);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: none;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.5s ease;
        z-index: -1;
    }

    .action-btn i {
        margin-right: 0.5rem;
        font-size: 0.875rem;
        transition: var(--transition);
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow);
    }

    .action-btn:hover::before {
        transform: scaleX(1);
        transform-origin: left;
    }

    .action-btn:hover i {
        transform: translateX(-2px);
    }

    .action-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .action-btn-primary:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        color: white;
    }

    .action-btn-outline {
        background-color: transparent;
        border: 1px solid var(--border);
        color: var(--text-secondary);
    }

    .action-btn-outline:hover {
        background-color: var(--primary-transparent);
        color: var(--primary);
        border-color: var(--primary-light);
    }

    .action-btn-outline.active {
        background: var(--primary-gradient);
        color: white;
        border-color: transparent;
    }

    .table-responsive.dashboard-table {
        margin-top: 0.5rem;
    }

    .dashboard-table table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        border-radius: var(--border-radius-sm);
        overflow: hidden;
    }

    .dashboard-table thead th {
        background-color: rgba(46, 125, 50, 0.05);
        font-weight: 600;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        text-transform: uppercase;
        padding: 0.85rem 1rem;
        border-top: none;
        color: var(--text-secondary);
        border-bottom: 2px solid rgba(46, 125, 50, 0.1);
    }

    .dashboard-table thead th:first-child {
        border-top-left-radius: var(--border-radius-sm);
    }

    .dashboard-table thead th:last-child {
        border-top-right-radius: var(--border-radius-sm);
    }

    .dashboard-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid var(--border);
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .dashboard-table tbody tr:last-child td:first-child {
        border-bottom-left-radius: var(--border-radius-sm);
    }

    .dashboard-table tbody tr:last-child td:last-child {
        border-bottom-right-radius: var(--border-radius-sm);
    }

    .dashboard-table tbody tr {
        cursor: pointer;
        transition: var(--transition);
    }

    .dashboard-table tbody tr:hover {
        background-color: rgba(46, 125, 50, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
    }

    .transfer-link {
        color: var(--text-primary);
        font-weight: 500;
        text-decoration: none;
    }

    .transfer-id {
        font-weight: 600;
        color: var(--primary);
    }

    .chart-container {
        position: relative;
        height: 300px;
        padding: 0.5rem;
    }

    .section-divider {
        height: 1px;
        background: linear-gradient(to right, transparent, var(--border), transparent);
        margin: 2rem 0;
        opacity: 0.6;
    }

    /* Badge styles */
    .status-badge {
        padding: 0.4rem 0.85rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.7rem;
        display: inline-flex;
        align-items: center;
        line-height: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        white-space: nowrap;
    }

    .status-badge::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 6px;
    }

    .badge-danger {
        background-color: var(--danger-light);
        color: var(--danger);
        border: 1px solid rgba(211, 47, 47, 0.2);
    }

    .badge-danger::before {
        background-color: var(--danger);
    }

    .badge-warning {
        background-color: var(--warning-light);
        color: var(--warning);
        border: 1px solid rgba(245, 124, 0, 0.2);
    }

    .badge-warning::before {
        background-color: var(--warning);
    }

    .badge-info {
        background-color: var(--info-light);
        color: var(--info);
        border: 1px solid rgba(2, 136, 209, 0.2);
    }

    .badge-info::before {
        background-color: var(--info);
    }

    .badge-success {
        background-color: var(--success-light);
        color: var(--success);
        border: 1px solid rgba(56, 142, 60, 0.2);
    }

    .badge-success::before {
        background-color: var(--success);
    }

    /* Activity list */
    .activity-list {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .activity-item {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--border);
        display: flex;
        align-items: flex-start;
        transition: var(--transition);
        position: relative;
    }

    .activity-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .activity-item:hover {
        background-color: rgba(46, 125, 50, 0.03);
        transform: translateX(3px);
    }

    .activity-item:hover::before {
        opacity: 1;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 42px;
        height: 42px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background: var(--primary-gradient);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: var(--transition);
    }

    .activity-item:hover .activity-icon {
        transform: scale(1.05) rotate(5deg);
    }

    .activity-content {
        flex-grow: 1;
    }

    .activity-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--text-primary);
        transition: var(--transition);
    }

    .activity-item:hover .activity-title {
        color: var(--primary);
    }

    .activity-time {
        color: var(--text-muted);
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        margin-top: 0.5rem;
    }

    .activity-time i {
        font-size: 0.875rem;
        margin-right: 0.375rem;
        opacity: 0.7;
        color: var(--primary);
    }

    /* Quick actions */
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.25rem;
        background-color: white;
        border: 1px solid var(--border);
        border-radius: 50px;
        transition: var(--transition);
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--text-secondary);
        box-shadow: var(--shadow-sm);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        z-index: -1;
        transition: opacity 0.3s ease;
    }

    .quick-action-btn:hover {
        color: white;
        border-color: transparent;
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }

    .quick-action-btn:hover::before {
        opacity: 1;
    }

    .quick-action-btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
        color: var(--primary);
        transition: var(--transition);
    }

    .quick-action-btn:hover i {
        color: white;
        transform: scale(1.1);
    }

    @media (max-width: 768px) {
        .quick-actions {
            margin-top: 1rem;
        }

        .dashboard-title {
            display: block;
            margin-bottom: 1rem;
        }
    }

    /* Remove underlines from all buttons and action links */
    .quick-action-btn,
    .action-btn,
    .action-btn-primary,
    .action-btn-outline,
    .dashboard-table a,
    .btn {
        text-decoration: none !important;
    }

    /* Ensure proper hover states without underlines */
    .dashboard-table a:hover {
        color: var(--primary);
        text-decoration: none !important;
    }

    /* Enhanced Sidebar Styling */
    .sidebar {
        background-color: #429e46;
        box-shadow: var(--shadow);
        border-right: none;
    }

    .sidebar .nav-item {
        margin-bottom: 0.25rem;
    }

    .sidebar .nav-link {
        border-radius: 0.5rem;
        margin: 0 0.75rem;
        padding: 0.75rem 1rem;
        color: rgba(255, 255, 255, 0.85);
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
    }

    .sidebar .nav-link:hover,
    .sidebar .nav-link:focus,
    .sidebar .nav-link.active {
        background-color: rgba(255, 255, 255, 0.15);
        color: #ffffff;
    }

    .sidebar .nav-link.active {
        font-weight: 600;
    }

    .sidebar .nav-link i {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.7);
    }

    .sidebar .nav-link:hover i,
    .sidebar .nav-link:focus i,
    .sidebar .nav-link.active i {
        color: #ffffff;
    }

    .sidebar .nav-link.active i {
        background: linear-gradient(135deg, #347c38 0%, #56b75b 100%);
        color: white;
        border-radius: 0.375rem;
    }

    /* Fix for sidebar dropdown */
    .sidebar .collapse,
    .sidebar .collapsing {
        margin: 0 0.75rem;
    }

    .sidebar .collapse .nav-item .nav-link,
    .sidebar .collapsing .nav-item .nav-link {
        margin: 0.2rem 0;
        padding: 0.5rem 0.75rem 0.5rem 2.5rem;
        font-size: 0.85rem;
        position: relative;
    }

    .sidebar .collapse .nav-item .nav-link::before,
    .sidebar .collapsing .nav-item .nav-link::before {
        content: '';
        position: absolute;
        left: 1.25rem;
        top: 50%;
        width: 0.35rem;
        height: 0.35rem;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        transform: translateY(-50%);
        transition: all 0.2s ease;
    }

    .sidebar .collapse .nav-item .nav-link:hover::before,
    .sidebar .collapsing .nav-item .nav-link:hover::before,
    .sidebar .collapse .nav-item .nav-link.active::before,
    .sidebar .collapsing .nav-item .nav-link.active::before {
        background-color: #ffffff;
    }

    .sidebar .nav-item .nav-link[data-toggle="collapse"]::after {
        width: 1rem;
        text-align: center;
        float: right;
        vertical-align: 0;
        border: 0;
        font-weight: 900;
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        color: rgba(255, 255, 255, 0.6);
    }

    .sidebar .nav-item .nav-link[data-toggle="collapse"].collapsed::after {
        content: '\f105';
    }

    .sidebar-divider {
        border-top: 1px solid rgba(255, 255, 255, 0.15);
        margin: 1rem 0;
        opacity: 0.6;
    }

    .sidebar-heading {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
        font-weight: 600;
        letter-spacing: 0.05em;
        text-transform: uppercase;
        padding: 0 1rem;
        margin: 1rem 0 0.5rem;
    }

    .sidebar-brand {
        height: 4.5rem;
        display: flex;
        align-items: center;
        padding: 0 1.5rem;
        text-decoration: none !important;
    }

    .sidebar-brand-icon {
        margin-right: 0.75rem;
        color: #ffffff;
    }

    .sidebar-brand-text {
        font-weight: 700;
        color: white;
        font-size: 1.25rem;
    }

    /* Collapsed sidebar icon styling */
    .sidebar.toggled .nav-item .nav-link {
        text-align: center;
        padding: 0.75rem;
        margin: 0 0.5rem;
    }

    .sidebar.toggled .nav-item .nav-link i {
        margin: 0 auto;
        width: 2rem;
        height: 2rem;
        font-size: 1.1rem;
    }

    .sidebar.toggled .collapse {
        position: absolute;
        left: calc(6.5rem + 1.5rem / 2);
        z-index: 1;
        top: 2px;
        animation-name: fadeIn;
        animation-duration: 0.3s;
        animation-timing-function: ease;
        background: var(--card-bg);
        border-radius: var(--border-radius-sm);
        box-shadow: var(--shadow);
        margin: 0;
        min-width: 10rem;
    }

    .sidebar.toggled .collapse .nav-item .nav-link {
        padding: 0.5rem 1rem;
        margin: 0;
        width: 100%;
        text-align: left;
    }

    .sidebar.toggled .collapse .nav-item .nav-link::before {
        display: none;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(5px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-header {
        display: flex;
        align-items: center;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border);
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.5rem;
        color: var(--primary);
    }

    /* Animation Classes */
    .animate__animated {
        animation-duration: 0.5s;
    }

    .animate__fadeIn {
        animation-name: fadeIn;
    }

    .animate__fadeInUp {
        animation-name: fadeInUp;
        animation-duration: 0.6s;
    }

    .animate__fadeInRight {
        animation-name: fadeInRight;
        animation-duration: 0.6s;
    }

    .animate__delay-1 {
        animation-delay: 0.1s;
    }

    .animate__delay-2 {
        animation-delay: 0.2s;
    }

    .animate__delay-3 {
        animation-delay: 0.3s;
    }

    .animate__delay-4 {
        animation-delay: 0.4s;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translate3d(0, 20px, 0);
        }
        to {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }

    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translate3d(20px, 0, 0);
        }
        to {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }

    /* Content Cards (matching logistics dashboard) */
    .content-card {
        background-color: var(--bg-white);
        border-radius: var(--radius);
        border: none;
        box-shadow: var(--shadow);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        height: 100%;
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .content-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .content-card .card-header {
        background-color: var(--bg-white);
        border-bottom: 1px solid var(--border);
        padding: 1.25rem 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .content-card .card-header h6 {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        color: var(--text-primary);
    }

    .content-card .card-header i {
        font-size: 1.1rem;
        color: var(--primary);
        margin-right: 0.75rem;
    }

    .content-card .card-body {
        padding: 1.5rem;
        position: relative;
    }

    /* Update layout for the admin tools section */
    .content-card .card-body .btn-light {
        background-color: var(--bg-white);
        border-radius: var(--radius);
        border: 1px solid var(--border);
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease;
        text-align: center;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .content-card .card-body .btn-light:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow);
        border-color: var(--primary-light);
        background-color: var(--primary-transparent);
    }

    .content-card .card-body .btn-light i {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
        transition: transform 0.2s ease;
    }

    .content-card .card-body .btn-light:hover i {
        transform: scale(1.2);
    }

    .content-card .card-body .btn-light h6 {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    /* Legend styling */
    .legend-item {
        display: flex;
        align-items: center;
        margin: 0.25rem 0.5rem;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .color-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 6px;
        display: inline-block;
    }

    /* Button group for chart type toggle */
    .btn-group .action-btn-outline {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .btn-group .action-btn-outline.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    .btn-group .action-btn-outline.active i {
        color: white;
    }

    /* Table inside cards */
    .dashboard-table {
        margin-top: 0;
    }

    .dashboard-table table {
        margin-bottom: 0;
    }

    .dashboard-table thead th {
        background-color: rgba(46, 125, 50, 0.05);
        color: var(--text-secondary);
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05rem;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border);
    }

    .dashboard-table tbody td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
        border-top: none;
        border-bottom: 1px solid var(--border);
        font-size: 0.875rem;
    }

    .dashboard-table tbody tr:last-child td {
        border-bottom: none;
    }
</style>

<div class="container-fluid dashboard-page py-4">
    <!-- Dashboard Header Section -->
    <header class="dashboard-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <?php
                // Extract first name from full name
                $firstName = '';
                if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                    $nameParts = explode(' ', $_SESSION['full_name']);
                    $firstName = $nameParts[0];
                }
                ?>
                <h1 class="dashboard-title">Hi, <?php echo htmlspecialchars($firstName); ?></h1>
            </div>
            <div class="dashboard-actions">
                <a href="/choims/modules/reports/assets.php" class="action-btn">
                    <i class="fas fa-laptop-medical"></i> Asset Reports
                </a>
                <a href="/choims/modules/reports/inventory.php" class="action-btn">
                    <i class="fas fa-box-open"></i> Consumables
                </a>
                <a href="/choims/modules/reports/transfers.php" class="action-btn">
                    <i class="fas fa-truck-loading"></i> Transfers
                </a>
                <a href="/choims/modules/reports/detailed_audit_logs.php" class="action-btn action-primary">
                    <i class="fas fa-file-alt"></i> Logs
                </a>
            </div>
        </div>
        <p class="dashboard-date"><i class="far fa-calendar-alt"></i> Today is <?php echo date('F d, Y'); ?></p>
    </header>

    <!-- SECTION: Key Statistics -->
    <div class="section-header mb-3">
        <h5 class="section-title">
            <i class="fas fa-chart-line"></i> Key Statistics
        </h5>
    </div>

    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card primary-card animate__animated animate__fadeInUp animate__delay-1">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-primary">
                            <i class="fas fa-laptop-medical"></i>
                        </div>
                        <div class="stats-info">
                            <div class="stats-title">Fixed Assets</div>
                            <div class="stats-value"><?php echo number_format($assetsCount); ?></div>
                            <div class="stats-description">Total registered equipment</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card info-card animate__animated animate__fadeInUp animate__delay-2">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-info">
                            <i class="fas fa-box-open"></i>
                        </div>
                        <div class="stats-info">
                            <div class="stats-title">Consumable Items</div>
                            <div class="stats-value"><?php echo number_format($totalConsumableQty); ?></div>
                            <div class="stats-description"><?php echo number_format($inventoryCount); ?> unique inventory items</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card warning-card animate__animated animate__fadeInUp animate__delay-3">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stats-info">
                            <div class="stats-title">Low Stock</div>
                            <div class="stats-value"><?php echo number_format($lowStockCount); ?></div>
                            <div class="stats-description">Items requiring attention</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card danger-card animate__animated animate__fadeInUp animate__delay-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-danger">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="stats-info">
                            <div class="stats-title">Pending Transfers</div>
                            <div class="stats-value"><?php echo number_format($pendingTransfersCount); ?></div>
                            <div class="stats-description">Awaiting approval</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SECTION: Alerts & Activity -->
    <div class="section-header mb-3">
        <h5 class="section-title">
            <i class="fas fa-bell"></i> Alerts & Activity
        </h5>
    </div>

    <div class="row">
        <!-- Low Inventory Alerts -->
        <div class="col-xl-4 col-lg-6">
            <div class="card content-card animate__animated animate__fadeInUp animate__delay-1">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h6 class="m-0">Low Consumables Alerts</h6>
                    </div>
                    <a href="/choims/modules/inventory/list.php?status=Low%20Stock" class="action-btn action-btn-outline">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($lowStockItemsResult) > 0): ?>
                        <div class="table-responsive dashboard-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($item = mysqli_fetch_assoc($lowStockItemsResult)): ?>
                                        <tr>
                                            <td><?php echo $item['sku_code'] . ' - ' . $item['sku_name']; ?></td>
                                            <td><?php echo $item['location_name']; ?></td>
                                            <td>
                                                <span class="status-badge <?php echo ($item['status'] == 'Out of Stock') ? 'badge-danger' : 'badge-warning'; ?>">
                                                    <?php echo $item['status']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $item['current_quantity']; ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> All inventory items are at adequate levels.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Activity Feed -->
        <div class="col-xl-8 col-lg-6">
            <div class="card content-card animate__animated animate__fadeInUp animate__delay-2">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-history"></i>
                        <h6 class="m-0">Recent Activity</h6>
                    </div>
                    <a href="/choims/modules/reports/detailed_audit_logs.php" class="action-btn action-btn-outline">
                        <i class="fas fa-list"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <ul class="activity-list">
                        <?php while($activity = mysqli_fetch_assoc($activityResult)):
                            // Determine icon and color based on action
                            $icon = 'fa-info-circle';
                            $bgClass = 'bg-info';

                            switch($activity['action']) {
                                case 'create':
                                    $icon = 'fa-plus';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'update':
                                    $icon = 'fa-pen';
                                    $bgClass = 'bg-info';
                                    break;
                                case 'delete':
                                    $icon = 'fa-trash';
                                    $bgClass = 'bg-danger';
                                    break;
                                case 'login':
                                    $icon = 'fa-sign-in-alt';
                                    $bgClass = 'bg-primary';
                                    break;
                                case 'logout':
                                    $icon = 'fa-sign-out-alt';
                                    $bgClass = 'bg-secondary';
                                    break;
                                case 'transfer_initiate':
                                    $icon = 'fa-exchange-alt';
                                    $bgClass = 'bg-warning';
                                    break;
                                case 'transfer_approve':
                                    $icon = 'fa-check';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'transfer_reject':
                                    $icon = 'fa-times';
                                    $bgClass = 'bg-danger';
                                    break;
                                case 'transfer_complete':
                                    $icon = 'fa-check-double';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'stock_in':
                                    $icon = 'fa-arrow-down';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'stock_out':
                                    $icon = 'fa-arrow-up';
                                    $bgClass = 'bg-danger';
                                    break;
                                case 'batch_transfer':
                                    $icon = 'fa-boxes';
                                    $bgClass = 'bg-warning';
                                    break;
                            }

                            // Format timestamp
                            $timestamp = strtotime($activity['log_time']);
                            $timeAgo = timeAgo($timestamp);
                        ?>
                            <li class="activity-item">
                                <div class="activity-icon <?php echo $bgClass; ?> text-white">
                                    <i class="fas <?php echo $icon; ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">
                                        <?php echo $activity['full_name'] ? $activity['full_name'] : 'System'; ?>
                                    </div>
                                    <div class="text-muted small">
                                        <?php
                                            // Display the changes summary from the detailed audit log
                                            if (!empty($activity['changes_summary'])) {
                                                echo $activity['changes_summary'];
                                            } else {
                                                echo $activity['action'] . ' ' . $activity['entity_type'];

                                                // Try to display a more descriptive message based on the action and entity
                                                $details = '';
                                                if (!empty($activity['details'])) {
                                                    $detailsObj = json_decode($activity['details'], true);
                                                    if ($detailsObj && isset($detailsObj['entity_name'])) {
                                                        $details = ': ' . $detailsObj['entity_name'];
                                                    } else if ($detailsObj && isset($detailsObj['name'])) {
                                                        $details = ': ' . $detailsObj['name'];
                                                    } else if ($detailsObj && isset($detailsObj['sku_name'])) {
                                                        $details = ': ' . $detailsObj['sku_name'];
                                                    } else if ($detailsObj && isset($detailsObj['asset_name'])) {
                                                        $details = ': ' . $detailsObj['asset_name'];
                                                    }
                                                    echo $details;
                                                }
                                            }
                                        ?>
                                    </div>
                                    <div class="activity-time"><i class="far fa-clock"></i> <?php echo $timeAgo; ?></div>
                                </div>
                            </li>
                        <?php endwhile; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- SECTION: Monthly Consumables Update Status -->
    <div class="section-header mb-3 mt-4">
        <h5 class="section-title">
            <i class="fas fa-calendar-check"></i> Monthly Consumables Update Status - <?php echo date('F Y'); ?>
        </h5>
    </div>

    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="inventory-update-card animate__animated animate__fadeInUp animate__delay-1">
                <div class="card-header">
                    <div class="header-title">
                        <i class="fas fa-clipboard-check"></i>
                        <h6 class="m-0">Location Consumables Updates</h6>
                    </div>
                    <a href="/choims/modules/inventory/monthly_status.php" class="view-report-btn">
                        <i class="fas fa-list"></i> View Full Report
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="monthly-update-stats">
                                <div class="progress-container">
                                    <?php
                                    $completionPercentage = $monthlyUpdateStats['total'] > 0 ?
                                        round(($monthlyUpdateStats['completed'] / $monthlyUpdateStats['total']) * 100) : 0;
                                    ?>
                                    <div class="progress-circle" data-percentage="<?php echo $completionPercentage; ?>">
                                        <div class="progress-circle-inner">
                                            <div class="progress-value"><?php echo $completionPercentage; ?>%</div>
                                            <div class="progress-text">Completed</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="stats-summary">
                                    <div class="stat-item">
                                        <div class="stat-label">Total Locations</div>
                                        <div class="stat-value"><?php echo $monthlyUpdateStats['total']; ?></div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Completed</div>
                                        <div class="stat-value text-success"><?php echo $monthlyUpdateStats['completed']; ?></div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-label">Pending</div>
                                        <div class="stat-value text-warning"><?php echo $monthlyUpdateStats['pending']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="table-responsive">
                                <table class="inventory-status-table">
                                    <thead>
                                        <tr>
                                            <th>Location</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Updated By</th>
                                            <th>Updated At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($monthlyUpdateStats['locations'])): ?>
                                            <?php foreach ($monthlyUpdateStats['locations'] as $location): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($location['location_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($location['location_type']); ?></td>
                                                    <td>
                                                        <?php if ($location['status'] === 'completed'): ?>
                                                            <span class="status-badge completed">Completed</span>
                                                        <?php else: ?>
                                                            <span class="status-badge pending">Pending</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo $location['updated_by_name'] ? htmlspecialchars($location['updated_by_name']) : 'N/A'; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo $location['updated_at'] ? date('M d, Y H:i', strtotime($location['updated_at'])) : 'N/A'; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center">No locations found</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SECTION: Analytics -->
    <div class="section-header mb-3 mt-4">
        <h5 class="section-title">
            <i class="fas fa-chart-bar"></i> Analytics
        </h5>
    </div>

    <div class="row">
        <!-- Assets by Location Chart -->
        <div class="col-xl-7">
            <div class="card content-card animate__animated animate__fadeInUp animate__delay-1">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-map-marker-alt"></i>
                        <h6 class="m-0">Assets by Location</h6>
                    </div>
                    <div class="btn-group">
                        <button type="button" id="viewBarChart" class="btn btn-sm action-btn action-btn-outline active">Bar</button>
                        <button type="button" id="viewTreemap" class="btn btn-sm action-btn action-btn-outline">Treemap</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="locationChart"></canvas>
                        <div id="locationTreemap" style="height: 300px; display: none;"></div>
                    </div>
                    <div class="mt-3">
                        <div class="d-flex flex-wrap justify-content-center">
                            <?php foreach (array_unique($locationTypes) as $type): ?>
                                <?php if (!empty($type)): ?>
                                    <div class="legend-item mx-2">
                                        <span class="color-dot" style="background-color: <?php echo $colorByType[$type] ?? '#e0e0e0'; ?>"></span>
                                        <?php echo $type; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consumables by Category Chart -->
        <div class="col-xl-5">
            <div class="card content-card animate__animated animate__fadeInUp animate__delay-2">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-chart-pie"></i>
                        <h6 class="m-0">Consumables by Category</h6>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="consumablesCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SECTION: Recent Transfers -->
    <div class="section-header mb-3 mt-4">
        <h5 class="section-title">
            <i class="fas fa-exchange-alt"></i> Recent Transfers
        </h5>
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="card content-card animate__animated animate__fadeInUp animate__delay-1">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exchange-alt"></i>
                        <h6 class="m-0">Transfer Activity</h6>
                    </div>
                    <a href="/choims/modules/reports/transfers.php" class="action-btn action-btn-primary">
                        <i class="fas fa-list"></i> View All Transfers
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive dashboard-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Status</th>
                                    <th>Initiated By</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (mysqli_num_rows($recentTransfersResult) > 0):
                                    while($transfer = mysqli_fetch_assoc($recentTransfersResult)):
                                        $statusClass = '';
                                        switch($transfer['status']) {
                                            case 'Pending':
                                                $statusClass = 'badge-warning';
                                                break;
                                            case 'Approved by Logistics':
                                            case 'Approved by HIMU':
                                                $statusClass = 'badge-info';
                                                break;
                                            case 'Completed':
                                                $statusClass = 'badge-success';
                                                break;
                                            case 'Rejected':
                                                $statusClass = 'badge-danger';
                                                break;
                                        }
                                        $transferLink = "/choims/modules/transfers/view.php?id=" . $transfer['transfer_id'];
                                    ?>
                                        <tr onclick="window.location='<?php echo $transferLink; ?>'">
                                            <td><span class="transfer-id">#<?php echo $transfer['transfer_id']; ?></span></td>
                                            <td><?php echo date('M d, Y', strtotime($transfer['transfer_date'])); ?></td>
                                            <td><?php echo $transfer['item_type']; ?></td>
                                            <td><?php echo $transfer['source_location_name']; ?></td>
                                            <td><?php echo $transfer['destination_location_name']; ?></td>
                                            <td><span class="status-badge <?php echo $statusClass; ?>"><?php echo $transfer['status']; ?></span></td>
                                            <td><?php echo $transfer['user_name']; ?></td>
                                        </tr>
                                <?php
                                    endwhile;
                                else:
                                ?>
                                <tr>
                                    <td colspan="7" class="text-center">No recent transfers found.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

<!-- Load ECharts for Treemap visualization -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>

<script>
// Update charts with new color palette
document.addEventListener('DOMContentLoaded', function() {
    // Monthly inventory update progress circle
    const progressCircle = document.querySelector('.progress-circle');
    if (progressCircle) {
        const percentage = progressCircle.getAttribute('data-percentage');
        progressCircle.style.setProperty('--percentage', percentage + '%');
    }

    // Configure chart colors
    const modernPalette = [
        '#00ADB5', '#3FC1D0', '#3B82F6', '#6366F1', '#8B5CF6',
        '#F43F5E', '#FB7185', '#F59E0B', '#34D399', '#06B6D4'
    ];

    let locationChart;

    // Function to render the bar chart
    function renderBarChart() {
        const locationCtx = document.getElementById('locationChart').getContext('2d');

        // Destroy existing chart if it exists
        if (locationChart) {
            locationChart.destroy();
        }

        // Group data by location type for better visualization
        const locationLabelsArray = <?php echo json_encode($locationLabels); ?>;
        const assetDataArray = <?php echo json_encode($assetData); ?>;
        const locationTypesArray = <?php echo json_encode($locationTypes); ?>;
        const backgroundColorsArray = <?php echo json_encode($backgroundColors); ?>;

        // Create the bar chart
        locationChart = new Chart(locationCtx, {
            type: 'bar',
            data: {
                labels: locationLabelsArray,
                datasets: [{
                    label: 'Number of Assets',
                    data: assetDataArray,
                    backgroundColor: backgroundColorsArray,
                    borderColor: 'rgba(255, 255, 255, 0.8)',
                    borderWidth: 1,
                    borderRadius: 4,
                    maxBarThickness: 40
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',  // Make it a horizontal bar chart for better readability with many locations
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        padding: 12,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 14
                        },
                        callbacks: {
                            label: function(context) {
                                return `Assets: ${context.raw}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to render the treemap
    function renderTreemap() {
        // Hide the bar chart canvas, show the treemap div
        document.getElementById('locationChart').style.display = 'none';
        document.getElementById('locationTreemap').style.display = 'block';

        // Prepare data for the treemap
        const locationLabelsArray = <?php echo json_encode($locationLabels); ?>;
        const assetDataArray = <?php echo json_encode($assetData); ?>;
        const locationTypesArray = <?php echo json_encode($locationTypes); ?>;
        const backgroundColorsArray = <?php echo json_encode($backgroundColors); ?>;
        const colorByType = <?php echo json_encode($colorByType); ?>;

        // Group data by location type
        const groupedData = [];
        const typeData = {};

        // First organize by types
        for (let i = 0; i < locationLabelsArray.length; i++) {
            const type = locationTypesArray[i] || 'Other';
            const location = locationLabelsArray[i];
            const value = assetDataArray[i];
            const color = backgroundColorsArray[i];

            if (!typeData[type]) {
                typeData[type] = {
                    name: type,
                    value: 0,
                    children: [],
                    itemStyle: {
                        color: colorByType[type] || '#e0e0e0'
                    }
                };
                groupedData.push(typeData[type]);
            }

            // Add location to its type
            typeData[type].children.push({
                name: location,
                value: value,
                itemStyle: {
                    color: color
                }
            });

            // Sum up the values for the type
            typeData[type].value += value;
        }

        // Sort types by total value
        groupedData.sort((a, b) => b.value - a.value);

        // Sort locations within each type by value
        groupedData.forEach(type => {
            type.children.sort((a, b) => b.value - a.value);
        });

        // Initialize the treemap chart
        const treemapChart = echarts.init(document.getElementById('locationTreemap'));

        // Set up treemap options
        const option = {
            tooltip: {
                formatter: function(info) {
                    // Show the full path and value in tooltip
                    const value = info.value;
                    const name = info.name;
                    if (info.treePathInfo.length > 1) {
                        const parentName = info.treePathInfo[0].name;
                        return `<div style="font-weight:bold">${name}</div>` +
                               `<div>Type: ${parentName}</div>` +
                               `<div>Assets: ${value}</div>`;
                    } else {
                        return `<div style="font-weight:bold">${name}</div>` +
                               `<div>Total Assets: ${value}</div>`;
                    }
                }
            },
            series: [{
                type: 'treemap',
                data: groupedData,
                width: '100%',
                height: '100%',
                roam: false,
                nodeClick: false, // Disable node clicking
                breadcrumb: {
                    show: false
                },
                label: {
                    show: true,
                    formatter: '{b}: {c}',
                    position: 'inside',
                    fontSize: 12,
                    color: '#fff',
                    fontWeight: 'bold'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                    gapWidth: 2
                },
                upperLabel: {
                    show: true,
                    height: 30,
                    color: '#000',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 12
                },
                levels: [
                    {
                        // Level 1 (Type level)
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 3,
                            gapWidth: 3
                        },
                        upperLabel: {
                            show: true
                        }
                    },
                    {
                        // Level 2 (Location level)
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 1,
                            gapWidth: 1
                        }
                    }
                ]
            }]
        };

        // Apply options to the chart
        treemapChart.setOption(option);

        // Handle window resize
        window.addEventListener('resize', function() {
            treemapChart.resize();
        });
    }

    // Update consumables category chart
    const consumablesCategoryCtx = document.getElementById('consumablesCategoryChart').getContext('2d');

    // Custom color palette for consumables chart - completely different from other charts
    const consumablesPalette = [
        '#FF5733', '#C70039', '#900C3F', '#581845', '#FFC300',
        '#DAF7A6', '#FF5733', '#C70039', '#900C3F', '#581845'
    ];

    const consumablesCategoryChart = new Chart(consumablesCategoryCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($consumablesCategoryLabels); ?>,
            datasets: [{
                data: <?php echo json_encode($consumablesCategoryData); ?>,
                backgroundColor: consumablesPalette,
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    padding: 12,
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 14
                    }
                }
            }
        }
    });

    // Initial render of the bar chart
    renderBarChart();

    // Toggle between bar chart and treemap
    document.getElementById('viewBarChart').addEventListener('click', function() {
        document.getElementById('locationChart').style.display = 'block';
        document.getElementById('locationTreemap').style.display = 'none';
        document.getElementById('viewBarChart').classList.add('active');
        document.getElementById('viewTreemap').classList.remove('active');
        renderBarChart();
    });

    document.getElementById('viewTreemap').addEventListener('click', function() {
        document.getElementById('locationChart').style.display = 'none';
        document.getElementById('locationTreemap').style.display = 'block';
        document.getElementById('viewBarChart').classList.remove('active');
        document.getElementById('viewTreemap').classList.add('active');
        renderTreemap();
    });
});
</script>

<?php
function timeAgo($timestamp) {
    $current_time = time();
    $diff = $current_time - $timestamp;

    // Handle invalid timestamps or future times
    if ($diff < 0) {
        // Log the time discrepancy for debugging
        error_log("Time discrepancy detected: timestamp ($timestamp) is in the future compared to current time ($current_time)");
        $diff = 0; // Treat as "just now" to avoid negative time
    }

    // Just now (less than a minute)
    if ($diff < 60) {
        return "Just now";
    }

    // Minutes
    $diff = floor($diff / 60);
    if ($diff < 60) {
        return $diff . " minute" . ($diff != 1 ? "s" : "") . " ago";
    }

    // Hours
    $diff = floor($diff / 60);
    if ($diff < 24) {
        return $diff . " hour" . ($diff != 1 ? "s" : "") . " ago";
    }

    // Days
    $diff = floor($diff / 24);
    if ($diff < 7) {
        return $diff . " day" . ($diff != 1 ? "s" : "") . " ago";
    }

    // Switch to date format after a week
    return date('M j, Y', $timestamp);
}

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>