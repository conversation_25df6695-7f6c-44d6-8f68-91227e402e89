<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Add custom CSS for modern maintenance edit page
echo '<link rel="stylesheet" href="/choims/assets/css/maintenance-edit-modern.css">';
echo '<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">';

// Add additional custom CSS for enhanced form elements
echo '<style>
    /* Enhanced form elements */
    .form-control-hover {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 0.15rem rgba(46, 125, 50, 0.15);
        transition: all 0.2s ease;
    }

    .form-group-focus {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }

    .form-group {
        transition: all 0.3s ease;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .form-label i {
        margin-right: 0.5rem;
        color: var(--primary);
    }

    .required {
        color: #ef4444;
        font-weight: bold;
        margin-left: 0.25rem;
    }

    .info-value {
        font-weight: 600;
        color: var(--text-primary);
        background-color: var(--bg-accent);
        padding: 0.5rem 0.75rem;
        border-radius: var(--radius-sm);
        box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
    }

    .asset-info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1.25rem;
        color: var(--primary-dark);
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 0.75rem;
    }

    .section-title i {
        margin-right: 0.5rem;
        color: var(--primary);
    }

    /* Status buttons styling */
    .status-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .status-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1rem;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-color);
        background-color: var(--bg-white);
        color: var(--text-primary);
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: var(--shadow-sm);
    }

    .status-btn i {
        margin-right: 0.5rem;
    }

    .status-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .status-btn.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    /* Status button colors */
    .status-btn[data-status="Scheduled"] {
        border-color: #3b82f6;
        color: #3b82f6;
    }

    .status-btn[data-status="Scheduled"].active {
        background-color: #3b82f6;
        color: white;
    }

    .status-btn[data-status="In Progress"] {
        border-color: #f59e0b;
        color: #f59e0b;
    }

    .status-btn[data-status="In Progress"].active {
        background-color: #f59e0b;
        color: white;
    }

    .status-btn[data-status="Completed"] {
        border-color: #10b981;
        color: #10b981;
    }

    .status-btn[data-status="Completed"].active {
        background-color: #10b981;
        color: white;
    }

    .status-btn[data-status="Cancelled"] {
        border-color: #ef4444;
        color: #ef4444;
    }

    .status-btn[data-status="Cancelled"].active {
        background-color: #ef4444;
        color: white;
    }

    @media (max-width: 768px) {
        .asset-info-grid {
            grid-template-columns: 1fr;
        }

        .status-buttons {
            flex-direction: column;
        }

        .status-btn {
            width: 100%;
        }
    }
</style>';

// Ensure user is logged in
requireLogin();

// Restrict access to authorized roles only
if (!hasRole('GodMode') && !hasRole('Superadmin') && !hasRole('Logistics')) {
    // Set flash message
    setFlashMessage('error', 'Access denied. You do not have permission to edit maintenance records for office and medical equipment.');

    // Redirect based on user role
    if (hasRole('HealthCenter')) {
        header('Location: /choims/dashboards/health_center.php');
    } else if (hasRole('Department')) {
        header('Location: /choims/dashboards/department.php');
    } else if (hasRole('HIMU')) {
        header('Location: /choims/dashboards/himu.php');
    } else {
        header('Location: /choims/index.php');
    }
    exit;
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    setFlashMessage('error', 'Invalid request. Maintenance record ID is required.');
    header('Location: /choims/modules/logistics/maintenance.php');
    exit;
}

$record_id = sanitizeInput($_GET['id']);

// Get maintenance record details
$recordQuery = "
    SELECT mr.*, fa.asset_name, fa.serial_number, sm.sku_name, c.category_name, l.location_name
    FROM maintenance_records mr
    JOIN fixed_assets fa ON mr.asset_id = fa.asset_id
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    WHERE mr.record_id = ?
";

$recordStmt = mysqli_prepare($conn, $recordQuery);
mysqli_stmt_bind_param($recordStmt, 'i', $record_id);
mysqli_stmt_execute($recordStmt);
$recordResult = mysqli_stmt_get_result($recordStmt);

// Check if record exists
if (mysqli_num_rows($recordResult) === 0) {
    setFlashMessage('error', 'Maintenance record not found.');
    header('Location: /choims/modules/logistics/maintenance.php');
    exit;
}

$record = mysqli_fetch_assoc($recordResult);

// Get technicians (users) for the edit form
$techniciansQuery = "
    SELECT user_id, username, full_name
    FROM users
    WHERE role IN ('admin', 'Logistics')
    ORDER BY username
";
$techniciansResult = mysqli_query($conn, $techniciansQuery);

// Get common maintenance types
$maintenanceTypes = [
    'Preventive Maintenance',
    'Corrective Maintenance',
    'Repair',
    'Upgrade',
    'Inspection',
    'Calibration',
    'Parts Replacement',
    'Cleaning',
    'Other'
];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Start transaction
    mysqli_begin_transaction($conn);

    try {
        // Get form data
        $maintenance_date = sanitizeInput($_POST['maintenance_date']);
        $maintenance_type = sanitizeInput($_POST['maintenance_type']);
        $cost = !empty($_POST['cost']) ? sanitizeInput($_POST['cost']) : null;
        $technician_id = !empty($_POST['technician_id']) ? sanitizeInput($_POST['technician_id']) : null;

        // Ensure status is set and valid
        if (!isset($_POST['status']) || empty($_POST['status'])) {
            throw new Exception("Status is required. Please select a status.");
        }

        $status = sanitizeInput($_POST['status']);

        // Validate status value
        $validStatuses = ['Scheduled', 'In Progress', 'Completed', 'Cancelled'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception("Invalid status value: " . $status);
        }

        $description = sanitizeInput($_POST['description']);
        $update_asset_status = isset($_POST['update_asset_status']) ? true : false;

        // Get old record data for audit log
        $oldData = [
            'maintenance_date' => $record['maintenance_date'],
            'maintenance_type' => $record['maintenance_type'],
            'cost' => $record['cost'],
            'technician_id' => $record['technician_id'],
            'status' => $record['status'],
            'description' => $record['description']
        ];

        // DIRECT UPDATE APPROACH - Update only the status first
        $statusUpdateQuery = "
            UPDATE maintenance_records
            SET status = ?, updated_at = NOW()
            WHERE record_id = ?
        ";

        $statusUpdateStmt = mysqli_prepare($conn, $statusUpdateQuery);
        mysqli_stmt_bind_param($statusUpdateStmt, 'si', $status, $record_id);
        $statusUpdateResult = mysqli_stmt_execute($statusUpdateStmt);

        if (!$statusUpdateResult) {
            throw new Exception("Failed to update maintenance status: " . mysqli_error($conn));
        }

        // Now update the rest of the fields
        $updateQuery = "
            UPDATE maintenance_records
            SET maintenance_date = ?,
                maintenance_type = ?,
                cost = ?,
                technician_id = ?,
                description = ?,
                updated_at = NOW()
            WHERE record_id = ?
        ";

        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param(
            $updateStmt,
            'ssdssi',
            $maintenance_date,
            $maintenance_type,
            $cost,
            $technician_id,
            $description,
            $record_id
        );

        $updateResult = mysqli_stmt_execute($updateStmt);

        if (!$updateResult) {
            throw new Exception("Failed to update maintenance details: " . mysqli_error($conn));
        }

        // Update asset status if checkbox is checked
        if ($update_asset_status) {
            $assetStatus = 'Available'; // Default

            if ($status === 'In Progress') {
                $assetStatus = 'Under Repair';
            } else if ($status === 'Scheduled') {
                $assetStatus = 'Under Repair';
            }

            $updateAssetQuery = "
                UPDATE fixed_assets
                SET status = ?, updated_at = NOW()
                WHERE asset_id = ?
            ";

            $updateAssetStmt = mysqli_prepare($conn, $updateAssetQuery);
            mysqli_stmt_bind_param($updateAssetStmt, 'si', $assetStatus, $record['asset_id']);
            $updateAssetResult = mysqli_stmt_execute($updateAssetStmt);

            if (!$updateAssetResult) {
                throw new Exception("Failed to update asset status: " . mysqli_error($conn));
            }
        }

        // Prepare log data
        $newData = [
            'maintenance_date' => $maintenance_date,
            'maintenance_type' => $maintenance_type,
            'cost' => $cost,
            'technician_id' => $technician_id,
            'status' => $status,
            'description' => $description,
            'update_asset_status' => $update_asset_status
        ];

        // Log to regular audit log
        logActivity($conn, 'Update Maintenance Record', 'maintenance_records', $record_id, json_encode($oldData), json_encode($newData));

        // Log to detailed audit system if available
        if (function_exists('logDetailedAction')) {
            $changes_summary = "Updated maintenance record for asset: " . $record['asset_name'];

            logDetailedAction($conn, $_SESSION['user_id'], 'update', 'other', $record_id, [
                'entity_name' => 'Maintenance Record',
                'changes_summary' => $changes_summary,
                'old_values' => json_encode($oldData),
                'new_values' => json_encode($newData)
            ]);
        }

        // Commit transaction
        mysqli_commit($conn);

        // Set success message
        setFlashMessage('success', 'Maintenance record updated successfully.');

        // Redirect to maintenance page
        header('Location: /choims/modules/logistics/maintenance.php');
        exit;

    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);

        // Set error message
        setFlashMessage('error', $e->getMessage());

        // Redirect to edit page
        header('Location: /choims/modules/logistics/edit_maintenance.php?id=' . $record_id);
        exit;
    }
}
?>

<div class="container-fluid">
    <!-- Modern Header Section -->
    <div class="edit-header animate__animated animate__fadeIn">
        <div class="header-row">
            <div class="title-container">
                <div class="edit-title-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div>
                    <h1 class="edit-title">Edit Maintenance Record</h1>
                    <p class="edit-subtitle mt-1">
                        <i class="fas fa-clipboard-check me-2"></i> Update maintenance details for <?php echo $record['asset_name']; ?> (Record #<?php echo $record_id; ?>)
                    </p>
                </div>
            </div>
            <div class="header-actions">
                <a href="/choims/modules/logistics/maintenance.php" class="action-btn action-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Maintenance
                </a>
            </div>
        </div>
        <div class="mt-2">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/choims/dashboards/logistics.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="/choims/modules/logistics/maintenance.php">Equipment Maintenance</a></li>
                    <li class="breadcrumb-item active">Edit Record</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Asset Information Card -->
    <div class="edit-card animate__animated animate__fadeInUp">
        <div class="edit-card-header">
            <h2 class="edit-card-title">
                <i class="fas fa-laptop-medical"></i> Asset Information
            </h2>
        </div>
        <div class="edit-card-body">
            <div class="asset-info-grid">
                <div class="info-item">
                    <div class="info-label">Asset Name</div>
                    <div class="info-value"><?php echo $record['asset_name']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Category</div>
                    <div class="info-value"><?php echo $record['category_name']; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Serial Number</div>
                    <div class="info-value"><?php echo !empty($record['serial_number']) ? $record['serial_number'] : 'N/A'; ?></div>
                </div>
                <div class="info-item">
                    <div class="info-label">Location</div>
                    <div class="info-value"><?php echo $record['location_name']; ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Form Card -->
    <div class="edit-card animate__animated animate__fadeInUp animate__delay-2">
        <div class="edit-card-header">
            <h2 class="edit-card-title">
                <i class="fas fa-edit"></i> Maintenance Details
            </h2>
        </div>
        <div class="edit-card-body">
            <form action="/choims/modules/logistics/edit_maintenance.php?id=<?php echo $record_id; ?>" method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label for="maintenance_date" class="form-label">Maintenance Date <span class="required">*</span></label>
                        <input type="date" class="form-control" id="maintenance_date" name="maintenance_date" required value="<?php echo $record['maintenance_date']; ?>">
                    </div>
                    <div class="form-group">
                        <label for="maintenance_type" class="form-label">Maintenance Type <span class="required">*</span></label>
                        <select class="form-select" id="maintenance_type" name="maintenance_type" required>
                            <option value="">Select Type</option>
                            <?php
                            foreach ($maintenanceTypes as $type):
                                $typeIcon = 'fas fa-tools';
                                switch ($type) {
                                    case 'Preventive Maintenance':
                                        $typeIcon = 'fas fa-shield-alt';
                                        break;
                                    case 'Corrective Maintenance':
                                        $typeIcon = 'fas fa-wrench';
                                        break;
                                    case 'Repair':
                                        $typeIcon = 'fas fa-tools';
                                        break;
                                    case 'Upgrade':
                                        $typeIcon = 'fas fa-arrow-up';
                                        break;
                                    case 'Inspection':
                                        $typeIcon = 'fas fa-search';
                                        break;
                                    case 'Calibration':
                                        $typeIcon = 'fas fa-balance-scale';
                                        break;
                                    case 'Parts Replacement':
                                        $typeIcon = 'fas fa-exchange-alt';
                                        break;
                                    case 'Cleaning':
                                        $typeIcon = 'fas fa-broom';
                                        break;
                                }
                            ?>
                                <option value="<?php echo $type; ?>" <?php echo ($record['maintenance_type'] === $type) ? 'selected' : ''; ?> data-icon="<?php echo $typeIcon; ?>">
                                    <?php echo $type; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Status <span class="required">*</span></label>
                        <div class="status-buttons">
                            <input type="hidden" id="status" name="status" value="<?php echo $record['status']; ?>" required>
                            <button type="button" class="status-btn <?php echo ($record['status'] === 'Scheduled') ? 'active' : ''; ?>" data-status="Scheduled">
                                <i class="fas fa-calendar-alt"></i> Scheduled
                            </button>
                            <button type="button" class="status-btn <?php echo ($record['status'] === 'In Progress') ? 'active' : ''; ?>" data-status="In Progress">
                                <i class="fas fa-spinner"></i> In Progress
                            </button>
                            <button type="button" class="status-btn <?php echo ($record['status'] === 'Completed') ? 'active' : ''; ?>" data-status="Completed">
                                <i class="fas fa-check-circle"></i> Completed
                            </button>
                            <button type="button" class="status-btn <?php echo ($record['status'] === 'Cancelled') ? 'active' : ''; ?>" data-status="Cancelled">
                                <i class="fas fa-times-circle"></i> Cancelled
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="technician_id" class="form-label">Technician</label>
                        <select class="form-select" id="technician_id" name="technician_id">
                            <option value="">Select Technician</option>
                            <?php mysqli_data_seek($techniciansResult, 0); ?>
                            <?php while ($technician = mysqli_fetch_assoc($techniciansResult)): ?>
                                <option value="<?php echo $technician['user_id']; ?>"
                                    <?php echo ($record['technician_id'] == $technician['user_id']) ? 'selected' : ''; ?>>
                                    <?php echo $technician['username']; ?>
                                    <?php if (!empty($technician['full_name'])): ?>
                                        (<?php echo $technician['full_name']; ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="cost" class="form-label">Cost (₱)</label>
                        <input type="number" class="form-control" id="cost" name="cost" step="0.01" min="0" value="<?php echo $record['cost']; ?>">
                    </div>
                    <div class="form-group">
                        <label for="update_asset_status" class="form-label">Asset Status</label>
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" id="update_asset_status" name="update_asset_status" checked>
                            <label class="form-check-label" for="update_asset_status">
                                Update asset status based on maintenance status
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Description <span class="required">*</span></label>
                    <textarea class="form-control" id="description" name="description" rows="4" required><?php echo $record['description']; ?></textarea>
                </div>

                <div class="form-actions">
                    <a href="/choims/modules/logistics/maintenance.php" class="action-btn action-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Cancel
                    </a>
                    <button type="submit" class="action-btn action-primary" id="submitButton">
                        <i class="fas fa-save me-1"></i> Update Record
                    </button>
                    <a href="/choims/modules/logistics/direct_update.php?id=<?php echo $record_id; ?>" class="action-btn action-warning" style="display: none;" id="directUpdateBtn">
                        <i class="fas fa-bolt me-1"></i> Direct Update
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php'); ?>

<script>
$(document).ready(function() {
    // Status buttons functionality
    $('.status-btn').on('click', function() {
        // Remove active class from all buttons
        $('.status-btn').removeClass('active');

        // Add active class to clicked button
        $(this).addClass('active');

        // Update hidden input value
        var selectedStatus = $(this).data('status');
        $('#status').val(selectedStatus);

        // Update asset status checkbox based on selected status
        updateAssetStatusCheckbox(selectedStatus);
    });

    // Update asset status checkbox logic
    function updateAssetStatusCheckbox(status) {
        var checkbox = $('#update_asset_status');

        if (status === 'Completed' || status === 'In Progress') {
            checkbox.prop('checked', true);
        } else {
            checkbox.prop('checked', false);
        }
    }

    // Initialize asset status checkbox based on current status
    updateAssetStatusCheckbox($('#status').val());

    // Simple form submission handler
    $('form').on('submit', function() {
        // Show loading animation
        $('#submitButton').html('<i class="fas fa-spinner fa-spin me-1"></i> Updating...');
        $('#submitButton').prop('disabled', true);

        // Add animation to cards
        $('.edit-card').removeClass('animate__fadeInUp').addClass('animate__fadeOutUp');

        // Continue with normal form submission
        return true;
    });

    // Add icons to select options for maintenance type
    function addIconsToSelect(selectElement) {
        $(selectElement + ' option').each(function() {
            var icon = $(this).data('icon');
            if (icon) {
                $(this).attr('data-content', '<i class="' + icon + ' me-2"></i>' + $(this).text());
            }
        });
    }

    // Initialize select elements with icons if Bootstrap Select is available
    if ($.fn.selectpicker) {
        addIconsToSelect('#maintenance_type');
        $('.form-select').selectpicker({
            iconBase: 'fas',
            tickIcon: 'fa-check',
            showSubtext: true
        });
    } else {
        // Fallback for when Bootstrap Select is not available
        // Add icons directly to the options
        $('#maintenance_type').each(function() {
            var select = $(this);
            select.find('option').each(function() {
                var icon = $(this).data('icon');
                if (icon) {
                    $(this).html('<i class="' + icon + ' me-2"></i>' + $(this).text());
                }
            });
        });
    }

    // Add hover effects to form elements
    $('.form-control, .form-select').hover(
        function() { $(this).addClass('form-control-hover'); },
        function() { $(this).removeClass('form-control-hover'); }
    );

    // Add focus animation to form groups
    $('.form-control, .form-select').on('focus', function() {
        $(this).closest('.form-group').addClass('form-group-focus');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('form-group-focus');
    });

    // Add animation to the submit button
    $('.action-primary').hover(
        function() { $(this).addClass('animate__animated animate__pulse'); },
        function() { $(this).removeClass('animate__animated animate__pulse'); }
    );

    // Add animation to status buttons
    $('.status-btn').hover(
        function() {
            if (!$(this).hasClass('active')) {
                $(this).addClass('animate__animated animate__pulse');
            }
        },
        function() {
            $(this).removeClass('animate__animated animate__pulse');
        }
    );

    // No fallback button handler needed anymore
});
</script>
