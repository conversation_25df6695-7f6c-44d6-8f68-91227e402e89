<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration first, then functions
require_once '../config/database.php';
require_once '../includes/functions.php';

// Don't need to check session_status or start session since functions.php already does this

// Debugging - check if $conn exists
if (!isset($conn)) {
    die("Database connection not established. Check the database.php file.");
}

// Debugging - check for hasRole function
if (!function_exists('hasRole')) {
    die("The hasRole function is not defined. Check the functions.php file.");
}

// Security check - only admins should access this
if (!isset($_SESSION['user_id']) || !hasRole('godmode')) {
    header("Location: /choims/index.php");
    exit;
}

// Database connection is already established in database.php as $conn (mysqli)
// No need to create a new connection

$tables = [
    'audit_logs',
    'detailed_audit_logs',
    'batch_transfers',
    'batch_transfer_assets',
    'batch_transfer_inventory',
    'consumable_inventory',
    'consumable_transactions',
    'fixed_assets',
    'maintenance_records',
    'notifications',
    'transfers'
];

$success = [];
$errors = [];

// Only proceed if confirmation is provided
$confirmed = isset($_POST['confirm']) && $_POST['confirm'] === 'yes';

// Display the confirmation form if not confirmed
if (!$confirmed) {
    include_once '../includes/header.php';
    ?>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h3><i class="fas fa-exclamation-triangle me-2"></i>WARNING: Data Clearing Tool</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <p><strong>CAUTION:</strong> This action will permanently delete ALL data from the following tables:</p>
                    <ul>
                        <?php foreach ($tables as $table): ?>
                            <li><code><?php echo htmlspecialchars($table); ?></code></li>
                        <?php endforeach; ?>
                    </ul>
                    <p>This operation <strong>CANNOT</strong> be undone. Please make sure you have a backup before proceeding.</p>
                </div>

                <form method="post" onsubmit="return confirm('Are you absolutely sure you want to delete ALL data? This action CANNOT be undone!');">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                        <label class="form-check-label" for="confirmCheck">
                            I understand that this will permanently delete all data from the selected tables
                        </label>
                    </div>

                    <input type="hidden" name="confirm" value="yes">
                    <button type="submit" class="btn btn-danger">Clear All Data</button>
                    <a href="/choims/dashboards/superadmin.php" class="btn btn-secondary">Cancel</a>
                </form>
            </div>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    exit;
}

// If confirmed, proceed with clearing data
try {
    // Start transaction by turning off autocommit
    mysqli_autocommit($conn, FALSE);

    // Disable foreign key checks temporarily to avoid constraint issues
    mysqli_query($conn, "SET FOREIGN_KEY_CHECKS = 0");

    foreach ($tables as $table) {
        try {
            $query = "DELETE FROM " . $table;
            $result = mysqli_query($conn, $query);

            if ($result) {
                $affected_rows = mysqli_affected_rows($conn);
                $success[] = "Cleared $affected_rows records from $table";
            } else {
                $errors[] = "Error clearing $table: " . mysqli_error($conn);
            }
        } catch (Exception $e) {
            $errors[] = "Error clearing $table: " . $e->getMessage();
        }
    }

    // Re-enable foreign key checks
    mysqli_query($conn, "SET FOREIGN_KEY_CHECKS = 1");

    // Commit transaction if no errors
    if (empty($errors)) {
        mysqli_commit($conn);
    } else {
        mysqli_rollback($conn);
    }

    // Re-enable autocommit
    mysqli_autocommit($conn, TRUE);
} catch (Exception $e) {
    $errors[] = "Database transaction error: " . $e->getMessage();
    mysqli_rollback($conn);
    mysqli_autocommit($conn, TRUE);
}

// Display results
include_once '../includes/header.php';
?>

<div class="container mt-5">
    <div class="card">
        <div class="card-header <?php echo empty($errors) ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h3>
                <i class="fas <?php echo empty($errors) ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> me-2"></i>
                Data Clearing Results
            </h3>
        </div>
        <div class="card-body">
            <?php if (!empty($success)): ?>
                <h4>Successful Operations:</h4>
                <ul class="list-group mb-4">
                    <?php foreach ($success as $message): ?>
                        <li class="list-group-item list-group-item-success">
                            <i class="fas fa-check me-2"></i> <?php echo htmlspecialchars($message); ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <h4>Errors:</h4>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <p>Some operations failed. The database has been rolled back to prevent partial data deletion.</p>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i> All tables were successfully cleared.
                </div>
            <?php endif; ?>

            <div class="mt-4">
                <a href="/choims/dashboards/superadmin.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i> Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>