<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireRole('Superadmin');

// Get system statistics

// Total fixed assets count
$assetsQuery = "SELECT COUNT(*) as total FROM fixed_assets WHERE is_active = 1 AND (is_deleted = 0 OR is_deleted IS NULL)";
$assetsResult = mysqli_query($conn, $assetsQuery);
$assetsCount = mysqli_fetch_assoc($assetsResult)['total'];

// Total consumables count and quantity
$inventoryQuery = "SELECT COUNT(*) as total, SUM(i.current_quantity) as total_qty
                  FROM consumable_inventory i
                  JOIN sku_master s ON i.sku_id = s.sku_id
                  WHERE s.item_type = 'Consumable' AND (i.is_deleted = 0 OR i.is_deleted IS NULL)";
$inventoryResult = mysqli_query($conn, $inventoryQuery);
$inventoryData = mysqli_fetch_assoc($inventoryResult);
$inventoryCount = $inventoryData['total'];
$totalConsumableQty = $inventoryData['total_qty'] ?: 0;

// Total number of locations
$locationsQuery = "SELECT COUNT(*) as total FROM locations";
$locationsResult = mysqli_query($conn, $locationsQuery);
$locationsCount = mysqli_fetch_assoc($locationsResult)['total'];

// Total number of users
$usersQuery = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
$usersResult = mysqli_query($conn, $usersQuery);
$usersCount = mysqli_fetch_assoc($usersResult)['total'];

// Low stock IT supplies count
$lowStockQuery = "SELECT COUNT(*) as total FROM consumable_inventory i
                JOIN sku_master s ON i.sku_id = s.sku_id
                WHERE (i.status = 'Low Stock' OR i.status = 'Out of Stock')
                AND s.category_id = 4
                AND (i.is_deleted = 0 OR i.is_deleted IS NULL)";
$lowStockResult = mysqli_query($conn, $lowStockQuery);
$lowStockCount = mysqli_fetch_assoc($lowStockResult)['total'];

// Get specific low stock items for alerts
$lowStockItemsQuery = "
    SELECT ci.*, l.location_name, s.sku_name, s.sku_code
    FROM consumable_inventory ci
    JOIN locations l ON ci.location_id = l.location_id
    JOIN sku_master s ON ci.sku_id = s.sku_id
    WHERE (ci.status = 'Low Stock' OR ci.status = 'Out of Stock') AND (ci.is_deleted = 0 OR ci.is_deleted IS NULL)
    ORDER BY ci.status ASC, ci.current_quantity ASC
    LIMIT 5
";
$lowStockItemsResult = mysqli_query($conn, $lowStockItemsQuery);

// Pending transfers count
$transfersQuery = "
    SELECT
        (SELECT COUNT(*) FROM transfers WHERE status = 'Pending' OR status = 'Approved by Logistics') +
        (SELECT COUNT(*) FROM batch_transfers WHERE status = 'Pending' OR status = 'Approved by Logistics') AS total
    ";
$transfersResult = mysqli_query($conn, $transfersQuery);
$pendingTransfersCount = mysqli_fetch_assoc($transfersResult)['total'];

// Recent system activity with more detailed information
$activityQuery = "
    SELECT dal.log_id, dal.user_id, dal.action_type as action, dal.entity_type, dal.entity_id, dal.ip_address, dal.created_at as log_time,
           dal.old_values, dal.new_values as details, dal.changes_summary, dal.username, dal.full_name
    FROM detailed_audit_logs dal
    ORDER BY dal.created_at DESC
    LIMIT 3";
$activityResult = mysqli_query($conn, $activityQuery);

// Assets by location chart data
$assetsByLocationQuery = "
    SELECT l.location_name, COUNT(a.asset_id) as asset_count,
           l.location_type
    FROM locations l
    LEFT JOIN fixed_assets a ON l.location_id = a.current_location_id AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    GROUP BY l.location_id
    ORDER BY l.location_type, l.location_name";
$assetsByLocationResult = mysqli_query($conn, $assetsByLocationQuery);
$locationLabels = [];
$assetData = [];
$locationTypes = [];
$backgroundColors = [];

// Color palette based on location type
$colorByType = [
    'Health Center' => '#4CAF50',
    'Department' => '#2196F3',
    'Office' => '#FFC107',
    'Warehouse' => '#9C27B0',
    'Other' => '#FF5722'
];

// Check if query executed successfully
if ($assetsByLocationResult) {
    while ($row = mysqli_fetch_assoc($assetsByLocationResult)) {
        $locationLabels[] = $row['location_name'];
        $assetData[] = (int)$row['asset_count'];
        $locationType = $row['location_type'] ?? 'Other';
        $locationTypes[] = $locationType;

        // Assign color based on location type
        $typeColor = isset($colorByType[$locationType]) ? $colorByType[$locationType] : $colorByType['Other'];

        // Create a slightly transparent version for the chart
        $backgroundColors[] = $typeColor;
    }
}

// Ensure we have at least some data for the chart
if (empty($locationLabels)) {
    $locationLabels = ['No Data'];
    $assetData = [0];
    $locationTypes = ['Other'];
    $backgroundColors = ['#e0e0e0'];
}

// Assets by category
$assetsByCategoryQuery = "
    SELECT c.category_name, COUNT(a.asset_id) as asset_count
    FROM categories c
    LEFT JOIN sku_master s ON c.category_id = s.category_id
    LEFT JOIN fixed_assets a ON s.sku_id = a.sku_id AND a.is_active = 1 AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
    GROUP BY c.category_id
    ORDER BY asset_count DESC";
$assetsByCategoryResult = mysqli_query($conn, $assetsByCategoryQuery);
$categoryLabels = [];
$categoryData = [];

// Check if query executed successfully
if ($assetsByCategoryResult) {
    while ($row = mysqli_fetch_assoc($assetsByCategoryResult)) {
        $categoryLabels[] = $row['category_name'];
        $categoryData[] = (int)$row['asset_count'];
    }
}

// Ensure we have at least some data for the chart
if (empty($categoryLabels)) {
    $categoryLabels = ['No Data'];
    $categoryData = [0];
}

// Get inventory levels by category for new chart
$inventoryCategoryQuery = "
    SELECT c.category_name, SUM(ci.current_quantity) as total_quantity
    FROM consumable_inventory ci
    JOIN sku_master s ON ci.sku_id = s.sku_id
    JOIN categories c ON s.category_id = c.category_id
    WHERE (ci.is_deleted = 0 OR ci.is_deleted IS NULL)
    GROUP BY c.category_id
    ORDER BY total_quantity DESC
    LIMIT 5
";
$inventoryCategoryResult = mysqli_query($conn, $inventoryCategoryQuery);
$inventoryCategoryLabels = [];
$inventoryCategoryData = [];

// Check if query executed successfully
if ($inventoryCategoryResult) {
    while ($row = mysqli_fetch_assoc($inventoryCategoryResult)) {
        $inventoryCategoryLabels[] = $row['category_name'];
        $inventoryCategoryData[] = (int)$row['total_quantity'];
    }
}

// Ensure we have at least some data for the chart
if (empty($inventoryCategoryLabels)) {
    $inventoryCategoryLabels = ['No Data'];
    $inventoryCategoryData = [0];
}

// Get latest transfer activities for activity feed
$recentTransfersQuery = "
    SELECT t.*,
           src.location_name as source_location_name,
           dst.location_name as destination_location_name,
           CASE WHEN t.asset_id IS NOT NULL THEN 'Fixed Asset' ELSE 'Consumable' END as item_type,
           u.full_name as user_name
    FROM transfers t
    JOIN locations src ON t.source_location_id = src.location_id
    JOIN locations dst ON t.destination_location_id = dst.location_id
    JOIN users u ON t.initiated_by = u.user_id
    ORDER BY t.transfer_date DESC
    LIMIT 5
";
$recentTransfersResult = mysqli_query($conn, $recentTransfersQuery);

// Transfer Status Chart Data
$transferStatusQuery = "
    SELECT status, COUNT(*) as count
    FROM transfers
    GROUP BY status
";
$transferStatusResult = mysqli_query($conn, $transferStatusQuery);

$statusLabels = [];
$statusData = [];
$colors = [];

$statusColors = [
    'Pending' => '#f6c23e',
    'Approved by Logistics' => '#36b9cc',
    'Approved by HIMU' => '#4e73df',
    'Completed' => '#1cc88a',
    'Rejected' => '#e74a3b'
];

if ($transferStatusResult && mysqli_num_rows($transferStatusResult) > 0) {
    while ($row = mysqli_fetch_assoc($transferStatusResult)) {
        $statusLabels[] = $row['status'];
        $statusData[] = (int)$row['count'];
        $colors[] = array_key_exists($row['status'], $statusColors) ? $statusColors[$row['status']] : '#858796';
    }
} else {
    // Add default data if no transfers exist
    $statusLabels = ['No Data'];
    $statusData = [1];
    $colors = ['#858796'];
}

// Get Batch Transfers pending approval
$pendingBatchesQuery = "
    SELECT bt.*,
           src.location_name as source_name,
           dst.location_name as destination_name,
           u.full_name as initiator_name
    FROM batch_transfers bt
    JOIN locations src ON bt.source_location_id = src.location_id
    JOIN locations dst ON bt.destination_location_id = dst.location_id
    JOIN users u ON bt.initiated_by = u.user_id
    WHERE bt.status = 'Pending'
    ORDER BY bt.transfer_date DESC
    LIMIT 5
";
$pendingBatchesResult = mysqli_query($conn, $pendingBatchesQuery);
?>

<style>
    /* Modern UI Dashboard Theme - Updated Color Scheme */
    :root {
        --primary: #2E7D32;
        --primary-light: #4CAF50;
        --primary-dark: #1B5E20;
        --primary-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        --primary-transparent: rgba(46, 125, 50, 0.1);

        --secondary: #3949AB;
        --secondary-light: #5C6BC0;
        --secondary-dark: #283593;
        --secondary-gradient: linear-gradient(135deg, #3949AB 0%, #5C6BC0 100%);

        --danger: #D32F2F;
        --danger-light: #FFCDD2;
        --danger-gradient: linear-gradient(135deg, #D32F2F 0%, #EF5350 100%);

        --warning: #F57C00;
        --warning-light: #FFE0B2;
        --warning-gradient: linear-gradient(135deg, #F57C00 0%, #FFB74D 100%);

        --info: #0288D1;
        --info-light: #B3E5FC;
        --info-gradient: linear-gradient(135deg, #0288D1 0%, #29B6F6 100%);

        --success: #388E3C;
        --success-light: #C8E6C9;
        --success-gradient: linear-gradient(135deg, #388E3C 0%, #66BB6A 100%);

        --dark: #263238;
        --text-primary: #37474F;
        --text-secondary: #546E7A;
        --text-muted: #78909C;

        --light: #ECEFF1;
        --border: #CFD8DC;
        --card-bg: #FFFFFF;

        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        --shadow-md: 0 8px 20px rgba(0, 0, 0, 0.12);
        --shadow-lg: 0 12px 28px rgba(0, 0, 0, 0.15);

        --border-radius: 0.75rem;
        --border-radius-lg: 1rem;
        --border-radius-sm: 0.5rem;

        --transition: all 0.3s ease;
    }

    .dashboard-page {
        background-color: var(--light);
        padding-top: 1.5rem;
        padding-bottom: 2rem;
        min-height: calc(100vh - 120px);
    }

    .dashboard-header {
        margin-bottom: 1rem;
        position: relative;
    }

    .dashboard-title {
        color: var(--dark);
        font-weight: 800;
        font-size: 1.75rem;
        letter-spacing: -0.025em;
        margin-bottom: 0.25rem;
        position: relative;
        display: inline-block;
    }

    .dashboard-title::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -6px;
        height: 4px;
        width: 60px;
        background: var(--primary-gradient);
        border-radius: 2px;
        box-shadow: 0 2px 4px rgba(46, 125, 50, 0.3);
    }

    .dashboard-subtitle {
        color: var(--text-secondary);
        font-size: 0.95rem;
        margin-top: 0.75rem;
        margin-bottom: 0;
        max-width: 600px;
    }

    .stats-card {
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        transition: var(--transition);
        margin-bottom: 1.5rem;
        border: none;
        background: var(--card-bg);
        box-shadow: var(--shadow);
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        opacity: 1;
        transition: height 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-md);
    }

    .stats-card:hover::before {
        height: 8px;
    }

    .stats-card .card-body {
        padding: 1.75rem;
        flex: 1;
    }

    .stats-card.primary-card::before {
        background: var(--primary-gradient);
    }

    .stats-card.info-card::before {
        background: var(--info-gradient);
    }

    .stats-card.danger-card::before {
        background: var(--danger-gradient);
    }

    .stats-card.warning-card::before {
        background: var(--warning-gradient);
    }

    .stats-icon {
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        margin-right: 1.25rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        transition: var(--transition);
    }

    .stats-icon::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0);
        border-radius: 50%;
        transition: transform 0.5s ease;
    }

    .stats-card:hover .stats-icon {
        transform: scale(1.05) rotate(5deg);
    }

    .stats-card:hover .stats-icon::after {
        transform: scale(2);
    }

    .stats-icon i {
        font-size: 1.75rem;
        position: relative;
        z-index: 1;
        filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
    }

    .icon-primary {
        background: var(--primary-gradient);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .icon-info {
        background: var(--info-gradient);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .icon-danger {
        background: var(--danger-gradient);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .icon-warning {
        background: var(--warning-gradient);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .stats-label {
        text-transform: uppercase;
        font-size: 0.8rem;
        font-weight: 700;
        letter-spacing: 0.05em;
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
    }

    .stats-value {
        font-size: 2.25rem;
        font-weight: 800;
        margin-bottom: 0.25rem;
        line-height: 1;
        letter-spacing: -0.025em;
    }

    .stats-desc {
        font-size: 0.85rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
    }

    .primary-text {
        color: var(--primary);
    }

    .info-text {
        color: var(--info);
    }

    .danger-text {
        color: var(--danger);
    }

    .warning-text {
        color: var(--warning);
    }

    .content-card {
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        border: none;
        box-shadow: var(--shadow);
        margin-bottom: 1.5rem;
        transition: var(--transition);
        background: var(--card-bg);
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .content-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 5px;
        height: 0%;
        background: var(--primary-gradient);
        transition: height 0.3s ease;
        border-radius: 0 0 4px 0;
    }

    .content-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-3px);
    }

    .content-card:hover::before {
        height: 100%;
    }

    .content-card .card-header {
        background: var(--card-bg);
        border-bottom: 1px solid var(--border);
        font-weight: 600;
        padding: 1.25rem 1.5rem;
        display: flex;
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .content-card .card-header h6 {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        color: var(--text-primary);
    }

    .content-card .card-header i {
        margin-right: 0.75rem;
        color: var(--primary);
        font-size: 1.25rem;
        filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
    }

    .content-card .card-body {
        padding: 1.5rem;
        flex: 1;
        position: relative;
        z-index: 1;
    }

    /* Card Footer */
    .stats-card .card-footer {
        background-color: var(--bg-white);
        border-top: 1px solid rgba(0,0,0,0.05);
        padding: 0.75rem 1.5rem;
        margin-top: auto;
    }

    .view-details {
        font-size: 0.85rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: var(--transition);
        text-decoration: none;
    }

    .view-details i {
        transition: var(--transition);
        font-size: 0.8rem;
        margin-left: 0.5rem;
    }

    .view-details:hover i {
        transform: translateX(3px);
    }

    .text-primary {
        color: var(--primary) !important;
    }

    .text-info {
        color: var(--info) !important;
    }

    .text-danger {
        color: var(--danger) !important;
    }

    .text-warning {
        color: var(--warning) !important;
    }

    .action-btn {
        border-radius: 50px;
        padding: 0.6rem 1.25rem;
        font-weight: 600;
        font-size: 0.875rem;
        transition: var(--transition);
        box-shadow: var(--shadow-sm);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: none;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.5s ease;
        z-index: -1;
    }

    .action-btn i {
        margin-right: 0.5rem;
        font-size: 0.875rem;
        transition: var(--transition);
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow);
    }

    .action-btn:hover::before {
        transform: scaleX(1);
        transform-origin: left;
    }

    .action-btn:hover i {
        transform: translateX(-2px);
    }

    .action-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .action-btn-primary:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
        color: white;
    }

    .action-btn-outline {
        background-color: transparent;
        border: 1px solid var(--border);
        color: var(--text-secondary);
    }

    .action-btn-outline:hover {
        background-color: var(--primary-transparent);
        color: var(--primary);
        border-color: var(--primary-light);
    }

    .action-btn-outline.active {
        background: var(--primary-gradient);
        color: white;
        border-color: transparent;
    }

    .table-responsive.dashboard-table {
        margin-top: 0.5rem;
    }

    .dashboard-table table {
        width: 100%;
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        border-radius: var(--border-radius-sm);
        overflow: hidden;
    }

    .dashboard-table thead th {
        background-color: rgba(46, 125, 50, 0.05);
        font-weight: 600;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        text-transform: uppercase;
        padding: 0.85rem 1rem;
        border-top: none;
        color: var(--text-secondary);
        border-bottom: 2px solid rgba(46, 125, 50, 0.1);
    }

    .dashboard-table thead th:first-child {
        border-top-left-radius: var(--border-radius-sm);
    }

    .dashboard-table thead th:last-child {
        border-top-right-radius: var(--border-radius-sm);
    }

    .dashboard-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid var(--border);
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .dashboard-table tbody tr:last-child td:first-child {
        border-bottom-left-radius: var(--border-radius-sm);
    }

    .dashboard-table tbody tr:last-child td:last-child {
        border-bottom-right-radius: var(--border-radius-sm);
    }

    .dashboard-table tbody tr {
        cursor: pointer;
        transition: var(--transition);
    }

    .dashboard-table tbody tr:hover {
        background-color: rgba(46, 125, 50, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
    }

    .transfer-link {
        color: var(--text-primary);
        font-weight: 500;
        text-decoration: none;
    }

    .transfer-id {
        font-weight: 600;
        color: var(--primary);
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .section-divider {
        height: 1px;
        background: linear-gradient(to right, transparent, var(--border), transparent);
        margin: 2rem 0;
        opacity: 0.6;
    }

    /* Badge styles */
    .status-badge {
        padding: 0.4rem 0.85rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.7rem;
        display: inline-flex;
        align-items: center;
        line-height: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        white-space: nowrap;
    }

    .status-badge::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 6px;
    }

    .badge-danger {
        background-color: var(--danger-light);
        color: var(--danger);
        border: 1px solid rgba(211, 47, 47, 0.2);
    }

    .badge-danger::before {
        background-color: var(--danger);
    }

    .badge-warning {
        background-color: var(--warning-light);
        color: var(--warning);
        border: 1px solid rgba(245, 124, 0, 0.2);
    }

    .badge-warning::before {
        background-color: var(--warning);
    }

    .badge-info {
        background-color: var(--info-light);
        color: var(--info);
        border: 1px solid rgba(2, 136, 209, 0.2);
    }

    .badge-info::before {
        background-color: var(--info);
    }

    .badge-success {
        background-color: var(--success-light);
        color: var(--success);
        border: 1px solid rgba(56, 142, 60, 0.2);
    }

    .badge-success::before {
        background-color: var(--success);
    }

    /* Activity list */
    .activity-list {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .activity-item {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--border);
        display: flex;
        align-items: flex-start;
        transition: var(--transition);
        position: relative;
    }

    .activity-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .activity-item:hover {
        background-color: rgba(46, 125, 50, 0.03);
        transform: translateX(3px);
    }

    .activity-item:hover::before {
        opacity: 1;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 42px;
        height: 42px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        background: var(--primary-gradient);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.2);
        transition: var(--transition);
    }

    .activity-item:hover .activity-icon {
        transform: scale(1.05) rotate(5deg);
    }

    .activity-content {
        flex-grow: 1;
    }

    .activity-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--text-primary);
        transition: var(--transition);
    }

    .activity-item:hover .activity-title {
        color: var(--primary);
    }

    .activity-time {
        color: var(--text-muted);
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        margin-top: 0.5rem;
    }

    .activity-time i {
        font-size: 0.875rem;
        margin-right: 0.375rem;
        opacity: 0.7;
        color: var(--primary);
    }

    /* Quick actions */
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.25rem;
        background-color: white;
        border: 1px solid var(--border);
        border-radius: 50px;
        transition: var(--transition);
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--text-secondary);
        box-shadow: var(--shadow-sm);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        z-index: -1;
        transition: opacity 0.3s ease;
    }

    .quick-action-btn:hover {
        color: white;
        border-color: transparent;
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }

    .quick-action-btn:hover::before {
        opacity: 1;
    }

    .quick-action-btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
        color: var(--primary);
        transition: var(--transition);
    }

    .quick-action-btn:hover i {
        color: white;
        transform: scale(1.1);
    }

    @media (max-width: 768px) {
        .quick-actions {
            margin-top: 1rem;
        }

        .dashboard-title {
            display: block;
            margin-bottom: 1rem;
        }
    }

    /* Remove underlines from all buttons and action links */
    .quick-action-btn,
    .action-btn,
    .action-btn-primary,
    .action-btn-outline,
    .dashboard-table a,
    .btn {
        text-decoration: none !important;
    }

    /* Ensure proper hover states without underlines */
    .dashboard-table a:hover {
        color: var(--primary);
        text-decoration: none !important;
    }

    /* Enhanced Sidebar Styling */
    .sidebar {
        background-color: #429e46;
        box-shadow: var(--shadow);
        border-right: none;
    }

    .sidebar .nav-item {
        margin-bottom: 0.25rem;
    }

    .sidebar .nav-link {
        border-radius: 0.5rem;
        margin: 0 0.75rem;
        padding: 0.75rem 1rem;
        color: rgba(255, 255, 255, 0.85);
        font-weight: 500;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
    }

    .sidebar .nav-link:hover,
    .sidebar .nav-link:focus,
    .sidebar .nav-link.active {
        background-color: rgba(255, 255, 255, 0.15);
        color: #ffffff;
    }

    .sidebar .nav-link.active {
        font-weight: 600;
    }

    .sidebar .nav-link i {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.7);
    }

    .sidebar .nav-link:hover i,
    .sidebar .nav-link:focus i,
    .sidebar .nav-link.active i {
        color: #ffffff;
    }

    .sidebar .nav-link.active i {
        background: linear-gradient(135deg, #347c38 0%, #56b75b 100%);
        color: white;
        border-radius: 0.375rem;
    }

    /* Fix for sidebar dropdown */
    .sidebar .collapse,
    .sidebar .collapsing {
        margin: 0 0.75rem;
    }

    .sidebar .collapse .nav-item .nav-link,
    .sidebar .collapsing .nav-item .nav-link {
        margin: 0.2rem 0;
        padding: 0.5rem 0.75rem 0.5rem 2.5rem;
        font-size: 0.85rem;
        position: relative;
    }

    .sidebar .collapse .nav-item .nav-link::before,
    .sidebar .collapsing .nav-item .nav-link::before {
        content: '';
        position: absolute;
        left: 1.25rem;
        top: 50%;
        width: 0.35rem;
        height: 0.35rem;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.6);
        transform: translateY(-50%);
        transition: all 0.2s ease;
    }

    .sidebar .collapse .nav-item .nav-link:hover::before,
    .sidebar .collapsing .nav-item .nav-link:hover::before,
    .sidebar .collapse .nav-item .nav-link.active::before,
    .sidebar .collapsing .nav-item .nav-link.active::before {
        background-color: #ffffff;
    }

    .sidebar .nav-item .nav-link[data-toggle="collapse"]::after {
        width: 1rem;
        text-align: center;
        float: right;
        vertical-align: 0;
        border: 0;
        font-weight: 900;
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        color: rgba(255, 255, 255, 0.6);
    }

    .sidebar .nav-item .nav-link[data-toggle="collapse"].collapsed::after {
        content: '\f105';
    }

    .sidebar-divider {
        border-top: 1px solid rgba(255, 255, 255, 0.15);
        margin: 1rem 0;
        opacity: 0.6;
    }

    .sidebar-heading {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
        font-weight: 600;
        letter-spacing: 0.05em;
        text-transform: uppercase;
        padding: 0 1rem;
        margin: 1rem 0 0.5rem;
    }

    .sidebar-brand {
        height: 4.5rem;
        display: flex;
        align-items: center;
        padding: 0 1.5rem;
        text-decoration: none !important;
    }

    .sidebar-brand-icon {
        margin-right: 0.75rem;
        color: #ffffff;
    }

    .sidebar-brand-text {
        font-weight: 700;
        color: white;
        font-size: 1.25rem;
    }

    /* Collapsed sidebar icon styling */
    .sidebar.toggled .nav-item .nav-link {
        text-align: center;
        padding: 0.75rem;
        margin: 0 0.5rem;
    }

    .sidebar.toggled .nav-item .nav-link i {
        margin: 0 auto;
        width: 2rem;
        height: 2rem;
        font-size: 1.1rem;
    }

    .sidebar.toggled .collapse {
        position: absolute;
        left: calc(6.5rem + 1.5rem / 2);
        z-index: 1;
        top: 2px;
        animation-name: fadeIn;
        animation-duration: 0.3s;
        animation-timing-function: ease;
        background: var(--card-bg);
        border-radius: var(--border-radius-sm);
        box-shadow: var(--shadow);
        margin: 0;
        min-width: 10rem;
    }

    .sidebar.toggled .collapse .nav-item .nav-link {
        padding: 0.5rem 1rem;
        margin: 0;
        width: 100%;
        text-align: left;
    }

    .sidebar.toggled .collapse .nav-item .nav-link::before {
        display: none;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(5px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<div class="container-fluid dashboard-page py-4">
    <!-- System Status Bar -->
    <div class="card mb-4 system-status-card">
        <div class="card-body py-2">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="status-indicator online"></div>
                    <span class="status-text">System Status: <strong>Online</strong></span>
                </div>
                <div class="d-flex align-items-center">
                    <div class="me-4">
                        <span class="status-label">Last Updated:</span>
                        <span class="status-value"><?php echo date('M d, Y h:i A'); ?></span>
                    </div>
                    <div>
                        <span class="status-label">Server Load:</span>
                        <span class="status-value">Normal</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <div class="dashboard-header">
            <h1 class="h2 dashboard-title mb-0">Superadmin Dashboard</h1>
            <p class="dashboard-subtitle">Welcome to the PCHIMS Superadmin Dashboard. Monitor system activity, manage inventory, and oversee all operations from this central control panel.</p>
        </div>
        <div class="quick-actions">
            <a href="/choims/modules/reports/assets.php" class="quick-action-btn">
                <i class="fas fa-laptop-medical"></i> Asset Reports
            </a>
            <a href="/choims/modules/reports/inventory.php" class="quick-action-btn">
                <i class="fas fa-box-open"></i> Inventory
            </a>
            <a href="/choims/modules/reports/transfers.php" class="quick-action-btn">
                <i class="fas fa-truck-loading"></i> Transfers
            </a>
            <a href="/choims/modules/reports/detailed_audit_logs.php" class="quick-action-btn">
                <i class="fas fa-file-alt"></i> Logs
            </a>
            <a href="/choims/modules/inventory/stock_logs.php" class="quick-action-btn">
                <i class="fas fa-exchange-alt"></i> Stock Logs
            </a>
        </div>
    </div>

    <style>
        .system-status-card {
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border-left: 4px solid var(--success);
            background-color: #FAFAFA;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.online {
            background-color: var(--success);
            box-shadow: 0 0 0 3px rgba(56, 142, 60, 0.2);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(56, 142, 60, 0.4); }
            70% { box-shadow: 0 0 0 6px rgba(56, 142, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(56, 142, 60, 0); }
        }

        .status-text {
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .status-label {
            font-size: 0.8rem;
            color: var(--text-muted);
            margin-right: 5px;
        }

        .status-value {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-primary);
        }
    </style>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card primary-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-primary">
                            <i class="fas fa-laptop-medical"></i>
                        </div>
                        <div>
                            <div class="stats-label">Fixed Assets</div>
                            <div class="stats-value primary-text"><?php echo $assetsCount; ?></div>
                            <div class="stats-desc">Total equipment in system</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/assets/list.php" class="view-details text-primary">
                        <span>View all assets</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card info-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-info">
                            <i class="fas fa-box-open"></i>
                        </div>
                        <div>
                            <div class="stats-label">Consumable Items</div>
                            <div class="stats-value info-text"><?php echo $inventoryCount; ?></div>
                            <div class="stats-desc">Total consumables in inventory</div>
                            <div class="mt-2 text-success fw-bold">
                                <i class="fas fa-cubes me-1"></i> Total Qty: <?php echo number_format($totalConsumableQty); ?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php" class="view-details text-info">
                        <span>View all consumables</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card danger-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-danger">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div>
                            <div class="stats-label">Low Stock Items</div>
                            <div class="stats-value danger-text"><?php echo $lowStockCount; ?></div>
                            <div class="stats-desc">Items requiring attention</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/inventory/list.php?status=Low%20Stock" class="view-details text-danger">
                        <span>View low stock items</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card warning-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon icon-warning">
                            <i class="fas fa-dolly-flatbed"></i>
                        </div>
                        <div>
                            <div class="stats-label">Pending Transfers</div>
                            <div class="stats-value warning-text"><?php echo $pendingTransfersCount; ?></div>
                            <div class="stats-desc">Transfers awaiting approval</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="/choims/modules/transfers/list.php?status=Pending" class="view-details text-warning">
                        <span>View pending transfers</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Low Inventory Alerts -->
        <div class="col-xl-4 col-lg-6">
            <div class="card content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h6 class="m-0">Low Inventory Alerts</h6>
                    </div>
                    <a href="/choims/modules/inventory/list.php?status=Low%20Stock" class="action-btn action-btn-outline">
                        <i class="fas fa-eye"></i> View All
                    </a>
                </div>
                <div class="card-body">
                    <?php if (mysqli_num_rows($lowStockItemsResult) > 0): ?>
                        <div class="table-responsive dashboard-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while($item = mysqli_fetch_assoc($lowStockItemsResult)): ?>
                                        <tr>
                                            <td><?php echo $item['sku_code'] . ' - ' . $item['sku_name']; ?></td>
                                            <td><?php echo $item['location_name']; ?></td>
                                            <td>
                                                <span class="status-badge <?php echo ($item['status'] == 'Out of Stock') ? 'badge-danger' : 'badge-warning'; ?>">
                                                    <?php echo $item['status']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $item['current_quantity']; ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i> All inventory items are at adequate levels.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Recent Activity Feed -->
        <div class="col-xl-8 col-lg-6">
            <div class="card content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-history"></i>
                        <h6 class="m-0">Recent Activity</h6>
                    </div>
                    <a href="/choims/modules/reports/detailed_audit_logs.php" class="action-btn action-btn-outline">
                        <i class="fas fa-list"></i> View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <ul class="activity-list">
                        <?php while($activity = mysqli_fetch_assoc($activityResult)):
                            // Determine icon and color based on action
                            $icon = 'fa-info-circle';
                            $bgClass = 'bg-info';

                            switch($activity['action']) {
                                case 'create':
                                    $icon = 'fa-plus';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'update':
                                    $icon = 'fa-pen';
                                    $bgClass = 'bg-info';
                                    break;
                                case 'delete':
                                    $icon = 'fa-trash';
                                    $bgClass = 'bg-danger';
                                    break;
                                case 'login':
                                    $icon = 'fa-sign-in-alt';
                                    $bgClass = 'bg-primary';
                                    break;
                                case 'logout':
                                    $icon = 'fa-sign-out-alt';
                                    $bgClass = 'bg-secondary';
                                    break;
                                case 'transfer_initiate':
                                    $icon = 'fa-exchange-alt';
                                    $bgClass = 'bg-warning';
                                    break;
                                case 'transfer_approve':
                                    $icon = 'fa-check';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'transfer_reject':
                                    $icon = 'fa-times';
                                    $bgClass = 'bg-danger';
                                    break;
                                case 'transfer_complete':
                                    $icon = 'fa-check-double';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'stock_in':
                                    $icon = 'fa-arrow-down';
                                    $bgClass = 'bg-success';
                                    break;
                                case 'stock_out':
                                    $icon = 'fa-arrow-up';
                                    $bgClass = 'bg-danger';
                                    break;
                                case 'batch_transfer':
                                    $icon = 'fa-boxes';
                                    $bgClass = 'bg-warning';
                                    break;
                            }

                            // Format timestamp
                            $timestamp = strtotime($activity['log_time']);
                            $timeAgo = timeAgo($timestamp);
                        ?>
                            <li class="activity-item">
                                <div class="activity-icon <?php echo $bgClass; ?> text-white">
                                    <i class="fas <?php echo $icon; ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">
                                        <?php echo $activity['full_name'] ? $activity['full_name'] : 'System'; ?>
                                    </div>
                                    <div class="text-muted small">
                                        <?php
                                            // Display the changes summary from the detailed audit log
                                            if (!empty($activity['changes_summary'])) {
                                                echo $activity['changes_summary'];
                                            } else {
                                                echo $activity['action'] . ' ' . $activity['entity_type'];

                                                // Try to display a more descriptive message based on the action and entity
                                                $details = '';
                                                if (!empty($activity['details'])) {
                                                    $detailsObj = json_decode($activity['details'], true);
                                                    if ($detailsObj && isset($detailsObj['entity_name'])) {
                                                        $details = ': ' . $detailsObj['entity_name'];
                                                    } else if ($detailsObj && isset($detailsObj['name'])) {
                                                        $details = ': ' . $detailsObj['name'];
                                                    } else if ($detailsObj && isset($detailsObj['sku_name'])) {
                                                        $details = ': ' . $detailsObj['sku_name'];
                                                    } else if ($detailsObj && isset($detailsObj['asset_name'])) {
                                                        $details = ': ' . $detailsObj['asset_name'];
                                                    }
                                                    echo $details;
                                                }
                                            }
                                        ?>
                                    </div>
                                    <div class="activity-time"><i class="far fa-clock"></i> <?php echo $timeAgo; ?></div>
                                </div>
                            </li>
                        <?php endwhile; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Assets by Location Chart -->
        <div class="col-xl-7">
            <div class="card content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-map-marker-alt"></i>
                        <h6 class="m-0">Assets by Location</h6>
                    </div>
                    <div class="btn-group">
                        <button type="button" id="viewBarChart" class="btn btn-sm action-btn action-btn-outline active">Bar</button>
                        <button type="button" id="viewTreemap" class="btn btn-sm action-btn action-btn-outline">Treemap</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="locationChart"></canvas>
                        <div id="locationTreemap" style="height: 300px; display: none;"></div>
                    </div>
                    <div class="mt-3">
                        <div class="d-flex flex-wrap justify-content-center">
                            <?php foreach (array_unique($locationTypes) as $type): ?>
                                <?php if (!empty($type)): ?>
                                    <div class="legend-item mx-2">
                                        <span class="color-dot" style="background-color: <?php echo $colorByType[$type] ?? '#e0e0e0'; ?>"></span>
                                        <?php echo $type; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory by Category Chart -->
        <div class="col-xl-5">
            <div class="card content-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-chart-pie"></i>
                        <h6 class="m-0">Inventory by Category</h6>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="inventoryCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section-divider"></div>

    <div class="row">
        <!-- Recent Transfers -->
        <div class="col-xl-12">
            <div class="card content-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exchange-alt"></i>
                        <h6 class="m-0">Recent Transfers</h6>
                    </div>
                    <a href="/choims/modules/reports/transfers.php" class="action-btn action-btn-primary">
                        <i class="fas fa-list"></i> View All Transfers
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive dashboard-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Status</th>
                                    <th>Initiated By</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (mysqli_num_rows($recentTransfersResult) > 0):
                                    while($transfer = mysqli_fetch_assoc($recentTransfersResult)):
                                        $statusClass = '';
                                        switch($transfer['status']) {
                                            case 'Pending':
                                                $statusClass = 'badge-warning';
                                                break;
                                            case 'Approved by Logistics':
                                            case 'Approved by HIMU':
                                                $statusClass = 'badge-info';
                                                break;
                                            case 'Completed':
                                                $statusClass = 'badge-success';
                                                break;
                                            case 'Rejected':
                                                $statusClass = 'badge-danger';
                                                break;
                                        }
                                        $transferLink = "/choims/modules/transfers/view.php?id=" . $transfer['transfer_id'];
                                    ?>
                                        <tr onclick="window.location='<?php echo $transferLink; ?>'">
                                            <td><span class="transfer-id">#<?php echo $transfer['transfer_id']; ?></span></td>
                                            <td><?php echo date('M d, Y', strtotime($transfer['transfer_date'])); ?></td>
                                            <td><?php echo $transfer['item_type']; ?></td>
                                            <td><?php echo $transfer['source_location_name']; ?></td>
                                            <td><?php echo $transfer['destination_location_name']; ?></td>
                                            <td><span class="status-badge <?php echo $statusClass; ?>"><?php echo $transfer['status']; ?></span></td>
                                            <td><?php echo $transfer['user_name']; ?></td>
                                        </tr>
                                <?php
                                    endwhile;
                                else:
                                ?>
                                <tr>
                                    <td colspan="7" class="text-center">No recent transfers found.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>



</div>

<!-- Load ECharts for Treemap visualization -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>

<script>
// Update charts with new color palette
document.addEventListener('DOMContentLoaded', function() {
    // Configure chart colors
    const modernPalette = [
        '#00ADB5', '#3FC1D0', '#3B82F6', '#6366F1', '#8B5CF6',
        '#F43F5E', '#FB7185', '#F59E0B', '#34D399', '#06B6D4'
    ];

    let locationChart;

    // Function to render the bar chart
    function renderBarChart() {
        const locationCtx = document.getElementById('locationChart').getContext('2d');

        // Destroy existing chart if it exists
        if (locationChart) {
            locationChart.destroy();
        }

        // Group data by location type for better visualization
        const locationLabelsArray = <?php echo json_encode($locationLabels); ?>;
        const assetDataArray = <?php echo json_encode($assetData); ?>;
        const locationTypesArray = <?php echo json_encode($locationTypes); ?>;
        const backgroundColorsArray = <?php echo json_encode($backgroundColors); ?>;

        // Create the bar chart
        locationChart = new Chart(locationCtx, {
            type: 'bar',
            data: {
                labels: locationLabelsArray,
                datasets: [{
                    label: 'Number of Assets',
                    data: assetDataArray,
                    backgroundColor: backgroundColorsArray,
                    borderColor: 'rgba(255, 255, 255, 0.8)',
                    borderWidth: 1,
                    borderRadius: 4,
                    maxBarThickness: 40
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',  // Make it a horizontal bar chart for better readability with many locations
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            precision: 0
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        padding: 12,
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 14
                        },
                        callbacks: {
                            label: function(context) {
                                return `Assets: ${context.raw}`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Function to render the treemap
    function renderTreemap() {
        // Hide the bar chart canvas, show the treemap div
        document.getElementById('locationChart').style.display = 'none';
        document.getElementById('locationTreemap').style.display = 'block';

        // Prepare data for the treemap
        const locationLabelsArray = <?php echo json_encode($locationLabels); ?>;
        const assetDataArray = <?php echo json_encode($assetData); ?>;
        const locationTypesArray = <?php echo json_encode($locationTypes); ?>;
        const backgroundColorsArray = <?php echo json_encode($backgroundColors); ?>;
        const colorByType = <?php echo json_encode($colorByType); ?>;

        // Group data by location type
        const groupedData = [];
        const typeData = {};

        // First organize by types
        for (let i = 0; i < locationLabelsArray.length; i++) {
            const type = locationTypesArray[i] || 'Other';
            const location = locationLabelsArray[i];
            const value = assetDataArray[i];
            const color = backgroundColorsArray[i];

            if (!typeData[type]) {
                typeData[type] = {
                    name: type,
                    value: 0,
                    children: [],
                    itemStyle: {
                        color: colorByType[type] || '#e0e0e0'
                    }
                };
                groupedData.push(typeData[type]);
            }

            // Add location to its type
            typeData[type].children.push({
                name: location,
                value: value,
                itemStyle: {
                    color: color
                }
            });

            // Sum up the values for the type
            typeData[type].value += value;
        }

        // Sort types by total value
        groupedData.sort((a, b) => b.value - a.value);

        // Sort locations within each type by value
        groupedData.forEach(type => {
            type.children.sort((a, b) => b.value - a.value);
        });

        // Initialize the treemap chart
        const treemapChart = echarts.init(document.getElementById('locationTreemap'));

        // Set up treemap options
        const option = {
            tooltip: {
                formatter: function(info) {
                    // Show the full path and value in tooltip
                    const value = info.value;
                    const name = info.name;
                    if (info.treePathInfo.length > 1) {
                        const parentName = info.treePathInfo[0].name;
                        return `<div style="font-weight:bold">${name}</div>` +
                               `<div>Type: ${parentName}</div>` +
                               `<div>Assets: ${value}</div>`;
                    } else {
                        return `<div style="font-weight:bold">${name}</div>` +
                               `<div>Total Assets: ${value}</div>`;
                    }
                }
            },
            series: [{
                type: 'treemap',
                data: groupedData,
                width: '100%',
                height: '100%',
                roam: false,
                nodeClick: false, // Disable node clicking
                breadcrumb: {
                    show: false
                },
                label: {
                    show: true,
                    formatter: '{b}: {c}',
                    position: 'inside',
                    fontSize: 12,
                    color: '#fff',
                    fontWeight: 'bold'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                    gapWidth: 2
                },
                upperLabel: {
                    show: true,
                    height: 30,
                    color: '#000',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    fontSize: 12
                },
                levels: [
                    {
                        // Level 1 (Type level)
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 3,
                            gapWidth: 3
                        },
                        upperLabel: {
                            show: true
                        }
                    },
                    {
                        // Level 2 (Location level)
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 1,
                            gapWidth: 1
                        }
                    }
                ]
            }]
        };

        // Apply options to the chart
        treemapChart.setOption(option);

        // Handle window resize
        window.addEventListener('resize', function() {
            treemapChart.resize();
        });
    }

    // Update inventory category chart
    const inventoryCategoryCtx = document.getElementById('inventoryCategoryChart').getContext('2d');
    const inventoryCategoryChart = new Chart(inventoryCategoryCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode($inventoryCategoryLabels); ?>,
            datasets: [{
                data: <?php echo json_encode($inventoryCategoryData); ?>,
                backgroundColor: modernPalette,
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    padding: 12,
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 14
                    }
                }
            }
        }
    });

    // Initial render of the bar chart
    renderBarChart();

    // Toggle between bar chart and treemap
    document.getElementById('viewBarChart').addEventListener('click', function() {
        document.getElementById('locationChart').style.display = 'block';
        document.getElementById('locationTreemap').style.display = 'none';
        document.getElementById('viewBarChart').classList.add('active');
        document.getElementById('viewTreemap').classList.remove('active');
        renderBarChart();
    });

    document.getElementById('viewTreemap').addEventListener('click', function() {
        document.getElementById('locationChart').style.display = 'none';
        document.getElementById('locationTreemap').style.display = 'block';
        document.getElementById('viewBarChart').classList.remove('active');
        document.getElementById('viewTreemap').classList.add('active');
        renderTreemap();
    });
});
</script>

<?php
function timeAgo($timestamp) {
    $current_time = time();
    $diff = $current_time - $timestamp;

    // Handle invalid timestamps or future times
    if ($diff < 0) {
        // Log the time discrepancy for debugging
        error_log("Time discrepancy detected: timestamp ($timestamp) is in the future compared to current time ($current_time)");
        $diff = 0; // Treat as "just now" to avoid negative time
    }

    // Just now (less than a minute)
    if ($diff < 60) {
        return "Just now";
    }

    // Minutes
    $diff = floor($diff / 60);
    if ($diff < 60) {
        return $diff . " minute" . ($diff != 1 ? "s" : "") . " ago";
    }

    // Hours
    $diff = floor($diff / 60);
    if ($diff < 24) {
        return $diff . " hour" . ($diff != 1 ? "s" : "") . " ago";
    }

    // Days
    $diff = floor($diff / 24);
    if ($diff < 7) {
        return $diff . " day" . ($diff != 1 ? "s" : "") . " ago";
    }

    // Switch to date format after a week
    return date('M j, Y', $timestamp);
}

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>