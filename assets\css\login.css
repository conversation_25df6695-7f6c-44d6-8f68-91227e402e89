/* Login Page Specific Styling - Professional Edition */
:root {
  --primary-dark: #1a5d1a;  
  --primary: #39913a;
  --primary-light: #86c086;
  --accent: #ffc857;
  --gray-dark: #333;
  --gray: #6c757d;
  --gray-light: #e9ecef;
  --success: #38b000;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

body {
  background: #f8faf8;
  font-family: 'Poppins', sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.login-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  background: white;
  box-shadow: var(--box-shadow);
  border: none;
  transform: translateY(0);
  transition: all 0.5s ease;
}

.login-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.login-header {
  padding: 2.5rem 1.5rem 1.5rem;
  position: relative;
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
  color: white;
  text-align: center;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}

.login-header:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 90% 10%, rgba(255, 255, 255, 0.12) 0%, transparent 40%);
  opacity: 0.6;
}

.login-header h2 {
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-header p {
  font-size: 0.95rem;
  opacity: 0.95;
}

.logo-container {
  width: 90px;
  height: 90px;
  margin: 0 auto 1.25rem;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.logo-container img {
  width: 70px;
  height: auto;
}

.login-body {
  padding: 2.5rem 2rem;
}

.form-group {
  margin-bottom: 1.75rem;
  position: relative;
}

.form-label {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--gray-dark);
  margin-bottom: 0.5rem;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.input-group {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.input-group:focus-within {
  box-shadow: 0 2px 10px rgba(57, 145, 58, 0.2);
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  z-index: 10;
}

.form-control {
  height: 56px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0.6rem 1rem 0.6rem 3rem;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: none;
  background-color: white;
}

.form-control::placeholder {
  color: #adb5bd;
  font-size: 0.95rem;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s;
}

.password-toggle:hover {
  color: var(--primary);
}

.btn-login {
  height: 56px;
  border-radius: 8px;
  border: none;
  background: linear-gradient(to right, var(--primary-dark), var(--primary));
  color: white;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(57, 145, 58, 0.3);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-login:before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: all 0.8s;
}

.btn-login:hover:before {
  left: 100%;
}

.btn-login:hover {
  background: linear-gradient(to right, var(--primary), var(--primary-dark));
  box-shadow: 0 6px 15px rgba(57, 145, 58, 0.4);
  transform: translateY(-2px);
}

.btn-login:active {
  transform: translateY(0);
  box-shadow: 0 4px 8px rgba(57, 145, 58, 0.25);
}

.login-footer {
  padding: 1.25rem;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  color: var(--gray);
  font-size: 0.85rem;
  text-align: center;
  border-radius: 0 0 16px 16px;
}

.alert {
  border-radius: 8px;
  border: none;
  padding: 1rem 1.25rem;
  margin-bottom: 1.75rem;
  position: relative;
  overflow: hidden;
}

.alert-danger {
  background-color: #fff5f5;
  border-left: 4px solid #dc3545;
  color: #dc3545;
}

.alert-icon {
  margin-right: 0.75rem;
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease forwards;
}

/* Custom focus styles */
.position-relative.focused .input-icon {
  color: var(--primary-dark);
}

/* Custom media queries */
@media (max-width: 576px) {
  .login-body {
    padding: 2rem 1.25rem;
  }
  
  .login-wrapper {
    margin: 1rem;
  }
}