<style>
    /* Modern sidebar theme with improved styles */
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    .sidebar {
        background: linear-gradient(180deg, #0A2E10 0%, #1B5E20 100%);
        min-height: 100vh;
        transition: width 0.3s ease, margin 0.3s ease; /* Optimize transitions */
        width: 280px;
        box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
        overflow-y: auto;
        z-index: 1000;
        position: fixed;
        left: 0;
        height: 100vh;
        will-change: width; /* Performance hint for browser */
        /* Hide scrollbar but keep functionality */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .sidebar::-webkit-scrollbar {
        display: none;
    }

    /* Logo and user info section */
    .sidebar-header {
        background: rgba(0, 0, 0, 0.2);
        padding: 1.5rem 1.5rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 0.5rem;
        position: relative;
    }

    .app-brand {
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
        margin-bottom: 1.25rem;
        margin-top: 0.5rem;
        padding: 0.5rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .app-brand:hover {
        transform: translateY(-2px);
        text-decoration: none;
    }

    .app-brand-logo {
        background: rgba(255, 255, 255, 0.1);
        width: 42px;
        height: 42px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 1.2rem;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }

    .app-brand-text {
        font-size: 1.6rem;
        font-weight: 700;
        letter-spacing: 0.5px;
        text-align: center;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        margin-bottom: 0;
    }

    .app-brand-subtext {
        font-size: 0.7rem;
        color: rgba(255, 255, 255, 0.7);
        text-align: center;
        display: block;
        letter-spacing: 0.5px;
        margin-top: 2px;
        line-height: 1;
    }

    /* Toggle button for sidebar */
    .sidebar-toggle {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: none;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        position: absolute;
        top: 1.25rem;
        right: 1rem;
        z-index: 1001;
    }

    .sidebar-toggle:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }

    /* Collapsed sidebar state */
    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar.collapsed .app-brand-text,
    .sidebar.collapsed .app-brand-subtext,
    .sidebar.collapsed .user-info,
    .sidebar.collapsed .nav-link span,
    .sidebar.collapsed .nav-section-label,
    .sidebar.collapsed .sidebar-footer,
    .sidebar.collapsed .user-profile .user-name,
    .sidebar.collapsed .user-profile .user-role {
        display: none !important;
    }

    /* Hide all text nodes in nav links when collapsed */
    .sidebar.collapsed .nav-link {
        text-align: center;
        padding: 0.8rem 0;
        font-size: 0;
        margin: 0.15rem auto;
        height: 40px;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sidebar.collapsed .nav-link i {
        margin: 0 !important;
        font-size: 1.05rem; /* Restore font size for icons */
        text-align: center;
    }

    /* Also hide badge text but keep badge visible */
    .sidebar.collapsed .badge {
        font-size: 0;
        padding: 0.35em;
        min-width: 10px;
        min-height: 10px;
        position: absolute;
        top: 5px;
        right: 5px;
    }

    /* Make sure the PCHIMS text and subtext are hidden when collapsed */
    .sidebar.collapsed .app-brand-text,
    .sidebar.collapsed .app-brand-subtext {
        display: none !important;
    }

    /* Add transition to navbar toggler to match sidebar animations */
    .navbar-toggler {
        transition: all 0.3s;
    }

    /* Adjust the toggle button when sidebar is collapsed */
    .sidebar.collapsed .sidebar-toggle {
        background: #1B5E20;
        z-index: 1001;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.25);
        top: 1rem;
        width: 36px;
        height: 36px;
        left: 17px;
    }

    .sidebar.collapsed ~ .main-content {
        margin-left: 70px;
        width: calc(100% - 70px);
    }

    .sidebar.collapsed .sidebar-content {
        padding: 0;
    }

    /* Ensure submenus show text properly */
    .sidebar.collapsed .collapse .nav-link {
        font-size: 0.85rem; /* Restore font size for submenu items */
    }

    /* Extra styles for collapsed state */
    .sidebar.collapsed .app-brand {
        justify-content: center;
        margin-bottom: 0.75rem;
    }

    .sidebar.collapsed .app-brand-logo {
        margin-right: 0;
        margin: 0 auto;
    }

    .sidebar.collapsed .user-avatar {
        margin: 0 auto;
    }

    .sidebar.collapsed .user-profile {
        justify-content: center;
        padding: 0.5rem 0;
    }

    .sidebar.collapsed .sidebar-header {
        padding: 1rem 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .sidebar.collapsed .sidebar-content .nav-item {
        display: flex;
        justify-content: center;
    }

    .sidebar.collapsed .nav-link {
        padding: 0.8rem 0;
        margin: 0.15rem auto;
        height: 40px;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .sidebar.collapsed .sidebar-toggle {
        top: 1rem;
        right: 17px;
        width: 36px;
        height: 36px;
        background: #1B5E20;
        z-index: 1001;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.25);
    }

    .sidebar.collapsed .nav-item .fa-chevron-down {
        display: none;
    }

    .sidebar.collapsed .collapse {
        position: absolute;
        left: 70px;
        top: 0;
        width: 200px;
        background: #1B5E20;
        border-radius: 0 4px 4px 0;
        box-shadow: 3px 0 10px rgba(0, 0, 0, 0.2);
        z-index: 1000;
    }

    /* Ensure submenus are properly visible on mobile */
    @media (max-width: 991.98px) {
        .sidebar .collapse {
            position: static;
            width: 100%;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .sidebar .collapse .nav-link {
            padding-left: 2.5rem;
            font-size: 0.9rem;
            padding-top: 0.7rem;
            padding-bottom: 0.7rem;
        }

        /* Make submenu items more touch-friendly */
        .sidebar .collapse .nav-item {
            margin-bottom: 2px;
        }

        /* Improve visibility of active submenu items */
        .sidebar .collapse .nav-link.active {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
    }

    .sidebar.collapsed .collapse .nav-link {
        text-align: left;
        padding-left: 1rem;
        font-size: 0.85rem;
        white-space: nowrap;
    }

    .sidebar.collapsed .collapse .nav-link::after {
        left: 0.25rem;
    }

    .sidebar.collapsed .nav-section-divider {
        margin: 0.5rem 10px;
    }

    .user-profile {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
    }

    .user-avatar {
        width: 42px;
        height: 42px;
        background: linear-gradient(45deg, #43A047, #66BB6A);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1.2rem;
        margin-right: 0.75rem;
        position: relative;
    }

    .user-avatar.online::after {
        content: '';
        position: absolute;
        right: -3px;
        bottom: -3px;
        width: 12px;
        height: 12px;
        background-color: #4CAF50;
        border: 2px solid #0A2E10;
        border-radius: 50%;
    }

    .user-info {
        flex: 1;
        min-width: 0;
    }

    .user-name {
        color: white;
        font-weight: 600;
        font-size: 0.95rem;
        margin: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .user-role {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin: 0;
    }

    /* Navigation */
    .sidebar-content {
        padding: 0 0.75rem;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .sidebar-content::-webkit-scrollbar {
        display: none;
    }

    .nav-section-label {
        font-size: 0.7rem;
        text-transform: uppercase;
        color: rgba(255, 255, 255, 0.5);
        padding: 1.25rem 1rem 0.5rem;
        letter-spacing: 1.5px;
        font-weight: 600;
    }

    .nav-section-divider {
        height: 1px;
        background: linear-gradient(to right, rgba(255,255,255,0.05), rgba(255,255,255,0.15), rgba(255,255,255,0.05));
        margin: 1rem 0;
    }

    .sidebar .nav-item {
        margin-bottom: 0.35rem;
    }

    .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.75);
        padding: 0.9rem 1.2rem;
        border-radius: 10px;
        margin: 0.2rem 0;
        transition: all 0.3s;
        font-weight: 500;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .sidebar .nav-link::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0.1), transparent);
        transition: width 0.3s ease;
        z-index: -1;
    }

    .sidebar .nav-link:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.07);
        transform: translateX(5px);
    }

    .sidebar .nav-link:hover::before {
        width: 100%;
    }

    .sidebar .nav-link.active {
        background: linear-gradient(90deg, #43A047, #2E7D32);
        color: white;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    }

    .sidebar .nav-link i.fas,
    .sidebar .nav-link i.far {
        width: 22px;
        text-align: center;
        margin-right: 10px;
        font-size: 1.05rem;
    }

    .fa-chevron-down {
        transition: transform 0.3s;
        font-size: 0.7rem;
        opacity: 0.8;
        margin-left: auto !important;
    }

    [aria-expanded="true"] .fa-chevron-down {
        transform: rotate(180deg);
    }

    .sidebar .collapse {
        transition: all 0.3s;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .sidebar .collapse::-webkit-scrollbar {
        display: none;
    }

    .sidebar .nav-link[aria-expanded="true"] {
        background-color: rgba(255, 255, 255, 0.07);
    }

    /* Submenu styles */
    .sidebar .collapse .nav-link {
        padding: 0.7rem 1rem 0.7rem 2.7rem;
        font-size: 0.85rem;
        border-radius: 8px;
        position: relative;
        margin-left: 0.75rem;
        margin-top: 0.1rem;
        margin-bottom: 0.1rem;
    }

    .sidebar .collapse .nav-link::after {
        content: '';
        position: absolute;
        left: 1.25rem;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 5px;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
    }

    .sidebar .collapse .nav-link:hover::after,
    .sidebar .collapse .nav-link.active::after {
        background-color: #8BC34A;
    }

    /* Badge */
    .sidebar .badge {
        padding: 0.35em 0.65em;
        font-size: 0.65rem;
        font-weight: 600;
        border-radius: 30px;
        margin-left: 0.5rem;
    }

    .badge-new {
        background-color: #FFC107;
        color: #1B5E20;
    }

    /* Logout button */
    .sidebar-footer {
        padding: 1rem;
        margin-top: 1rem;
    }

    .btn-logout {
        background: linear-gradient(45deg, #F44336, #E53935);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-weight: 600;
        text-align: center;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-logout:hover {
        background: linear-gradient(45deg, #E53935, #C62828);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        color: white;
    }

    .btn-logout i {
        margin-right: 0.5rem;
    }

    /* Collapsed state for responsive design */
    @media (max-width: 991.98px) {
        .sidebar {
            transform: translateX(-100%);
            visibility: hidden; /* Hide sidebar when not shown */
        }

        .sidebar.show {
            transform: translateX(0);
            visibility: visible; /* Make sidebar visible when shown */
        }

        /* Ensure submenu toggles are more visible on mobile */
        .sidebar .nav-link[data-bs-toggle="collapse"] {
            position: relative;
        }

        .sidebar .nav-link[data-bs-toggle="collapse"] .fa-chevron-down {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.4rem;
            border-radius: 50%;
            font-size: 0.7rem;
        }

        /* Ensure collapsed sidebar is also properly hidden on mobile */
        .sidebar.collapsed {
            transform: translateX(-100%);
            width: 70px;
        }

        .sidebar.collapsed.show {
            transform: translateX(0);
        }

        /* When collapsed and shown on mobile, adjust main content */
        .sidebar.collapsed.show ~ .main-content {
            margin-left: 0;
            width: 100%;
        }

        /* Add overlay when sidebar is shown */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }

        body.sidebar-open::after {
            opacity: 1;
            visibility: visible;
        }

        /* Make main content take full width */
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
    }

    /* Main content adjustment */
    .main-content {
        margin-left: 280px;
        width: calc(100% - 280px);
        transition: margin-left 0.3s ease, width 0.3s ease; /* Specific transitions */
        will-change: margin-left, width; /* Performance hint */
    }

    /* Adjust main content when sidebar is collapsed */
    .sidebar.collapsed + .main-content,
    .sidebar.collapsed ~ .main-content {
        margin-left: 80px;
        width: calc(100% - 80px);
    }

    /* Fix for badge positioning in collapsed mode */
    .sidebar.collapsed .badge-new {
        position: absolute;
        right: 5px;
        top: 5px;
        min-width: 8px;
        min-height: 8px;
    }
</style>

<div class="sidebar-header">
    <!-- Toggle Button for Sidebar - Now in the header with hamburger menu -->
    <button type="button" class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars" aria-hidden="true"></i>
    </button>

    <a href="/choims/index.php" class="app-brand">
        <div style="display: flex; flex-direction: column; align-items: center;">
            <span class="app-brand-text">PCHIMS</span>
            <span class="app-brand-subtext">Inventory Management System</span>
        </div>
    </a>

    <div class="user-profile">
        <div class="user-avatar online">
            <?php
                $initials = '';
                if (isset($_SESSION['full_name']) && !empty($_SESSION['full_name'])) {
                    $name_parts = explode(' ', $_SESSION['full_name']);
                    foreach ($name_parts as $part) {
                        $initials .= $part[0];
                    }
                    $initials = substr($initials, 0, 2);
                } else if (isset($_SESSION['username'])) {
                    $initials = substr($_SESSION['username'], 0, 2);
                }
                echo strtoupper($initials);
            ?>
        </div>
        <div class="user-info">
            <h5 class="user-name"><?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : $_SESSION['username']; ?></h5>
            <p class="user-role"><?php echo $_SESSION['role']; ?></p>
        </div>
    </div>
</div>

<div class="sidebar-content">
    <ul class="nav flex-column">
        <?php
        $userRole = strtolower($_SESSION['role']);
        ?>

        <div class="nav-section-label">OVERVIEW</div>

        <?php if ($userRole == 'godmode' || $userRole == 'superadmin'): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'superadmin.php' ? 'active' : ''; ?>" href="/choims/dashboards/superadmin.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>
        <?php elseif ($userRole == 'logistics'): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'logistics.php' ? 'active' : ''; ?>" href="/choims/dashboards/logistics.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>
        <?php elseif ($userRole == 'himu'): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'himu.php' ? 'active' : ''; ?>" href="/choims/dashboards/himu.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>

        <!-- Reports & Analytics Section for HIMU -->
        <li class="nav-item">
            <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/modules/reports/') !== false ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#reportsCollapse" role="button" aria-expanded="false" aria-controls="reportsCollapse">
                <i class="fas fa-chart-bar"></i> Reports & Analytics <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse" id="reportsCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'assets.php' && strpos($_SERVER['PHP_SELF'], '/modules/reports/') !== false ? 'active' : ''; ?>" href="/choims/modules/reports/assets.php?category_id=1">
                            <i class="fas fa-laptop me-2"></i> IT Equipment Report
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'inventory.php' && strpos($_SERVER['PHP_SELF'], '/modules/reports/') !== false ? 'active' : ''; ?>" href="/choims/modules/reports/inventory.php?category_id=4">
                            <i class="fas fa-boxes me-2"></i> IT Supplies Report
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'transfers.php' && strpos($_SERVER['PHP_SELF'], '/modules/reports/') !== false ? 'active' : ''; ?>" href="/choims/modules/reports/transfers.php?requires_himu_approval=1">
                            <i class="fas fa-exchange-alt me-2"></i> IT Transfers Report
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'mr_report.php' ? 'active' : ''; ?>" href="/choims/modules/reports/mr_report.php">
                            MR Report
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        <?php elseif ($userRole == 'department'): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'department.php' ? 'active' : ''; ?>" href="/choims/dashboards/department.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>
        <?php elseif ($userRole == 'healthcenter'): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'health_center.php' ? 'active' : ''; ?>" href="/choims/dashboards/health_center.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>
        <?php endif; ?>

        <div class="nav-section-divider"></div>

        <?php if ((hasRole('logistics') || hasRole('godmode')) && strtolower($_SESSION['role']) !== 'superadmin'): ?>
        <div class="nav-section-label">RESOURCE MANAGEMENT</div>
        <li class="nav-item">
            <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/modules/assets/') !== false ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#assetsCollapse" role="button" aria-expanded="true" aria-controls="assetsCollapse">
                <i class="fas fa-laptop"></i> Asset Management <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="assetsCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'add.php' && strpos($_SERVER['PHP_SELF'], '/modules/assets/') !== false ? 'active' : ''; ?>" href="/choims/modules/assets/add.php">
                            Add Asset
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && strpos($_SERVER['PHP_SELF'], '/modules/assets/') !== false ? 'active' : ''; ?>" href="/choims/modules/assets/list.php">
                            Fixed Assets
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        <?php endif; ?>

        <!-- Inventory Section - Available for all users -->
        <div class="nav-section-label">INVENTORY & LOGISTICS</div>
        <li class="nav-item">
            <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/modules/inventory/') !== false ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#inventoryCollapse" role="button" aria-expanded="true" aria-controls="inventoryCollapse">
                <i class="fas fa-boxes"></i> Inventory <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="inventoryCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && strpos($_SERVER['PHP_SELF'], '/modules/inventory/') !== false ? 'active' : ''; ?>" href="/choims/modules/inventory/list.php">
                            Consumable Inventory
                        </a>
                    </li>
                    <?php if (hasRole('himu') || hasRole('department') || hasRole('healthcenter')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && strpos($_SERVER['PHP_SELF'], '/modules/assets/') !== false ? 'active' : ''; ?>" href="/choims/modules/assets/list.php">
                            Fixed Assets
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if (hasRole('godmode') || hasRole('logistics')): ?>
                    <?php if (strtolower($_SESSION['role']) !== 'superadmin'): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'add_product.php' ? 'active' : ''; ?>" href="/choims/modules/inventory/add_product.php">
                            Add New Product
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'stock_in.php' ? 'active' : ''; ?>" href="/choims/modules/inventory/stock_in.php">
                            Stock In
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'stock_out.php' ? 'active' : ''; ?>" href="/choims/modules/inventory/stock_out.php">
                            Stock Out
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'stock_logs.php' ? 'active' : ''; ?>" href="/choims/modules/inventory/stock_logs.php">
                            Stock Logs
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/modules/transfers/') !== false ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#transfersCollapse" role="button" aria-expanded="true" aria-controls="transfersCollapse">
                <i class="fas fa-exchange-alt"></i> Transfers <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="transfersCollapse">
                <ul class="nav flex-column">
                    <?php if ((hasRole('logistics') || hasRole('department') || hasRole('healthcenter') || hasRole('himu')) && strtolower($_SESSION['role']) !== 'superadmin'): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'create.php' ? 'active' : ''; ?>" href="/choims/modules/transfers/create.php">
                            Initiate Transfer
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if ((hasRole('logistics') || hasRole('department') || hasRole('healthcenter') || hasRole('himu')) && strtolower($_SESSION['role']) !== 'superadmin'): ?>
                    <?php if (!hasRole('himu')): // Show regular batch transfer options for non-HIMU roles ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'select.php' && isset($_GET['type']) && $_GET['type'] == 'asset' ? 'active' : ''; ?>" href="/choims/modules/transfers/batch/select.php?type=asset">
                            Batch Transfer Assets
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'select.php' && isset($_GET['type']) && $_GET['type'] == 'inventory' ? 'active' : ''; ?>" href="/choims/modules/transfers/batch/select.php?type=inventory">
                            Batch Transfer Consumables
                        </a>
                    </li>
                    <?php else: // Show HIMU-specific batch transfer options ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'select.php' && isset($_GET['type']) && $_GET['type'] == 'asset' ? 'active' : ''; ?>" href="/choims/modules/transfers/batch/select.php?type=asset">
                            Batch Transfer Assets
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'select.php' && isset($_GET['type']) && $_GET['type'] == 'inventory' ? 'active' : ''; ?>" href="/choims/modules/transfers/batch/select.php?type=inventory">
                            Batch Transfer Consumables
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>

                    <?php if ((hasRole('logistics') || hasRole('himu')) && strtolower($_SESSION['role']) !== 'superadmin'): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && isset($_GET['status']) && $_GET['status'] == 'Pending' ? 'active' : ''; ?>" href="/choims/modules/transfers/list.php?filter=1&status=Pending">
                            Approve Transfers
                        </a>
                    </li>
                    <?php endif; ?>

                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && !isset($_GET['status']) ? 'active' : ''; ?>" href="/choims/modules/transfers/list.php">
                            Transfer History
                        </a>
                    </li>
                </ul>
            </div>
        </li>

        <?php if (hasRole('godmode') || hasRole('superadmin') || hasRole('logistics') || hasRole('department') || hasRole('healthcenter')): ?>
        <div class="nav-section-divider"></div>
        <div class="nav-section-label">ANALYSIS & CONTROL</div>

        <?php if (hasRole('godmode') || hasRole('superadmin') || hasRole('logistics')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'assets.php' ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#reportsCollapse" role="button" aria-expanded="true" aria-controls="reportsCollapse">
                <i class="fas fa-chart-bar"></i> Reports <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="reportsCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'mr_report.php' || basename($_SERVER['PHP_SELF']) == 'mr_by_person.php' ? 'active' : ''; ?>" href="/choims/modules/reports/mr_report.php">
                            MR Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'assets.php' ? 'active' : ''; ?>" href="/choims/modules/reports/assets.php">
                            Asset Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'inventory.php' || basename($_SERVER['PHP_SELF']) == 'inventory_summary.php' ? 'active' : ''; ?>" href="/choims/modules/reports/inventory.php">
                            Inventory Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'transfers.php' ? 'active' : ''; ?>" href="/choims/modules/reports/transfers.php">
                            Transfer Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'inventory_additions.php' ? 'active' : ''; ?>" href="/choims/modules/reports/inventory_additions.php">
                            Inventory Additions
                        </a>
                    </li>
                    <?php /* Removed User Activity link */ ?>

                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'location_assets.php' ? 'active' : ''; ?>" href="/choims/modules/reports/location_assets.php">
                            Location Asset Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'asset_history.php' ? 'active' : ''; ?>" href="/choims/modules/reports/asset_history.php">
                            Asset History
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        <?php endif; ?>

        <?php if ((hasRole('department') || hasRole('healthcenter')) && !hasRole('godmode') && !hasRole('superadmin')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'mr_report.php' || basename($_SERVER['PHP_SELF']) == 'mr_by_person.php' ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#deptReportsCollapse" role="button" aria-expanded="true" aria-controls="deptReportsCollapse">
                <i class="fas fa-chart-bar"></i> Reports <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="deptReportsCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'mr_report.php' || basename($_SERVER['PHP_SELF']) == 'mr_by_person.php' ? 'active' : ''; ?>" href="/choims/modules/reports/mr_report.php">
                            MR Reports
                        </a>
                    </li>
                </ul>
            </div>
        </li>
        <?php endif; ?>

        <?php if (hasRole('godmode') || hasRole('superadmin')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'admin.php' ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#adminCollapse" role="button" aria-expanded="true" aria-controls="adminCollapse">
                <i class="fas fa-cog"></i> Administration <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="adminCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>" href="/choims/modules/admin/users.php">
                            <?php echo strtolower($_SESSION['role']) === 'superadmin' ? 'View Users' : 'User Management'; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'locations.php' ? 'active' : ''; ?>" href="/choims/modules/admin/locations.php">
                            <?php echo strtolower($_SESSION['role']) === 'superadmin' ? 'View Locations' : 'Locations'; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : ''; ?>" href="/choims/modules/admin/categories.php">
                            <?php echo strtolower($_SESSION['role']) === 'superadmin' ? 'View Categories' : 'Categories'; ?>
                        </a>
                    </li>

                    <?php if (strtolower($_SESSION['role']) == 'superadmin' || strtolower($_SESSION['role']) == 'godmode'): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'detailed_audit_logs.php' ? 'active' : ''; ?>" href="/choims/modules/reports/detailed_audit_logs.php">
                            Audit Logs
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php if (hasRole('Superadmin')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && strpos($_SERVER['PHP_SELF'], '/modules/assets/') !== false ? 'active' : ''; ?>" href="/choims/modules/assets/list.php?show_deleted=1">
                            Deleted Assets
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'list.php' && isset($_GET['show_deleted']) && $_GET['show_deleted'] == 1 && strpos($_SERVER['PHP_SELF'], '/modules/inventory/') !== false ? 'active' : ''; ?>" href="/choims/modules/inventory/list.php?show_deleted=1">
                            Deleted Inventory Items
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </li>
        <?php endif; ?>

        <?php if (hasRole('godmode')): ?>
        <li class="nav-item">
            <a class="nav-link <?php echo strpos($_SERVER['PHP_SELF'], '/maintenance/') !== false ? 'active' : ''; ?>" data-bs-toggle="collapse" href="#maintenanceCollapse" role="button" aria-expanded="true" aria-controls="maintenanceCollapse">
                <i class="fas fa-tools"></i> Maintenance <i class="fas fa-chevron-down"></i>
            </a>
            <div class="collapse show" id="maintenanceCollapse">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'db_maintenance.php' ? 'active' : ''; ?>" href="/choims/maintenance/db_maintenance.php">
                            Database Maintenance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'performance_optimize.php' ? 'active' : ''; ?>" href="/choims/maintenance/performance_optimize.php">
                            Performance Optimization
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'query_monitor.php' ? 'active' : ''; ?>" href="/choims/maintenance/query_monitor.php">
                            Query Monitor
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'clear_data.php' ? 'active' : ''; ?>" href="/choims/maintenance/clear_data.php">
                            <span class="text-danger">Clear Data</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'reset_ids.php' ? 'active' : ''; ?>" href="/choims/maintenance/reset_ids.php">
                            <span class="text-danger">Reset Table IDs</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'fix_it_supplies.php' ? 'active' : ''; ?>" href="/choims/maintenance/fix_it_supplies.php">
                            <span class="text-danger">Fix IT Supplies</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'clear_users.php' ? 'active' : ''; ?>" href="/choims/modules/admin/clear_users.php">
                            <span class="text-danger">Clear Users</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'sku.php' ? 'active' : ''; ?>" href="/choims/modules/admin/sku.php">
                            <?php echo strtolower($_SESSION['role']) === 'superadmin' ? 'View SKUs' : 'SKU Management'; ?>
                        </a>
                    </li>

                </ul>
            </div>
        </li>
        <?php endif; ?>
        <?php endif; ?>
    </ul>
</div>

<div class="sidebar-footer">
    <a href="/choims/modules/auth/logout.php" class="btn-logout">
        <i class="fas fa-sign-out-alt"></i> Sign Out
    </a>
</div>

<!-- Add script to ensure collapse functionality works properly -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handler for sidebar collapse items
    const sidebarCollapseItems = document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]');

    sidebarCollapseItems.forEach(function(item) {
        item.addEventListener('click', function(e) {
            // Check if sidebar is collapsed
            const sidebar = document.querySelector('.sidebar');
            const isCollapsed = sidebar.classList.contains('collapsed');

            // If sidebar is collapsed, expand it first
            if (isCollapsed) {
                sidebar.classList.remove('collapsed');
                localStorage.setItem('sidebarState', 'expanded');

                // Clean up hover listeners
                cleanupHoverListeners();

                // Allow some time for the sidebar to expand before toggling the menu
                setTimeout(() => {
                    // Get the target collapse element
                    const targetId = this.getAttribute('href');
                    const targetCollapse = document.querySelector(targetId);

                    // Get Bootstrap collapse instance
                    const bsCollapse = bootstrap.Collapse.getInstance(targetCollapse);

                    if (bsCollapse) {
                        // If instance exists, toggle it
                        bsCollapse.toggle();
                    } else {
                        // Otherwise, initialize a new one and toggle
                        new bootstrap.Collapse(targetCollapse, {
                            toggle: true
                        });
                    }
                }, 300); // Short delay to let the sidebar expand animation finish
            } else {
                // If sidebar is already expanded, just toggle the menu
                // Get the target collapse element
                const targetId = this.getAttribute('href');
                const targetCollapse = document.querySelector(targetId);

                // Get Bootstrap collapse instance
                const bsCollapse = bootstrap.Collapse.getInstance(targetCollapse);

                if (bsCollapse) {
                    // If instance exists, toggle it
                    bsCollapse.toggle();
                } else {
                    // Otherwise, initialize a new one and toggle
                    new bootstrap.Collapse(targetCollapse, {
                        toggle: true
                    });
                }
            }

            // Prevent default to avoid scroll jump
            e.preventDefault();
        });
    });

    // We'll let the footer.js handle menu state persistence
    // This function is kept for backward compatibility but won't do anything
    function expandActiveMenus() {
        // This function is now handled by the menu state persistence code in footer.php
        console.log('Menu state is now handled by the persistence code in footer.php');
    }

    // Toggle sidebar on mobile - coordinate with hamburger menu
    const sidebarToggler = document.querySelector('.navbar-toggler');
    if (sidebarToggler) {
        sidebarToggler.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const sidebarIsCollapsed = sidebar.classList.contains('collapsed');

            // If sidebar is collapsed, expand it first
            if (sidebarIsCollapsed) {
                sidebar.classList.remove('collapsed');
                localStorage.setItem('sidebarState', 'expanded');
                cleanupHoverListeners();

                // Update toggle button state
                const toggleButton = document.getElementById('sidebarToggle');
                if (toggleButton) {
                    toggleButton.setAttribute('aria-expanded', 'true');
                }
            }

            // Then toggle mobile view
            sidebar.classList.toggle('show');
            document.body.classList.toggle('sidebar-open');
        });
    }

    // Handle clicks outside sidebar more robustly
    document.addEventListener('click', function(event) {
        const sidebar = document.querySelector('.sidebar');
        const sidebarToggler = document.querySelector('.navbar-toggler');
        const sidebarToggle = document.getElementById('sidebarToggle');

        // Check if we should close the mobile sidebar
        if (sidebar && sidebar.classList.contains('show') &&
            !sidebar.contains(event.target) &&
            !sidebarToggler?.contains(event.target) &&
            !sidebarToggle?.contains(event.target)) {
            sidebar.classList.remove('show');
            document.body.classList.remove('sidebar-open');
        }
    });

    // Sidebar toggle button functionality
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const sidebar = document.querySelector('.sidebar');

            // Toggle the collapsed class
            if (sidebar.classList.contains('collapsed')) {
                // Expanding the sidebar
                sidebar.classList.remove('collapsed');
                this.setAttribute('aria-expanded', 'true');
                this.setAttribute('aria-label', 'Collapse sidebar');
                localStorage.setItem('sidebarState', 'expanded');

                // Clean up hover functionality when expanded
                if (typeof cleanupHoverListeners === 'function') {
                    cleanupHoverListeners();
                }

                // Announce to screen readers
                if (typeof announceToScreenReaders === 'function') {
                    announceToScreenReaders('Sidebar expanded');
                }
            } else {
                // Collapsing the sidebar
                sidebar.classList.add('collapsed');
                this.setAttribute('aria-expanded', 'false');
                this.setAttribute('aria-label', 'Expand sidebar');
                localStorage.setItem('sidebarState', 'collapsed');

                // Don't add hover functionality as requested by user
                if (typeof cleanupHoverListeners === 'function') {
                    cleanupHoverListeners();
                    // Hover functionality removed as requested
                }

                // Announce to screen readers
                if (typeof announceToScreenReaders === 'function') {
                    announceToScreenReaders('Sidebar collapsed');
                }
            }
        });

        // Check localStorage for saved sidebar state - moved to DOMContentLoaded for reliability
    }

    // Pre-load all sidebar icons to prevent jank during transitions
    function preloadIcons() {
        const iconElements = document.querySelectorAll('.sidebar .nav-link i');
        if (iconElements.length > 0) {
            // Create a hidden container to force icon font loading
            const preloadContainer = document.createElement('div');
            preloadContainer.style.position = 'absolute';
            preloadContainer.style.width = '0';
            preloadContainer.style.height = '0';
            preloadContainer.style.overflow = 'hidden';
            document.body.appendChild(preloadContainer);

            // Preload each unique icon
            const iconClasses = new Set();
            iconElements.forEach(icon => {
                iconClasses.add(icon.className);
            });

            iconClasses.forEach(className => {
                const dummyIcon = document.createElement('i');
                dummyIcon.className = className;
                preloadContainer.appendChild(dummyIcon);
            });

            // Remove the container after icons are loaded
            setTimeout(() => {
                document.body.removeChild(preloadContainer);
            }, 1000);
        }
    }

    // This code runs when the page loads to set the correct sidebar state - with performance optimization
    document.addEventListener('DOMContentLoaded', function() {
        // Prevent flash of unstyled content
        document.body.style.visibility = 'hidden';

        // Preload all icons to prevent layout shifting
        preloadIcons();
        // Get elements once at the start
        const sidebar = document.querySelector('.sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const savedSidebarState = localStorage.getItem('sidebarState');

        if (!sidebar || !sidebarToggle) return;

        // Force the sidebar to be in the correct state based on localStorage
        if (savedSidebarState === 'collapsed') {
            // Apply collapsed state
            sidebar.classList.add('collapsed');
            sidebarToggle.setAttribute('aria-expanded', 'false');
            sidebarToggle.setAttribute('aria-label', 'Expand sidebar');

            // Clean up any existing listeners - NO hover functionality as requested
            if (typeof cleanupHoverListeners === 'function') {
                cleanupHoverListeners();
            }
        } else {
            // Expanded state
            sidebar.classList.remove('collapsed');
            sidebarToggle.setAttribute('aria-expanded', 'true');
            sidebarToggle.setAttribute('aria-label', 'Collapse sidebar');
        }

        // Ensure main content area is adjusted properly
        const mainContent = document.querySelector('.main-content');
        if (mainContent && sidebar.classList.contains('collapsed')) {
            mainContent.style.marginLeft = '80px';
            mainContent.style.width = 'calc(100% - 80px)';
        }

        // Improve link click performance by adding active state instantly
        const navLinks = document.querySelectorAll('.sidebar .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (!this.classList.contains('active') && !this.getAttribute('data-bs-toggle')) {
                    // Store the clicked link in sessionStorage
                    sessionStorage.setItem('lastClickedLink', this.getAttribute('href'));
                }
            });

            // Apply active state to the current page's link
            if (link.getAttribute('href') === window.location.pathname) {
                link.classList.add('active');
            }
        });

        // Prevent flash of unstyled content - make visible after sidebar state is set
        requestAnimationFrame(() => {
            document.body.style.visibility = 'visible';
        });
    });

    // Track hover event listeners so we can remove them
    let activeNavItems = [];

    // Function for screen reader announcements
    function announceToScreenReaders(message) {
        const announcer = document.getElementById('sidebar-announcer');
        if (!announcer) {
            const newAnnouncer = document.createElement('div');
            newAnnouncer.id = 'sidebar-announcer';
            newAnnouncer.setAttribute('aria-live', 'polite');
            newAnnouncer.setAttribute('class', 'sr-only');
            document.body.appendChild(newAnnouncer);
            setTimeout(() => {
                newAnnouncer.textContent = message;
            }, 100);
        } else {
            announcer.textContent = '';
            setTimeout(() => {
                announcer.textContent = message;
            }, 100);
        }
    }

    // Function to clean up existing hover listeners
    function cleanupHoverListeners() {
        // Clean up any existing hover events by cloning and replacing
        activeNavItems.forEach(item => {
            const parent = item.parentNode;
            if (parent) {
                const clone = item.cloneNode(true);
                parent.replaceChild(clone, item);
            }
        });
        activeNavItems = [];
    }

    // Function to add hover functionality for submenus in collapsed state
    function addCollapseHoverFunctionality(isCollapsed) {
    // Hover functionality disabled per user request
    return; // Skip all hover functionality
        if (!isCollapsed) return;

        const navItems = document.querySelectorAll('.sidebar .nav-item');
        activeNavItems = [];

        navItems.forEach(function(item) {
            const collapseLink = item.querySelector('.nav-link[data-bs-toggle="collapse"]');
            if (collapseLink) {
                const targetId = collapseLink.getAttribute('href');
                const targetCollapse = document.querySelector(targetId);

                if (targetCollapse) {
                    // Style submenus for proper appearance when collapsed - with enhanced styling
                    targetCollapse.style.position = 'fixed';
                    targetCollapse.style.left = '80px'; // Adjusted to avoid overlapping
                    targetCollapse.style.zIndex = '1050';
                    targetCollapse.style.minWidth = '220px';
                    targetCollapse.style.maxWidth = '280px';
                    targetCollapse.style.maxHeight = '75vh';
                    targetCollapse.style.overflowY = 'auto';
                    targetCollapse.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
                    targetCollapse.style.background = '#fff';
                    targetCollapse.style.borderRadius = '8px';
                    targetCollapse.style.padding = '8px 0';
                    targetCollapse.style.border = '1px solid rgba(0,0,0,0.08)';
                    targetCollapse.style.opacity = '0';
                    targetCollapse.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
                    targetCollapse.style.transform = 'translateX(-10px)';

                    // Style the submenu links to look more like a dropdown
                    const submenuLinks = targetCollapse.querySelectorAll('.nav-link');
                    submenuLinks.forEach(link => {
                        link.style.fontSize = '0.9rem';
                        link.style.padding = '8px 16px';
                        link.style.color = '#333';
                        link.style.borderRadius = '4px';
                        link.style.margin = '2px 8px';
                        link.style.textAlign = 'left';
                        link.style.display = 'block';
                        link.style.transition = 'all 0.2s ease';
                    });

                    // Ensure submenu stays within viewport
                    const viewportHeight = window.innerHeight;
                    const menuTop = (item.offsetTop - 10);
                    const menuHeight = targetCollapse.scrollHeight;

                    // If menu would go below viewport, position it differently
                    if (menuTop + menuHeight > viewportHeight - 50) {
                        targetCollapse.style.top = Math.max(10, viewportHeight - menuHeight - 50) + 'px';
                    } else {
                        targetCollapse.style.top = menuTop + 'px';
                    }

                    // Add hover event listeners with smooth animation
                    const mouseEnterHandler = function() {
                        if (document.querySelector('.sidebar').classList.contains('collapsed')) {
                            // Close any other open submenus first
                            document.querySelectorAll('.sidebar .collapse.show').forEach(menu => {
                                if (menu !== targetCollapse) {
                                    // Smooth closing of other menus
                                    menu.style.opacity = '0';
                                    menu.style.transform = 'translateX(-10px)';
                                    setTimeout(() => {
                                        bootstrap.Collapse.getInstance(menu)?.hide();
                                    }, 200);
                                }
                            });

                            // Show this submenu
                            new bootstrap.Collapse(targetCollapse, {
                                show: true
                            });

                            // Position the submenu correctly
                            targetCollapse.style.top = (item.offsetTop - 10) + 'px';

                            // Animate in with a small delay
                            setTimeout(() => {
                                targetCollapse.style.opacity = '1';
                                targetCollapse.style.transform = 'translateX(0)';
                                // Add a subtle highlight to the parent menu item
                                item.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
                            }, 50);
                        }
                    };

                    const mouseLeaveHandler = function() {
                        if (document.querySelector('.sidebar').classList.contains('collapsed')) {
                            // Use setTimeout to delay closing, allowing user to move to submenu
                            setTimeout(() => {
                                // Check if mouse is over the menu before hiding
                                if (!item.matches(':hover') && !targetCollapse.matches(':hover')) {
                                    // Animate out before hiding
                                    targetCollapse.style.opacity = '0';
                                    targetCollapse.style.transform = 'translateX(-10px)';

                                    // Reset the background color of the menu item
                                    item.style.backgroundColor = '';

                                    setTimeout(() => {
                                        bootstrap.Collapse.getInstance(targetCollapse)?.hide();
                                    }, 200);
                                }
                            }, 300);
                        }
                    };

                    item.addEventListener('mouseenter', mouseEnterHandler);
                    item.addEventListener('mouseleave', mouseLeaveHandler);
                    targetCollapse.addEventListener('mouseleave', mouseLeaveHandler);

                    // Track this item for later cleanup
                    activeNavItems.push(item);
                    activeNavItems.push(targetCollapse);
                }
            }
        });
    }
});
</script>