<?php
// Disable error reporting for production
ini_set('display_errors', 0);
error_reporting(0);

// Log errors instead of displaying them
ini_set('log_errors', 1);
ini_set('error_log', '../../logs/error.log');

// Use relative paths
require_once('../../config/database.php');
require_once('../../includes/functions.php');
require_once('../../includes/detailed_audit_log.php');
require_once('../../includes/csrf.php');

// Redirect if already logged in
if (isLoggedIn()) {
    // Redirect based on role
    if ($_SESSION['role'] == 'GodMode' || $_SESSION['role'] == 'Superadmin') {
        header('Location: ../../dashboards/superadmin.php');
    } else if ($_SESSION['role'] == 'Logistics') {
        header('Location: ../../dashboards/logistics.php');
    } else if ($_SESSION['role'] == 'HIMU') {
        header('Location: ../../dashboards/himu.php');
    } else if ($_SESSION['role'] == 'Department') {
        header('Location: ../../dashboards/department.php');
    } else if ($_SESSION['role'] == 'HealthCenter') {
        header('Location: ../../dashboards/health_center.php');
    } else {
        header('Location: ../../index.php');
    }
    exit();
}

$error = '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Verify CSRF token
    if (!check_csrf_token('login_form')) {
        $error = 'Security validation failed. Please try again.';
    } else {
        $username = sanitizeInput($_POST['username']);
        $password = $_POST['password'];

        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
            $query = "SELECT * FROM users WHERE username = ? AND is_active = 1";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 's', $username);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            if ($user = mysqli_fetch_assoc($result)) {
                // Check if the password is already hashed (starts with $2y$)
                $is_hashed = (strpos($user['password'], '$2y$') === 0);

                // Verify password - support both hashed and plain text during transition
                $password_verified = $is_hashed ?
                    password_verify($password, $user['password']) :
                    ($password === $user['password']);

                // If using plain text, update to hashed version for future logins
                if (!$is_hashed && $password === $user['password']) {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $update_pwd_query = "UPDATE users SET password = ? WHERE user_id = ?";
                    $update_pwd_stmt = mysqli_prepare($conn, $update_pwd_query);
                    mysqli_stmt_bind_param($update_pwd_stmt, 'si', $hashed_password, $user['user_id']);
                    mysqli_stmt_execute($update_pwd_stmt);
                    // Log the password update
                    error_log("Updated password hash for user ID: {$user['user_id']}");
                }

                if ($password_verified) {
                    // Set session variables
                    $_SESSION['user_id'] = $user['user_id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['location_id'] = $user['location_id'];

                    // Update last login time
                    $updateQuery = "UPDATE users SET last_login = NOW() WHERE user_id = ?";
                    $updateStmt = mysqli_prepare($conn, $updateQuery);
                    mysqli_stmt_bind_param($updateStmt, 'i', $user['user_id']);
                    mysqli_stmt_execute($updateStmt);

                    // Log login activity
                    logActivity($conn, 'Login', 'User', $user['user_id'], null, null);

                    // Log to detailed audit system
                    // Get location name if location_id is available
                    $location_name = null;
                    if (isset($user['location_id']) && $user['location_id']) {
                        $location_query = "SELECT location_name FROM locations WHERE location_id = ?";
                        $location_stmt = mysqli_prepare($conn, $location_query);
                        mysqli_stmt_bind_param($location_stmt, 'i', $user['location_id']);
                        mysqli_stmt_execute($location_stmt);
                        $location_result = mysqli_stmt_get_result($location_stmt);
                        if ($location = mysqli_fetch_assoc($location_result)) {
                            $location_name = $location['location_name'];
                        }
                        mysqli_stmt_close($location_stmt);
                    }

                    // Include IP address and location in the new_values
                    $new_values = [
                        'ip_address' => $_SERVER['REMOTE_ADDR'],
                        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                        'location_id' => $user['location_id'] ?? null,
                        'location_name' => $location_name
                    ];

                    // Call logUserAction with the location name
                    logUserAction($conn, $user['user_id'], 'login', $user['user_id'], null, $new_values);

                    // Redirect based on role
                    if ($user['role'] == 'GodMode' || $user['role'] == 'Superadmin') {
                        header('Location: ../../dashboards/superadmin.php');
                    } else if ($user['role'] == 'Logistics') {
                        header('Location: ../../dashboards/logistics.php');
                    } else if ($user['role'] == 'HIMU') {
                        header('Location: ../../dashboards/himu.php');
                    } else if ($user['role'] == 'Department') {
                        header('Location: ../../dashboards/department.php');
                    } else if ($user['role'] == 'HealthCenter') {
                        header('Location: ../../dashboards/health_center.php');
                    } else {
                        header('Location: ../../index.php');
                    }
                exit();
                } else {
                    $error = 'Invalid password.';
                }
            } else {
                $error = 'User not found or account is inactive.';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCHIMS | Login</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Hide Footer CSS -->
    <link href="../../assets/css/hide-footer.css" rel="stylesheet">

    <style>
        :root {
            --primary: #2c5e2e;
            --primary-dark: #234a25;
            --primary-light: rgba(44, 94, 46, 0.1);
            --text-dark: #1a2024;
            --text-light: #6e7c87;
            --text-muted: #97a4ae;
            --border-color: #e2e8f0;
            --background: #ffffff;
            --error: #e11d48;
            --error-light: #fff1f1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
            background-color: var(--background);
            color: var(--text-dark);
        }

        .login-container {
            display: flex;
            height: 100vh;
            width: 100%;
        }

        .login-image {
            flex: 1.2;
            position: relative;
            box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
            clip-path: polygon(0 0, 100% 0, 92% 100%, 0% 100%);
            overflow: hidden;
            isolation: isolate; /* Creates a new stacking context */
        }

        .slideshow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1; /* Explicitly set z-index */
        }

        .slideshow-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            opacity: 0;
            transition: opacity 0.8s ease-in-out;
            will-change: opacity; /* Optimization for animations */
            transform: translateZ(0); /* Force hardware acceleration */
        }

        .slideshow-item.active {
            opacity: 1;
        }

        .slideshow-loader {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 20;
            color: white;
            font-size: 24px;
            text-align: center;
        }

        .slideshow-loader i {
            animation: spin 1.5s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent; /* Removed green tint */
            z-index: 10; /* Higher than slideshow */
            pointer-events: none;
        }

        .image-caption {
            position: absolute;
            bottom: 60px;
            left: 60px;
            color: white;
            z-index: 15; /* Higher than overlay */
            max-width: 480px;
            background-color: rgba(0, 0, 0, 0.5);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .image-caption h3 {
            font-weight: 700;
            font-size: 2.4rem;
            margin-bottom: 12px;
            line-height: 1.2;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
            color: #ffffff;
        }

        .image-caption p {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.95;
            font-weight: 400;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
            color: #ffffff;
        }

        .login-form {
            flex: 0.8;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 60px;
            max-width: 560px;
            position: relative;
            overflow-y: auto;
            background-color: white;
        }

        .city-logo {
            text-align: center;
            margin-bottom: 25px;
            margin-top: -50px;
        }

        .city-logo img {
            max-width: 300px;
            height: auto;
            border-radius: 6px;
        }

        .login-header {
            margin-bottom: 48px;
        }

        .login-header h1 {
            font-weight: 700;
            color: var(--text-dark);
            font-size: 32px;
            margin-bottom: 12px;
            line-height: 1.2;
        }

        .login-header p {
            color: var(--text-light);
            font-size: 16px;
            margin-bottom: 0;
            line-height: 1.5;
        }

        .form-group {
            margin-bottom: 28px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
            display: block;
            letter-spacing: 0.02em;
        }

        .form-control {
            padding: 14px 18px 14px 48px;
            height: auto;
            border: 1.5px solid var(--border-color);
            border-radius: 12px;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.25s ease;
            background-color: var(--background);
            color: var(--text-dark);
            width: 100%;
        }

        .form-control::placeholder {
            color: var(--text-muted);
            font-weight: 400;
        }

        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(44, 94, 46, 0.15);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary);
            font-size: 16px;
            z-index: 10;
            transition: all 0.25s ease;
        }

        .input-group:focus-within .input-icon {
            color: var(--primary-dark);
        }

        .password-toggle {
            position: absolute;
            right: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            cursor: pointer;
            z-index: 10;
            transition: all 0.25s ease;
            font-size: 16px;
        }

        .password-toggle:hover {
            color: var(--text-dark);
        }

        .btn-login {
            background-color: var(--primary);
            border: none;
            padding: 15px;
            font-weight: 600;
            font-size: 16px;
            border-radius: 12px;
            color: white;
            width: 100%;
            margin-top: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.02em;
            height: 56px;
            box-shadow: 0 4px 12px rgba(44, 94, 46, 0.25);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: left 0.7s ease;
        }

        .btn-login:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(44, 94, 46, 0.3);
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:active {
            transform: translateY(0);
            box-shadow: 0 3px 10px rgba(44, 94, 46, 0.2);
        }

        .alert {
            border-radius: 12px;
            padding: 16px 18px;
            font-size: 14px;
            margin-bottom: 28px;
            background-color: var(--error-light);
            color: var(--error);
            border: 1px solid rgba(225, 29, 72, 0.1);
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .alert i {
            font-size: 18px;
            margin-right: 12px;
        }

        .footer {
            margin-top: 60px;
            font-size: 14px;
            color: var(--text-muted);
            text-align: center;
            padding-top: 16px;
            border-top: 1px solid var(--border-color);
            position: relative;
        }

        /* HIMU Logo */
        .himu-logo-container {
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 100;
        }

        .himu-logo {
            width: 90px;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .himu-logo:hover {
            transform: scale(1.05);
        }

        /* Animation Classes */
        .fade-in {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeIn 0.5s ease forwards;
        }

        .delay-1 {
            animation-delay: 0.1s;
        }

        .delay-2 {
            animation-delay: 0.2s;
        }

        .delay-3 {
            animation-delay: 0.3s;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 1200px) {
            .login-image {
                flex: 1;
            }
            .login-form {
                flex: 1;
            }
            .image-caption {
                bottom: 40px;
                left: 40px;
            }
        }

        @media (max-width: 992px) {
            .login-container {
                flex-direction: column;
                height: auto;
                overflow-y: auto;
            }

            .login-image {
                height: 300px;
                flex: none;
                clip-path: none;
            }

            .slideshow, .slideshow-item {
                height: 300px;
            }

            .image-overlay {
                clip-path: none;
                z-index: 10;
                background: transparent;
            }

            .login-form {
                padding: 40px 24px;
                max-width: 100%;
            }

            .city-logo {
                text-align: center;
                margin-top: 0;
            }

            .city-logo img {
                max-width: 220px;
            }

            .image-caption {
                left: 20px;
                right: 20px;
                bottom: 20px;
                max-width: calc(100% - 40px);
                padding: 15px;
            }

            .image-caption h3 {
                font-size: 1.6rem;
                margin-bottom: 8px;
            }

            .image-caption p {
                font-size: 1rem;
                margin-bottom: 0;
            }

            .login-header {
                margin-bottom: 32px;
            }

            .himu-logo-container {
                position: static;
                display: flex;
                justify-content: center;
                margin-top: 20px;
            }

            .himu-logo {
                width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-image">
            <!-- Slideshow container -->
            <div class="slideshow" id="healthCenterSlideshow">
                <!-- Slideshow items will be added dynamically via JavaScript -->
                <div class="slideshow-loader" id="slideshowLoader">
                    <i class="fas fa-circle-notch"></i>
                    <div>Loading health centers...</div>
                </div>
            </div>

            <!-- Overlay layer for maintaining z-index hierarchy -->
            <div class="image-overlay"></div>

            <!-- Caption with highest z-index -->
            <div class="image-caption">
                <h3 class="fade-in">PCHIMS</h3>
                <p class="fade-in delay-1">Inventory Management System</p>
            </div>
        </div>

        <div class="login-form">
            <div class="city-logo fade-in">
                <img src="/choims/assets/img/alagangparanaque.jpg" alt="Alaga ng Paranaque Logo">
            </div>
            <div class="login-header fade-in">
                <h1>Welcome to PCHIMS</h1>
                <p>Sign in to access your account</p>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert fade-in" role="alert">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <form method="post" action="">
                <?php echo csrf_token_field('login_form'); ?>
                <div class="form-group fade-in delay-1">
                    <label for="username" class="form-label">Username</label>
                    <div class="input-group">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" name="username"
                               placeholder="Enter your username" required
                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                    </div>
                </div>

                <div class="form-group fade-in delay-2">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" name="password"
                               placeholder="Enter your password" required>
                        <i class="fas fa-eye-slash password-toggle" id="togglePassword"></i>
                    </div>
                </div>

                <button type="submit" class="btn btn-login fade-in delay-3">
                    Sign In
                </button>
            </form>

            <div class="footer fade-in delay-3">
                &copy; <?php echo date('Y'); ?> PCHIMS
            </div>

            <!-- HIMU Logo in bottom right corner -->
            <div class="himu-logo-container fade-in delay-3">
                <img src="/choims/assets/img/himulogo.png" alt="HIMU Logo" class="himu-logo">
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Password toggle visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = this;

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            }
        });

        // Focus effect for input fields
        const inputFields = document.querySelectorAll('.form-control');
        inputFields.forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', () => {
                input.parentElement.classList.remove('focused');
            });
        });

        // Health Center Slideshow
        document.addEventListener('DOMContentLoaded', function() {
            const slideshowContainer = document.getElementById('healthCenterSlideshow');

            // List of potential health center images
            const healthCenterImages = [
                '/choims/assets/img/centers/455757310_1214472023075189_6508655481554706135_n.jpg',
                '/choims/assets/img/centers/Bagong Silang HC (Cleverland) FRONT VIEW.jpg',
                '/choims/assets/img/centers/BF HOMES HEA_LTH CENTER (DELA RAMA) LOBBY ENTRANCE.jpg',
                '/choims/assets/img/centers/CITYEMPLOYEESCLINIC_Julia.jpg',
                '/choims/assets/img/centers/Don Galo HC_NJ (2).jpg',
                '/choims/assets/img/centers/IMG_2083.JPG',
                '/choims/assets/img/centers/IMG_2675.JPG',
                '/choims/assets/img/centers/IMG_8234.jpeg',
                '/choims/assets/img/centers/LA HUERTA HEALTH CENTEER.jpg',
                '/choims/assets/img/centers/SAMPALOC SITE II HC PORTRAIT.jpg',
                '/choims/assets/img/centers/San Dionisio HC1_Remigia .jpg',
                '/choims/assets/img/centers/Sto. Nino-HC_Nolijane (3).jpg',
                '/choims/assets/img/centers/TAMBO HC FRONT VIEW 1.jpeg',
                '/choims/assets/img/centers/viber_image_2025-04-25_14-37-10-052.jpg',
                '/choims/assets/img/centers/VITALEZ HC FRONT VIEW 3.jpg'
            ];

            let currentIndex = 0;
            let slideshowInterval;
            let validImages = [];
            let imagesChecked = 0;

            // Check which images exist and build a valid image array
            function checkImages(callback) {
                if (healthCenterImages.length === 0) {
                    // Fallback if no images are in the list
                    callback([]);
                    return;
                }

                healthCenterImages.forEach((src) => {
                    const img = new Image();

                    img.onload = function() {
                        // Image loaded successfully, add to valid images
                        validImages.push(src);
                        imagesChecked++;
                        if (imagesChecked === healthCenterImages.length) {
                            callback(validImages);
                        }
                    };

                    img.onerror = function() {
                        // Image failed to load, skip it
                        imagesChecked++;
                        if (imagesChecked === healthCenterImages.length) {
                            callback(validImages);
                        }
                    };

                    img.src = src;
                });
            }

            // Initialize slideshow with valid images
            checkImages(function(validImageList) {
                // Hide the loader
                const loader = document.getElementById('slideshowLoader');
                if (loader) {
                    loader.style.display = 'none';
                }

                // Check if we have any valid images
                if (validImageList.length === 0) {
                    // No valid images found, display a fallback
                    const fallbackItem = document.createElement('div');
                    fallbackItem.className = 'slideshow-item active';
                    fallbackItem.style.backgroundColor = '#f5f5f5';
                    slideshowContainer.appendChild(fallbackItem);
                    return; // Exit early, no slideshow needed
                }

                // Create slideshow items with valid images
                validImageList.forEach((imagePath, index) => {
                    const slideItem = document.createElement('div');
                    slideItem.className = 'slideshow-item';
                    slideItem.style.backgroundImage = `url('${imagePath}')`;

                    // Make the first item active
                    if (index === 0) {
                        slideItem.classList.add('active');
                    }

                    slideshowContainer.appendChild(slideItem);
                });

                // Only setup slideshow if we have more than one image
                if (validImageList.length > 1) {
                    const slideItems = document.querySelectorAll('.slideshow-item');

                    // Function to change slides
                    function changeSlide() {
                        // Remove active class from current slide
                        slideItems[currentIndex].classList.remove('active');

                        // Move to next slide or back to first slide if at the end
                        currentIndex = (currentIndex + 1) % slideItems.length;

                        // Add active class to new current slide
                        setTimeout(() => {
                            slideItems[currentIndex].classList.add('active');
                        }, 50); // Small delay to ensure proper transition
                    }

                    // Change slide every 1.7 seconds
                    slideshowInterval = setInterval(changeSlide, 1700);

                    // Clean up interval when page is unloaded
                    window.addEventListener('beforeunload', function() {
                        if (slideshowInterval) {
                            clearInterval(slideshowInterval);
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>