/* Monthly Status Page - Soft Green Theme with Rounded Edges and Shadow Depth */

:root {
  /* Soft Green Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-lighter: #81C784;
  --primary-dark: #1B5E20;
  --secondary: #607D8B;
  --text-on-primary: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-accent: #E8F5E9;
  --border-color: #C8E6C9;
  --success: #388E3C;
  --warning: #FFA000;
  --danger: #D32F2F;
  --info: #0288D1;
  --light: #F1F8E9;
  --dark: #1B5E20;
  --white: #ffffff;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* Shadows & Effects */
  --shadow-sm: 0 4px 6px rgba(46, 125, 50, 0.05);
  --shadow: 0 8px 15px rgba(46, 125, 50, 0.08);
  --shadow-md: 0 12px 22px rgba(46, 125, 50, 0.1);
  --shadow-lg: 0 15px 30px rgba(46, 125, 50, 0.12);
  --shadow-xl: 0 20px 40px rgba(46, 125, 50, 0.15);
  --transition: all 0.3s ease;

  /* Border Radius */
  --radius-sm: 10px;
  --radius: 15px;
  --radius-md: 20px;
  --radius-lg: 25px;
  --radius-full: 9999px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
  --space-8: 3rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Base Styles */
body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: var(--space-5);
}

/* Page Title */
.page-title {
  margin-bottom: var(--space-5);
  animation: fadeIn 0.5s ease-out;
}

.page-title h1 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--primary-dark);
  margin-bottom: var(--space-2);
}

.page-title .lead {
  color: var(--text-secondary);
  font-weight: 400;
}

.page-title i {
  color: var(--primary);
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 4px;
  background: var(--primary);
  border-radius: 2px;
}

/* Filter Section */
.filter-buttons {
  animation: fadeIn 0.5s ease-out;
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.filter-buttons .input-group {
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.filter-buttons .input-group-text,
.filter-buttons .form-select,
.filter-buttons .btn {
  border: none;
  padding: var(--space-3) var(--space-4);
}

.filter-buttons .input-group-text {
  background-color: var(--light);
  color: var(--primary);
}

.filter-buttons .form-select {
  background-color: var(--white);
  color: var(--text-primary);
  cursor: pointer;
  transition: var(--transition);
}

.filter-buttons .form-select:focus {
  box-shadow: none;
  background-color: var(--bg-accent);
}

.filter-buttons .btn {
  font-weight: 600;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

/* Stat Cards */
.stat-card {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  margin-bottom: var(--space-5);
  border: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: var(--white);
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.stat-card .card-body {
  padding: var(--space-5);
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, var(--white) 0%, var(--light) 100%);
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--primary);
  margin-bottom: var(--space-2);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-dark);
  margin-bottom: var(--space-3);
  line-height: 1;
}

.stat-link {
  color: var(--primary);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: var(--transition);
}

.stat-link:hover {
  color: var(--primary-dark);
}

.stat-icon-wrapper {
  position: absolute;
  top: var(--space-5);
  right: var(--space-5);
  font-size: 2.5rem;
  opacity: 0.15;
  color: var(--primary);
}

/* Progress Bar */
.progress {
  height: 8px;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  overflow: hidden;
  margin-bottom: var(--space-3);
}

.progress-bar {
  background-color: var(--primary) !important;
  border-radius: var(--radius-full);
}

/* Content Card */
.content-card {
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  background-color: var(--white);
  overflow: hidden;
  animation: scaleIn 0.5s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.content-card .card-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%) !important;
  color: var(--white) !important;
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-4) var(--space-5);
}

.content-card .card-header h5 {
  font-weight: 600;
  margin: 0;
  color: var(--white) !important;
}

.content-card .card-header i {
  color: var(--white) !important;
}

.content-card .card-body {
  padding: 0;
}

/* Table Styling */
.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: var(--bg-accent) !important;
  color: var(--primary-dark) !important;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  padding: var(--space-3) var(--space-4);
  border-top: none;
  border-bottom: 1px solid var(--border-color);
}

.table tbody td {
  padding: var(--space-4);
  vertical-align: middle;
  border-top: 1px solid var(--border-color);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--bg-accent);
}

/* Status Indicators */
.status-row.completed {
  background-color: rgba(56, 142, 60, 0.05);
}

.status-row.pending {
  background-color: rgba(255, 160, 0, 0.05);
}

.badge.bg-dark {
  background-color: var(--primary) !important;
}

.badge.bg-secondary {
  background-color: var(--warning) !important;
  color: var(--white);
}

/* Buttons */
.btn {
  border-radius: var(--radius-sm);
  font-weight: 600;
  padding: var(--space-2) var(--space-4);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn-dark {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: var(--white);
}

.btn-dark:hover {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
  box-shadow: var(--shadow-sm);
}

.btn-outline-dark {
  color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.btn-outline-dark:hover {
  background-color: var(--primary) !important;
  color: var(--white) !important;
  box-shadow: var(--shadow-sm);
}

.btn-sm {
  font-size: 0.75rem;
  padding: var(--space-1) var(--space-2);
}

.btn-group {
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-sm);
  overflow: hidden;
}
