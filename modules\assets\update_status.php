<?php
// Start output buffering
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/audit_log.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Ensure user is logged in
requireLogin();

// Get asset ID and new status from URL parameters
$asset_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$new_status = isset($_POST['status']) ? sanitizeInput($_POST['status']) : '';
$reason = isset($_POST['reason']) ? sanitizeInput($_POST['reason']) : '';
$redirect_url = isset($_POST['redirect_url']) ? $_POST['redirect_url'] : "/choims/modules/assets/view.php?id=$asset_id";

// Validate inputs
if ($asset_id <= 0) {
    $_SESSION['error'] = "Invalid asset ID";
    header("Location: /choims/modules/assets/list.php");
    exit();
}

if (empty($new_status)) {
    $_SESSION['error'] = "Status cannot be empty";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Validate reason
if (empty($reason)) {
    $_SESSION['error'] = "Reason for status change is required";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Validate status value
$allowed_statuses = ['In use', 'Available', 'Under Repair', 'Defective'];
if (!in_array($new_status, $allowed_statuses)) {
    $_SESSION['error'] = "Invalid status value";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Role-based status change restrictions
$userRole = strtolower($_SESSION['role']);

// Health centers and departments can only mark items as "Defective" or "In use" or "Available"
// They cannot mark items as "Under Repair" as that's HIMU's responsibility
if (($userRole == 'healthcenter' || $userRole == 'department') && $new_status == 'Under Repair') {
    $_SESSION['error'] = "Only HIMU can change status to 'Under Repair'. Please mark the item as 'Defective' instead.";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Only HIMU, GodMode, and Superadmin can change status from "Defective" to "Under Repair" or "Available"
if ($asset['status'] == 'Defective' && ($new_status == 'Under Repair' || $new_status == 'Available') &&
    !hasRole('HIMU', 'GodMode', 'Superadmin')) {
    $_SESSION['error'] = "Only HIMU can change status from 'Defective' to 'Under Repair' or 'Available'.";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Check if the asset exists and user has permission to update it
$query = "
    SELECT fa.*, l.location_id
    FROM fixed_assets fa
    JOIN locations l ON fa.current_location_id = l.location_id
    WHERE fa.asset_id = ?
";
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    $_SESSION['error'] = "Asset not found";
    header("Location: /choims/modules/assets/list.php");
    exit();
}

$asset = mysqli_fetch_assoc($result);

// Check if the asset has an active transfer
$activeTransferQuery = "
    SELECT COUNT(*) as count
    FROM transfers
    WHERE asset_id = ?
    AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
";
$activeTransferStmt = mysqli_prepare($conn, $activeTransferQuery);
mysqli_stmt_bind_param($activeTransferStmt, 'i', $asset_id);
mysqli_stmt_execute($activeTransferStmt);
$activeTransferResult = mysqli_stmt_get_result($activeTransferStmt);
$activeTransfer = mysqli_fetch_assoc($activeTransferResult);
$hasActiveTransfer = $activeTransfer['count'] > 0;

// Prevent status changes for assets with active transfers
if ($hasActiveTransfer) {
    $_SESSION['error'] = "This asset has an active transfer and cannot be modified";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Check user permission (all roles can update status of items in their location)
$user_location_id = getUserLocationId();

// For non-godmode/superadmin/logistics roles, ensure they can only update items in their location
if (!hasRole('GodMode', 'Superadmin', 'Logistics') && $asset['location_id'] != $user_location_id) {
    $_SESSION['error'] = "You don't have permission to update this asset";
    header("Location: /choims/modules/assets/view.php?id=$asset_id");
    exit();
}

// Get the old status value for audit log
$old_status = $asset['status'];

// Update the asset status
$update_query = "UPDATE fixed_assets SET status = ?, updated_at = NOW() WHERE asset_id = ?";
$update_stmt = mysqli_prepare($conn, $update_query);
mysqli_stmt_bind_param($update_stmt, 'si', $new_status, $asset_id);

if (mysqli_stmt_execute($update_stmt)) {
    // Add to audit log with reason
    logAction($conn, $_SESSION['user_id'], "Update Status", "fixed_assets", $asset_id,
              json_encode(["status" => $old_status]),
              json_encode(["status" => $new_status, "reason" => $reason]));

    // Add to detailed audit log
    // Get asset details for the log
    $asset_query = "SELECT fa.*, sm.sku_name, sm.sku_code, c.category_name, l.location_name
                    FROM fixed_assets fa
                    JOIN sku_master sm ON fa.sku_id = sm.sku_id
                    JOIN categories c ON sm.category_id = c.category_id
                    JOIN locations l ON fa.current_location_id = l.location_id
                    WHERE fa.asset_id = ?";
    $asset_stmt = mysqli_prepare($conn, $asset_query);
    mysqli_stmt_bind_param($asset_stmt, 'i', $asset_id);
    mysqli_stmt_execute($asset_stmt);
    $asset_result = mysqli_stmt_get_result($asset_stmt);
    $asset_data = mysqli_fetch_assoc($asset_result);

    // Prepare old and new values for the log
    $old_values = [
        'status' => $old_status
    ];

    $new_values = [
        'status' => $new_status,
        'reason' => $reason
    ];

    // Log the status change
    logFixedAssetAction($conn, $_SESSION['user_id'], 'update', $asset_id, $old_values, $new_values);

    // If the new status is "Defective", create a notification for HIMU users (IT equipment only) or Logistics users (office/medical equipment)
    if ($new_status === 'Defective') {
        // Get asset details for the notification
        $assetDetailsQuery = "
            SELECT fa.asset_name, fa.serial_number, sm.sku_name, c.category_name, c.category_id, l.location_name
            FROM fixed_assets fa
            JOIN sku_master sm ON fa.sku_id = sm.sku_id
            JOIN categories c ON sm.category_id = c.category_id
            JOIN locations l ON fa.current_location_id = l.location_id
            WHERE fa.asset_id = ?
        ";
        $assetDetailsStmt = mysqli_prepare($conn, $assetDetailsQuery);
        mysqli_stmt_bind_param($assetDetailsStmt, 'i', $asset_id);
        mysqli_stmt_execute($assetDetailsStmt);
        $assetDetailsResult = mysqli_stmt_get_result($assetDetailsStmt);
        $assetDetails = mysqli_fetch_assoc($assetDetailsResult);

        // Create notification title and message
        $notificationTitle = "Defective Asset Reported";
        $notificationMessage = "Asset: {$assetDetails['asset_name']} ({$assetDetails['sku_name']})";
        if (!empty($assetDetails['serial_number'])) {
            $notificationMessage .= "\nSerial Number: {$assetDetails['serial_number']}";
        }
        $notificationMessage .= "\nCategory: {$assetDetails['category_name']}";
        $notificationMessage .= "\nLocation: {$assetDetails['location_name']}";
        $notificationMessage .= "\nReason: {$reason}";

        // Determine which role should receive the notification based on category
        if ($assetDetails['category_id'] == 1) {
            // IT Equipment (category_id = 1) - notify HIMU users
            $notifyQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id, is_read, created_at)
                            SELECT user_id, 'asset_defective', ?, ?, 'asset', ?, 0, NOW()
                            FROM users
                            WHERE LOWER(role) = 'himu'";
        } else if ($assetDetails['category_id'] == 2 || $assetDetails['category_id'] == 3) {
            // Office Equipment (category_id = 2) or Medical Equipment (category_id = 3) - notify Logistics users
            $notifyQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id, is_read, created_at)
                            SELECT user_id, 'asset_defective', ?, ?, 'asset', ?, 0, NOW()
                            FROM users
                            WHERE LOWER(role) = 'logistics'";
        } else {
            // For other categories, notify both HIMU and Logistics
            $notifyQuery = "INSERT INTO notifications (user_id, notification_type, title, message, related_entity, related_id, is_read, created_at)
                            SELECT user_id, 'asset_defective', ?, ?, 'asset', ?, 0, NOW()
                            FROM users
                            WHERE LOWER(role) IN ('himu', 'logistics')";
        }

        $notifyStmt = mysqli_prepare($conn, $notifyQuery);
        mysqli_stmt_bind_param($notifyStmt, 'ssi', $notificationTitle, $notificationMessage, $asset_id);
        mysqli_stmt_execute($notifyStmt);
    }

    $_SESSION['success'] = "Asset status updated successfully";
} else {
    $_SESSION['error'] = "Failed to update asset status: " . mysqli_error($conn);
}

// Clear output buffer before redirect
ob_end_clean();

// Redirect back to the asset view or specified redirect URL
header("Location: $redirect_url");
exit();