/* Asset Maintenance Management - Modern Design */

:root {
  /* Modern Color Palette */
  --primary: #2E7D32;
  --primary-light: #4CAF50;
  --primary-dark: #1B5E20;
  --secondary: #1B5E20;
  --text-on-primary: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-accent: #f5f5f5;
  --border-color: #f0f0f0;
  --success: #4CAF50;
  --warning: #ff9800;
  --danger: #ef4444;
  --info: #3b82f6;
  --light: #E8F5E9;
  --dark: #1B5E20;

  /* Background Colors */
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --bg-pattern: #f9f9f9;

  /* Shadows & Effects */
  --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 12px 28px rgba(0, 0, 0, 0.15);
  --transition: all 0.2s ease;

  /* Border Radius */
  --radius-sm: 12px;
  --radius: 16px;
  --radius-lg: 20px;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.5rem;
  --space-6: 2rem;
}

/* Base styles and typography */
body {
  background-color: var(--bg-light);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.container-fluid {
  padding: 0 1.5rem;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

/* Maintenance header section */
.maintenance-header {
  position: relative;
  background: #ffffff;
  margin: -1.5rem -1.5rem 2rem -1.5rem;
  padding: 1.5rem 2rem 1rem;
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  color: var(--text-primary);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.maintenance-header .title-container {
  display: flex;
  align-items: center;
}

.maintenance-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.025em;
  white-space: nowrap;
}

.maintenance-title-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-light);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
  margin-right: 1rem;
}

.maintenance-subtitle {
  color: var(--text-secondary);
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

/* Breadcrumb styling */
.breadcrumb {
  margin-bottom: 0;
  padding: 0;
  background: transparent;
}

.breadcrumb-item {
  font-size: 0.875rem;
  font-weight: 500;
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--text-secondary);
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: var(--text-secondary);
}

/* Header actions */
.header-actions {
  display: flex;
  gap: 0.75rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.7rem 1.25rem;
  background-color: var(--bg-white);
  color: var(--text-primary);
  border-radius: var(--radius);
  font-weight: 600;
  transition: all 0.2s ease;
  text-decoration: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  font-size: 0.9rem;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.action-btn:hover {
  background-color: var(--bg-white);
  color: #000000;
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.action-btn:hover::after {
  opacity: 1;
}

.action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.action-btn i {
  font-size: 1rem;
  color: #000000;
}

.action-primary {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.action-primary:hover {
  background-color: #ffffff;
  color: #000000;
}

.action-primary i {
  color: #000000;
}

/* Filter Card */
.filter-card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.filter-card:hover {
  box-shadow: var(--shadow-lg);
}

.filter-card-header {
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-title {
  font-weight: 600;
  font-size: 1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-title i {
  color: var(--primary);
}

.filter-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: var(--primary-light);
  color: white;
  border-radius: 50px;
  font-size: 0.7rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.filter-toggle-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.7rem 1.25rem;
  background-color: var(--bg-white);
  color: var(--text-primary);
  border-radius: var(--radius);
  font-weight: 600;
  transition: all 0.2s ease;
  text-decoration: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  font-size: 0.9rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.filter-card-body {
  padding: 1.5rem;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.filter-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filter-control:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  outline: none;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.filter-btn {
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
}

.filter-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.filter-btn:hover::after {
  opacity: 1;
}

.filter-btn-primary {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.filter-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.filter-btn-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.filter-btn-secondary {
  background-color: #f5f5f5;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-btn-secondary:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.05);
}

.filter-btn-secondary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Input groups with icons */
.input-group-icon {
  position: relative;
}

.input-group-icon .filter-control {
  padding-left: 2.5rem;
}

.input-group-icon i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

/* Date range picker */
.date-range {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.date-range .filter-control {
  flex: 1;
}

.date-separator {
  color: var(--text-secondary);
  font-weight: 500;
}

/* Defective Assets Card */
.defective-card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.defective-card:hover {
  box-shadow: var(--shadow-lg);
}

.defective-card-header {
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.defective-title {
  font-weight: 600;
  font-size: 1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--danger);
}

.defective-title i {
  color: var(--danger);
}

.defective-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.defective-card-body {
  padding: 1.5rem;
}

.defective-search {
  margin-bottom: 1rem;
  position: relative;
}

.defective-search input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.defective-search input:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  outline: none;
}

.defective-search i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

/* Maintenance Records Card */
.records-card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  border: none;
  box-shadow: var(--shadow);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.records-card:hover {
  box-shadow: var(--shadow-lg);
}

.records-card-header {
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.records-title {
  font-weight: 600;
  font-size: 1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.records-title i {
  color: var(--primary);
}

.records-card-body {
  padding: 1.5rem;
}

.records-search {
  margin-bottom: 1rem;
  position: relative;
}

.records-search input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.records-search input:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  outline: none;
}

.records-search i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

/* Table Styling */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 1.25rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.modern-table th:first-child {
  border-top-left-radius: var(--radius-sm);
}

.modern-table th:last-child {
  border-top-right-radius: var(--radius-sm);
}

.modern-table td {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
  vertical-align: middle;
}

.modern-table tr:last-child td {
  border-bottom: none;
}

.modern-table tr:last-child td:first-child {
  border-bottom-left-radius: var(--radius-sm);
}

.modern-table tr:last-child td:last-child {
  border-bottom-right-radius: var(--radius-sm);
}

.modern-table tr:hover td {
  background-color: var(--bg-secondary);
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.status-available {
  background-color: #e8f5e9;
  color: var(--success);
}

.status-in-use {
  background-color: #e3f2fd;
  color: var(--info);
}

.status-under-repair {
  background-color: #fff8e1;
  color: var(--warning);
}

.status-defective {
  background-color: #fee2e2;
  color: var(--danger);
}

.status-disposed {
  background-color: #e2e8f0;
  color: var(--text-secondary);
}

.status-completed {
  background-color: #e8f5e9;
  color: var(--success);
}

.status-in-progress {
  background-color: #fff8e1;
  color: var(--warning);
}

/* Action buttons */
.table-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-sm);
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  margin-right: 0.25rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.table-action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.table-action-btn:hover {
  background-color: #ffffff;
  color: #000000;
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.05);
}

.table-action-btn:hover::after {
  opacity: 1;
}

.table-action-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table-action-btn i {
  font-size: 0.875rem;
  color: #000000;
}

.table-action-btn.view-btn {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.table-action-btn.view-btn:hover {
  background-color: #ffffff;
  color: #000000;
}

.table-action-btn.edit-btn {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.table-action-btn.edit-btn:hover {
  background-color: #ffffff;
  color: #000000;
}

.table-action-btn.delete-btn {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.table-action-btn.delete-btn:hover {
  background-color: #ffffff;
  color: #000000;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1.5rem;
  text-align: center;
}

.empty-state i {
  font-size: 3rem;
  color: #cbd5e1;
  margin-bottom: 1.5rem;
}

.empty-state h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.empty-state p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  max-width: 400px;
}

.empty-state .action-btn,
.empty-state .action-primary {
  background-color: #ffffff;
  color: #000000;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 0.75rem 1.5rem;
}

.empty-state .action-btn:hover,
.empty-state .action-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  color: #000000;
}

.empty-state .action-btn i,
.empty-state .action-primary i {
  color: #000000;
}

/* Modal styling */
.modal {
  z-index: 1050;
}

.modal-backdrop {
  z-index: 1040;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-backdrop.show {
  opacity: 1;
}

.modal-dialog {
  margin: 1.75rem auto;
  max-width: 800px;
}

.modal-content {
  border: none;
  border-radius: var(--radius);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  background-color: #ffffff;
  position: relative;
}

.modal-header {
  background-color: #ffffff;
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--text-primary);
  margin: 0;
}

.btn-close {
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  opacity: 0.5;
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
  border: 0;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

.modal-body {
  padding: 1.5rem;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid var(--border-color);
  padding: 1.25rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Modal buttons */
.modal-footer .btn,
.btn-primary,
.btn-secondary,
.btn-danger,
.btn-warning {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
  background-color: #ffffff;
  color: #000000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  line-height: 1.5;
}

.modal-footer .btn::after,
.btn-primary::after,
.btn-secondary::after,
.btn-danger::after,
.btn-warning::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.modal-footer .btn:hover,
.btn-primary:hover,
.btn-secondary:hover,
.btn-danger:hover,
.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.05);
  text-decoration: none;
}

.modal-footer .btn:hover::after,
.btn-primary:hover::after,
.btn-secondary:hover::after,
.btn-danger:hover::after,
.btn-warning:hover::after {
  opacity: 1;
}

.modal-footer .btn:active,
.btn-primary:active,
.btn-secondary:active,
.btn-danger:active,
.btn-warning:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.btn-primary {
  background-color: #ffffff;
  color: #000000;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #000000;
}

.btn-danger {
  background-color: #ffffff;
  color: #000000;
}

.btn-warning {
  background-color: #ffffff;
  color: #000000;
}

/* Badge styling in modals */
.badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50px;
}

.bg-danger {
  background-color: #fee2e2 !important;
  color: #ef4444 !important;
}

.bg-warning {
  background-color: #fff8e1 !important;
  color: #ff9800 !important;
}

.bg-success {
  background-color: #e8f5e9 !important;
  color: #4CAF50 !important;
}

.bg-info {
  background-color: #e3f2fd !important;
  color: #3b82f6 !important;
}

/* Alert styling in modals */
.alert {
  position: relative;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
}

.alert-info {
  background-color: #e3f2fd;
  color: #0c63e4;
}

.alert .fas {
  margin-right: 0.75rem;
  font-size: 1.125rem;
}

/* Form styling */
.form-group,
.mb-3 {
  margin-bottom: 1.25rem;
}

.form-label,
.col-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: auto;
}

.form-control:focus {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  outline: none;
}

.form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background-color: #ffffff;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: auto;
}

.form-select:focus {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.05);
  outline: none;
}

.form-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.form-check {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.form-check-input {
  position: absolute;
  left: 0;
  width: 1rem;
  height: 1rem;
  margin-top: 0.25rem;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 0.25rem;
  background-color: #fff;
}

.form-check-input:checked {
  background-color: #000000;
  border-color: #000000;
}

.form-check-label {
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: 0;
}

/* Row and column styling for forms */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.75rem;
  margin-left: -0.75rem;
}

.col-md-6 {
  position: relative;
  width: 100%;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

@media (min-width: 768px) {
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

/* Background light for readonly inputs */
.bg-light {
  background-color: #f8f9fa !important;
}

/* Alert styling */
.alert {
  padding: 1rem 1.25rem;
  border-radius: var(--radius-sm);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alert-icon {
  font-size: 1.25rem;
  line-height: 1;
}

.alert-info {
  background-color: #e3f2fd;
  color: var(--info);
}

.alert-success {
  background-color: #e8f5e9;
  color: var(--success);
}

.alert-warning {
  background-color: #fff8e1;
  color: var(--warning);
}

.alert-danger {
  background-color: #fee2e2;
  color: var(--danger);
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
}

.pagination {
  display: flex;
  gap: 0.25rem;
}

.page-item {
  list-style: none;
}

.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--radius-sm);
  background-color: var(--bg-white);
  color: var(--text-primary);
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.page-link:hover {
  background-color: var(--bg-secondary);
  color: var(--primary);
  border-color: var(--primary-light);
}

.page-item.active .page-link {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.page-item.disabled .page-link {
  background-color: var(--bg-accent);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* Loading state */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: var(--radius);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animations */
.animate__animated.animate__fadeIn {
  animation-duration: 0.8s;
}

.animate__animated.animate__fadeInUp {
  animation-duration: 0.6s;
}

.animate__delay-1 {
  animation-delay: 0.2s;
}

.animate__delay-2 {
  animation-delay: 0.4s;
}

.animate__delay-3 {
  animation-delay: 0.6s;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .maintenance-header {
    padding: 1.25rem;
  }

  .maintenance-title {
    font-size: 1.5rem;
  }

  .maintenance-title-icon {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .filter-form {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-group {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .header-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .filter-card-header,
  .defective-card-header,
  .records-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .date-range {
    flex-direction: column;
    gap: 0.5rem;
  }

  .date-separator {
    display: none;
  }

  .table-responsive {
    margin: 0 -1.5rem;
    width: calc(100% + 3rem);
  }

  .modern-table th,
  .modern-table td {
    padding: 0.75rem 1rem;
  }
}
